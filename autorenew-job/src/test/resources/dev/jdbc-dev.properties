jdbc.driver=com.mysql.jdbc.Driver

#jdbc.url=*****************************************************************************************
#jdbc.username=bosstest
#jdbc.password=626f737374657374
#
#master.jdbc.url=*****************************************************************************************&user=bosstest&password=bosstest
#master.jdbc.username=bosstest
#master.jdbc.password=626f737374657374
#
#slave.jdbc.url=*************************************************************************************************************************
#slave.jdbc.username=bosstest
#slave.jdbc.password=626f737374657374

jdbc.url=*******************************************************************************************************************************
jdbc.username=db_hk
jdbc.password=44425f74657374

master.jdbc.url=*******************************************************************************************************************************
master.jdbc.username=db_hk
master.jdbc.password=44425f74657374

slave.jdbc.url=*******************************************************************************************************************************
slave.jdbc.username=db_hk
slave.jdbc.password=44425f74657374

#jdbc.url=*****************************************************************************************************************************
#jdbc.username=boss
#jdbc.password=5264692148297569342a415a
#
#master.jdbc.url=*****************************************************************************************************************************
#master.jdbc.username=boss
#master.jdbc.password=5264692148297569342a415a
#
#slave.jdbc.url=*****************************************************************************************************************************
#slave.jdbc.username=boss
#slave.jdbc.password=5264692148297569342a415a


#qiyue datasource
#qiyue.jdbc.url=6a6462633a6d7973716c3a2f2f626a2e626f7373746573742e772e716979692e64623a363138332f6875697975616e5f6f726465723f757365556e69636f64653d7472756526636861726163746572456e636f64696e673d5554462d3826757365723d626f7373746573742670617373776f72643d626f737374657374
#qiyue.jdbc.username=626f737374657374
#qiyue.jdbc.password=626f737374657374

qiyue.jdbc.url=6a6462633a6d7973716c3a2f2f686b2e6462766970686b636c75737465722e722e716979692e64623a333538372f6875697975616e5f6f726465723f757365556e69636f64653d7472756526636861726163746572456e636f64696e673d5554462d3826757365723d626f7373746573742670617373776f72643d626f737374657374
qiyue.jdbc.username=64625f686b
qiyue.jdbc.password=44425f74657374

# druid common config
druid.filters = wall,mergeStat,slf4j
druid.maxActive = 120
druid.initialSize = 20
druid.maxWait = 10000
druid.minIdle = 20
druid.timeBetweenEvictionRunsMillis = 600000
druid.minEvictableIdleTimeMillis = 300000
druid.validationQuery = select 'x'
druid.testWhileIdle = true
druid.testOnBorrow = false
druid.testOnReturn = false
druid.poolPreparedStatements = false
druid.maxOpenPreparedStatements = -1

# 分库配置
#sharding.jdbc.username=bosstest
#sharding.jdbc.password=bosstest
#sharding.jdbc.url.0.master=*******************************************************************************************
#sharding.jdbc.url.1.master=*******************************************************************************************
#sharding.jdbc.url.2.master=*******************************************************************************************
#sharding.jdbc.url.3.master=*******************************************************************************************
#
#sharding.jdbc.url.0.slave=*******************************************************************************************
#sharding.jdbc.url.1.slave=*******************************************************************************************
#sharding.jdbc.url.2.slave=*******************************************************************************************
#sharding.jdbc.url.3.slave=*******************************************************************************************

sharding.jdbc.username=db_hk
sharding.jdbc.password=DB_test
sharding.jdbc.url.0.master=*************************************************************************************************
sharding.jdbc.url.1.master=*************************************************************************************************
sharding.jdbc.url.2.master=*************************************************************************************************
sharding.jdbc.url.3.master=*************************************************************************************************

sharding.jdbc.url.0.slave=*************************************************************************************************
sharding.jdbc.url.1.slave=*************************************************************************************************
sharding.jdbc.url.2.slave=*************************************************************************************************
sharding.jdbc.url.3.slave=*************************************************************************************************

#sharding.jdbc.username=autorenew
#sharding.jdbc.password=Rdi!H)ui4*AZ
#sharding.jdbc.url.0.master=************************************************************************************************************
#sharding.jdbc.url.1.master=************************************************************************************************************
#sharding.jdbc.url.2.master=************************************************************************************************************
#sharding.jdbc.url.3.master=************************************************************************************************************
#
#sharding.jdbc.url.0.slave= ************************************************************************************************************
#sharding.jdbc.url.1.slave= ************************************************************************************************************
#sharding.jdbc.url.2.slave= ************************************************************************************************************
#sharding.jdbc.url.3.slave= ************************************************************************************************************