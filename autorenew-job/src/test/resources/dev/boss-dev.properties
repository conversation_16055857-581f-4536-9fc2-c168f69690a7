host=TEST-Mac OS X-autorenew-api-1.0.88-${boss.build.timestamp}

mail.user.name=vipmessage
mail.user.address=<EMAIL>
mail.user.token=8u36q9d63g96hqlr
mail.error.contact=liji<PERSON><PERSON>@qiyi.com,yang<PERSON><PERSON>@qiyi.com,l<PERSON><PERSON><PERSON><PERSON>@qiyi.com,<EMAIL>

internal.pay.key=123456

qiyi.pay.key=**********
qiyi.pay.key.new=${qiyi.pay.key.new}

boss.autorenew.key=**********
partner.autorenew.key=**********
partner.sign.page=https://vip.iqiyi.com/html5VIP/activity/partner_sign/index.html

#BOSS\u652F\u4ED8\u5BC6\u94A5
boss.pay.key=**********

#\u8D26\u6237\u4E2D\u5FC3\u8D60\u5145\u503C\u5947\u8C46\u7B7E\u540Dkey
account.pay.key=**********

account.dutquery.url=http://inter-test.account.qiyi.domain/pay/dut/query.action
account.dutsign.url=http://account.iqiyi.com/pay/alipaydeduct/sign.action
account.dutquery.key=123456

promotion.process.key=iua7#6UDH5%sa678z
promotion.process.url=http://*************:8080/promotion/internal/common/process.action

passport.query.isclosed.url=http://passport.qiyi.domain/apis/inner/user_info.action

autorenew.sms.remind.task.execute.time=10:00:00

shortUrl_wappay_prefix=http://**************/t/

vip.freeorder.url=http://i.vip.qiyi.domain/api/internal/free-pay/dopay.action
vip.freeorder.key=123456

#VIP\u4FE1\u606F\u540C\u6B65Passport URL
notify_url_passport=http://vip.passport.qiyi.domain/apis/vip/updateVipInfo.action

encoding=utf-8
#boss-api �������ģ������ļ����
boss.api.config=${boss.api.config}

subscribe_shard_strategy=shardColumn%3
subscribe_shard_tables=0-2
subscirbe_shard_num=3

dutuser_shard_strategy=shardColumn%3
dutuser_shard_tables=0-2
dutuser_shard_num=3

subscribe_log_shard_strategy=shardColumn%3
subscribe_log_shard_tables=0-2
subscribe_log_shard_num=3

dutlog_shard_strategy=shardColumn%3
dutlog_shard_tables=0-2
dutlog_shard_num=3

dutsetlog_shard_strategy=shardColumn%3
dutsetlog_shard_tables=0-2
dutsetlog_shard_num=3

account.unSign.url=http://account.iqiyi.com/pay/dut/unSignNoLogin.action

query.boss.vip.info=http://serv.vip.qiyi.domain/api/internal/auth/vip_info.action
query.vip.infos.url=http://vinfo.vip.qiyi.domain/internal/users/{uid}/vip_infos

isDecoupledWithProductTable=true

auto_api_biz_config_file=test_auto_api_biz_config_file

shortUrlCancelAutoRenew.key=**********

autorenew.dataservice.serverAddr=http://tradeapi.vip.qiyi.domain
autorenew.dataservice.connectTimeout=500
autorenew.dataservice.readTimeout=10000
autorenew.dataservice.invoke.channel=qiyue-web
autorenew.dataservice.invoke.signkey=f24d31f28ad963c6f48679c668cc092d

# iqiyi cloud config
cloud.config.app.name=vip-autorenew
cloud.config.app.env=dev

app.name=vip-autorenew
module.name=vip-autorenew

# passport 主站接口
passport.queryUserByUid.url=http://passport.qiyi.domain/apis/profile/byUid.action
passport.queryUserByUsername.url=http://passport.qiyi.domain/apis/profile/byUsername.action
passport.profileInfo.url=http://passport.qiyi.domain/apis/profile/info.action
passport.batchQueryUserByUids.url=http://passport.qiyi.domain/apis/profile/batch/byUids.action