package com.qiyi.boss.service;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.ExecutorService;

import com.qiyi.boss.component.SmartJedisClient;
import com.qiyi.boss.enums.AccountBindStatusEnum;
import com.qiyi.boss.enums.BindStatusEnum;
import com.qiyi.vip.trade.autorenew.domain.AccountDutUser;
import com.qiyi.vip.trade.autorenewtidb.mapper.AccountDutUserMapper;
import com.iqiyi.solar.config.client.CloudConfig;

/**
 * <AUTHOR>
 * @className AccountBindStatusUpdateExecutor
 * @description
 * @date 2023/4/10
 **/
@Component
@Slf4j
public class AccountBindStatusUpdateExecutor {

    @Resource
    private CloudConfig cloudConfig;


    @Resource
    private SmartJedisClient smartJedisClient;

    @Resource
    private UserAgreementService userAgreementService;

    @Resource
    private AccountDutUserMapper accountDutUserMapper;

    @Resource(name = "updateStatusThreadPool")
    private ExecutorService updateStatusThreadPool;

    public static final String MAXIMUM_ID_KEY = "MAXIMUM_ID";

    public void createDutTask(Integer shardItem) {
        if (shardItem != 0) {
            return;
        }

        int startTableIndex;
        Integer endTableIndex;
        boolean updateAccountStatus = cloudConfig.getBooleanProperty("update.account.status", false);
        while (updateAccountStatus) {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            Integer processNum = cloudConfig.getIntProperty("update.account.status.num", 5000);
            Integer maximumId = smartJedisClient.get(MAXIMUM_ID_KEY, new TypeReference<Integer>() {
            }, true);
            if (maximumId == null) {
                log.info("maximumId is null, will set maximumId to 0");
                smartJedisClient.setex(MAXIMUM_ID_KEY, "0", 259200);
                maximumId = 0;
                log.info("maximumId is set to 0");
            }

            startTableIndex = maximumId + 1;
            endTableIndex = maximumId + processNum;
            smartJedisClient.setex(MAXIMUM_ID_KEY, String.valueOf(endTableIndex), 259200);
            log.info("maximumId is set to {}", endTableIndex);
            List<AccountDutUser> accountDutUsers = accountDutUserMapper.getByIdRange(startTableIndex, endTableIndex);
            if (CollectionUtils.isEmpty(accountDutUsers)) {
                log.info("not find data between {} and {}", startTableIndex, endTableIndex);
                return;
            }

            for (AccountDutUser accountDutUser : accountDutUsers) {
                updateStatusThreadPool.execute(() -> processUser(accountDutUser));
            }
            updateAccountStatus = cloudConfig.getBooleanProperty("update.account.status", false);

            try {
                boolean needSpeedLimit = cloudConfig.getBooleanProperty("update.account.status.speed.limit", true);
                if (needSpeedLimit) {
                    Thread.sleep(cloudConfig.getLongProperty("update.account.status.sleep.seconds", 2000L));
                }
            } catch (Exception e) {
                log.error("speedLimit error:", e);
                Thread.currentThread().interrupt();
            }
            stopWatch.stop();
            log.info("processed at: {} num:{} costs{}s", maximumId, accountDutUsers.size(), stopWatch.getTotalTimeSeconds());
        }
        log.info("updateAccountStatus is false");
    }

    private void processUser(AccountDutUser accountDutUser) {
        BigDecimal accountUserId = accountDutUser.getUserId();
        Long type = accountDutUser.getType();
        BigDecimal accountStatus = accountDutUser.getStatus();
        if (accountUserId == null || type == null || accountStatus == null) {
            return;
        }
        int dutType = type.intValue();
        long userId = accountUserId.longValue();
        Integer status = transferStatus(accountStatus);
        if (dutType == 333) {
            doUpdateStatus(userId, dutType, status);
            doUpdateStatus(userId, 945, status);
            return;
        }
        doUpdateStatus(userId, dutType, status);
        log.info("process accountDutUser id: {}", accountDutUser.getId());
    }



    private Integer transferStatus(BigDecimal status) {
        int value = status.intValue();
        if (AccountBindStatusEnum.UNBIND.getValue() == value) {
            return BindStatusEnum.UNBIND.getValue();
        }
        return BindStatusEnum.BIND.getValue();
    }

    private void doUpdateStatus(Long uid, int dutType, int status) {
        userAgreementService.updateDutUserNewBindStatus(uid, dutType, status);
    }

}

