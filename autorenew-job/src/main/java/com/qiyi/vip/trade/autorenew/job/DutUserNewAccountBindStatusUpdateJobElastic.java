package com.qiyi.vip.trade.autorenew.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import com.qiyi.boss.service.AccountBindStatusUpdateExecutor;

/**
 * <AUTHOR>
 * @className DutUserNewAccountBindStatusUpdateJobElastic
 * @description
 * @date 2023/4/10
 **/
@Slf4j
@Component(value = "dutUserNewAccountBindStatusUpdateJobElastic")
public class DutUserNewAccountBindStatusUpdateJobElastic {

    @Resource
    private AccountBindStatusUpdateExecutor accountBindStatusUpdateExecutor;

    public void execute(ShardingContext shardingContext) {
        log.info("dutUserNewAccountBindStatusUpdateJobElastic start execute!");
        accountBindStatusUpdateExecutor.createDutTask(shardingContext.getShardingItem());
    }
}
