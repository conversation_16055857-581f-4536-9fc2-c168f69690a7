package com.qiyi.vip.trade.autorenew.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import com.qiyi.boss.service.CommonDutAutoRenewSmsRemindJobExecutor;
import com.iqiyi.solar.config.client.CloudConfig;

import static com.qiyi.vip.trade.autorenew.constants.ConfigConstants.SHOULD_EXECUTE_SMS_REMIND_JOB;

/**
 * <AUTHOR>
 */
@Slf4j
@Component(value = "commonDutAutoRenewSmsRemindJobElastic")
public class CommonDutAutoRenewSmsRemindJobElastic implements SimpleJob {

    @Resource
    private CloudConfig cloudConfig;

    @Resource
    private CommonDutAutoRenewSmsRemindJobExecutor commonDutAutoRenewSmsRemindJobExecutor;

    @Override
    public void execute(ShardingContext shardingContext) {
        if (cloudConfig.getBooleanProperty(SHOULD_EXECUTE_SMS_REMIND_JOB, true)) {
            log.info("commonDutAutoRenewSmsRemindJobElastic start execute!");
            long start = System.currentTimeMillis();
            try {
                commonDutAutoRenewSmsRemindJobExecutor.createDutTask(shardingContext.getShardingItem());
            } catch (Exception e) {
                log.error("CommonDutAutoRenewSmsRemindJobElastic exception", e);
                throw e;
            }
            long end = System.currentTimeMillis();
            log.info("commonDutAutoRenewSmsRemindJobElastic execute finished! costTime:{}ms.", end - start);
        } else {
            log.info("should not execute commonDutAutoRenewSmsRemindJobElastic!");
        }
    }
}
