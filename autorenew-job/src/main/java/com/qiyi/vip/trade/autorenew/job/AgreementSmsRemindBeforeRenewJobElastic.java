package com.qiyi.vip.trade.autorenew.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import com.qiyi.boss.service.AgreementSmsRemindBeforeDutRenewExecutor;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.constants.ConfigConstants;

/**
 * Created at: 2021-07-07
 * <p>
 * 协议扣费前消息提醒Job
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = "agreementSmsRemindBeforeRenewJobElastic")
public class AgreementSmsRemindBeforeRenewJobElastic implements SimpleJob {

    @Resource
    AgreementSmsRemindBeforeDutRenewExecutor agreementSmsRemindBeforeDutRenewExecutor;

    @Override
    public void execute(ShardingContext shardingContext) {
        boolean needExecute = CloudConfigUtil.getBooleanValue(ConfigConstants.SHOULD_EXECUTE_AGREEMENT_SMS_REMIND_JOB, true);
        if (!needExecute) {
            log.info("should not execute AgreementSmsRemindBeforeDutJobElastic!");
            return;
        }
        log.info("AgreementSmsRemindBeforeDutJobElastic start execute!");
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            agreementSmsRemindBeforeDutRenewExecutor.createDutTask(shardingContext.getShardingItem());
        } catch (Exception e) {
            log.error("AgreementSmsRemindBeforeRenewJobElastic exception", e);
            throw e;
        }
        log.info("AgreementSmsRemindBeforeDutJobElastic execute finished! costTime:{}ms.", stopWatch.getTime());
    }
}
