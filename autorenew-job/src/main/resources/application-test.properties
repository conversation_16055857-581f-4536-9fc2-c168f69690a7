spring.application.name=autorenew-job-test
server.port=8080
# eureka config
#eureka.instance.hostname=${spring.cloud.client.ip-address}
#eureka.instance.non-secure-port=8080
eureka.instance.hostname=${HOST}
eureka.instance.non-secure-port=${PORT_8080}
eureka.instance.instance-id=${eureka.instance.hostname}:${eureka.instance.non-secure-port}

#设置与Eureka Server交互的地址，查询服务和注册服务都需要依赖这个地址。
#默认是http://localhost:8761/eureka ；多个地址可使用 , 分隔
eureka.client.serviceUrl.defaultZone=http://*************:8080/eureka/
#开启健康检查（需要spring-boot-starter-actuator依赖）
eureka.client.register-with-eureka=false
spring.cloud.netflix.metrics.enabled=false
#租期更新时间间隔（默认30秒）
eureka.instance.lease-renewal-interval-in-seconds=5
#租期到期时间（默认90秒）不怕出错的话，时间可以更短
eureka.instance.lease-expiration-duration-in-seconds=15
# actuator config
management.endpoints.enabled-by-default=true
management.endpoints.web.exposure.include=health,prometheus
management.endpoints.web.exposure.exclude=
management.health.defaults.enabled=true
management.health.redis.enabled=false
management.health.db.enabled=false
management.health.mail.enabled=false
management.health.eureka.enabled=false
management.endpoint.health.show-details=always
management.info.git.mode=full
management.server.port=8099

# 通过ribbon进行负载均衡
spring.cloud.loadbalancer.ribbon.enabled=true
ribbon.eureka.enabled=true
# 开启Ribbon的饥饿加载模式，减少第一次调用时由于创建RibbonClient导致的超时问题
ribbon.eager-load.enabled=true
# 需要饥饿加载的服务提供者的 instace-name 列表
ribbon.eager-load.clients=viptrade-promotion-api-test,vip-autorenew-marketing-api-test,viptrade-dataservice-test,partner-renew-api-test,vip-content-query-server-test,viptrade-refundservice-api,viptrade-pay-info-test,vip-info-server-test,ORDER-SYSTEM-TEST
#使用okhttp的话需要手工引入okhttp的依赖（https://mvnrepository.com/artifact/com.squareup.okhttp3/okhttp）
#ribbon.okhttp.enabled=true

###### 如果需要重试功能，可以增加如下配置 #######
# 开启重试，需要添加Spring-Retry依赖（https://mvnrepository.com/artifact/org.springframework.retry/spring-retry）
spring.cloud.loadbalancer.retry.enabled=true
# Ribbon默认只对GET请求进行重试，如果想对所有操作请求都进行重试，可以配置该属性（如果POST接口不支持幂等的话慎用）为true
ribbon.OkToRetryOnAllOperations=false
# 对响应中返回某些状态码时重试请求
#ribbon.retryableStatusCodes=500,502
# 同一个Server重试次数
ribbon.MaxAutoRetries=0
# 最多重试几个Server
ribbon.MaxAutoRetriesNextServer=1
# 也可以按服务提供者分别配置
# serviceId1.ribbon.MaxAutoRetries=0
#
DBM_CONFIG_APPID=qpaas-db-autorenew-api-TEST
APOLLO_PAAS_TOKEN=3979529b-465e-1e0a-ede0-3c38c21f58c4

# qiyue mysql config
#spring.datasource.qiyue.url=6a6462633a6d7973716c3a2f2f626a2e626f7373746573742e772e716979692e64623a363138332f6875697975616e5f6f726465723f757365556e69636f64653d7472756526636861726163746572456e636f64696e673d5554462d3826757365723d626f7373746573742670617373776f72643d626f737374657374
#spring.datasource.qiyue.username=7669705f74657374
#spring.datasource.qiyue.password=72675f7a5f36554629773d59
#spring.datasource.qiyue.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.qiyue.minimumIdle=10
#spring.datasource.qiyue.maximumPoolSize=10
#spring.datasource.qiyue.maxLifetime=2400000
#spring.datasource.qiyue.idleTimeout=600000
#spring.datasource.qiyue.connectionTimeout=10000
#
## autorenew tidb config
#spring.datasource.autorenewtidb.url=6a6462633a6d7973716c3a2f2f76697074726164656f72646572757365722e746964622e716979692e64617461626173653a343030302f6175746f72656e65775f757365723f757365556e69636f64653d7472756526636861726163746572456e636f64696e673d5554462d3826636861726163746572536574526573756c74733d5554462d38266175746f5265636f6e6e6563743d74727565267573654166666563746564526f77733d74727565267573655365727665725072657053746d74733d66616c7365267573654c6f63616c53657373696f6e53746174653d74727565
#spring.datasource.autorenewtidb.username=76697074726164655f6f726465725f75736572
#spring.datasource.autorenewtidb.password=2b37442f384e736c7854544f
#spring.datasource.autorenewtidb.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.autorenewtidb.minimumIdle=10
#spring.datasource.autorenewtidb.maximumPoolSize=10
#spring.datasource.autorenewtidb.maxLifetime=2400000
#spring.datasource.autorenewtidb.idleTimeout=600000
#spring.datasource.autorenewtidb.connectionTimeout=10000
#
## autorenew mysql master config
#spring.datasource.autorenew.master.url=*****************************************************************************************************************************
#spring.datasource.autorenew.master.username=vip_test
#spring.datasource.autorenew.master.password=72675f7a5f36554629773d59
#spring.datasource.autorenew.master.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.autorenew.master.minimumIdle=10
#spring.datasource.autorenew.master.maximumPoolSize=10
#spring.datasource.autorenew.master.maxLifetime=2400000
#spring.datasource.autorenew.master.idleTimeout=600000
#spring.datasource.autorenew.master.connectionTimeout=10000
#
## autorenew mysql slave config
#spring.datasource.autorenew.slave.url=*****************************************************************************************************************************
#spring.datasource.autorenew.slave.username=vip_test
#spring.datasource.autorenew.slave.password=72675f7a5f36554629773d59
#spring.datasource.autorenew.slave.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.autorenew.slave.minimumIdle=10
#spring.datasource.autorenew.slave.maximumPoolSize=10
#spring.datasource.autorenew.slave.maxLifetime=2400000
#spring.datasource.autorenew.slave.idleTimeout=600000
#spring.datasource.autorenew.slave.connectionTimeout=10000
#
## �ֿ�����
#spring.datasource.autorenew.sharding.master0.url=*******************************************************************************************
#spring.datasource.autorenew.sharding.master0.username=vip_test
#spring.datasource.autorenew.sharding.master0.password=rg_z_6UF)w=Y
#spring.datasource.autorenew.sharding.master0.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.autorenew.sharding.master0.minimumIdle=10
#spring.datasource.autorenew.sharding.master0.maximumPoolSize=10
#spring.datasource.autorenew.sharding.master0.maxLifetime=2400000
#spring.datasource.autorenew.sharding.master0.idleTimeout=600000
#spring.datasource.autorenew.sharding.master0.connectionTimeout=10000
#
#spring.datasource.autorenew.sharding.master1.url=*******************************************************************************************
#spring.datasource.autorenew.sharding.master1.username=vip_test
#spring.datasource.autorenew.sharding.master1.password=rg_z_6UF)w=Y
#spring.datasource.autorenew.sharding.master1.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.autorenew.sharding.master1.minimumIdle=10
#spring.datasource.autorenew.sharding.master1.maximumPoolSize=10
#spring.datasource.autorenew.sharding.master1.maxLifetime=2400000
#spring.datasource.autorenew.sharding.master1.idleTimeout=600000
#spring.datasource.autorenew.sharding.master1.connectionTimeout=10000
#
#spring.datasource.autorenew.sharding.master2.url=*******************************************************************************************
#spring.datasource.autorenew.sharding.master2.username=vip_test
#spring.datasource.autorenew.sharding.master2.password=rg_z_6UF)w=Y
#spring.datasource.autorenew.sharding.master2.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.autorenew.sharding.master2.minimumIdle=10
#spring.datasource.autorenew.sharding.master2.maximumPoolSize=10
#spring.datasource.autorenew.sharding.master2.maxLifetime=2400000
#spring.datasource.autorenew.sharding.master2.idleTimeout=600000
#spring.datasource.autorenew.sharding.master2.connectionTimeout=10000
#
#spring.datasource.autorenew.sharding.master3.url=*******************************************************************************************
#spring.datasource.autorenew.sharding.master3.username=vip_test
#spring.datasource.autorenew.sharding.master3.password=rg_z_6UF)w=Y
#spring.datasource.autorenew.sharding.master3.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.autorenew.sharding.master3.minimumIdle=10
#spring.datasource.autorenew.sharding.master3.maximumPoolSize=10
#spring.datasource.autorenew.sharding.master3.maxLifetime=2400000
#spring.datasource.autorenew.sharding.master3.idleTimeout=600000
#spring.datasource.autorenew.sharding.master3.connectionTimeout=10000
#
#spring.datasource.autorenew.sharding.slave0.url=*******************************************************************************************
#spring.datasource.autorenew.sharding.slave0.username=vip_test
#spring.datasource.autorenew.sharding.slave0.password=rg_z_6UF)w=Y
#spring.datasource.autorenew.sharding.slave0.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.autorenew.sharding.slave0.minimumIdle=10
#spring.datasource.autorenew.sharding.slave0.maximumPoolSize=10
#spring.datasource.autorenew.sharding.slave0.maxLifetime=2400000
#spring.datasource.autorenew.sharding.slave0.idleTimeout=600000
#spring.datasource.autorenew.sharding.slave0.connectionTimeout=10000
#
#spring.datasource.autorenew.sharding.slave1.url=*******************************************************************************************
#spring.datasource.autorenew.sharding.slave1.username=vip_test
#spring.datasource.autorenew.sharding.slave1.password=rg_z_6UF)w=Y
#spring.datasource.autorenew.sharding.slave1.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.autorenew.sharding.slave1.minimumIdle=10
#spring.datasource.autorenew.sharding.slave1.maximumPoolSize=10
#spring.datasource.autorenew.sharding.slave1.maxLifetime=2400000
#spring.datasource.autorenew.sharding.slave1.idleTimeout=600000
#spring.datasource.autorenew.sharding.slave1.connectionTimeout=10000
#
#spring.datasource.autorenew.sharding.slave2.url=*******************************************************************************************
#spring.datasource.autorenew.sharding.slave2.username=vip_test
#spring.datasource.autorenew.sharding.slave2.password=rg_z_6UF)w=Y
#spring.datasource.autorenew.sharding.slave2.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.autorenew.sharding.slave2.minimumIdle=10
#spring.datasource.autorenew.sharding.slave2.maximumPoolSize=10
#spring.datasource.autorenew.sharding.slave2.maxLifetime=2400000
#spring.datasource.autorenew.sharding.slave2.idleTimeout=600000
#spring.datasource.autorenew.sharding.slave2.connectionTimeout=10000
#
#spring.datasource.autorenew.sharding.slave3.url=*******************************************************************************************
#spring.datasource.autorenew.sharding.slave3.username=vip_test
#spring.datasource.autorenew.sharding.slave3.password=rg_z_6UF)w=Y
#spring.datasource.autorenew.sharding.slave3.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.autorenew.sharding.slave3.minimumIdle=10
#spring.datasource.autorenew.sharding.slave3.maximumPoolSize=10
#spring.datasource.autorenew.sharding.slave3.maxLifetime=2400000
#spring.datasource.autorenew.sharding.slave3.idleTimeout=600000
#spring.datasource.autorenew.sharding.slave3.connectionTimeout=10000

###微信模板消息 配置项--start#####
#账户中心 模板消息接口地址
host=${host}
wechat.msg.account.url=http://account.iqiyi.com/wechat/msg/send.action
#自动续费到期提醒模板id
wechat.msg.autoRenew.expire.template.id=Rrj1k8Fu4n1DwZcApKkkZherf_Bt3Ns6n4VATomBO98
#非自动续费到期提醒模板id
wechat.msg.nonAutoRenew.expire.template.id=bSewIpGZBSTDIfFgYCtBciAmY5L_AM_ctp2Op4yz8X8
#自动续费完成提醒模板id
wechat.msg.autoRenew.finished.template.id=a4rUgGwZTUNg9G9ERA41-k1rmpYqxOhPpxCssoStwdw
#非自动续费完成提醒模板id
wechat.msg.nonAutoRenew.finished.template.id=phbSBQ5QpwOcuKzDVlo26OmWijIDXkyoeNav9CEAiJc
#微信自动续费到期提醒job
renewalExpireReminder.needdo=false
###微信模板消息 配置项--end#####

mail.user.name=vipmessage
mail.user.address=<EMAIL>
mail.user.token=8u36q9d63g96hqlr
mail.error.contact=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

mail.tw.autorenew.baddebt.contact=<EMAIL>,<EMAIL>

account.batch.unbind.url=http://inter-test.account.qiyi.domain/pay/dut/unbindByTypes
dut.renew.pay.url=http://i.vip.qiyi.domain/pay/dutPay.action
account.dutquery.url=http://inter-test.account.qiyi.domain/pay/dut/query.action
account.dutsign.url=http://account.iqiyi.com/pay/alipaydeduct/sign.action
account.dutquery.key=123456
account.unbind.key=f1a34dc464325d35f6dc90c1f3
account.center.server.readTimeout=6000

promotion.process.key=iua7#6UDH5%sa678z
promotion.process.url=http://viptrade-promotion-api-test/promotion/internal/common/process.action

passport.query.isclosed.url=http://passport.qiyi.domain/apis/inner/user_info.action

autorenew.sms.remind.task.execute.time=10:00:00

#TV
common_autorenew_sms_remind_task_execute_time=10:00:00

notify_url_passport=http://vip.passport.qiyi.domain/apis/vip/updateVipInfo.action

vip.freeorder.url=http://i.vip.qiyi.domain/api/internal/free-pay/dopay.action

vip.freeorder.key=123456


subscribe_shard_strategy=shardColumn%3
subscribe_shard_tables=0-2
subscirbe_shard_num=3


job.needdo=${job.needdo}



qiyi.pay.key=**********
qiyi.pay.key.new=d65f3fb538374715a557937cb3e8612f
pay.key.qibubble_audio=0c95208a4664432e887a6b5f7ecb2df0
qiyi.pay.service.provider.code=QIYUESP
qiyi.pay.service.provider.key=**********

pay.center.dut.mobile=http://inter.pay.qiyi.domain/dut/gateway.action
pay.centerpay.gateway.domain=http://inter.pay.qiyi.domain/pay/gateway.action
pay.centerpay.frontend.domain=http://inter.pay.qiyi.domain/pay-web-frontend/frontend/pay
pay.centerpay.close.order.url=http://pay-test.iqiyi.com/pay-web-frontend/frontend/closeOrder
pay.center.apple.dut.info.url=http://inter-test.pay.qiyi.domain/pay-product-iap/apple/dut-info

#BOSS支付密钥
boss.pay.key=123456


async.task.loadDB=${async.task.loadDB}



#账户中心赠充值奇豆签名key
account.pay.key=**********





account.unSign.url=http://inter.account.qiyi.domain/pay/dut/unSignNoLogin.action

shortUrl_wappay_prefix=http://**************/t/

#自动续费默认扣款方式
default.autorenew.type=1

mail.favor.contact=${mail.favor.contact}

qiyi.boss.config.file=BossSwitch_test

isDecoupledWithProductTable=true

auto_api_biz_config_file=test_auto_api_biz_config_file
mail.autorenew.error.contact=

autorenew.retry.task.exe.time=11:00:00

autorenew.renewday.retry.task.exec.time=20:30:00
autorenew.outdated.retry.task.exec.time=08:00:00

app.name=autorenew-job
module.name=autorenew-job


autorenew.dataservice.serverAddr=http://viptrade-dataservice-test
autorenew.dataservice.connectTimeout=500
autorenew.dataservice.readTimeout=10000
autorenew.dataservice.invoke.channel=qiyue-web
autorenew.dataservice.invoke.signkey=f24d31f28ad963c6f48679c668cc092d

# iqiyi cloud config
cloud.config.app.name=vip-autorenew
cloud.config.app.env=test
cloud.config.app.region=default

elastic.job.zk.host=cnhb4.public-test-baoding.dev002.zk.qiyi.middle:2181,cnhb4.public-test-baoding.dev003.zk.qiyi.middle:2181,cnhb4.public-test-baoding.dev005.zk.qiyi.middle:2181,cnhb4.public-test-baoding.dev001.zk.qiyi.middle:2181,cnhb4.public-test-baoding.dev004.zk.qiyi.middle:2181
elastic.job.digest=autorenew-job:123456
elastic.job.session.timeout=60000
elastic.job.connection.timeout=15000

query.boss.vip.info=http://serv.vip.qiyi.domain/api/internal/auth/vip_info.action
query.vip.infos.url=http://vinfo.vip.qiyi.domain/internal/users/{uid}/vip_infos

findRecentUpdatedValidUsers.url=http://serv.vip.qiyi.domain/vipUserCheck/findRecentUpdatedValidUsers.action

# vip-commons-component 配置
boss.query.api.readTimeout=5000
userInfoApi.fallbackTimeOut=500
vinfo.queryVipInfo.url=http://vinfo.vip.qiyi.domain/internal/vip_users
serv.orderVerify.url=http://serv.vip.qiyi.domain/order/verify.action
serv.autoRenewStatus.url=http://serv.vip.qiyi.domain/services/autoRenewStatus.action
serv.notityAutoRenewDut.url=http://serv.vip.qiyi.domain/pay/notify/dut-autorenew.action
vinfo.queryVipsInfos.url=http://vinfo.vip.qiyi.domain/internal/users/{uid}/vip_infos
vinfo.batchQueryVipUsers.url=http://vinfo.vip.qiyi.domain/internal/batch/vip_users
vip.infos.domain=http://vip-info-server-test
batch.query.vip.user.url=http://vip-info-server-test/internal/batch/vip_users

#自动续费变更消息RocketMQ配置
autorenew.msg.rmq.namesrvaddr=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
autorenew.remind.msg.producer.token=PT-bf91335d-3b89-43c3-9190-462466f45fa7

#用户历史绑定关系解约消息配置
order.msg.rmq.nameServer=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
user.history.bind.cancelled.producer.token=PT-05f1093b-dd5b-4d52-827d-a2bb13d1058d

# Order Sharding Configuration
order.sharding.database.urls=******************************************************,******************************************************
order.sharding.database.username=vip_order_test
order.sharding.database.password=agh3!schingood5TR$
order.sharding.tableSize=2
# passport 主站接口
passport.queryUserByUid.url=http://passport.qiyi.domain/apis/profile/byUid.action
passport.queryUserByUsername.url=http://passport.qiyi.domain/apis/profile/byUsername.action
passport.profileInfo.url=http://passport.qiyi.domain/apis/profile/info.action
passport.batchQueryUserByUids.url=http://passport.qiyi.domain/apis/profile/batch/byUids.action
passport.inner.user.uid.info.url=http://am.passport.qiyi.domain/apis/user/inner/uid.action
passport.closeaccout.status.verification.url=http://passport.qiyi.domain/closeaccount/inner/biz_status_callback.action

#Smart Jedis配置, hostName=vip-test.bjdx.qiyi.redis
#smart.jedis.appId=boss-cloud-config-redis
#smart.jedis.cluster=vip-test
#smart.jedis.namespace=application
#smart.jedis.env=PRO
#smart.jedis.password=1b86da5e24413b2ec3961ceccbe0a3a4
#smart.jedis.pool.minIdle=10
#smart.jedis.pool.maxIdle=30
#smart.jedis.pool.maxActive=30
#smart.jedis.pool.maxWaitMillis=1000
#smart.jedis.read.timeout=2000
#smart.jedis.connection.timeout=1000
#
##redisson lock
#redisson.address=redis://vip-test.bjdx.qiyi.redis:18524
#redisson.password=ZSKnkI71BWSTUCoNoB4
#redisson.connectTimeout=2000
#redisson.timeout=1000
#redisson.retryAttempts=2
#redisson.retryInterval=300
#redisson.connectionPoolSize=30
#redisson.connectionMinimumIdleSize=10

#业务加锁设置（通用）
business.redisson.lock.leaseTime=3000
##退款接口
refund.url=http://viptrade-refundservice-api/refundService/order/common/refund
refund.sign.key=1c8c1d2cbfe5e5695476aacf3ad52f96

#自动续费营销服务域名
autorenew.marketing.domain=http://vip-autorenew-marketing-api-test
autorenew.marketing.invoke.channel=autorenew
autorenew.marketing.invoke.sign.key=123456

#对外合作签约关系服务域名
partner.renew.server.domain=http://partner-renew-api-test
partner.renew.server.sign.key=123456
partner.renew.server.readTimeout=6000
partner.renew.server.cancel.readTimeout=6000

content.query.server.domain=http://vip-content-query-server-test

#国际站查询代金券域名
global.coupon.search.url=http://intl-i.vip.qiyi.domain/vip-global-coupon/user/optimal
global.coupon.consume.url=http://intl-i.vip.qiyi.domain/internal/api/renewal/consumeCoupon

# 支付渠道配置
pay.info.channel=autorenew
pay.info.sign.key=123456
pay.info.url=http://viptrade-pay-info-test/api/payChannel/

order.system.sign.key=test

#商品中心
#commodity.config.app.profile=dev
commodity.config.app.profile=prod
#commodity.config.app.url=http://vcc-test.vip.qiyi.domain/vip-commodity
commodity.config.app.url=http://vcc.vip.qiyi.domain/vip-commodity
commodity.config.app.caller=vip-autorenew
#commodity.config.app.signKey=123456
commodity.config.app.signKey=766c8baf38d547e7a7e318617532c5e1
commodity.config.http.connectTimeout=6000
commodity.config.http.readTimeout=6000

order.core.domain=http://ORDER-SYSTEM-TEST
order.core.signKey=123456

coupon.proxy.domain=http://vip-coupon-proxy-test

logging.level.com.qiyi.vip.trade=debug

# vipCharge
vip.charge.domain=http://vip-charge-agent-api-test
vip.charge.signKey=123456