package com.qiyi.boss.utils;

import org.junit.Assert;
import org.junit.Test;

import java.sql.Timestamp;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;

import static org.junit.Assert.assertEquals;

/**
 * Created at: 2020-10-26
 *
 * <AUTHOR>
 */
public class DateHelperTest {

    @Test
    public void name() {
        String startDateStr = "2020-10-26";
        String endDateStr = "2020-10-28";
        int dateInterval = DateHelper.getDateInterval(startDateStr, endDateStr);
        assertEquals(2, dateInterval);

        String startDateStr2 = "2020-10-26";
        String endDateStr2 = "2020-12-01";
        int dateInterval2 = DateHelper.getDateInterval(startDateStr2, endDateStr2);
        assertEquals(36, dateInterval2);

        Timestamp timestamp1 = Timestamp.valueOf("2025-03-17 12:51:11");
        Timestamp timestamp2 = Timestamp.valueOf("2025-03-19 20:43:37");
        long localDateInterval1 = DateHelper.getLocalDateInterval(timestamp1, timestamp2);
        System.out.println(localDateInterval1);
        long localDateInterval2 = DateHelper.getLocalDateInterval(timestamp2, timestamp1);
        System.out.println(localDateInterval2);
        System.out.println(Math.abs(localDateInterval2));
    }


    @Test
    public void getZeroTime() {
        Timestamp dateTime = DateHelper.getDateTime();
        Timestamp zeroTime = DateHelper.getZeroTime(dateTime);
        Assert.assertEquals(DateHelper.getFormatDate(dateTime), DateHelper.getFormatDate(zeroTime));
        System.out.println(zeroTime);

        Timestamp t1 = Timestamp.valueOf("2024-07-04 23:59:59");
        Timestamp t2 = Timestamp.valueOf("2024-01-15 23:59:59");
        Timestamp t3 = Timestamp.valueOf("2024-02-26 23:59:59");
        Timestamp t4 = Timestamp.valueOf("2024-03-26 23:59:59");
        System.out.println(ChronoUnit.DAYS.between(t1.toLocalDateTime(), t2.toLocalDateTime()));
        System.out.println(DateHelper.plusMonths(t1, 1));
        System.out.println(DateHelper.plusMonths(t2, 1));
        System.out.println(DateHelper.plusMonths(t3, 1));
        System.out.println(DateHelper.plusMonths(t4, 1));
        System.out.println(DateHelper.getDayInterval(DateHelper.plusMonths(t1, 1), t1));
        System.out.println(DateHelper.getDayInterval(DateHelper.plusMonths(t2, 1), t2));
        System.out.println(DateHelper.getDayInterval(DateHelper.plusMonths(t3, 1), t3));
        System.out.println(DateHelper.getDayInterval(DateHelper.plusMonths(t4, 1), t4));
//        System.out.println(DateHelper.plusDays(t1, 1));
//        System.out.println(DateHelper.plusMinutes(t1, 1));

        YearMonth yearMonth1 = YearMonth.now();
        YearMonth yearMonth2 = YearMonth.of(2024, 1);
        YearMonth yearMonth3 = YearMonth.of(2024, 2);
        System.out.println(yearMonth1.lengthOfMonth());
        System.out.println(yearMonth2.lengthOfMonth());
        System.out.println(yearMonth3.lengthOfMonth());
    }


}