package com.qiyi.boss.async.task;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import com.qiyi.boss.component.AgreementHandler;
import com.qiyi.boss.component.AgreementHandlerFactory;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.PricingStrategyEnum;
import com.qiyi.boss.model.RenewPriceDto;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.FacadeManager;
import com.qiyi.vip.trade.autorenew.AutoRenewTaskApplication;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * Created at: 2021-10-09
 *
 * <AUTHOR>
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AutoRenewTaskApplication.class)
public class AgreementRenewTaskTest {

    static {
        System.setProperty("spring.profiles.active", "test");
        System.setProperty("eureka.instance.hostname", "${spring.cloud.client.ip-address}");
        System.setProperty("eureka.instance.non-secure-port", "8080");
        System.setProperty("app.id", "vip-autorenew");
        System.setProperty("env", "test");
        System.setProperty("management.metrics.export.prometheus.enabled", "true");
    }

    @Resource
    FacadeManager facadeManager;
    @Resource
    AgreementHandlerFactory agreementHandlerFactory;
    @Resource
    AgreementNoInfoManager agreementNoInfoManager;

    @Test
    public void exec() {
        String data = "productCode=8e88e624727a03e6&userId=4133117363224576&dutType=940&signKey=202312071810585017602000&agreementCode=9107fd86a97c54fb&agreementNo=20002&agreementType=2&vipType=1&taskType=normal&notifyCount=1";
        AgreementRenewTask renewTask = new AgreementRenewTask();
        renewTask.setFacadeManager(facadeManager);
        renewTask.deserialize(data);
        renewTask.execute();
    }

    @Test
    public void getPrice() {
        AgreementHandler agreementHandler = agreementHandlerFactory.getHandler(AgreementTypeEnum.ZHIMA_GO);
        AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(20007);
        DutUserNew dutUserNew = DutUserNew.builder().agreementNo(agreementNoInfo.getId()).serialRenewCount(0).build();
        RenewPriceDto renewPriceDto = agreementHandler.getRenewPrice(dutUserNew, agreementNoInfo, PricingStrategyEnum.PERIOD);
        System.out.println(renewPriceDto);
    }
}
