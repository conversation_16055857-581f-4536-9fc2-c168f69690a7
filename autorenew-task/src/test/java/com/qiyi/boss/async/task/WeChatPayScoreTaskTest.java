package com.qiyi.boss.async.task;

import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.service.impl.UserAgreementLogManager;
import com.qiyi.vip.trade.autorenew.AutoRenewTaskApplication;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import com.qiyi.boss.service.impl.FacadeManager;
import com.qiyi.boss.service.impl.RenewTaskComponent;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2021/8/2 17:10
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AutoRenewTaskApplication.class)
public class WeChatPayScoreTaskTest {

    static {
        System.setProperty("spring.profiles.active", "test");
    }

    @Resource
    FacadeManager facadeManager;
    @Resource
    RenewTaskComponent renewTaskComponent;
    @Resource
    UserAgreementLogManager userAgreementLogManager;

    @Test
    public void executeCreateTask() {
        long userId = 1480388721L;
        String signKey = "2021120415383907721000";
        String data = "productCode=a09c674b4c141dc3&userId=1480388721&dutType=942&signKey=2021120415383907721000&agreementCode=90caa5ed19363254&agreementType=3&vipType=1&taskType=normal&notifyCount=0&aid=5632507771010200";
        WeChatPayScoreCreateTask task = new WeChatPayScoreCreateTask();
        task.setFacadeManager(facadeManager);
        task.deserialize(data);
        task.execute();
        System.out.println("=====");
        List<DutRenewLog> dutRenewLogs = userAgreementLogManager.listRenewLogBySignKey(userId, signKey);
        Assert.assertTrue(CollectionUtils.isNotEmpty(dutRenewLogs));
    }

    @Test
    public void executeCompleteTask() {
        String data = "productCode=a09c674b4c141dc3&userId=1480388721&dutType=942&signKey=2021120415383907721000&agreementCode=90caa5ed19363254&agreementType=3&vipType=1&taskType=normal&notifyCount=0";
        WeChatPayScoreCompleteTask task = new WeChatPayScoreCompleteTask();
        task.setFacadeManager(facadeManager);
        task.deserialize(data);
        task.execute();
        System.out.println("=====");
    }

    @Test
    public void saveRetryTask(){
        String data = "productCode=a47bac390c51df6a&userId=1480409160&dutType=939&signKey=2021073011210259560000&agreementCode=abb67f5c267f33b3&agreementType=3&vipType=1&taskType=normal&notifyCount=0";
        WeChatPayScoreCreateTask createTask = new WeChatPayScoreCreateTask();
        createTask.setFacadeManager(facadeManager);
        createTask.deserialize(data);
        renewTaskComponent.saveAgreementRetryTask(createTask, null, new DutRenewLog());
    }

}