package com.qiyi.boss.async.task;

import com.qiyi.boss.service.impl.FacadeManager;
import com.qiyi.vip.trade.autorenew.AutoRenewTaskApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Created at: 2022-01-13
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AutoRenewTaskApplication.class)
public class MobileDutAutoRenewTaskTest {

    static {
        System.setProperty("spring.profiles.active", "test");
        System.setProperty("eureka.instance.hostname", "${spring.cloud.client.ip-address}");
        System.setProperty("eureka.instance.non-secure-port", "8080");
        System.setProperty("app.id", "vip-autorenew");
        System.setProperty("env", "test");
        System.setProperty("management.metrics.export.prometheus.enabled", "true");
    }

    @Resource
    FacadeManager facadeManager;

    @Test
    public void execute() {
        MobileDutAutoRenewTask mobileDutAutoRenewTask = new MobileDutAutoRenewTask();
        mobileDutAutoRenewTask.setFacadeManager(facadeManager);
        mobileDutAutoRenewTask.deserialize("product=4&user=1480401558&dutBindType=993&executedTypes=993&taskType=normal&notifyCount=0&fc=&autoRenewLinkedDutConfig=533&enableMobileDutRetry=true&productCode=a0226bd958843452&skuId=sku_177079580672367690");
        mobileDutAutoRenewTask.execute();
    }

}
