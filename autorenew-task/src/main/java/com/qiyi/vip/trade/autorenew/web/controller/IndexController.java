package com.qiyi.vip.trade.autorenew.web.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

import com.qiyi.boss.component.SmartJedisClient;

/**
 * <AUTHOR>
 * created 2019/3/1 - 16:28
 */
@Controller
public class IndexController {

    @Resource
    private SmartJedisClient smartJedisClient;

    @RequestMapping("/")
    @ResponseBody
    public String index() {
        return "success";
    }

    @RequestMapping("/status")
    @ResponseBody
    public String status() {
        return "ok";
    }

    @RequestMapping("/queryRedis")
    @ResponseBody
    public String queryRedis(String token, String key) {
        if (!"6784f2b557c1fca2d9ddb178df2d3b11".equals(token) || StringUtils.isBlank(key)) {
            return "fail";
        }
        String result1 = smartJedisClient.getString(key);
        String result2 = smartJedisClient.get(key, new TypeReference<String>() {}, false);
        return result1 + "_" + result2;
    }

    @RequestMapping("/setRedis")
    @ResponseBody
    public String setRedis(String token, String key, Integer timeOut) {
        if (!"6784f2b557c1fca2d9ddb178df2d3b11".equals(token) || StringUtils.isBlank(key) || timeOut == null) {
            return "fail";
        }
        smartJedisClient.setex(key, "value", timeOut);
        return key;
    }
}
