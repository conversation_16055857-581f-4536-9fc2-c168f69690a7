package com.qiyi.boss.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import com.qiyi.AutoRenewApiApplication;
import com.qiyi.boss.service.impl.AgreementTemplateManager;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2021/8/30 16:09
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AutoRenewApiApplication.class)
public class AgreementTemplateManagerTest {

    static {
        System.setProperty("spring.profiles.active", "test");
    }

    @Resource
    AgreementTemplateManager agreementTemplateManager;

    @Test
    public void getByCode() {
        AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode("8b8836946507ebb8");
        assertNotNull(agreementTemplate);
    }
}
