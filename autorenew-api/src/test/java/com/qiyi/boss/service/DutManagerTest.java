package com.qiyi.boss.service;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

import com.qiyi.AutoRenewApiApplication;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.service.impl.DutManager;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * @auther: guojing
 * @date: 2023/5/17 10:36 AM
 * @description:
 */
@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AutoRenewApiApplication.class)
public class DutManagerTest {

    @Resource
    DutManager dutManager;
    @Resource
    AutoRenewDutTypeManager autoRenewDutTypeManager;

    @Test
    public void test() {
        String shardTableTemplate = "boss_dut_user_new_%03d";
        Timestamp startTime = DateHelper.getTimestamp("2023-05-17 00:00:00");
        Timestamp deadline = DateHelper.getTimestamp("2023-05-18 00:00:00");
        List<Integer> excludeDutTypes = autoRenewDutTypeManager.getExcludePassiveDutTypeByVipType(1L, AgreementTypeEnum.AUTO_RENEW.getValue());

        List<DutUserNew> dutUserList = dutManager
            .getSyncAutoRenewListByTblIndex(1, 1L, startTime, deadline, excludeDutTypes, String.format(shardTableTemplate, 22));
        Assert.assertNotNull(dutUserList);
    }
}
