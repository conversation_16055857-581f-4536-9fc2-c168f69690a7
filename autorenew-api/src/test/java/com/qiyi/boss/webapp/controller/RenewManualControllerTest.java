package com.qiyi.boss.webapp.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.qiyi.AutoRenewApiApplication;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.utils.JacksonUtils;
import com.qiyi.boss.webapp.interceptor.RequestContextInterceptor;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import javax.annotation.Resource;

import static org.junit.Assert.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Created at: 2021-11-12
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AutoRenewApiApplication.class)
public class RenewManualControllerTest {

    static {
        System.setProperty("spring.profiles.active", "test");
    }

    @Resource
    RenewManualController renewManualController;

    private MockMvc mockMvc;

    @Before
    public void setUp() throws Exception {
        mockMvc = MockMvcBuilders.standaloneSetup(renewManualController)
                .addInterceptors(new RequestContextInterceptor())
                .build();
    }

    @Test
    public void userManual() throws Exception {
        String userManualRenewUrl = "/services/autorenew/renew/manual/user";
        String uid = "1480388721";
        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.get(userManualRenewUrl)
                .param("userId", uid)
                .param("agreementType", AgreementTypeEnum.WECHAT_PAY_SCORE.getValue() + "")
                .param("vipType", "1")
                .param("aid", "3517998595930601"));
        resultActions.andExpect(status().isOk());
        String contentAsString = resultActions.andReturn().getResponse().getContentAsString();
        BaseResponse<Boolean> response = JacksonUtils.parseObject(contentAsString, new TypeReference<BaseResponse<Boolean>>() {});
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertTrue(response.getData());
    }

    @Test
    public void kefuManual() throws Exception {
        String userManualRenewUrl = "/services/autorenew/renew/manual/kefu";
        String uid = "1480388721";
        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.get(userManualRenewUrl)
                .param("uidList", uid)
                .param("agreementCode", "90caa5ed19363254"));
        resultActions.andExpect(status().isOk());
        String contentAsString = resultActions.andReturn().getResponse().getContentAsString();
        BaseResponse<Boolean> response = JacksonUtils.parseObject(contentAsString, new TypeReference<BaseResponse<Boolean>>() {});
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertTrue(response.getData());
    }
}