<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/common/taglibs.jsp" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <%@ include file="/common/meta.jsp" %>
    <link rel="shortcut icon" href="http://www.iqiyipic.com/common/images/logo.ico" type="image/x-icon" />
    <link rel="stylesheet" type="text/css" href="http://static.qiyi.com/css/common/qiyue_css/style.css" />
    <link rel="stylesheet" type="text/css" href="http://static.qiyi.com/css/common/common_css/global_play.css"/>

    <link rel="stylesheet" type="text/css" href="http://static.qiyi.com/css/common/v3-basic/v3-basic_other.css" />
    <link rel="stylesheet" type="text/css" href="http://static.qiyi.com/css/common/hy_css/hy_basic.css">
    <link rel="stylesheet" type="text/css" href="http://static.qiyi.com/css/common/play_new_css/play_new.css">
    <script type="text/javascript" src="http://static.iqiyi.com/js/huiyuan/jquery.js"></script>
    <script type="text/javascript" src="http://static.iqiyi.com/js/lib/sea1.2.js"></script>

    <title>
        <c:choose>
              <c:when test="${code == 'A00000'}">
                <c:out value="开通连续包月服务返回-成功"/>
              </c:when>
              <c:otherwise>
                <c:out value="开通连续包月服务返回-失败"/>
              </c:otherwise>
        </c:choose>
    </title>
    <script type="text/javascript">
        window.info = window.info || {};
        window.info.qy_pt = 'pay';
    </script>

</head>
<body class="style1">
<%@ include file="/common/header.jsp" %>
<%@ include file="/common/header_qiyue.jsp" %>
<div class="container">
        <div class="c-box1 clearfix">
            <div class="bt_border-green">
                <div class="hy-hd0815 vip20130401 c30 clearfix">
                    <div class="pay_box">
                        <c:choose>
                            <c:when test="${code == 'A00000'}">
                                <h3><i class="pay_icons icons-succeed"></i>开通连续包月服务成功!</h3>
                                <ul>
                                    <li>
                                        <c:choose>
                                            <c:when test="${dutUser.returnUrl==null}">
                                                <a href='http://vip.iqiyi.com' class="c00">立即返回</a>
                                            </c:when>
                                            <c:otherwise>
                                                <a href='${dutUser.returnUrl}' class="c00">立即返回</a>
                                            </c:otherwise>
                                        </c:choose>
                                        <a href="http://vip.iqiyi.com" title="" class="c00">前往VIP首页</a>
                                        <a href="http://vip.iqiyi.com/tcxqy.html?pid=a0226bd958843452" title="" class="c00">查看VIP特权</a>
                                    </li>
                                </ul>
                            </c:when>
                            <c:otherwise>
                                <h3><i class="pay_icons icons-fail"></i>开通连续包月服务失败!</h3>
                                <ul class="disc">
                                    <li>
                                        支付宝账号绑定失败
                                        <p class="c999">您的支付宝帐号已经与其他爱奇艺&PPS帐号绑定，请更换支付宝账号。</p>
                                    </li>
                                </ul>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
            </div>
        </div>

        <!--广告位-->
         <c:choose>
              <c:when test="${user.phone == null || user.phone.isEmpty()}">
                <fragment:static fragmentId="154"/>
              </c:when>
              <c:otherwise>
                <fragment:static fragmentId="155"/>
              </c:otherwise>
        </c:choose>
        <!--推荐列表-->
    <div class="c-box1 clearfix">
        <div class="hy-bd0815" id="widget-payEndRecomd"
             data-payEndRecomd-sortKey="2"
             data-payEndRecomd-category="电影"
             data-payEndRecomd-cur="1"
             data-payEndRecomd-limit="20"
             data-payEndRecomd-isPurchase="2"
             data-payEndRecomd-purchaseType="1"
             data-payEndRecomd-from ="PCClient">
            <div class="hy-tab">
                <ul class="c-tab">
                    <li data-elem="tabtitle" data-seq="1" data-payEndRecomd-sortKey="2" class="selected"> 最近更新 </li>
                    <li data-elem="tabtitle" data-seq="2" data-payEndRecomd-sortKey="3"> 最近热播 </li>
                    <li data-elem="tabtitle" data-seq="3" data-payEndRecomd-sortKey="6"> 最近上映 </li>
                </ul>
            </div>
            <div class="vip0815 p10">
                <div class="tabJuji clearfix sl_videos tabJuji_title">
                    <div data-elem="tabbody" data-seq="1">
                        <div data-widget-videoslide="payend_update" data-videoslide-disp="5" data-videoslide-defaultoffset="1" data-videoslide-finger="0">
                            <div class="tabContent pdscrollBox">
                                <div class="loading" data-payend-elem="loading">
                                    <img src="http://www.iqiyipic.com/common/images/load.gif" alt="正在加载...">&nbsp;正在加载...
                                </div>
                                <div class="wrapScrollBox" data-payend-elem="content" data-videoslide-elem="content" style="display:none">
                                    <div class="fl pageControl">
                                        <a href="javascript:void(0);" class="sarrow_page arrayL page_pre_no" data-videoslide-elem="pbtn"></a>
                                        <a href="javascript:void(0);" class="sarrow_page arrayR" data-videoslide-elem="nbtn"></a>
                                    </div>
                                    <div class="scrollBox">
                                        <textarea data-payend-tpl="list" style="display:none">
                                            <li data-videoslide-elem="item">
                                                <a href="{{TvApplication_purl}}" class="piclist_img">
                                                    <img src="{{vrsVideoTv_TvBigPic_145_90}}" width="145" height="90" title="{{VrsVideoTv_tvName}}" alt="{{VrsVideoTv_tvName}}">
                                                </a>
                                                <div class="piclist_title">
                                                    <p class="fs14"><a href="{{TvApplication_purl}}" title="午夜微博"> {{VrsVideoTv_tvName}} </a></p>
                                                    <p><span class="c999">主演：</span><span data-payend-list="creditsname"></span></p>
                                                    <p class="c999">{{tvFocus}}</p>
                                                    <p><span class="orange fs18">{{VrsVideoScore_score}}</span>分</p>
                                                </div>
                                            </li>
                                        </textarea>
                                        <div class="scrollList" data-videoslide-elem="list">
                                            <ul class="piclist11070 clearfix" data-payend-elem="list"   data-videoscroll-elem="list"
                                                data-payend-picsize="_145_90"
                                                data-payend-creditsnamesize="20">
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div data-elem="tabbody" data-seq="2" style="display: none;">
                        <div data-widget-videoslide="payend_hot" data-videoslide-disp="5" data-videoslide-defaultoffset="1" data-videoslide-finger="0">
                            <div class="tabContent pdscrollBox">
                                <div class="loading" data-payend-elem="loading">
                                    <img src="http://www.iqiyipic.com/common/images/load.gif" alt="正在加载...">&nbsp;正在加载...
                                </div>
                                <div class="wrapScrollBox" data-payend-elem="content" data-videoslide-elem="content" style="display:none">
                                    <div class="fl pageControl">
                                        <a href="javascript:void(0);" class="sarrow_page arrayL page_pre_no" data-videoslide-elem="pbtn"></a>
                                        <a href="javascript:void(0);" class="sarrow_page arrayR" data-videoslide-elem="nbtn"></a>
                                    </div>
                                    <div class="scrollBox">
                                        <textarea data-payend-tpl="list" style="display:none">
                                            <li data-videoslide-elem="item">
                                                <a href="{{TvApplication_purl}}" class="piclist_img">
                                                    <img src="{{vrsVideoTv_TvBigPic_145_90}}" width="145" height="90" title="{{VrsVideoTv_tvName}}" alt="{{VrsVideoTv_tvName}}">
                                                </a>
                                                <div class="piclist_title">
                                                    <p class="fs14"><a href="{{TvApplication_purl}}" title="午夜微博"> {{VrsVideoTv_tvName}} </a></p>
                                                    <p><span class="c999">主演：</span><span data-payend-list="creditsname"></span></p>
                                                    <p class="c999">{{tvFocus}}</p>
                                                    <p><span class="orange fs18">{{VrsVideoScore_score}}</span>分</p>
                                                </div>
                                            </li>
                                        </textarea>
                                        <div class="scrollList" data-videoslide-elem="list">
                                            <ul class="piclist11070 clearfix" data-payend-elem="list"   data-videoscroll-elem="list"
                                                data-payend-picsize="_145_90"
                                                data-payend-creditsnamesize="20">
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div data-elem="tabbody" data-seq="3" style="display: none;">
                        <div data-widget-videoslide="payend_release" data-videoslide-disp="5" data-videoslide-defaultoffset="1" data-videoslide-finger="0">
                            <div class="tabContent pdscrollBox">
                                <div class="loading" data-payend-elem="loading">
                                    <img src="http://www.iqiyipic.com/common/images/load.gif" alt="正在加载...">&nbsp;正在加载...
                                </div>
                                <div class="wrapScrollBox" data-payend-elem="content" data-videoslide-elem="content" style="display:none">
                                    <div class="fl pageControl">
                                        <a href="javascript:void(0);" class="sarrow_page arrayL page_pre_no" data-videoslide-elem="pbtn"></a>
                                        <a href="javascript:void(0);" class="sarrow_page arrayR" data-videoslide-elem="nbtn"></a>
                                    </div>
                                    <div class="scrollBox">
                                        <textarea data-payend-tpl="list" style="display:none">
                                            <li data-videoslide-elem="item">
                                                <a href="{{TvApplication_purl}}" class="piclist_img">
                                                    <img src="{{vrsVideoTv_TvBigPic_145_90}}" width="145" height="90" title="{{VrsVideoTv_tvName}}" alt="{{VrsVideoTv_tvName}}">
                                                </a>
                                                <div class="piclist_title">
                                                    <p class="fs14"><a href="{{TvApplication_purl}}" title="午夜微博"> {{VrsVideoTv_tvName}} </a></p>
                                                    <p><span class="c999">主演：</span><span data-payend-list="creditsname"></span></p>
                                                    <p class="c999">{{tvFocus}}</p>
                                                    <p><span class="orange fs18">{{VrsVideoScore_score}}</span>分</p>
                                                </div>
                                            </li>
                                        </textarea>
                                        <div class="scrollList" data-videoslide-elem="list">
                                            <ul class="piclist11070 clearfix" data-payend-elem="list"     data-videoscroll-elem="list"
                                                data-payend-picsize="_145_90"
                                                data-payend-creditsnamesize="20">
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <!--推荐列表-->
</div>
<%@ include file="/common/footer.jsp" %>

<!--footer_end qi_hong_cai-->
<script type="text/javascript">
    Q.projectName = 'qiyiV2';
    Q.load("vipPayEnd");
</script>

<script type="text/javascript">
    (function(){
        var s = document.createElement("script"), el = document.getElementsByTagName("script")[0];
        s.async = true;
        s.src = "http://static.qiyi.com/js/pingback/vipqa.js?r=" + Math.random();
        el.parentNode.insertBefore(s, el);
    })();
</script>
</body>
<!-- Begin comScore Tag -->
<script>
    var _comscore = _comscore || [];
    _comscore.push({ c1: "2", c2: "7290408" });
    (function() {
        var s = document.createElement("script"), el = document.getElementsByTagName("script")[0];
        s.async = true;
        s.src = (document.location.protocol == "https:" ? "https://sb" : "http://b") + ".scorecardresearch.com/beacon.js";
        el.parentNode.insertBefore(s, el);
    })();
</script>
<noscript>
    <img src="http://b.scorecardresearch.com/p?c1=2&c2=7290408&cv=2.0&cj=1"/>
</noscript>
<!-- End comScore Tag -->

<!-- Begin 艾瑞 -->
<script>
    var _iwt_UA="UA-iqiyi-000001",_iwt_no_flash=1; //客户项目编号,根据实际生成
    (function (D) {
        var s=D.createElement("script"),h=D.getElementsByTagName("head")[0];s.src="http://static.qiyi.com/js/pingback/iwt.js";s.type="text/javascript";s.charset="utf-8";h.appendChild(s);
    })(document);
</script>
<!-- End 艾瑞 -->

<!-- Begin 百度 -->
<script type="text/javascript">
    var _bdhmProtocol = (("https:" == document.location.protocol) ? " https://" : " http://");
    document.write(unescape("%3Cscript src='" + _bdhmProtocol +  "hm.baidu.com/h.js%3F231feb9b35ad5582e75f8d4a2679d0d8' type='text/javascript'%3E%3C/script%3E"));
</script>
<!-- Begin 百度 -->
</html>




