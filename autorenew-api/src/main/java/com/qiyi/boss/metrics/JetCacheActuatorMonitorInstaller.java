/**
 * Created on 2022/08/01.
 */
package com.qiyi.boss.metrics;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.CacheUtil;
import com.alicp.jetcache.MultiLevelCache;
import com.alicp.jetcache.support.AbstractLifecycle;
import com.alicp.jetcache.support.DefaultCacheMonitor;
import com.alicp.jetcache.template.CacheMonitorInstaller;
import com.alicp.jetcache.template.QuickConfig;
import io.micrometer.core.instrument.FunctionCounter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;

/**
 * <AUTHOR> href="mailto:<EMAIL>">huangli</a>
 */
public class JetCacheActuatorMonitorInstaller extends AbstractLifecycle implements CacheMonitorInstaller {

    private MeterRegistry meterRegistry;
    public static final String JETCACHE_METRIC_NAME_PREFIX = "jetcache";
    /**
     * metrics
     */
    private static final String METRIC_NAME_JET_CACHE_CACHE_QPS = JETCACHE_METRIC_NAME_PREFIX + ".qps";
    private static final String METRIC_NAME_JET_CACHE_CACHE_RATE = JETCACHE_METRIC_NAME_PREFIX + ".rate";
    private static final String METRIC_NAME_JET_CACHE_CACHE_GET = JETCACHE_METRIC_NAME_PREFIX + ".get";
    private static final String METRIC_NAME_JET_CACHE_CACHE_HIT = JETCACHE_METRIC_NAME_PREFIX + ".hit";
    private static final String METRIC_NAME_JET_CACHE_CACHE_FAIL = JETCACHE_METRIC_NAME_PREFIX + ".fail";
    private static final String METRIC_NAME_JET_CACHE_CACHE_EXPIRE = JETCACHE_METRIC_NAME_PREFIX + ".expire";
    private static final String METRIC_NAME_JET_CACHE_CACHE_AVG_LOAD_TIME = JETCACHE_METRIC_NAME_PREFIX + ".avg.load.time";
    private static final String METRIC_NAME_JET_CACHE_CACHE_MAX_LOAD_TIME = JETCACHE_METRIC_NAME_PREFIX + ".max.load.time";

    public JetCacheActuatorMonitorInstaller(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }


    @Override
    protected void doInit() {
    }

    @Override
    protected void doShutdown() {
    }

    @Override
    public void addMonitors(CacheManager cacheManager, Cache cache, QuickConfig quickConfig) {
        cache = CacheUtil.getAbstractCache(cache);
        if (cache instanceof MultiLevelCache) {
            MultiLevelCache mc = (MultiLevelCache) cache;
            if (mc.caches().length == 2) {
                Cache local = mc.caches()[0];
                Cache remote = mc.caches()[1];
                DefaultCacheMonitor localMonitor = new DefaultCacheMonitor(quickConfig.getName() + "_local");
                local.config().getMonitors().add(localMonitor);
                registerMeters(meterRegistry, quickConfig.getName() + "_local", localMonitor);
                DefaultCacheMonitor remoteMonitor = new DefaultCacheMonitor(quickConfig.getName() + "_remote");
                remote.config().getMonitors().add(remoteMonitor);
                registerMeters(meterRegistry, quickConfig.getName() + "_remote", localMonitor);
            }
        }

        DefaultCacheMonitor monitor = new DefaultCacheMonitor(quickConfig.getName());
        registerMeters(meterRegistry, quickConfig.getName(), monitor);
        cache.config().getMonitors().add(monitor);
    }




    private static void registerMeters(MeterRegistry meterRegistry,
        String cacheName,
        DefaultCacheMonitor cacheMonitor) {
//		"qps", "rate", "get", "hit", "fail", "expire"
        Gauge.builder(METRIC_NAME_JET_CACHE_CACHE_QPS, cacheMonitor, (monitor) -> monitor.getCacheStat().qps())
            .description("JetCache qps")
            .tag("name", cacheName)
            .register(meterRegistry);
        Gauge.builder(METRIC_NAME_JET_CACHE_CACHE_RATE, cacheMonitor, (monitor) -> monitor.getCacheStat().hitRate())
            .description("JetCache rate")
            .tag("name", cacheName)
            .register(meterRegistry);
        FunctionCounter.builder(METRIC_NAME_JET_CACHE_CACHE_GET, cacheMonitor, (monitor) -> monitor.getCacheStat().getGetCount())
            .description("JetCache get")
            .tag("name", cacheName)
            .register(meterRegistry);
        FunctionCounter.builder(METRIC_NAME_JET_CACHE_CACHE_HIT, cacheMonitor, (monitor) -> monitor.getCacheStat().getGetHitCount())
            .description("JetCache hit")
            .tag("name", cacheName)
            .register(meterRegistry);
        FunctionCounter.builder(METRIC_NAME_JET_CACHE_CACHE_FAIL, cacheMonitor, (monitor) -> monitor.getCacheStat().getGetFailCount())
            .description("JetCache fail")
            .tag("name", cacheName)
            .register(meterRegistry);
        FunctionCounter.builder(METRIC_NAME_JET_CACHE_CACHE_EXPIRE, cacheMonitor, (monitor) -> monitor.getCacheStat().getGetExpireCount())
            .description("JetCache expire")
            .tag("name", cacheName)
            .register(meterRegistry);
//		"avgLoadTime", "maxLoadTime"
        Gauge.builder(METRIC_NAME_JET_CACHE_CACHE_AVG_LOAD_TIME, cacheMonitor, (monitor) -> monitor.getCacheStat().avgLoadTime())
            .description("JetCache avg load time")
            .tag("name", cacheName)
            .register(meterRegistry);
        Gauge.builder(METRIC_NAME_JET_CACHE_CACHE_MAX_LOAD_TIME, cacheMonitor, (monitor) -> monitor.getCacheStat().getMaxLoadTime())
            .description("JetCache max load time")
            .tag("name", cacheName)
            .register(meterRegistry);
    }
}
