package com.qiyi.boss.webapp.exception;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.qiyi.boss.exception.BizException;
import com.qiyi.boss.utils.JacksonUtils;
import com.qiyi.vip.commons.web.dto.WebResult;
import com.qiyi.vip.commons.web.exception.VipExceptionEnum;
import com.iqiyi.vip.component.advice.vo.HttpLogVo;
import com.iqiyi.vip.component.util.PayUtils;
import com.iqiyi.vip.component.util.RequestUtils;

/**
 * @author: kongwenqiang
 * DateTime: 2017/10/20 下午5:22
 * Mail:<EMAIL>
 * Description: desc
 */
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    private static final Logger HTTP_TRACE_LOG = LoggerFactory.getLogger(System.getProperty("app.access.log", "APP_ACCESS_LOG"));

    @ResponseBody
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public Map handle404(HttpServletRequest request, Exception exception) {
        Map<String, String> res = Maps.newHashMap();
        res.put("code", "404");
        res.put("message", "资源不存在!");
        recordHttpTrace(request, res);
        return res;
    }

    @ResponseBody
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public WebResult handle500(HttpServletRequest request, Exception exception) {
        logger.error("error occured ", exception);
        WebResult<Map> webResult = WebResult.newWithFullInfo(VipExceptionEnum.SYSTEM_ERROR.getCode(), VipExceptionEnum.SYSTEM_ERROR.getMsg(), Collections.EMPTY_MAP);
        recordHttpTrace(request, webResult);
        return webResult;
    }

    @ResponseBody
    @ExceptionHandler(BizException.class)
    public WebResult bizException(HttpServletRequest request, BizException exception) {
        logger.warn("biz exception. code: {}, msg: {}", exception.getCode(), exception.getMessage(), exception);
        WebResult<Object> webResult = WebResult.newWithFullInfo(exception.getCode(), exception.getMessage(), null);
        recordHttpTrace(request, webResult);
        return webResult;
    }

    @ResponseBody
    @ExceptionHandler(IllegalArgumentException.class)
    public WebResult bizException(HttpServletRequest request, IllegalArgumentException exception) {
        logger.warn("biz exception. msg: {}", exception.getMessage(), exception);
        WebResult<Object> webResult = WebResult.newWithFullInfo(VipExceptionEnum.PARAMS_ERROR.getCode(), exception.getMessage(), null);
        recordHttpTrace(request, webResult);
        return webResult;
    }

    @ExceptionHandler(value = {MethodArgumentNotValidException.class})
    @ResponseBody
    public WebResult<String> methodArgumentNotValidException(HttpServletRequest servletRequest, MethodArgumentNotValidException exception) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        return buildControllerParamErrorMsg(fieldErrors, exception, servletRequest);
    }

    @ExceptionHandler(value = {BindException.class})
    @ResponseBody
    public WebResult<String> bindException(HttpServletRequest servletRequest, BindException exception) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        return buildControllerParamErrorMsg(fieldErrors, exception, servletRequest);
    }

    @ExceptionHandler(value = {ConstraintViolationException.class})
    @ResponseBody
    public WebResult<String> constraintViolationException(HttpServletRequest servletRequest, ConstraintViolationException exception) {
        String errorMsg = exception.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; "));
        logger.warn("request param illegal occurred: uri={}, parameters={}", servletRequest.getRequestURI(), JSON.toJSONString(servletRequest.getParameterMap()), exception);
        WebResult<String> webResult = WebResult.newWithCodeMsg(VipExceptionEnum.PARAMS_ERROR.getCode(), errorMsg);
        recordHttpTrace(servletRequest, webResult);
        return webResult;
    }

    private WebResult<String> buildControllerParamErrorMsg(List<FieldError> fieldErrors, Exception exception, HttpServletRequest servletRequest) {
        String errorMsg = null;
        if (CollectionUtils.isNotEmpty(fieldErrors)) {
            List<String> paramErrorMsgs = fieldErrors.stream().map(FieldError::getDefaultMessage).collect(Collectors.toList());
            errorMsg = String.join("; ", paramErrorMsgs);
        }
        errorMsg = errorMsg != null ? errorMsg : exception.getMessage();
        logger.warn("request param illegal occurred: uri={}, parameters={}", servletRequest.getRequestURI(), JSON.toJSONString(servletRequest.getParameterMap()), exception);
        WebResult<String> webResult = WebResult.newWithCodeMsg(VipExceptionEnum.PARAMS_ERROR.getCode(), errorMsg);
        recordHttpTrace(servletRequest, webResult);
        return webResult;
    }

    private void recordHttpTrace(HttpServletRequest request, Object response) {
        Map params = request.getParameterMap();
        params = PayUtils.genMapByRequestParas(params);
        HttpLogVo httpLogVo = new HttpLogVo();
        httpLogVo.setClientIp(RequestUtils.getRemoteAddr(request));
        httpLogVo.setLocalIp(RequestUtils.getLocalIP());
        httpLogVo.setHttpUrl(request.getRequestURL().toString());
        httpLogVo.setMethod(request.getMethod());
        httpLogVo.setParams(params);
        httpLogVo.setTime(new Date());
        httpLogVo.setCost(1L);
        httpLogVo.setResponse(JacksonUtils.toJsonString(response));
        HTTP_TRACE_LOG.info(httpLogVo.toJSONString());
    }

}
