package com.qiyi.boss.webapp.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> kongwenqiang
 * DateTime: 2017/11/13 上午11:38
 * Mail:kong<PERSON><EMAIL>
 * Description: 透传错误码接口请求参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DealFailOrderReqDto {

    private String orderCode;
    private String sign;
    private Long user_id;
    private Long vip_type;
    private Integer pay_type;
    private String error_code;
    private String req_error_type;
    private String third_error_code;
    private String third_error_msg;
    private String cb;
}
