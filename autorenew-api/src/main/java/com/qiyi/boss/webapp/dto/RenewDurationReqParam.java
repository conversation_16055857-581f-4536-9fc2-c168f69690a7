package com.qiyi.boss.webapp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("用户自动续费签约时长信息请求参数")
public class RenewDurationReqParam {
    @ApiModelProperty(value = "用户authCookie")
    private String P00001;
    @ApiModelProperty(value = "用户uid")
    private Long uid;
    private String cb;
    @ApiModelProperty(value = "会员类型",required = true)
    @NotNull(message = "vipType为空")
    private Long vipType;
}
