package com.qiyi.boss.webapp.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.SupplierWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.qiyi.boss.Constants;
import com.qiyi.boss.async.SimpleThreadPoolExecutor;
import com.qiyi.boss.dto.ActPeriodTip;
import com.qiyi.boss.dto.AutoRenewManagementDto;
import com.qiyi.boss.dto.AutoRenewProductRecommendAreaInfo;
import com.qiyi.boss.dto.CardBaseInfo;
import com.qiyi.boss.dto.CsRenewInfoRespDto;
import com.qiyi.boss.dto.CsRenewInfoResponse;
import com.qiyi.boss.dto.ExclusiveGiftAreaInfo;
import com.qiyi.boss.dto.FirstDutInfo;
import com.qiyi.boss.dto.FirstXDiscountInfo;
import com.qiyi.boss.dto.GenerateAutoRenewInfoDto;
import com.qiyi.boss.dto.GiftCard;
import com.qiyi.boss.dto.ManagementDto;
import com.qiyi.boss.dto.ManagementPageCoverInfo;
import com.qiyi.boss.dto.ManagementRenewGiftArea;
import com.qiyi.boss.dto.ManagementRenewInfoListDTO;
import com.qiyi.boss.dto.ManagementRenewInfoVO;
import com.qiyi.boss.dto.ManagementRespDto;
import com.qiyi.boss.dto.MarketingInfo;
import com.qiyi.boss.dto.OtherRenewServiceInfoVO;
import com.qiyi.boss.dto.PriceInsuredPeriodInfo;
import com.qiyi.boss.dto.RenewGiftArea;
import com.qiyi.boss.dto.RenewInfoVO;
import com.qiyi.boss.dto.RenewVipInfoVO;
import com.qiyi.boss.dto.UserDutTypeInfo;
import com.qiyi.boss.enums.AgreementStatusEnum;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.BizTypeEnum;
import com.qiyi.boss.enums.DetailDisplayMode;
import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.boss.exception.BizException;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.model.BaseResponse.CodeEnum;
import com.qiyi.boss.model.DutSkuInfo;
import com.qiyi.boss.model.UidInfo;
import com.qiyi.boss.model.UserInfo;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.outerinvoke.AutoRenewMarketingProxy;
import com.qiyi.boss.outerinvoke.CommodityProxy;
import com.qiyi.boss.outerinvoke.InteractMktApi;
import com.qiyi.boss.outerinvoke.VipPartnerRenewServerProxy;
import com.qiyi.boss.outerinvoke.VipTradeCouponProxy;
import com.qiyi.boss.outerinvoke.param.InteractMktReqParam;
import com.qiyi.boss.outerinvoke.result.CouponContext;
import com.qiyi.boss.outerinvoke.result.PartnerUserSignRecordQueryResult;
import com.qiyi.boss.processor.AutoRenewManagementProcessor;
import com.qiyi.boss.service.DutService;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.service.impl.AutoRenewNodeLocationManager;
import com.qiyi.boss.service.impl.AutoRenewUpgradeConfigManager;
import com.qiyi.boss.service.impl.BusinessManager;
import com.qiyi.boss.service.impl.UserManager;
import com.qiyi.boss.utils.AutoRenewConfig;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.DutCouponUtils;
import com.qiyi.boss.utils.HystrixFutureHelper;
import com.qiyi.boss.utils.NumberFormatUtils;
import com.qiyi.boss.utils.PassportApi;
import com.qiyi.boss.utils.PayUtils;
import com.qiyi.boss.utils.RespResultUtils;
import com.qiyi.boss.utils.SignatureUtil;
import com.qiyi.boss.utils.TempIpUtil;
import com.qiyi.boss.utils.TipsUtils;
import com.qiyi.boss.webapp.constant.MktInterfaceCodeConstants;
import com.qiyi.boss.webapp.dto.CsRenewInfoReqParam;
import com.qiyi.boss.webapp.util.RequestUtils;
import com.qiyi.vip.commons.enums.VipTypesEnum;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewUpgradeConfig;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.qiyue.domain.Business;
import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import static com.qiyi.boss.constants.AgreementConstants.EXCLUDE_AGREEMENT_TYPES;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2020-03-02
 * Time: 15:12
 */
@RequestMapping("/services/autorenew")
@Controller
@Api(value = "AutoRenewManagementController", tags = "自动续费管理页接口")
public class AutoRenewManagementController {


    private static final Logger LOGGER = LoggerFactory.getLogger(AutoRenewManagementController.class);
    private static final int ONE_MONTH_IN_DAY = 30;
    private static final int ONE_YEAR_IN_DAY = 365;
    public static final int ONE = 1;

    @ConfigJsonValue("${cannot.use.coupon.payChannel:[9,44,45,46,50,51,52]}")
    private List<Integer> cannotUseCouponPayChannels;

    @Resource
	private PassportApi passportApi;
	@Resource
	private CloudConfig cloudConfig;
	@Resource
	private DutService dutService;
	@Resource
	private UserManager userService;
	@Resource
	private AutoRenewManagementProcessor autoRenewManagementProcessor;
	@Resource
	private AutoRenewNodeLocationManager autoRenewNodeLocationManager;
	@Resource
	InteractMktApi interactMktApi;
	@Resource
	AutoRenewMarketingProxy autoRenewMarketingProxy;
	@Resource
	AutoRenewConfig autoRenewConfig;
	@Resource
	VipPartnerRenewServerProxy partnerRenewServerProxy;
    @Resource
    UserAgreementService agreementService;
    @Resource
    AutoRenewUpgradeConfigManager autoRenewUpgradeConfigManager;
    @Resource
    private VipTradeCouponProxy vipTradeCouponProxy;
    @Resource
    private CommodityProxy commodityProxy;
    @Resource
    private BusinessManager businessManager;

	/**
	 * http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
	 *
	 * @param autoRenewManagementDto  {@link AutoRenewManagementDto}
	 * @param servletRequest {@link HttpServletRequest}
	 */
	@RequestMapping("/management")
	@ResponseBody
	public void autoRenewManagement(AutoRenewManagementDto autoRenewManagementDto, HttpServletRequest servletRequest) {
		try {
			UserInfo user = passportApi.getUserByPassport(servletRequest);
			if (user == null) {
				Map<String, Object> resultMap = Maps.newHashMap();
				resultMap.put("code", "Q00304");
				resultMap.put("message", "未登录");
				RespResultUtils.responseJsonOrJsonp(autoRenewManagementDto.getCb(), resultMap);
				return;
			}

			Map<String, Object> resultMap = doAutoRenewManagement(user, autoRenewManagementDto, servletRequest);
			RespResultUtils.responseJsonOrJsonp(autoRenewManagementDto.getCb(), resultMap);
		} catch (Exception e) {
			LOGGER.error("自动续费管理业接口查询error, param: {}", JSONObject.toJSONString(autoRenewManagementDto), e);
			Map<String, Object> resultMap = Maps.newHashMap();
			resultMap.put("code", "Q00332");
			resultMap.put("message", "系统错误");
			RespResultUtils.responseJsonOrJsonp(autoRenewManagementDto.getCb(), resultMap);
		}

	}

    /**
     * <a href="https://iq.feishu.cn/wiki/KLHjwYoBpiDukRkMtnrcDZbXnBg">...</a>
     * @param servletRequest {@link HttpServletRequest}
     */
    @RequestMapping(value = "/managementV2", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "新版自动续费管理页接口", httpMethod = "GET")
    public BaseResponse<ManagementRespDto> autoRenewManagement(ManagementDto managementDto, HttpServletRequest servletRequest)
        throws ExecutionException, InterruptedException {
        UserInfo user = passportApi.getUserByPassport(servletRequest);
        if (user == null) {
            return BaseResponse.notLoggedIn();
        }
        Long userId = user.getId();
        CompletableFuture<List<OtherRenewServiceInfoVO>> future = CompletableFuture.supplyAsync(() -> autoRenewManagementProcessor.buildOtherRenewServiceInfoVO(user, managementDto), SimpleThreadPoolExecutor.threadPool);
        Future<List<PartnerUserSignRecordQueryResult>> partnerUserSignRecordsFuture = null;
        if (CloudConfigUtil.enableNewMobileFunc()) {
            partnerUserSignRecordsFuture = partnerRenewServerProxy.queryUserSignRecordWithMultiVipTypeAsync(userId, null);
        }

        ManagementRenewInfoListDTO managementRenewInfoListDTO = buildAutoRenewList(user, managementDto, servletRequest, true, partnerUserSignRecordsFuture);
        List<ManagementRenewInfoVO> autoRenewList = managementRenewInfoListDTO != null ? managementRenewInfoListDTO.getAutoRenewList() : Collections.emptyList();
        Future<List<CouponContext>> userCouponListFuture = null;
        if (managementRenewInfoListDTO != null && CollectionUtils.isNotEmpty(managementRenewInfoListDTO.getDutSkuInfo())) {
            List<DutSkuInfo> dutSkuInfo = managementRenewInfoListDTO.getDutSkuInfo();
            List<String> partnerList = getPartnerList(dutSkuInfo);
            if (partnerList.size() != ONE) {
                LOGGER.error("partner info not valid uid:{}, partnerList: {}", userId, partnerList);
            }
            userCouponListFuture = vipTradeCouponProxy.queryAvailableDutCouponAsync(userId, dutSkuInfo, partnerList);
        }
        String authCookie = RequestUtils.getTicket(servletRequest);
        
        // 异步获取续费礼品区域信息
        CompletableFuture<ManagementRenewGiftArea> renewGiftAreaFuture = CompletableFuture.supplyAsync(SupplierWrapper.of(() -> autoRenewManagementProcessor.buildManagementRenewGiftArea(authCookie, userId, autoRenewList)), SimpleThreadPoolExecutor.threadPool);
        // 在获取礼品区域信息的同时，并行执行优惠券计算
        DutCouponUtils.calcAndSetPriceAfterCoupon(autoRenewList, userCouponListFuture, cannotUseCouponPayChannels);
        
        // 等待礼品区域信息获取完成，然后构建响应
        ManagementRespDto respDto = ManagementRespDto.builder()
            .renewGiftArea(renewGiftAreaFuture.get())
            .otherRenewService(future.get())
            .autoRenewList(autoRenewList)
            .build();
        return BaseResponse.createSuccess(respDto);
    }
    // TODO：默认当前自动续费关理页展示的列表都是同1个partner，后续注意业务变动有影响
    private List<String> getPartnerList(List<DutSkuInfo> dutSkuInfo) {
        List<String> partnerList = dutSkuInfo.stream().map(DutSkuInfo::getSkuId)
            .filter(StringUtils::isNotBlank)
            .map(skuId -> commodityProxy.queryCommodity(skuId))
            .filter(Objects::nonNull)
            .filter(c -> StringUtils.isNotBlank(c.getBusinessCode()))
            .map(c -> businessManager.getBusinessByCode(c.getBusinessCode()))
            .filter(Objects::nonNull)
            .map(Business::getPayCenterPartnerId)
            .distinct()
            .collect(Collectors.toList());
        return partnerList;
    }

    @RequestMapping(value = "/csRenewInfo")
    @ResponseBody
    @ApiOperation(value = "智能客服用户自动续费信息查询接口", httpMethod = "GET")
    public BaseResponse<CsRenewInfoResponse> customerServiceRenewInfo(HttpServletRequest request, CsRenewInfoReqParam reqParam) {
        LOGGER.info("customerServiceRenewInfo enter, reqParam:{}", reqParam);
        String p00001 = reqParam.getP00001();
        Long uid = reqParam.getUid();
        String partner = reqParam.getPartner();
        boolean valid = SignatureUtil.checkSign(request.getParameterMap(), CloudConfigUtil.getSignKey(partner));
        if (!valid) {
            throw BizException.newException(BaseResponse.CodeEnum.INVALID_SIGN);
        }
        UidInfo uidInfo;
        if (StringUtils.isNotBlank(p00001)) {
            uidInfo = passportApi.getUidByAuthCookie(p00001);
            if (uidInfo == null) {
                throw BizException.newException(BaseResponse.CodeEnum.ERROR_LOGIN);
            }
            uid = Long.valueOf(uidInfo.getUid());
        }
        if (uid == null) {
            throw BizException.newException(BaseResponse.CodeEnum.ERROR_PARAM);
        }
        Future<List<PartnerUserSignRecordQueryResult>> partnerUserSignRecordsFuture = null;
        if (CloudConfigUtil.enableNewMobileFunc()) {
            partnerUserSignRecordsFuture = partnerRenewServerProxy.queryUserSignRecordWithMultiVipTypeAsync(uid, null);
        }
        UserInfo userInfo = new UserInfo();
        userInfo.setId(uid);
        CsRenewInfoResponse response = CsRenewInfoResponse.builder()
            .uid(uid)
            .csRenewInfoRespDtoList(buildCsRenewInfoList(userInfo, reqParam, request, partnerUserSignRecordsFuture))
            .build();
        return BaseResponse.createSuccess(response);
    }

	private Map<String, Object> doAutoRenewManagement(UserInfo user, ManagementDto autoRenewManagementDto,
													  HttpServletRequest servletRequest) {
		Map<String, String> params = PayUtils.genMapByRequestParas(servletRequest.getParameterMap());
		LOGGER.info("[AutoRenewManagement Params:{}] [step:enter] [clientIp:{}]", params, TempIpUtil.getAndDiffIp(servletRequest));
		Map<String, Object> resultMap = Maps.newHashMap();
		Map<String, Object> userDataMap = Maps.newHashMap();
		Long userId = user.getId();
		userDataMap.put("bindInfo", autoRenewManagementProcessor.getBindInfo(userId));
		userDataMap.put("uid", userId.toString());

		String platform = StringUtils.defaultIfEmpty(autoRenewManagementDto.getPlatform(), Constants.PLATFORM_DEFAULT_CODE);
        Future<List<PartnerUserSignRecordQueryResult>> partnerUserSignRecordsFuture = null;
        if (CloudConfigUtil.enableNewMobileFunc()) {
            partnerUserSignRecordsFuture = partnerRenewServerProxy.queryUserSignRecordWithMultiVipTypeAsync(user.getId(), null);
        }
        ManagementRenewInfoListDTO managementRenewInfoListDTO = buildAutoRenewList(user, autoRenewManagementDto, servletRequest, false, partnerUserSignRecordsFuture);
        List<ManagementRenewInfoVO> autoRenewList = managementRenewInfoListDTO != null ? managementRenewInfoListDTO.getAutoRenewList() : Collections.emptyList();
        userDataMap.put("autoRenewList", autoRenewList);
		userDataMap.put("nodeLocations", autoRenewNodeLocationManager.findNodeLocationMap(null, platform));
		buildNextRenewGiftsAndMktInfo(params, userDataMap, autoRenewList);

		LOGGER.info("[AutoRenewManagement Params:{}] [step:exit] [ip:{}] [uid:{}]",
				params, TempIpUtil.getAndDiffIp(servletRequest), user.getId());
		resultMap.put("code", CodeEnum.SUCCESS.getCode());
		resultMap.put("message", CodeEnum.SUCCESS.getMsg());
		resultMap.put("data", userDataMap);

		return resultMap;
	}

	/**
	 * 构建下次续费福利和互动营销位信息节点数据
	 * @param params
	 * @param userDataMap
	 */
	private void buildNextRenewGiftsAndMktInfo(Map<String, String> params, Map<String, Object> userDataMap, List<ManagementRenewInfoVO> autoRenewList) {
		if (MapUtils.isEmpty(params)) {
			return;
		}
		InteractMktReqParam interactMktReqParam = InteractMktReqParam.buildFromReqParams(params);
		Map<Integer, String> vipTypeToNextDutDateMap = new HashMap<>();
		Set<String> exclusiveGiftMktCodes = new HashSet<>();
		if (CollectionUtils.isNotEmpty(autoRenewList)) {
			for (ManagementRenewInfoVO managementRenewInfoVO: autoRenewList) {
                RenewVipInfoVO vipInfo = managementRenewInfoVO.getVipInfo();
                Integer vipType = vipInfo.getVipType();
                RenewInfoVO autoRenewInfo = managementRenewInfoVO.getAutoRenewInfo();
                vipTypeToNextDutDateMap.put(vipType, autoRenewInfo.getDoPayTime());

				String exclusiveGiftMktCode = MktInterfaceCodeConstants.vipTypeToExclusiveGiftMktCodeMap().get(vipType.longValue());
				if (StringUtils.isNotBlank(exclusiveGiftMktCode)) {
					exclusiveGiftMktCodes.add(exclusiveGiftMktCode);
				}
			}
			if (CollectionUtils.isNotEmpty(exclusiveGiftMktCodes)) {
				interactMktReqParam.setInterfaceCode(String.join(",", exclusiveGiftMktCodes));
			}
		} else {
			interactMktReqParam.setInterfaceCode(MktInterfaceCodeConstants.AUTO_RENEW_PRODUCTS_RECOMMEND_CODE);
		}
		String interfaceCodes = interactMktReqParam.getInterfaceCode() != null ? interactMktReqParam.getInterfaceCode() + "," : "";
		interfaceCodes = interfaceCodes + MktInterfaceCodeConstants.AUTO_RENEW_MANAGEMENT_PAGE_COVER_CODE;
		interactMktReqParam.setInterfaceCode(interfaceCodes);

		boolean needShowReceivableGiftInfo = autoRenewConfig.needShowReceivableGiftInfo(params.get("platform"));
		Map<Integer, List<GiftCard>> vipTypeToGiftInfosMap = Collections.emptyMap();
        if (needShowReceivableGiftInfo) {
			vipTypeToGiftInfosMap = autoRenewMarketingProxy.nextRenewGifts(params.get("P00001"), Lists.newArrayList(vipTypeToNextDutDateMap.keySet()));
		}
		MarketingInfo marketingInfo = interactMktApi.getMarketingInfo(interactMktReqParam);
		List<AutoRenewProductRecommendAreaInfo> productRecommendInfos = Collections.emptyList();
		List<ManagementPageCoverInfo> managementPageCoverInfos = Collections.emptyList();
		Map<String, List<ExclusiveGiftAreaInfo>> interfaceCodeToListMap = Collections.emptyMap();
		if (marketingInfo != null) {
			productRecommendInfos = marketingInfo.getAutoRenewProductRecommendInfos();
			managementPageCoverInfos = marketingInfo.getManagementPageCoverInfo();
			List<ExclusiveGiftAreaInfo> exclusiveGiftAreaInfos = marketingInfo.getExclusiveGiftInfos();
			interfaceCodeToListMap = CollectionUtils.isNotEmpty(exclusiveGiftAreaInfos)
					? exclusiveGiftAreaInfos.stream().collect(Collectors.groupingBy(ExclusiveGiftAreaInfo::getInterfaceCode))
					: Collections.emptyMap();
		}

		for (ManagementRenewInfoVO managementRenewInfoVO : autoRenewList) {
            RenewVipInfoVO vipInfo = managementRenewInfoVO.getVipInfo();
            Integer vipType = vipInfo.getVipType();
            String interfaceCode = MktInterfaceCodeConstants.vipTypeToExclusiveGiftMktCodeMap().get(vipType.longValue());
            RenewInfoVO autoRenewInfo = managementRenewInfoVO.getAutoRenewInfo();

            autoRenewInfo.setExclusiveGiftInfos(interfaceCodeToListMap.get(interfaceCode));
			RenewGiftArea renewGiftArea = buildRenewGiftAreaInfo(autoRenewInfo, vipTypeToNextDutDateMap.get(vipType), vipTypeToGiftInfosMap.get(vipType));
            autoRenewInfo.setRenewGiftArea(renewGiftArea);
		}

		userDataMap.put("autoRenewProductRecommendInfos", productRecommendInfos);
		userDataMap.put("coverInfos", managementPageCoverInfos);
	}

	/**
	 * 构建续费奖励信息
	 * @param renewInfoVO
	 * @param nextDutDateStr
	 * @param giftCards
	 */
	private RenewGiftArea buildRenewGiftAreaInfo(RenewInfoVO renewInfoVO, String nextDutDateStr, List<GiftCard> giftCards) {
        if (StringUtils.isBlank(nextDutDateStr)) {
            return null;
        }
		if (giftCards == null) {
			giftCards = new ArrayList<>();
		}

		GiftCard firstXDiscountCard = null;
        GiftCard priceInsuredPeriodCard = null;
        ActPeriodTip actPeriodTips = renewInfoVO.getActPeriodTips();
        if (actPeriodTips != null && actPeriodTips.getPriceInsuredPeriodInfo() != null) {
            PriceInsuredPeriodInfo periodInfo = actPeriodTips.getPriceInsuredPeriodInfo();
            CardBaseInfo cardBaseInfo = TipsUtils.buildPriceInsuredPeriodTips(periodInfo);
			priceInsuredPeriodCard = GiftCard.builder()
                    .title(cardBaseInfo.getTitle())
                    .promotionText(cardBaseInfo.getPromotionText())
                    .imgUrl(CloudConfigUtil.firstXDiscountGiftImgUrl())
                    .highVersionImgUrl(CloudConfigUtil.getGiftCardCommonNewGiftImgUrl())
                    .detailDisplayMode(DetailDisplayMode.COVER.getValue())
                    .detailTips(cardBaseInfo.getDetailTips())
                    .buttonText("查详情")
                    .colorValue("#EBB94F")
                    .build();
            actPeriodTips.setPriceInsuredPeriodInfo(null);
			giftCards.add(0, priceInsuredPeriodCard);
		}
        if (actPeriodTips != null && actPeriodTips.getFirstXDiscountInfo() != null) {
            FirstXDiscountInfo discountInfo = actPeriodTips.getFirstXDiscountInfo();
            CardBaseInfo cardBaseInfo = TipsUtils.buildFirstXDiscountTips(discountInfo);
            firstXDiscountCard = GiftCard.builder()
                    .title(cardBaseInfo.getTitle())
                    .promotionText(cardBaseInfo.getPromotionText())
                    .imgUrl(CloudConfigUtil.priceInsuredPeriodGiftImgUrl())
                    .highVersionImgUrl(CloudConfigUtil.getGiftCardCommonNewGiftImgUrl())
                    .detailDisplayMode(DetailDisplayMode.COVER.getValue())
                    .detailTips(cardBaseInfo.getDetailTips())
					.buttonText("查详情")
					.colorValue("#EBB94F")
                    .build();
            actPeriodTips.setFirstXDiscountInfo(null);
            giftCards.add(0, firstXDiscountCard);
        }

		int dateInterval = DateHelper.getDateInterval(DateHelper.getNowDate(), nextDutDateStr);
		giftCards.forEach(giftCard -> setGiftCardButtonTextAndColorValue(giftCard, getRenewDescPrefix(dateInterval)));
		return RenewGiftArea.builder()
				.title(CollectionUtils.isEmpty(giftCards) ? "自动续费专属权益" : "续费奖励")
				.description("您已获得以下奖励，中途取消将全部失去")
				.giftSendRecordUrl(CloudConfigUtil.getManagementPageGiftSendRecordUrl())
				.giftCards(giftCards)
				.build();
	}

    private String getRenewDescPrefix(int dateInterval) {
	    if (dateInterval <= ONE_MONTH_IN_DAY) {
            return dateInterval + "天后领";
        } else if (dateInterval <= ONE_YEAR_IN_DAY) {
            return dateInterval / ONE_MONTH_IN_DAY + "个月后领";
        }
        return dateInterval / ONE_YEAR_IN_DAY + "年后领";
    }


	private void setGiftCardButtonTextAndColorValue(GiftCard giftCard, String renewDescPrefix) {
		if (StringUtils.isBlank(giftCard.getButtonText())) {
            giftCard.setButtonText(renewDescPrefix);
		}
        if (StringUtils.isBlank(giftCard.getColorValue())) {
            giftCard.setColorValue(CloudConfigUtil.getGiftCardCommonColorValue());
        }
	}

	private ManagementRenewInfoListDTO buildAutoRenewList(UserInfo user, ManagementDto managementDto, HttpServletRequest servletRequest, boolean newVersion, Future<List<PartnerUserSignRecordQueryResult>> partnerUserSignRecordsFuture) {
		Long userId = user.getId();
		List<ManagementRenewInfoVO> autoRenewList = Lists.newArrayList();
		boolean autoRenewStatus = false;
		boolean isDutUser;
		Integer index = 0;
        List<Long> vipTypeDisplaySort = getVipTypeDisplaySort(managementDto);

		List<PartnerUserSignRecordQueryResult> partnerUserSignRecords = HystrixFutureHelper.getFutureResult(partnerUserSignRecordsFuture,
				Collections.emptyList(), HystrixCommandPropsEnum.QUERY_USER_SIGN_RECORD_URL);
		Map<Long, List<PartnerUserSignRecordQueryResult>> vipTypeToPartnerUserSignRecordMap = partnerUserSignRecords.stream()
				.collect(Collectors.groupingBy(PartnerUserSignRecordQueryResult::getVipType));

        String platform = Constants.PLATFORM_DEFAULT_CODE;
        Map<Long, VipUser> userMultiVipTypeInfo = userService.getUserMultiVipTypeInfo(userId, vipTypeDisplaySort, platform);
        List<DutUserNew> allDutUserNews = agreementService.getByExcludeAgreementTypesAndVipTypes(userId, EXCLUDE_AGREEMENT_TYPES, Collections.emptyList(), AgreementStatusEnum.VALID);
        Map<Long, List<DutUserNew>> dutUserNewByVip = allDutUserNews
            .stream()
            .filter(DutUserNew::isAutoRenewUser)
            .collect(Collectors.groupingBy(DutUserNew::getVipType));
        Integer bizType = managementDto.getBizType();
        List<DutSkuInfo> dutSkuInfo = new ArrayList<>();
        for (Long vipType : vipTypeDisplaySort) {
			try {
                List<DutUserNew> dutUserNews = dutUserNewByVip.getOrDefault(vipType, Collections.emptyList());
                isDutUser = CollectionUtils.isNotEmpty(dutUserNews);
				List<PartnerUserSignRecordQueryResult> partnerUserSignRecordsOfVipType = vipTypeToPartnerUserSignRecordMap.get(vipType);
				if (!isDutUser && (CollectionUtils.isEmpty(partnerUserSignRecordsOfVipType))) {
					continue;
				}

				boolean justSignNewMobile = false;
				if (!isDutUser) {
					justSignNewMobile = true;
				}

				autoRenewStatus = true;
				index++;
				Timestamp deadline;
                RenewVipInfoVO vipInfo = new RenewVipInfoVO();
                vipInfo.setVipType(vipType.intValue());
                VipUser vipUser = userMultiVipTypeInfo.get(vipType);
                deadline = vipUser != null ? vipUser.getDeadline() : null;
                if (Constants.VIP_USER_SUPER == vipType && vipUser != null) {
                    vipInfo.setMonthly((vipUser.getPayType() != null && (vipUser.getPayType() == 1 || vipUser.getPayType() == 2)));
                }
				// 已过期用户不展示自动续费信息
				if (deadline == null || DateHelper.getCurrentTime().after(deadline)) {
					continue;
				}
                RenewVipInfoVO vipInfoVO = autoRenewManagementProcessor.getVipInfo(vipType, vipInfo, AgreementTypeEnum.AUTO_RENEW.getValue(), dutUserNews);

                // 此处根据bizType展示签约的套餐
                List<Integer> bizManagementAgreementType = CloudConfigUtil.getBizManagementAgreementType(bizType);
                for (Integer agreementType : bizManagementAgreementType) {
                    List<DutUserNew> matchVipAndAgreementTypeUser = dutUserNews.stream()
                        .filter(d -> ObjectUtils.equals(agreementType, d.getAgreementType()))
                        .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(matchVipAndAgreementTypeUser)) {
                        continue;
                    }

                    ManagementRenewInfoVO managementRenewInfoVO = new ManagementRenewInfoVO();
                    managementRenewInfoVO.setStatus(DutUserNew.RENEW_AUTO);
                    managementRenewInfoVO.setVipInfo(vipInfoVO);
                    PartnerUserSignRecordQueryResult partnerUserSignRecord = CollectionUtils.isNotEmpty(partnerUserSignRecordsOfVipType)
                        ? partnerUserSignRecordsOfVipType.stream().min(Comparator.comparing(PartnerUserSignRecordQueryResult::getSignTime)).orElse(null)
                        : null;
                    boolean commonAutoRenew = AgreementTypeEnum.commonAutoRenew(agreementType);
                    GenerateAutoRenewInfoDto generateAutoRenewInfoDto = GenerateAutoRenewInfoDto.builder()
                        .servletRequest(servletRequest)
                        .user(user)
                        .deadline(deadline)
                        .vipType(vipType)
                        .vipInfo(vipInfo)
                        .isDutUser(isDutUser)
                        .platform(managementDto.getPlatform())
                        .fc(managementDto.getFc())
                        .fv(managementDto.getFv())
                        .lang(managementDto.getLang())
                        .pageType(AutoRenewManagementProcessor.PAGE_TYPE_MIX)
                        .amount(managementDto.getAmount())
                        .justSignNewMobile(justSignNewMobile && commonAutoRenew)
                        .partnerUserSignRecord(commonAutoRenew ? partnerUserSignRecord : null)
                        .needGenPureSignUrl(commonAutoRenew)
                        .newVersion(newVersion)
                        .dutUserNews(matchVipAndAgreementTypeUser)
                        .agreementType(agreementType)
                        .bizType(bizType)
                        .build();
                    RenewInfoVO renewInfoVO = autoRenewManagementProcessor.generateAutoRenewInfo(generateAutoRenewInfoDto);
                    managementRenewInfoVO.setAutoRenewInfo(renewInfoVO);
                    if (renewInfoVO != null && renewInfoVO.getFirstDutInfo() != null) {
                        FirstDutInfo firstDutInfo = renewInfoVO.getFirstDutInfo();
                        dutSkuInfo.add(new DutSkuInfo(firstDutInfo.getFirstRenewPrice(), firstDutInfo.getSkuId()));
                    }
                    if (!newVersion) {
                        Map<String, Object> vipTypeNodeLocations = autoRenewNodeLocationManager.findNodeLocationMap(vipType, platform);
                        managementRenewInfoVO.setVipTypeNodeLocations(vipTypeNodeLocations);
                        managementRenewInfoVO.setSort(index);
                    }
                    autoRenewList.add(managementRenewInfoVO);
                }
                sortRenewInfoVO(autoRenewList);
			} catch (Exception e) {
				LOGGER.error("查询自动续费信息异常, uid:{}", userId, e);
				return null;
			}
		}
		return autoRenewStatus ? new ManagementRenewInfoListDTO(autoRenewList, dutSkuInfo) : null;
	}

    private void sortRenewInfoVO(List<ManagementRenewInfoVO> autoRenewList) {
        if (CollectionUtils.isEmpty(autoRenewList)) {
            return;
        }
        // 使用三个列表来存储不同类型的元素
        List<ManagementRenewInfoVO> joinTypes = new ArrayList<>();
        List<ManagementRenewInfoVO> familyTypes = new ArrayList<>();
        List<ManagementRenewInfoVO> otherTypes = new ArrayList<>();

        // 遍历一次列表，根据条件将元素添加到相应列表
        for (ManagementRenewInfoVO m : autoRenewList) {
            RenewInfoVO info = m.getAutoRenewInfo();
            if (AgreementTypeEnum.jointType(info.getAgreementType())) {
                joinTypes.add(m);
            } else if (AgreementTypeEnum.familyType(info.getAgreementType())) {
                familyTypes.add(m);
            } else if (!EXCLUDE_AGREEMENT_TYPES.contains(info.getAgreementType())) {
                otherTypes.add(m);
            }
        }
        joinTypes.sort(Comparator.comparing(m -> m.getAutoRenewInfo().getLastOperateTime()));
        ArrayList<ManagementRenewInfoVO> result = new ArrayList<>();
        result.addAll(joinTypes);
        result.addAll(familyTypes);
        result.addAll(otherTypes);
        autoRenewList.clear();
        autoRenewList.addAll(result);
    }

    public List<CsRenewInfoRespDto> buildCsRenewInfoList(UserInfo userInfo, CsRenewInfoReqParam renewInfoReqParam, HttpServletRequest servletRequest, Future<List<PartnerUserSignRecordQueryResult>> partnerUserSignRecordsFuture) {

        boolean autoRenewStatus = false;
        boolean isDutUser;
        List<Long> vipList = getCsRenewInfoVipList();
        if (CollectionUtils.isEmpty(vipList)) {
            return Collections.emptyList();
        }
        Long uid = renewInfoReqParam.getUid();
        List<PartnerUserSignRecordQueryResult> partnerUserSignRecords = HystrixFutureHelper.getFutureResult(partnerUserSignRecordsFuture,
            Collections.emptyList(), HystrixCommandPropsEnum.QUERY_USER_SIGN_RECORD_URL);
        Map<Long, List<PartnerUserSignRecordQueryResult>> vipTypeToPartnerUserSignRecordMap = partnerUserSignRecords.stream()
            .collect(Collectors.groupingBy(PartnerUserSignRecordQueryResult::getVipType));
        String platform = Constants.PLATFORM_DEFAULT_CODE;
        Map<Long, VipUser> userMultiVipTypeInfo = userService.getUserMultiVipTypeInfo(uid, vipList, platform);
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        Map<Long, DutUserNew> dutUserNewMap = agreementService.getEffectiveByAgreementType(uid, agreementType)
            .stream()
            .filter(DutUserNew::isAutoRenewUser)
            .filter(d -> d.getDeadline() != null && DateHelper.getCurrentTime().before(d.getDeadline()))
            .collect(Collectors.toMap(DutUserNew::getVipType, Function.identity(), (d1, d2) -> d2));

        ArrayList<CsRenewInfoRespDto> renewInfoRespDto = new ArrayList<>();
        for (Long vipType : vipList) {
            try {
                isDutUser = dutUserNewMap.get(vipType) != null;
                List<PartnerUserSignRecordQueryResult> partnerUserSignRecordsOfVipType = vipTypeToPartnerUserSignRecordMap.get(vipType);
                if (!isDutUser && (CollectionUtils.isEmpty(partnerUserSignRecordsOfVipType))) {
                    continue;
                }

                boolean justSignNewMobile = false;
                if (!isDutUser) {
                    justSignNewMobile = true;
                }

                autoRenewStatus = true;
                RenewVipInfoVO vipInfo = new RenewVipInfoVO();
                vipInfo.setVipType(vipType.intValue());
                VipUser user = userMultiVipTypeInfo.get(vipType);
                Timestamp deadline  = user == null ? null : user.getDeadline();

                // 已过期用户不展示自动续费信息
                if (deadline == null || DateHelper.getCurrentTime().after(deadline)) {
                    continue;
                }

                PartnerUserSignRecordQueryResult partnerUserSignRecord = CollectionUtils.isNotEmpty(partnerUserSignRecordsOfVipType)
                    ? partnerUserSignRecordsOfVipType.stream().min(Comparator.comparing(PartnerUserSignRecordQueryResult::getSignTime)).orElse(null)
                    : null;
                GenerateAutoRenewInfoDto generateAutoRenewInfoDto = GenerateAutoRenewInfoDto.builder()
                    .servletRequest(servletRequest)
                    .user(userInfo)
                    .deadline(deadline)
                    .vipType(vipType)
                    .vipInfo(vipInfo)
                    .isDutUser(isDutUser)
                    .platform(platform)
                    .pageType(AutoRenewManagementProcessor.PAGE_TYPE_MIX)
                    .justSignNewMobile(justSignNewMobile)
                    .partnerUserSignRecord(partnerUserSignRecord)
                    .newVersion(true)
                    .needGenPureSignUrl(false)
                    .agreementType(agreementType)
                    .bizType(null)
                    .build();
                RenewInfoVO renewInfoVO = autoRenewManagementProcessor.generateAutoRenewInfo(generateAutoRenewInfoDto);
                if (renewInfoVO == null) {
                    continue;
                }
                CsRenewInfoRespDto csRenewInfoRespDto = new CsRenewInfoRespDto();
                csRenewInfoRespDto.setVipType(vipType);
                csRenewInfoRespDto.setProductName(renewInfoVO.getProductName());
                Integer firstRenewPrice = renewInfoVO.getFirstDutInfo().getFirstRenewPrice();
                csRenewInfoRespDto.setRenewPrice(NumberFormatUtils.formatToStr(firstRenewPrice, "#.#"));
                boolean insurancePeriod = isInPriceInsurancePeriod(renewInfoVO);
                csRenewInfoRespDto.setPriceInsurancePeriod(BooleanUtils.toInteger(insurancePeriod));
                csRenewInfoRespDto.setNormalDutPrice(getNormalDutPrice(insurancePeriod, renewInfoVO));
                renewInfoRespDto.add(csRenewInfoRespDto);
            } catch (Exception e) {
                LOGGER.error("查询自动续费信息异常", e);
                return Collections.emptyList();
            }
        }
        return autoRenewStatus ? renewInfoRespDto : Collections.emptyList();
    }

    private boolean isInPriceInsurancePeriod(RenewInfoVO renewInfoVO) {
        ActPeriodTip actPeriodTips = renewInfoVO.getActPeriodTips();
        return actPeriodTips != null && actPeriodTips.getPriceInsuredPeriodInfo() != null;
    }

    private String getNormalDutPrice(boolean insurancePeriod, RenewInfoVO renewInfoVO) {
        if (!insurancePeriod) {
            return null;
        }
        UserDutTypeInfo userDutTypeInfo = renewInfoVO.getUserDutTypeInfo();
        if (userDutTypeInfo != null && userDutTypeInfo.getIsUpgrade()) {
            AutoRenewDutType dutType = userDutTypeInfo.getAutoRenewDutType();
            Short maxPriority = userDutTypeInfo.getMaxPriority();
            AutoRenewUpgradeConfig upgradeConfig = autoRenewUpgradeConfigManager.getByVipType(dutType.getSourceVipType(), dutType.getVipType(), maxPriority);
            return upgradeConfig != null ? NumberFormatUtils.formatToStr(upgradeConfig.getFinalRenewPrice(), "#.#") : null;
        }
        ActPeriodTip actPeriodTips = renewInfoVO.getActPeriodTips();
        if (actPeriodTips == null) {
            return null;
        }
        PriceInsuredPeriodInfo priceInsuredPeriodInfo = actPeriodTips.getPriceInsuredPeriodInfo();
        if (priceInsuredPeriodInfo != null && priceInsuredPeriodInfo.getContractPrice() != null) {
            return NumberFormatUtils.formatToStr(priceInsuredPeriodInfo.getContractPrice(), "#.#");
        }
        return null;
    }

	private List<Long> getVipTypeDisplaySort(ManagementDto managementDto) {
        Integer bizType = managementDto.getBizType();
        Long vipType = managementDto.getVipType();
        BizTypeEnum bizTypeEnum = BizTypeEnum.of(bizType);
        if (bizTypeEnum == null || vipType == null) {
            return CloudConfigUtil.getDefaultManagementVipTypeSort();
        }
        List<Long> displaySort = CloudConfigUtil.getBizManagementVipTypeSort(bizType);
        List<Long> vipTypeSort = Lists.newLinkedList();
        vipTypeSort.add(vipType);
        vipTypeSort.addAll(displaySort.stream().filter(v -> !v.equals(vipType)).collect(Collectors.toList()));
        return vipTypeSort;
	}

    private List<Long> getCsRenewInfoVipList() {
        return Splitter.on(",").trimResults().splitToList(cloudConfig.getProperty("customer.service.renewinfo.viptype.list", "1, 58 , 16 , 4, 56, 5, 54, 57, 13, 50"))
            .stream()
            .map(Long::parseLong)
            .collect(Collectors.toList());
    }



    private boolean notBasicVipType(Long vipType) {
        return !VipTypesEnum.VIPTYPE_BASIC.getValue().equals(vipType.intValue());
    }
}
