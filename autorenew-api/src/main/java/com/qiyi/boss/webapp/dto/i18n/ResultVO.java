package com.qiyi.boss.webapp.dto.i18n;

import lombok.Builder;
import lombok.Data;

import com.qiyi.boss.enums.ResultCodeEnum;

import static com.qiyi.boss.model.BaseResponse.CodeEnum.ERROR_RESTRICT_CANCELLATION;

/**
 * @author: zhangtengfei01
 * @date: 2020/7/3 20:51
 * @desc: 同步数据接口返回结果
 */
@Builder
@Data
public class ResultVO<T> {
    private String code;
    private String msg;
    private T data;

    /**
     * 系统异常
     *
     * @return
     */
    public static ResultVO error(String code, String msg) {
        return ResultVO.builder()
                .code(code)
                .msg(msg)
                .build();
    }

    public static ResultVO sysError() {
        return ResultVO.builder()
                .code(ResultCodeEnum.SYSTEM_ERROR.value())
                .msg(ResultCodeEnum.SYSTEM_ERROR.desc())
                .build();
    }

    public static ResultVO paramError() {
        return ResultVO.builder()
                .code(ResultCodeEnum.PARAM_ERROR.value())
                .msg(ResultCodeEnum.PARAM_ERROR.desc())
                .build();
    }

    public static ResultVO paramErrorWithMsg(String msg) {
        return ResultVO.builder()
                .code(ResultCodeEnum.PARAM_ERROR.value())
                .msg(msg)
                .build();
    }

    public static ResultVO nogLoginError() {
        return ResultVO.builder()
                .code(ResultCodeEnum.NOT_LOGIN.value())
                .msg(ResultCodeEnum.NOT_LOGIN.desc())
                .build();
    }

    public static ResultVO notAllowCancelError() {
        return ResultVO.builder()
            .code(ERROR_RESTRICT_CANCELLATION.getCode())
            .msg(ERROR_RESTRICT_CANCELLATION.getMsg())
            .build();
    }

    /**
     * 成功请求
     *
     * @return
     */
    public static <T> ResultVO success(T data) {
        return ResultVO.builder()
                .code(ResultCodeEnum.SUCCESS.value())
                .msg(ResultCodeEnum.SUCCESS.desc())
                .data(data)
                .build();
    }

    /**
     * 成功请求
     *
     * @return
     */
    public static <T> ResultVO successWithMsg(String msg) {
        return ResultVO.builder()
                .code(ResultCodeEnum.SUCCESS.value())
                .msg(msg)
                .build();
    }
}
