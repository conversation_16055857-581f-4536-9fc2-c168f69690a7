package com.qiyi.boss.webapp.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> kongwenqiang
 * DateTime: 2017/11/13 上午10:35
 * Mail:<EMAIL>   
 * Description: desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutorenewInfoVO {

    private Integer opentype;

    private String opentypename;
    private Integer agreementNo;
    private String agreementNoName;

    public AutorenewInfoVO(Integer opentype, String opentypename) {
        this.opentype = opentype;
        this.opentypename = opentypename;
    }
}
