package com.qiyi.boss.webapp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created at: 2020-10-28
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("用户开通的多个会员类型自动续费信息查询请求参数")
public class RenewInfoMultiVipTypeReqParam {

    @ApiModelProperty(value = "uid，uid和P00001二选一，使用uid方式时需要加签名")
    private Long uid;
    @ApiModelProperty(value = "用户P00001")
    private String P00001;
    @ApiModelProperty(value = "会员类型列表")
    private List<Long> vipTypes;
    @ApiModelProperty(value = "平台")
    private String platform;
    @ApiModelProperty(value = "cb")
    private String cb;

}
