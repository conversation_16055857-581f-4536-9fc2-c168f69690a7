package com.qiyi.boss.service;

import com.iqiyi.vip.order.dal.model.Order;
import com.qiyi.boss.Constants;
import com.qiyi.boss.dto.AccountResponse;
import com.qiyi.boss.message.AccountChangeMsgProcessService;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.model.BaseResponse.CodeEnum;
import com.qiyi.boss.outerinvoke.OrderCoreProxy;
import com.qiyi.boss.processor.DutProcessor;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.service.impl.DutManager;
import com.qiyi.boss.utils.JacksonUtils;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.service.DutRenewSetLogService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 补偿服务
 * 用于从订单角度补偿一些订单没有及时开通自动续费的情况
 * 
 * <AUTHOR>
 * @date 2024/12/28
 */
@Service
@Slf4j
public class CompensateService {

    @Resource
    private UserAgreementService userAgreementService;

    @Resource
    private OrderCoreProxy orderCoreProxy;

    @Resource
    private AccountChangeMsgProcessService accountChangeMsgProcessService;

    @Resource
    private AgreementNoInfoManager agreementNoInfoManager;

    @Resource
    private AutoRenewDutTypeManager autoRenewDutTypeManager;

    @Resource
    private DutProcessor dutProcessor;

    @Resource
    private DutManager dutManager;

    /**
     * 从订单角度补偿自动续费开通
     * 
     * @param orderCode 订单号
     * @param source 来源标识
     * @return 处理结果
     */
    public BaseResponse<String> compensateAutoRenewByOrder(String orderCode, String source) {
        log.info("开始补偿订单自动续费开通, orderCode:{}, source:{}", orderCode, source);
        try {
            // 根据订单号查询订单，如果没有则返回未查到订单
            Order order = orderCoreProxy.getAutoRenewOrderByOrderCode(orderCode);
            if (order == null) {
                log.warn("未查到订单, orderCode:{}", orderCode);
                return BaseResponse.create(BaseResponse.CodeEnum.ERROR_PARAM.getCode(), "未查到订单: " + orderCode);
            }

            // 查询自动续费签约关系列表（忽略状态），如果有其中1个签约关系上订单号=orderCode，返回成功
            String autorenewOrderCode = order.getOrderCode();
            Long userId = order.getUserId();
            Boolean existingAgreement = checkProcessed(order);
            if (BooleanUtils.isTrue(existingAgreement)) {
                log.info("已存在签约关系，无需重复处理, orderCode:{}, userId:{}, dutType:{}", orderCode, userId);
                return BaseResponse.createSuccess("已存在签约关系，无需重复处理");
            }

            // 检查订单是否已退款或已取消，如果是，则返回成功
            if (accountChangeMsgProcessService.isOrderRefundedOrCancelled(order)) {
                log.info("订单已退款或已取消，无需处理, orderCode:{}, status:{}", orderCode, order.getStatus());
                return BaseResponse.createSuccess("订单已退款或已取消，无需处理");
            }

            //  获取AutoRenewDutType
            Integer renewType = order.getRenewType();
            if (renewType == null) {
                log.error("订单renewType为空, orderCode:{}", orderCode);
                return BaseResponse.create(BaseResponse.CodeEnum.ERROR_PARAM.getCode(), "订单dutType为空");
            }
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(renewType);
            if (agreementNoInfo == null) {
                log.error("未找到对应的协议信息, orderCode:{}, renewType:{}", orderCode, renewType);
                return BaseResponse.create(BaseResponse.CodeEnum.ERROR_PARAM.getCode(), "未找到对应的协议信息");
            }

            // 校验账户绑定信息
            boolean accountBindValidation = validateAccountBindInfo(order, agreementNoInfo);
            if (!accountBindValidation) {
                log.error("账户绑定信息校验失败, orderCode:{}, agreementNoInfo:{}", orderCode, agreementNoInfo);
                return BaseResponse.create(CodeEnum.PAY_CENTER_NOT_BIND);
            }

            // 构造AutorenewRequest
            AutorenewRequest autorenewRequest = accountChangeMsgProcessService.convertToAutorenewRequest(order, agreementNoInfo.getVipType(), source);
            if (autorenewRequest == null) {
                log.error("转换AutorenewRequest失败, orderCode:{}", orderCode);
                return BaseResponse.create(BaseResponse.CodeEnum.ERROR_PARAM.getCode(), "转换请求对象失败");
            }

            // 检查订单状态，决定是否延迟处理
            if (accountChangeMsgProcessService.needDelayProcess(order, autorenewRequest)) {
                log.info("订单状态不满足条件，需要延迟处理, orderCode:{}, status:{}", orderCode, order.getStatus());
                return BaseResponse.create(BaseResponse.CodeEnum.ERROR_PARAM.getCode(), "订单状态不满足条件");
            }

            // 调用开通逻辑
            String msgId = "COMPENSATE_" + System.currentTimeMillis();
            
            // 直接调用开通逻辑，跳过延迟处理
            accountChangeMsgProcessService.doSignContractLogic(autorenewRequest, order, msgId);
            log.info("补偿处理成功, orderCode:{}, userId:{}, dutType:{}", orderCode, userId, renewType);
            return BaseResponse.createSuccess("补偿处理成功");
            
        } catch (Exception e) {
            log.error("补偿处理异常, orderCode:{}, source:{}", orderCode, source, e);
            return BaseResponse.create(BaseResponse.CodeEnum.ERROR_SYSTEM.getCode(), "补偿处理异常: " + e.getMessage());
        }
    }

    /**
     * 校验账户绑定信息
     * 根据您提供的代码逻辑进行校验，判断是否能拿到符合条件的结果
     */
    private boolean validateAccountBindInfo(Order order, AgreementNoInfo agreementNoInfo) {
        try {
            Long userId = order.getUserId();
            Integer dutType = agreementNoInfo.getDutType();
            Long vipType = agreementNoInfo.getVipType();
            String orderCode = order.getOrderCode();
            log.info("开始校验账户绑定信息, userId:{}, dutType:{}, orderCode:{}", userId, dutType, orderCode);

            // 根据您提供的代码逻辑进行校验
            String partnerId = agreementNoInfo != null ? agreementNoInfo.getPartnerId() : null;
            String partner = StringUtils.isNotBlank(partnerId) ? partnerId :
                           CloudConfigUtil.getPartnerByVipType(vipType);
            // 查询账户绑定信息
            String partnerOrderNo = StringUtils.length(orderCode) == Constants.DEFAULT_LENGTH_OF_VIP_PLUS_ORDER_CODE ? orderCode.substring(0, Constants.DEFAULT_LENGTH_OF_VIP_ORDER_CODE) : orderCode;

            Optional<AccountResponse> accountResponse = dutProcessor.queryAccountBindInfos(userId, dutType, partner, partnerOrderNo);
            if (!accountResponse.isPresent()) {
                log.warn("未查询到账户绑定信息, userId:{}, dutType:{}, partner:{}, partnerOrderNo:{}", userId, dutType, partner, partnerOrderNo);
                return false;
            }
            boolean matchBind = accountResponse.get().getData().stream().anyMatch(a -> a.getType().equals(dutType));
            log.info("uid has binded uid:{} dutType: {}", userId, dutType);
            return matchBind;
        } catch (Exception e) {
            log.error("校验账户绑定信息异常, orderCode:{}", order.getOrderCode(), e);
            return false;
        }
    }

    /**
     * 检查是否已存在签约关系
     * 通过查询订单信息，然后查询该用户的所有签约关系来判断
     */
    private Boolean checkProcessed(Order order) {
        String orderCode = order.getOrderCode();
        Long userId = order.getUserId();
        Integer renewType = order.getRenewType();
        try {
            log.info("检查是否已存在签约关系, userId:{}, orderCode:{}", userId, orderCode);
            // 查询用户所有的签约关系（忽略状态）
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(renewType);
            Integer dutType = null;
            if (agreementNoInfo != null) {
                dutType = agreementNoInfo.getDutType();
            } else {
                dutType = renewType;
            }
            List<DutRenewSetLog> dutRenewSetLogs = dutManager.shardGetDutRenewSetLogList(userId, Lists.newArrayList(dutType), DutRenewSetLog.RENEW_SET);
            if (CollectionUtils.isEmpty(dutRenewSetLogs)) {
                log.info("检查是否已存在签约关系未找到该用户的签约日志, userId:{}, orderCode:{}", userId, orderCode);
                return false;
            }
            dutRenewSetLogs.stream()
                .filter(s -> StringUtils.isNotBlank(s.getDescription()))
                .map(s -> JacksonUtils.parseMap(s.getDescription()))
                .filter(s -> Objects.nonNull(s))
                .anyMatch(m -> MapUtils.isNotEmpty(m) && ObjectUtils.equals(m.get("orderCode"), orderCode));
        } catch (Exception e) {
            log.error("检查已存在签约关系异常, orderCode:{}", orderCode, e);
            return false;
         }
        return false;
     }
}
