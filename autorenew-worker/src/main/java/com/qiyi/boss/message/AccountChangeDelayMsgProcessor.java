package com.qiyi.boss.message;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Charsets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.CloudRocketMQMessageListener;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import com.qiyi.boss.Constants;
import com.qiyi.boss.async.SimpleThreadPoolExecutor;
import com.qiyi.boss.async.task.BadDebtSendEmailTask;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.dto.CancelAutoRenewOptDto;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.model.AccountChangeMsg;
import com.qiyi.boss.service.AutoRenewService;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.service.impl.DutManager;
import com.qiyi.boss.utils.AppConfig;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.iqiyi.vip.uitls.model.MailHeader;

/**
 * AccountChangeMsg延迟消息处理器
 * 用于处理需要延迟处理的会员订单或打包购订单
 * 
 * <AUTHOR>
 * @date 2024/12/26
 */
@Component
@DependsOn("cloudConfigUtil")
@CloudRocketMQMessageListener(nickname = "accountChangeDelayMsgProcessor", consumeMode = ConsumeMode.ORDERLY)
public class AccountChangeDelayMsgProcessor implements RocketMQListener<MessageExt> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountChangeDelayMsgProcessor.class);

    @Resource
    private AccountChangeMsgProcessService accountChangeMsgProcessService;
    
    @Resource
    private AutoRenewDutTypeManager autoRenewDutTypeManager;
    @Resource
    private DutManager dutManager;
    @Resource
    private UserAgreementService userAgreementService;
    @Resource
    private AutoRenewService autoRenewService;
    
    @Value("${account.change.msg.max.retry.times:5}")
    private int maxRetryTimes;

    @Override
    public void onMessage(MessageExt messageExt) {
        long start = System.currentTimeMillis();
        String msgId = messageExt.getMsgId();
        int reconsumeTimes = messageExt.getReconsumeTimes();
        
        LOGGER.info("收到延迟消息, msgId:{}, 重投递次数:{}", msgId, reconsumeTimes);
        // 检查重投递次数是否超过配置的最大次数
        if (reconsumeTimes >= maxRetryTimes) {
            LOGGER.error("延迟消息重投递次数超过最大限制, msgId:{}, reconsumeTimes:{}, maxRetryTimes:{}", msgId, reconsumeTimes, maxRetryTimes);
            return;
        }
        
        String messageBody = new String(messageExt.getBody(), Charsets.UTF_8);
        LOGGER.info("处理延迟消息, message: {}, msgId:{}", messageBody, msgId);
        try {
            // 解析消息体
            AccountChangeMsg accountChangeMsg = JSON.parseObject(messageBody, AccountChangeMsg.class);
            if (accountChangeMsg == null || accountChangeMsg.getUid() == null) {
                LOGGER.error("延迟消息解析失败或uid为空, content:{}", messageBody);
                return;
            }
            // 获取代扣类型信息
            AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(accountChangeMsg.getType());
            if (autoRenewDutType == null) {
                LOGGER.error("延迟消息处理失败，未找到代扣类型信息, dutType:{}, uid:{}", accountChangeMsg.getType(), accountChangeMsg.getUid());
                return;
            }
                
            if (accountChangeMsg.isAutoRenewMsg() && accountChangeMsg.isUnbindMsg()) {
                dealAccountUnbindMessage(accountChangeMsg, autoRenewDutType);
                return;
            }
            //处理自动续费开通消息
            if (accountChangeMsg.isAutoRenewMsg() && accountChangeMsg.isBindMsg()) {
                accountChangeMsgProcessService.processAccountBindMessage(accountChangeMsg, autoRenewDutType, true, msgId);
                return;
            }
            LOGGER.info("延迟消息处理完成, cost:{}ms, msgId:{}", System.currentTimeMillis() - start, msgId);
        } catch (Exception e) {
            LOGGER.error("延迟消息处理异常， accountChangeMsg:{}", messageBody , e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 处理自动续费解绑消息
     */
    private void dealAccountUnbindMessage(AccountChangeMsg accountChangeMsg, AutoRenewDutType autoRenewDutType) {
        Long uid = accountChangeMsg.getUid();
        Integer type = accountChangeMsg.getType();
        List<Integer> needProcessDutTypes = CloudConfigUtil.getNeedProcessDutTypes(type);
        LOGGER.info("needProcessDutTypes:{}", needProcessDutTypes);
        Integer agreementType = accountChangeMsg.getAgreementType();
        List<DutUserNew> dutUserNews = userAgreementService.getByAgreementTypeAndVipType(uid, AgreementTypeEnum.valueOf(agreementType), autoRenewDutType.getVipType(), null);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            LOGGER.error("unbind not find dutUserNews, accountChangeMsg:{}", accountChangeMsg);
            return;
        }
        OperateSceneEnum operateScene = accountChangeMsg.getAutoRenewOperateScene();
        for (DutUserNew dutUserNew : dutUserNews) {
            if (!needProcessDutTypes.contains(dutUserNew.getType())) {
                continue;
            }
            //台湾自动续费退租减少坏账，发送邮件通知
            if (CloudConfigUtil.badDebtSendEmail() && Objects.equals(Constants.VIP_CATEGORY_TAIWAN, dutUserNew.getVipCategory())) {
                DutRenewLog dutRenewLog = dutManager.getRecentOneDayDutRenewLog(uid, Constants.VIP_CATEGORY_TAIWAN);
                if (dutRenewLog != null) {
                    sendBadDebtEmail(uid, dutRenewLog.getOrderCode(), dutUserNew.getOperateTime(), dutUserNew.getNextDutTime());
                }
            }
            CancelAutoRenewOptDto cancelAutoRenewOptDto = CancelAutoRenewOptDto.buildFromDutUserNew(dutUserNew, operateScene, Constants.UNBIND_FC, true);
            cancelAutoRenewOptDto.setPayChannel(autoRenewDutType.getPayChannel());
            userAgreementService.cancelAutoRenew(dutUserNew, cancelAutoRenewOptDto);
        }
        autoRenewService.resetUserRenewStatusWhenCancel(uid, needProcessDutTypes);
    }


    private void sendBadDebtEmail(Long uid, String orderCode, Timestamp operateTime, Timestamp nextDutTime) {
        MailHeader header = new MailHeader();
        String contact = AppConfig.getProperty("mail.tw.autorenew.baddebt.contact");
        if (StringUtils.isNotBlank(contact)) {
            header.setTos(contact.split(","));
            header.setTitle("台湾自动续费退租减少坏账");

            BadDebtSendEmailTask.BadDebtSendEmailContent content = new BadDebtSendEmailTask.BadDebtSendEmailContent();
            content.setUid(uid);
            content.setOrderCode(orderCode);
            content.setOperateTime(operateTime);
            content.setNextDutTime(nextDutTime);
            content.setRefundTime(DateHelper.getDateTime());

            SimpleThreadPoolExecutor.execute(new BadDebtSendEmailTask(header, content));
        }
    }
}
