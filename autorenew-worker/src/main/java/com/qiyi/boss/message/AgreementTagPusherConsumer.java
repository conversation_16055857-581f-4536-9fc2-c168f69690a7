package com.qiyi.boss.message;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.CloudRocketMQMessageListener;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import com.qiyi.boss.config.AgreementTagSaveSender;
import com.qiyi.boss.constants.TagConstants;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.enums.PeriodUnitEnum;
import com.qiyi.boss.model.AgreementChangedMsg;
import com.qiyi.boss.model.VipCustomTagSaveMsg;
import com.qiyi.boss.model.VipCustomTagSaveMsg.TagItem;
import com.qiyi.boss.service.impl.AgreementTemplateManager;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.JacksonUtils;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;

/**
 * <AUTHOR>
 * @date 2021/8/13 15:04
 */
@Slf4j
@Component
@CloudRocketMQMessageListener(nickname = "agreementTagPusherConsumer", consumeMode = ConsumeMode.CONCURRENTLY)
public class AgreementTagPusherConsumer implements RocketMQListener<MessageExt> {

    private static final String SOURCE = "vip-autorenew";
    private static final String MESSAGE_TITLE = "saveCustomTag";

    @Resource
    AgreementTagSaveSender agreementTagSaveSender;
    @Resource
    AgreementTemplateManager agreementTemplateManager;

    @Override
    public void onMessage(MessageExt messageExt) {
        try {
            handleOneMsg(messageExt);
        } catch (Exception e) {
            log.error("some error happened during process msg, msgId:{}", messageExt.getMsgId(), e);
            throw e;
        }
    }

    private void handleOneMsg(MessageExt messageExt) {
        String msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
        log.info("[AgreementTagPusherConsumer] [msgId:{}] [msgBody:{}]", messageExt.getMsgId(), msgBody);
        AgreementChangedMsg agreementChangedMsg = JacksonUtils.parseObject(msgBody, AgreementChangedMsg.class);
        if (invalidMsg(agreementChangedMsg)) {
            return;
        }
        AgreementTypeEnum agreementType = AgreementTypeEnum.valueOf(agreementChangedMsg.getAgreementType());
        List<TagItem> tagItems = null;
        if (agreementType == AgreementTypeEnum.ZHIMA_GO) {
            tagItems = buildZhiMaGoTags(agreementChangedMsg);
        }
        if (agreementType == AgreementTypeEnum.WECHAT_PAY_SCORE) {
            tagItems = buildWeChatPayScoreTags(agreementChangedMsg);
        }
        if (CollectionUtils.isEmpty(tagItems)) {
            return;
        }
        VipCustomTagSaveMsg tagSaveMsg = VipCustomTagSaveMsg.builder()
            .messageId(SOURCE + "-" + UUID.randomUUID().toString())
            .messageTitle(MESSAGE_TITLE)
            .source(SOURCE)
            .timestamp(DateHelper.getDateTime().getTime())
            .data(tagItems)
            .build();
        agreementTagSaveSender.sendAgreementTag(tagSaveMsg);
    }

    private boolean invalidMsg(AgreementChangedMsg agreementChangedMsg) {
        return agreementChangedMsg == null
            || agreementChangedMsg.getAgreementType() == null
            || agreementChangedMsg.getAgreementCode() == null
            || agreementChangedMsg.getUid() == null
            || agreementChangedMsg.getVipType() == null
            || agreementChangedMsg.getOperateType() == null;
    }

    private List<TagItem> buildZhiMaGoTags(AgreementChangedMsg agreementChangedMsg) {
        String uid = agreementChangedMsg.getUid().toString();
        int vipType = agreementChangedMsg.getVipType().intValue();
        long busiTime = DateHelper.getDateTime().getTime();
        List<TagItem> tagItems = new ArrayList<>();
        OperateTypeEnum operateType = OperateTypeEnum.parseValue(agreementChangedMsg.getOperateType());
        AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementChangedMsg.getAgreementCode());
        if (agreementTemplate == null) {
            log.error("agreement template not exists, msgBody:{}", JacksonUtils.toJsonString(agreementChangedMsg));
            return Collections.emptyList();
        }
        Integer periods = agreementTemplate.getPeriods();
        PeriodUnitEnum periodUnit = PeriodUnitEnum.valueOf(agreementTemplate.getPeriodUnit());
        String periodUnitName = periodUnit.getUnit().toString();
        if (operateType == OperateTypeEnum.CANCEL) {
            int serialRenewCount = agreementChangedMsg.getSerialRenewCount() != null ? agreementChangedMsg.getSerialRenewCount() : 0;
            String isMidwayQuitZhiMaGoTagCode = MessageFormat.format(TagConstants.IS_MIDWAY_QUIT_ZHIMA_GO, periodUnitName, periods);
            String serialRenewCountWhenQuitZhiMaGoTagCode = MessageFormat.format(TagConstants.SERIAL_RENEW_COUNT_WHEN_QUIT_ZHIMA_GO, periodUnitName, periods);
            TagItem isMidwayQuitZhiMaGo = new TagItem(isMidwayQuitZhiMaGoTagCode, "1", uid, busiTime, vipType);
            TagItem serialRenewCountWhenQuitZhiMaGo = new TagItem(serialRenewCountWhenQuitZhiMaGoTagCode, Integer.toString(serialRenewCount), uid, busiTime, vipType);
            tagItems.add(isMidwayQuitZhiMaGo);
            tagItems.add(serialRenewCountWhenQuitZhiMaGo);
        }
        if (operateType == OperateTypeEnum.FINISHED) {
            String isExpireQuitZhiMaGoTagCode = MessageFormat.format(TagConstants.IS_EXPIRE_QUIT_ZHIMA_GO, periodUnitName, periods);
            TagItem isExpireQuitZhiMaGo = new TagItem(isExpireQuitZhiMaGoTagCode, "1", uid, busiTime, vipType);
            tagItems.add(isExpireQuitZhiMaGo);
        }
        return tagItems;
    }

    private List<TagItem> buildWeChatPayScoreTags(AgreementChangedMsg agreementChangedMsg) {
        String uid = agreementChangedMsg.getUid().toString();
        int vipType = agreementChangedMsg.getVipType().intValue();
        String operateType = agreementChangedMsg.getOperateType().toString();
        long busiTime = DateHelper.getDateTime().getTime();
        TagItem userWeChatPayScoreStatus = new TagItem(TagConstants.USER_WECHAT_PAY_SCORE_STATUS, operateType, uid, busiTime, vipType);
        return Collections.singletonList(userWeChatPayScoreStatus);
    }

}
