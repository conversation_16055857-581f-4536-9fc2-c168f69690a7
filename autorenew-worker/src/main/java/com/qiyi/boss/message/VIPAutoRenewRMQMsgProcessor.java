package com.qiyi.boss.message;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.CloudRocketMQMessageListener;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;  
import java.util.List;
import java.util.Map;
import com.qiyi.boss.Constants;
import com.qiyi.boss.async.queue.AsyncTaskFactory;
import com.qiyi.boss.async.task.AbstractTask;
import com.qiyi.boss.async.task.AutoRenewRefundTask;
import com.qiyi.boss.async.task.MonthlyRenewTask_Dut;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.dto.AgreementOptDto;
import com.qiyi.boss.enums.AgreementStatusEnum;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.PasswordFreeSignTypeEnum;
import com.qiyi.boss.enums.PasswordFreeSourceEnum;
import com.qiyi.boss.enums.TypeOfOrderEnum;
import com.qiyi.boss.outerinvoke.CommodityProxy;
import com.qiyi.boss.outerinvoke.result.CommodityInfo;
import com.qiyi.boss.service.AgreementOperateService;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.boss.service.PayTypeService;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.AgreementTemplateManager;
import com.qiyi.boss.service.impl.DutManager;
import com.qiyi.boss.service.impl.PasswordFreeManager;
import com.qiyi.boss.service.impl.PaymentDutTypeManager;
import com.qiyi.boss.service.impl.RefundHandleService;
import com.qiyi.boss.utils.AutoRenewConfig;
import com.qiyi.boss.utils.EagleMeterReporter;
import com.qiyi.boss.utils.JacksonUtils;
import com.qiyi.boss.utils.MessageMQUtils;
import com.qiyi.vip.commons.web.constant.WebConstants;
import com.qiyi.vip.dto.data.PaymentDutTypeDTO;
import com.qiyi.vip.service.PaymentTypeClient;
import com.qiyi.vip.trade.autorenew.utils.AccountChangeMsgTrafficSplitUtil;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.constants.TaskConstants;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.AsyncTask;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.UserPasswordFree;
import com.qiyi.vip.trade.autorenew.domain.UserPasswordFreeLog;
import static com.qiyi.boss.Constants.AMOUNT_OF_COMMON_AUTORENEW;
import static com.qiyi.boss.Constants.SOURCE_ACCOUNT_MESSAGE;
import static com.qiyi.boss.Constants.SOURCE_ORDER_COPILOT;
import static com.qiyi.boss.outerinvoke.result.CommodityInfo.DEDUCTION_MODEL_DIRECT;

@Slf4j
@Component
@CloudRocketMQMessageListener(nickname = "autoRenewRMQMsgProcessor", consumeMode = ConsumeMode.CONCURRENTLY)
public class VIPAutoRenewRMQMsgProcessor implements RocketMQListener<MessageExt> {

    @Resource
    AgreementTemplateManager agreementTemplateManager;
    @Resource
    AgreementNoInfoManager agreementNoInfoManager;
    @Resource
    AgreementOperateService agreementOperateService;
    @Resource
    DutManager dutManager;
    @Resource
    PasswordFreeManager passwordFreeManager;
    @Resource
    PaymentDutTypeManager paymentDutTypeManager;
    @Resource
    AutoRenewConfig autoRenewConfig;
    @Resource
    RefundHandleService refundHandleService;
    @Resource
    UserAgreementService userAgreementService;
    @Resource
    private CommodityProxy commodityProxy;
    @Resource
    private PayTypeService payTypeService;
  

    @Override
    public void onMessage(MessageExt messageExt) {
        String msgId = messageExt.getMsgId();
        log.info("[module:VIPAutoRenewRMQMsgProcessor] [enter] [msgId:{}]", msgId);
        if (MessageMQUtils.isPressureMsg(messageExt)) {
            return;
        }
        try {
            String msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
            Map<String, String> msgContent = JSON.parseObject(msgBody, new TypeReference<Map<String, String>>() {});
            log.info("[module:VIPAutoRenewRMQMsgProcessor] topic:{}, msg data:{}, msgId:{}", messageExt.getTopic(), msgBody, msgId);
            AutorenewRequest autorenewRequest = AutorenewRequest.transferFromMessage(msgContent, msgId);
            handleOneMsg(autorenewRequest, msgId);
            log.info("[module:VIPAutoRenewRMQMsgProcessor] [success] [msgId:{}]", msgId);
        } catch (Exception e) {
            log.error("some error happened during process msg, msgId:{}", msgId, e);
            throw e;
        }
    }

    public void handleOneMsg(AutorenewRequest autorenewRequest, String msgId) {
        String source = autorenewRequest.getSource();
        if (agreementNoInfoManager.pureSignCommonAutoRenew(autorenewRequest)) {
            log.info("pure sign CommonAutoRenew, param:{}, msgId:{}", autorenewRequest, msgId);
            if (SOURCE_ACCOUNT_MESSAGE.equals(source)) {
                log.error("handleOneMsg source error, order:{}", autorenewRequest.getOrderCode());
            }
            return;
        }
        if (newUpgradeMode(autorenewRequest)) {
            log.info("new upgrade mode, orderCode:{}, msgId:{}", autorenewRequest.getOrderCode(), msgId);
            autorenewRequest.setSourceVipType(null);
            autorenewRequest.setNewUpgradeMode(true);
        }
        // 支付成功
        if (ObjectUtils.equals(autorenewRequest.getStatus(), Constants.ORDER_STATUS_DELIVERED)) {
            if (isAliPayPasswordFreeSignPay(autorenewRequest)) {
                addPassWordFreeSignPayLog(autorenewRequest);
                return;
            }
            //非连包订单
            if (!autorenewRequest.isAutoRenewRequest()) {
                return;
            }
            dealVIPAutoRenewMsg(autorenewRequest, msgId);
        } else if (isValidRefundOrder(autorenewRequest)) {
            //芝麻GO退款解约
            if (TypeOfOrderEnum.zhiMaGoRightOrder((autorenewRequest.getType()))) {
                AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(autorenewRequest.getDutType());
                if (agreementNoInfo == null) {
                    log.error("[agreementNoInfo is null.] [Params:{}], msgId:{}", autorenewRequest, msgId);
                    return;
                }
                AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
                if (agreementTemplate == null) {
                    agreementTemplate = agreementTemplateManager.getByCode(autorenewRequest.getTradeNo());
                }
                if (AgreementTypeEnum.ZHIMA_GO.getValue() == agreementTemplate.getType()) {
                    dealZhiMaGoRefundMessage(autorenewRequest, agreementNoInfo.getId());
                }
                return;
            }
            // 退款取消自动续费
            if (autoRenewConfig.isRefundCancelAutorenew() && autoRenewConfig.cancelAutoRenewWhenRefund(autorenewRequest.getUserId())) {
                dealRefundAutoRenewOrder(autorenewRequest, null, msgId); // 从AccountChangeMsgProcessor调用时没有priority信息
            } else {
                log.info("[module:VIPAutoRenewMessageProcessor] [refund_cancel_autorenew switch: false], msgId:{}", msgId);
            }
        }
    }

    private boolean isValidRefundOrder(AutorenewRequest autorenewRequest) {
        return autorenewRequest.getStatus() == Constants.ORDER_STATUS_NEGATIVE_PAID
            && RefundHandleService.isRefundAutoRenewOrder(autorenewRequest.getAutoRenew())
            && RefundHandleService.isRefundProductType(autorenewRequest.getProductType());
    }

    private boolean newUpgradeMode(AutorenewRequest autorenewRequest) {
        String skuId = autorenewRequest.getSkuId();
        if (StringUtils.isBlank(skuId)) {
            return false;
        }
        CommodityInfo commodityInfo = commodityProxy.queryCommodity(skuId);
        if (commodityInfo == null) {
            return false;
        }
        return ObjectUtils.equals(commodityInfo.getDeductionModel(), DEDUCTION_MODEL_DIRECT);
    }

    /**
     * 执行退单取消自动续费的逻辑
     *
     * @param request AutorenewRequest
     * @param messagePriority 优先级
     */
    private void dealRefundAutoRenewOrder(AutorenewRequest request, String messagePriority, String msgId) {
        log.info("执行退单取消自动续费, msgId:{}", msgId);
        request.setAmount(Math.abs(request.getAmount()));
        Long userId = request.getUserId();
        Long vipType = request.getVipType();
        String fc = request.getFc();
        String orderCode = request.getOrderCode();
        try {
            log.info("执行取消自动续费前参数 userId:{} vipType:{}, orderCode:{}, msgId:{}", userId, vipType, orderCode, msgId);
            refundHandleService.handle(userId, vipType, fc, orderCode);
        } catch (Exception e) {
            log.error("退单取消自动续费执行出现异常. userId:{} vipType:{} orderCode:{} msgId:{}, exception:{}", userId, vipType, orderCode, msgId, e.getMessage());
            makeRefundAsyncTask(userId, vipType, messagePriority, fc, orderCode);
        }
    }

    /**
     * 退单重试的任务
     *
     * @param userId 用户id
     * @param vipType 会员类型
     * @param priorityStr 优先级
     */
    private void makeRefundAsyncTask(Long userId, Long vipType, String priorityStr, String fc, String orderCode) {
        AbstractTask task = new AutoRenewRefundTask(userId, vipType, fc, orderCode);
        int priority = StringUtils.isBlank(priorityStr) ? TaskConstants.PRIORITY_NORMAL : Integer.parseInt(priorityStr);
        AsyncTaskFactory.getInstance().insertIntoDB(task, AsyncTask.POOL_TYPE_AUTO_RENEW, priority);
    }

    private void dealVIPAutoRenewMsg(AutorenewRequest autorenewRequest, String msgId) {
        Integer dutType = reMappingDutType(autorenewRequest);
        if (null == dutType) {
            log.error("[dutType is null.] [Params:{}], msgId:{}", autorenewRequest, msgId);
            return;
        }
        try {
            autorenewRequest.setDutType(dutType);
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(dutType);
            if (agreementNoInfo != null) {
                Integer agreementType = agreementNoInfo.getType();
                if (noNeedProcessAgreementType(agreementType)) {
                    log.info("no need process agreementType, agreementType:{}, msgId:{}", agreementType, msgId);
                    return;
                }
                autorenewRequest.setAgreementType(agreementType);
                
                if (!payTypeService.isSupportSign(autorenewRequest.getPayType()) && autorenewRequest.isAutoRenewRequest()) {
                    autorenewRequest.setSignTransferCommon(true);
                }
                // 此处需要判断是否需要处理，因为AccountChangeMsg开关控制，跳过VIPAutoRenewRMQMsgProcessor处理
                if (needProcess(autorenewRequest)) {
                    agreementOperateService.openBySignPay(autorenewRequest, agreementNoInfo);
                } else {
                    log.info("AccountChangeMsg开关控制，跳过VIPAutoRenewRMQMsgProcessor处理, userId:{}, orderCode:{}, msgId:{}", autorenewRequest.getUserId(), autorenewRequest.getOrderCode(), msgId);
                }
            } else {
                dutManager.processMonthlyRenew(autorenewRequest);
                EagleMeterReporter.incrCountOfMQConsumer("VIPAutoRenewRMQMsgProcessor");
            }
        } catch (Exception e) {
            log.error("[module:BossMessage Consumer] [action:dealVIPAutoRenewMsg] msgId:{}", msgId, e);
            /*处理失败后，交给异步任务处理*/
            AbstractTask task = new MonthlyRenewTask_Dut(autorenewRequest);
            AsyncTaskFactory.getInstance().insertIntoDB(task, AsyncTask.POOL_TYPE_AUTO_RENEW, TaskConstants.PRIORITY_NORMAL);
        }
    }

    private boolean noNeedProcessAgreementType(Integer agreementType) {
        return AgreementTypeEnum.jointType(agreementType) || AgreementTypeEnum.familyType(agreementType);
    }

    private void addPassWordFreeSignPayLog(AutorenewRequest autorenewRequest) {
        Integer payType = autorenewRequest.getPayType();
        PaymentDutTypeDTO paymentDutTypeDTO = paymentDutTypeManager.getPasswordFreeDutType(payType);
        UserPasswordFreeLog userPasswordFreeLog = UserPasswordFreeLog.builder()
            .userId(autorenewRequest.getUserId())
            .dutType(paymentDutTypeDTO == null ? null : paymentDutTypeDTO.getDutType())
            .payChannel(paymentDutTypeDTO == null ? null : paymentDutTypeDTO.getPayChannel())
            .operation(UserPasswordFree.PASSWORD_FREE_OPEN)
            .source(PasswordFreeSourceEnum.ORDER_FINISHED_MESSAGE.getValue())
            .signType(PasswordFreeSignTypeEnum.PAY_SIGN.getValue())
            .extInfo(passwordFreeManager.buildPassFreeLogExtraInformation(autorenewRequest.getOrderCode(), null))
            .build();
        passwordFreeManager.saveuserPasswordFreeLog(userPasswordFreeLog);
    }

    /**
     * Sometimes the dut type in Boss message is not correct, so need to re-mapping from payType.
     *
     * @param autorenewRequest AutorenewRequest
     * @return dutType 代扣类型
     */
    private Integer reMappingDutType(AutorenewRequest autorenewRequest) {
        Integer amount = autorenewRequest.getOpeningAmount();

        // 升级自动续费产品
        if (null != autorenewRequest.getSourceVipType() || autorenewRequest.isNewUpgradeMode()) {
            amount = AMOUNT_OF_COMMON_AUTORENEW;
            autorenewRequest.setAmount(AMOUNT_OF_COMMON_AUTORENEW);
        }
        //订单消息中已经有dutType
        if (autorenewRequest.getDutType() != null) {
            return autorenewRequest.getDutType();
        }

        Integer sourceVipType = null == autorenewRequest.getSourceVipType() ? null : autorenewRequest.getSourceVipType().intValue();
        Long userId = autorenewRequest.getUserId();
        String skuId = autorenewRequest.getSkuId();
        boolean newUpgradeMode = newUpgradeMode(autorenewRequest);
        Integer sourceVipTypeParam = newUpgradeMode ? null : sourceVipType;
        int amountParam = newUpgradeMode ? AMOUNT_OF_COMMON_AUTORENEW : amount;
        Long vipType = autorenewRequest.getVipType();
        if (vipType == null) {
            log.error("VipType is null in order msg. orderCode:{}", autorenewRequest.getOrderCode());
            return null;
        }
        Integer dutType = paymentDutTypeManager.getDutTypeWithSourceVipType(autorenewRequest.getPayType(), autorenewRequest.getPayChannel(),
            sourceVipTypeParam, vipType.intValue(), autorenewRequest.getActCode(), amountParam, userId, skuId);
        log.warn("DutType {} is missing in order msg. orderCode:{}", dutType, autorenewRequest.getOrderCode());
        return dutType;
    }



    private Boolean isAliPayPasswordFreeSignPay(AutorenewRequest autorenewRequest) {
        String payTypes = CloudConfigUtil.getVipStorePasswordFreePaySignPayTypes();
        if (StringUtils.isBlank(payTypes)) {
            return false;
        }
        return Splitter.on(WebConstants.COMMON_DELIMITER_COMMA).splitToList(payTypes).contains(String.valueOf(autorenewRequest.getPayType()));
    }

    private void dealZhiMaGoRefundMessage(AutorenewRequest autorenewRequest, Integer agreementNo) {
        AgreementOptDto agreementOptDto = AgreementOptDto.buildFromOrderFinishMsg(autorenewRequest, OperateSceneEnum.CANCEL_REFUND, agreementNo);
        List<DutUserNew> dutUserNewList = userAgreementService.getByAgreementNo(autorenewRequest.getUserId(), agreementNo, AgreementStatusEnum.VALID);
        if (CollectionUtils.isEmpty(dutUserNewList)) {
            log.warn("未查询到用户协议信息，msgContent: {}", JacksonUtils.toJsonString(autorenewRequest));
            return;
        }
        agreementOperateService.batchCancelDutUserNews(agreementNo, agreementOptDto, dutUserNewList);
    }

    private boolean needProcess(AutorenewRequest autorenewRequest) {
        String source = autorenewRequest.getSource();
        if (SOURCE_ACCOUNT_MESSAGE.equals(source) || SOURCE_ORDER_COPILOT.equals(source)) {
            return true;
        }
        Integer dutType = autorenewRequest.getDutType();
        AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(dutType);
        if (agreementNoInfo != null && agreementNoInfo.isAppleChannel()) {
            return true;
        }

        if (BooleanUtils.isTrue(autorenewRequest.getSignTransferCommon())) {
            log.info("signTransferCommon is true, orderCode:{}", autorenewRequest.getOrderCode());
            return true;
        }
        return !AccountChangeMsgTrafficSplitUtil.shouldProcessByAccountChangeMsg(autorenewRequest.getUserId());
    }
}
