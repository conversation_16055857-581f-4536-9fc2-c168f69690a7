package com.qiyi.boss.message;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Charsets;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.CloudRocketMQMessageListener;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import com.qiyi.boss.async.SimpleThreadPoolExecutor;
import com.qiyi.boss.async.task.BadDebtSendEmailTask;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.dto.AgreementOptDto;
import com.qiyi.boss.enums.PasswordFreeSourceEnum;
import com.qiyi.boss.enums.UserSignActionEnum;
import com.qiyi.boss.model.AccountChangeMsg;
import com.qiyi.boss.service.AutoRenewService;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.service.UserPasswordFreeService;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.AgreementTemplateManager;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.service.impl.DutManager;
import com.qiyi.boss.service.impl.PasswordFreeManager;
import com.qiyi.boss.utils.AppConfig;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.UserPasswordFree;
import com.qiyi.vip.trade.autorenew.domain.UserPasswordFreeLog;
import com.iqiyi.vip.uitls.model.MailHeader;

/**
 * Created by IntelliJ IDEA.
 *
 * 支付中心签约消息格式wiki：http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
 *
 * <AUTHOR>
 * <AUTHOR>
 * Date: 15-4-29
 * Time: 下午4:23
 * To change this template use File | Settings | File Templates.
 */
@Component
@DependsOn("cloudConfigUtil")
@CloudRocketMQMessageListener(nickname = "accountMessageProcessor", consumeMode = ConsumeMode.ORDERLY)
public class AccountMessageProcessor implements RocketMQListener<MessageExt> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountMessageProcessor.class);

    @Resource
    DutManager dutManager;
    @Resource
    AutoRenewDutTypeManager autoRenewDutTypeManager;
    @Resource
    AgreementTemplateManager agreementTemplateManager;
    @Resource
    UserAgreementService userAgreementService;
    @Resource
    PasswordFreeManager passwordFreeManager;
    @Resource
    UserPasswordFreeService passwordFreeService;
    @Resource
    AgreementNoInfoManager agreementNoInfoManager;
    @Resource
    AutoRenewService autoRenewService;
    @Resource
    private AccountChangeMsgProcessService accountChangeMsgProcessService;

    @Value("${account.unbind.msg.delay.seconds:7}")
    private int unbindMsgDelaySeconds;

    @Override
    public void onMessage(MessageExt messageExt) {
        long start = System.currentTimeMillis();
        String msgId = messageExt.getMsgId();
        if (messageExt.getReconsumeTimes() >= 3) {
            LOGGER.error("the rmq account message = {} redelivery = {} times!", msgId, messageExt.getReconsumeTimes());
            return;
        }
        String messageBody = new String(messageExt.getBody(), Charsets.UTF_8);

        try {
            LOGGER.info("Consume account rmq message, message: {}, msgId:{}", messageBody, msgId);
            @SuppressWarnings("unchecked")
            Map<String, String> content = JSON.parseObject(messageBody, Map.class);
            if (StringUtils.isEmpty(MapUtils.getString(content, "uid"))) {
                LOGGER.error("account unbind message uid error, content:{}", content);
                return;
            }
            if (!msgNeedHandler(content)) {
                LOGGER.info("account message no need handle, content:{}", content);
                return;
            }
            if (!content.containsKey("type")) {
                LOGGER.error("account unbind message error[type empty], content:{}", content);
                return;
            }
            Integer dutType = Integer.parseInt(MapUtils.getString(content, "type"));
            AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutType);
            if (autoRenewDutType == null) {
                LOGGER.error("account unbind message error[type not exists], content:{}", content);
                return;
            }

            AccountChangeMsg accountChangeMsg = AccountChangeMsg.buildFromMsg(content, autoRenewDutType.getAgreementType());
            //处理自动续费解约消息, 延迟7秒处理
            if (accountChangeMsg.isAutoRenewMsg() && accountChangeMsg.isUnbindMsg()) {
                accountChangeMsgProcessService.sendDelayMessage(accountChangeMsg, unbindMsgDelaySeconds);
                return;
            }

            //处理自动续费开通消息
            if (accountChangeMsg.isAutoRenewMsg() && accountChangeMsg.isBindMsg()) {
                accountChangeMsgProcessService.processAccountBindMessage(accountChangeMsg, autoRenewDutType, false, msgId);
                return;
            }

            //处理芝麻GO和微信支付分签约关系变更消息
            if (accountChangeMsg.isZhiMaGoMsg() || accountChangeMsg.isWeChatPayScoreMsg()) {
                dealMessage(accountChangeMsg);
                return;
            }
            //处理免密签约/解约消息
            if (accountChangeMsg.isPasswordFreeMsg()) {
                dealPasswordFreeMessage(accountChangeMsg, autoRenewDutType);
            }

            LOGGER.info("consume account rmq message, cost:{}ms, msgId:{}", System.currentTimeMillis() - start, msgId);
        } catch (Exception e) {
            LOGGER.error("error consume message, msg:{}", messageBody, e);
            throw new RuntimeException(e);
        }
    }

    private boolean msgNeedHandler(Map<String, String> content) {
        String msgType = String.valueOf(content.get("msgtype"));
        return AccountChangeMsg.ACCOUNT_DUT_BIND.equals(msgType)
            || AccountChangeMsg.ACCOUNT_DUT_UNBINDING.equals(msgType)
            || AccountChangeMsg.ACCOUNT_DUT_UNBIND.equals(msgType);
    }

    /**
     * 处理芝麻go或微信支付分消息
     */
    private void dealMessage(AccountChangeMsg accountChangeMsg) {
        if (accountChangeMsg.isBindMsg()) {
            return;
        }
        Long uid = accountChangeMsg.getUid();
        Integer dutType = accountChangeMsg.getType();
        OperateSceneEnum operateScene = accountChangeMsg.getOperateScene();
        List<Integer> agreementNos = agreementNoInfoManager.getAgreementNoByDutType(dutType);

        List<DutUserNew> dutUserNews = userAgreementService.getEffectiveByUserId(uid);
        for (DutUserNew dutUserNew : dutUserNews) {
            Integer agreementNo = dutUserNew.getAgreementNo();
            if (!agreementNos.contains(agreementNo)) {
                continue;
            }
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
            if (agreementNoInfo == null) {
                LOGGER.error("未查询到协议编号信息, uid:{},agreementNo:{}", uid, agreementNo);
                continue;
            }
            AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
            if (agreementTemplate == null) {
                LOGGER.error("未查询到协议模板信息, uid:{},agreementNo:{},templateCode:{}", uid, agreementNo, agreementNoInfo.getTemplateCode());
                continue;
            }
            AgreementOptDto agreementOptDto = AgreementOptDto.buildFromAccountUnBindMsg(accountChangeMsg, dutUserNew, operateScene, agreementNoInfo.getPayChannel());
            if (accountChangeMsg.isUnbindingMsg()) {
                userAgreementService.updateToSettling(dutUserNew, agreementOptDto);
            }
            if (accountChangeMsg.isUnbindMsg()) {
                userAgreementService.cancelByAccountUnbindMsg(dutUserNew, agreementTemplate, agreementOptDto);
            }
        }
        if (accountChangeMsg.isUnbindMsg()) {
            autoRenewService.resetUserRenewStatusWhenCancel(uid, Collections.singletonList(dutType));
        }
    }

    private void dealPasswordFreeMessage(AccountChangeMsg accountChangeMsg, AutoRenewDutType autoRenewDutType) {
        Long uid = accountChangeMsg.getUid();
        Integer dutType = accountChangeMsg.getType();
        Integer status = mappingSignStatus(accountChangeMsg.getMsgType());
        UserPasswordFree userPasswordFree = UserPasswordFree.builder()
                .userId(uid)
                .dutType(dutType)
                .status(status)
                .payChannel(autoRenewDutType.getPayChannel())
                .build();
        UserPasswordFreeLog userPasswordFreeLog = UserPasswordFreeLog.builder()
                .userId(uid)
                .dutType(dutType)
                .payChannel(autoRenewDutType.getPayChannel())
                .operation(status)
                .source(PasswordFreeSourceEnum.PAY_CENTER_MSG_NOTIFY.getValue())
                .build();
        if (UserSignActionEnum.CLOSED.getValue().equals(status)) {
            passwordFreeManager.updatePasswordFree(userPasswordFree, userPasswordFreeLog);
            return;
        }
        passwordFreeService.saveOrUpdate(userPasswordFree);
    }

    private Integer mappingSignStatus(String msgType) {
        switch (msgType) {
            case AccountChangeMsg.ACCOUNT_DUT_BIND:
                return UserSignActionEnum.OPEN.getValue();
            case AccountChangeMsg.ACCOUNT_DUT_UNBIND:
                return UserSignActionEnum.CLOSED.getValue();
            default:
                throw new IllegalArgumentException();
        }
    }
}
