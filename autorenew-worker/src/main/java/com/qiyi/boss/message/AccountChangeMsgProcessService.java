package com.qiyi.boss.message;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import java.util.Map;
import javax.annotation.Resource;
import com.qiyi.boss.Constants;
import com.qiyi.boss.model.AccountChangeMsg;
import com.qiyi.boss.outerinvoke.CommodityProxy;
import com.qiyi.boss.outerinvoke.OrderCoreProxy;
import com.qiyi.boss.outerinvoke.result.CommodityInfo;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.boss.service.PayTypeService;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.GatewayManager;
import com.qiyi.boss.service.impl.QiYuePlatformManager;
import com.qiyi.boss.utils.GsonUtils;
import com.qiyi.vip.trade.autorenew.utils.AccountChangeMsgTrafficSplitUtil;
import com.qiyi.vip.trade.autorenew.CoreCloudRocketMQTemplate;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.qiyue.domain.Gateway;
import com.iqiyi.vip.order.dal.model.Order;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.springframework.messaging.support.MessageBuilder;
import static com.qiyi.boss.Constants.SOURCE_ACCOUNT_MESSAGE;

/**
 * AccountChangeMsg处理服务
 * 提取AccountMessageProcessor和AccountChangeMsgDelayProcessor的公共逻辑
 * 
 * <AUTHOR>
 * @date 2024/12/26
 */
@Component
public class AccountChangeMsgProcessService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountChangeMsgProcessService.class);

    @Resource
    private OrderCoreProxy orderCoreProxy;
    @Resource
    private CoreCloudRocketMQTemplate coreCloudRocketMQTemplate;
    @Resource
    private AgreementNoInfoManager agreementNoInfoManager;
    @Resource
    private CommodityProxy commodityProxy;
    @Resource
    private GatewayManager gatewayManager;
    @Resource
    private QiYuePlatformManager qiYuePlatformManager;
    @Resource
    private VIPAutoRenewRMQMsgProcessor vipAutoRenewRMQMsgProcessor;
    @Resource
    private PaidSignOrderMsgProcessor paidSignOrderMsgProcessor;
    @Resource
    private PayTypeService payTypeService;
    
    @Value("${account.change.msg.delay.seconds:5}")
    private int accountBindMsgDelaySeconds;
    
    private static final String DELAY_TOPIC_NICKNAME = "accountChangeDelayMsgProducer";
    private static final long SEND_TIME_OUT = 3000;

    /**
     * 处理AccountChangeMsg签约开通逻辑
     * @param accountChangeMsg 账户变更消息
     * @param autoRenewDutType 自动续费代扣类型
     * @param isDelay 是否为延迟处理
     */
    public void processAccountBindMessage(AccountChangeMsg accountChangeMsg, AutoRenewDutType autoRenewDutType, boolean isDelay, String msgId) throws Exception {
        String orderCode = accountChangeMsg.getPartnerOrderNo();
        Long uid = accountChangeMsg.getUid();
        LOGGER.info("支付中心自动续费绑定消息, accountChangeMsg:{}, isDelay:{}", accountChangeMsg, isDelay);

        if (autoRenewDutType.isAppleDutType()) {
            LOGGER.info("支付中心自动续费绑定消息苹果渠道暂不处理, uid:{}, orderCode:{}", uid, orderCode);
            return;
        }
        
        // 1. 检查签约开关和uid分流比例
        if (!AccountChangeMsgTrafficSplitUtil.shouldProcessByAccountChangeMsg(uid)) {
            LOGGER.info("跳过处理 uid:{}, orderCode:{}， type:{}", uid, orderCode, accountChangeMsg.getType());
            return;
        }
        
        // 2. 根据订单号查询签约订单
        Order autorenewOrder = orderCoreProxy.getAutoRenewOrderByOrderCode(orderCode);
        if (autorenewOrder == null) {
            LOGGER.error("未查询到签约单订单信息, uid:{},orderCode:{}", uid, orderCode);
            return;
        }
        
        // 3. 检查订单是否已退款或已取消
        if (isOrderRefundedOrCancelled(autorenewOrder)) {
            LOGGER.error("订单已退款或已取消，不处理, uid:{}, orderCode:{}, status:{}", uid, orderCode, autorenewOrder.getStatus());
            return;
        }

        AutorenewRequest autorenewRequest = convertToAutorenewRequest(autorenewOrder, autoRenewDutType.getVipType(), SOURCE_ACCOUNT_MESSAGE);
        if (autorenewRequest == null) {
            LOGGER.error("转换AutorenewRequest失败, uid:{}, orderCode:{}", accountChangeMsg.getUid(), accountChangeMsg.getPartnerOrderNo());
            return;
        }
        
        // 5. 检查订单状态，决定是否延迟处理
        if (needDelayProcess(autorenewOrder, autorenewRequest)) {
            sendDelayMessage(accountChangeMsg, accountBindMsgDelaySeconds);
            return;
        }
        
        // 6. 订单状态满足条件，转换并调用原有开通逻辑
        doSignContractLogic(autorenewRequest, autorenewOrder, msgId);
    }

    /**
     * 检查订单是否已退款或已取消
     */
    public boolean isOrderRefundedOrCancelled(Order order) {
        if (order == null || order.getStatus() == null) {
            return false;
        }
        return order.getStatus() == Constants.ORDER_STATUS_NEGATIVE_PAID || order.getStatus() == Constants.ORDER_STATUS_CANCELLED;
    }

    /**
     * 检查是否需要延迟处理
     * 根据订单分类和处理的流程逻辑：
     * 
     * 1. 纯签约订单处理逻辑：
     *    - 已支付 调用PaidSignOrderMsgProcessor（不延迟）
     *    - 未支付：延迟发送到下一个处理队列
     * 
     * 2. 非纯签约订单处理逻辑：
     *    a. 打包订单：
     *       - 已支付 调用PaidSignOrderMsgProcessor（不延迟）
     *       - 未支付：延迟发送到下一个处理队列
     *    
     *    b. 非打包订单：
     *       - 已支付且已履约：调用VIPAutoRenewRMQMsgProcessor（不延迟）
     *       - 未满足条件：延迟发送到下一个处理队列
     */
    public boolean needDelayProcess(Order order, AutorenewRequest autorenewRequest) {
        if (order == null || autorenewRequest == null) {
            return false;
        }
        // 1. 使用pureSignCommonAutoRenew判断是否是纯签约订单
        boolean isPureSign = agreementNoInfoManager.pureSignCommonAutoRenew(autorenewRequest);
        if (isPureSign) {
            // 纯签约订单处理逻辑
            return !canProcessPureSignOrder(order);
        } else {
            // 非纯签约订单处理逻辑
            return !canProcessNonPureSignOrder(order);
        }
    }
    
    private boolean canProcessPureSignOrder(Order order) {
        return isOrderPaid(order);
    }
    
    /**
     * 处理非纯签约订单的延迟逻辑（单一职责原则）
     */
    private boolean canProcessNonPureSignOrder(Order order) {
        Integer orderType = order.getType();
        
        if (isPackageOrder(orderType)) {
            // a. 打包订单处理逻辑
            return canProcessPackageOrder(order);
        } else {
            // b. 非打包订单处理逻辑
            return canProcessNonPackageOrder(order);
        }
    }
    
    /**
     * 处理打包订单的延迟逻辑（单一职责原则）
     */
    private boolean canProcessPackageOrder(Order order) {
        return isOrderPaid(order);
    }
    
    /**
     * 处理非打包gou订单的延迟逻辑（单一职责原则）
     */
    private boolean canProcessNonPackageOrder(Order order) {
        return isOrderPaidAndFulfilled(order);
    }
    
    /**
     * 判断订单是否已支付（开闭原则：易于扩展支付状态判断逻辑）
     */
    private boolean isOrderPaid(Order order) {
        return order.getStatus() != null && order.getStatus().equals(Constants.ORDER_STATUS_DELIVERED);
    }
    
    /**
     * 判断订单是否已支付且已履约（开闭原则：易于扩展履约状态判断逻辑）
     */
    private boolean isOrderPaidAndFulfilled(Order order) {
        return isOrderPaid(order) && isOrderFulfilled(order);
    }
    
    /**
     * 判断订单是否已履约（依赖倒置原则：抽象履约状态判断）
     */
    private boolean isOrderFulfilled(Order order) {
        return isOrderPaid(order) && "A00000".equals(order.getNotifyResult());
    }
    
    
    /**
     * 判断是否是打包订单（里氏替换原则：可以被子类重写）
     */
    private boolean isPackageOrder(Integer orderType) {
        return Constants.PACKAGE_ORDER_TYPE.equals(orderType);
    }

    /**
     * 发送延迟消息
     */
    public void sendDelayMessage(AccountChangeMsg accountChangeMsg, int delaySeconds) {
        try {
            Message<AccountChangeMsg> message = MessageBuilder.withPayload(accountChangeMsg)
                .setHeader(RocketMQHeaders.KEYS, String.valueOf(accountChangeMsg.getUid()))
                .setHeader(RocketMQHeaders.TAGS, Constants.RMQ_NOMAL_TAG)
                .build();
            SendResult sendResult = coreCloudRocketMQTemplate.syncSendInDelaySeconds(DELAY_TOPIC_NICKNAME, message, SEND_TIME_OUT, delaySeconds);
            LOGGER.info("发送延迟消息成功, msg:{}, msgId:{}, delaySeconds:{}", accountChangeMsg, sendResult.getMsgId(), delaySeconds);
        } catch (Exception e) {
            LOGGER.error("发送延迟消息失败, msg:{}", accountChangeMsg, e);
            throw e;
        }
    }


    /**
     * 根据订单类型调用对应的开通逻辑
     */
    public void doSignContractLogic(AutorenewRequest autorenewRequest, Order order, String msgId) throws Exception {
        // 根据订单分类判断应该调用哪个处理器
        Long userId = autorenewRequest.getUserId();
        String orderCode = autorenewRequest.getOrderCode();
        if (shouldUseVIPAutoRenewProcessor(order, autorenewRequest)) {
            // 调用VIPAutoRenewRMQMsgProcessor的处理逻辑
            vipAutoRenewRMQMsgProcessor.handleOneMsg(autorenewRequest, msgId);
            LOGGER.info("调用VIPAutoRenewRMQMsgProcessor处理完成, uid:{}, orderCode:{}", userId, orderCode);
        } else {
            // 调用PaidSignOrderMsgProcessor的处理逻辑
            paidSignOrderMsgProcessor.handleOneMsg(autorenewRequest, msgId);
            LOGGER.info("调用PaidSignOrderMsgProcessor处理完成, uid:{}, orderCode:{}", userId, orderCode);
        }
    }

    /**
     * 根据图片逻辑判断是否应该使用VIPAutoRenewProcessor
     * @param order 订单信息
     * @param autorenewRequest 自动续费请求
     * @return true表示使用VIPAutoRenewProcessor，false表示使用PaidSignOrderMsgProcessor
     */
    private boolean shouldUseVIPAutoRenewProcessor(Order order, AutorenewRequest autorenewRequest) {
        // 1. 先判断是否是纯签约，使用pureSignCommonAutoRenew方法
        boolean isPureSign = agreementNoInfoManager.pureSignCommonAutoRenew(autorenewRequest);
        if (isPureSign) {
            // 纯签约场景走PaidSignOrderMsgProcessor
            return false;
        }
        
        // 2. 根据订单类型决定处理器
        Integer orderType = order.getType();
        
        if (Constants.FORMAL_ORDER_TYPE.equals(orderType)) {
            // 普通订单 - 非纯签约场景走PaidSignOrderMsgProcessor  
            return true;
        } else if (Constants.PACKAGE_ORDER_TYPE.equals(orderType)) {
            // 打包购订单走PaidSignOrderMsgProcessor
            return false;
        }
        // 非打包购订单（普通、组合支付、加价购等）走VIPAutoRenewRMQMsgProcessor
        return true;
    }

    /**
     * 转换为AutorenewRequest
     */
    public AutorenewRequest convertToAutorenewRequest(Order order, Long vipType, String source) {
        try {
            AutorenewRequest request = new AutorenewRequest();
            
            // 按照resetRequest方法中的设置顺序来设置属性
            
            // 1. userId (uid或userId)
            request.setUserId(order.getUserId());
            
            // 2. autoRenew
            Integer autoRenew = order.getAutoRenew();
            request.setAutoRenew(autoRenew != null ? String.valueOf(autoRenew) : null);
            
            // 3. payType
            request.setPayType(order.getPayType());
            
            // 4. productType
            request.setProductType(order.getProductType());
            
            // 5. vipType (productSubType - 从商品信息或autoRenewDutType获取)
            setVipTypeFromCommodity(request, order, vipType);
            
            // 6. amount
            request.setAmount(order.getAmount());
            
            // 7. orderCode
            request.setOrderCode(order.getOrderCode());
            
            // 8. platformCode (根据platform获取)
            setPlatformCodeFromPlatform(request, order);
            
            // 10. startTime
            request.setStartTime(order.getStartTime());
            
            // 11. deadline (endTime)
            request.setDeadline(order.getDeadline());
            
            // 12. actCode
            request.setActCode(order.getActCode());
            
            // 13. status
            request.setStatus(order.getStatus());
            
            // 14. expireTime
            request.setExpireTime(order.getExpireTime());
            
            // 15. orderFee
            request.setOrderFee(order.getFee()); // 使用Order的fee字段
            
            // 16. payTime
            request.setPayTime(order.getPayTime());
            
            // 17. pid (从商品信息获取)
            setPidFromCommodity(request, order);
            
            // 18. fc (根据gateway获取)
            setFcFromGateway(request, order);
            
            // 19. fv
            request.setFv(order.getFv());
            
            // 21. currencyUnit
            request.setCurrencyUnit(order.getCurrencyUnit());
            
            // 22. timeZone
            setTimeZone(request, order);
            
            // 23. payChannel
            setPayChannelFromPayType(request, order);
            
            // 24. partner
            request.setPartner(order.getPartner());
            
            // 25. thirdUid
            setThirdUid(request, order);
            
            // 26. tradeNo
            request.setTradeNo(order.getTradeNo());
            
            // 27. dutType (renewType)
            request.setDutType(order.getRenewType());
            
            // 28. type (orderType)
            request.setType(order.getType());
            
            // 29. skuId
            request.setSkuId(order.getSkuId());
            
            // 30. dutAmount
            setDutAmount(request, order);
            
            // 31. realFee
            setRealFee(request, order);
            
            // 32. bundleID (对于苹果订单)
            setBundleID(request, order);

            request.setSource(source);
            return request;
        } catch (Exception e) {
            LOGGER.error("转换AutorenewRequest异常", e);
            return null;
        }
    }

    /**
     * 从商品信息设置PID
     */
    private void setPidFromCommodity(AutorenewRequest request, Order order) {
        String skuId = order.getSkuId();
        if (StringUtils.isNotBlank(skuId)) {
            try {
                CommodityInfo commodityInfo = commodityProxy.queryCommodity(skuId);
                if (commodityInfo != null && commodityInfo.getProductId() != null) {
                    request.setPid(String.valueOf(commodityInfo.getProductId()));
                    LOGGER.debug("从商品信息设置PID, skuId:{}, productId:{}", skuId, commodityInfo.getProductId());
                }
            } catch (Exception e) {
                LOGGER.error("查询商品信息异常, skuId:{}", skuId, e);
            }
        }
    }

    /**
     * 从商品信息设置会员类型
     */
    private void setVipTypeFromCommodity(AutorenewRequest request, Order order, Long vipType) {
        String skuId = order.getSkuId();
        if (StringUtils.isNotBlank(skuId)) {
            try {
                CommodityInfo commodityInfo = commodityProxy.queryCommodity(skuId);
                if (commodityInfo != null) {
                    request.setVipType(commodityInfo.getSubType());
                    request.setSourceVipType(commodityInfo.getSourceSubType());
                    LOGGER.debug("从商品信息查询会员类型, skuId:{}", skuId);
                }
            } catch (Exception e) {
                LOGGER.error("查询商品信息异常, skuId:{}", skuId, e);
            }
        }
        
        // 如果从商品信息获取不到，从autoRenewDutType获取
        if (request.getVipType() == null && vipType != null) {
            request.setVipType(vipType);
            request.setSourceVipType(vipType);
        }
    }

    /**
     * 根据支付类型设置支付渠道
     */
    private void setPayChannelFromPayType(AutorenewRequest request, Order order) {
        Integer payType = order.getPayType();
        if (payType != null) {
            Integer payChannel = payTypeService.getPayChannelByPayType(payType);
            if (payChannel != null) {
                request.setPayChannel(payChannel);
                LOGGER.debug("根据支付类型设置支付渠道, payType:{}, payChannel:{}", payType, payChannel);
            }
        }
    }

    /**
     * 设置时区
     */
    private void setTimeZone(AutorenewRequest request, Order order) {
        // TODO: Order类中可能没有getTimeZone方法，需要确认实际字段名称
        // request.setTimeZone(order.getTimeZone());
        LOGGER.debug("设置时区, orderCode:{}", order.getOrderCode());
    }

    /**
     * 设置第三方用户ID
     */
    private void setThirdUid(AutorenewRequest request, Order order) {
        request.setThirdUid(order.getAccountId());
    }

    /**
     * 设置代扣金额
     */
    private void setDutAmount(AutorenewRequest request, Order order) {
        //request.setDutAmount(order.getDutAmount());
    }

    /**
     * 设置实际费用
     */
    private void setRealFee(AutorenewRequest request, Order order) {
        request.setRealFee(order.getRealFee());
    }

    /**
     * 设置Bundle ID (对于苹果订单)
     */
    private void setBundleID(AutorenewRequest request, Order order) {
        if (request.isAppleOrder()) {
            String refer = order.getRefer();
            @SuppressWarnings("unchecked")
            Map<String, Object> referMap = GsonUtils.parseObject(refer, Map.class);
            request.setBundleID(MapUtils.getString(referMap, Constants.ORDER_REFER_KEY_BUNDLE_ID));
        }
    }

    /**
     * 根据gateway设置fc
     */
    private void setFcFromGateway(AutorenewRequest request, Order order) {
        Long gatewayId = order.getGateway();
        if (gatewayId != null) {
            try {
                Gateway gateway = gatewayManager.getGatewayById(gatewayId);
                if (gateway != null && StringUtils.isNotBlank(gateway.getCode())) {
                    request.setFc(gateway.getCode());
                    LOGGER.info("根据gateway设置fc, gatewayId:{}, code:{}", gatewayId, gateway.getCode());
                } else {
                    LOGGER.warn("未找到gateway信息, gatewayId:{}", gatewayId);
                }
            } catch (Exception e) {
                LOGGER.error("查询gateway信息异常, gatewayId:{}", gatewayId, e);
            }
        }
    }

    /**
     * 根据platform设置平台代码
     */
    private void setPlatformCodeFromPlatform(AutorenewRequest request, Order order) {
        Long platformId = order.getPlatform();
        if (platformId != null) {
            try {
                String platformCode = qiYuePlatformManager.getPlatformCodeById(platformId);
                if (StringUtils.isNotBlank(platformCode)) {
                    request.setPlatformCode(platformCode);
                    LOGGER.info("根据platform设置平台代码, platformId:{}, code:{}", platformId, platformCode);
                } else {
                    LOGGER.warn("未找到platform代码, platformId:{}", platformId);
                }
                // 同时设置platformId
                request.setPlatformId(platformId);
            } catch (Exception e) {
                LOGGER.error("查询platform信息异常, platformId:{}", platformId, e);
            }
        }
    }
}
