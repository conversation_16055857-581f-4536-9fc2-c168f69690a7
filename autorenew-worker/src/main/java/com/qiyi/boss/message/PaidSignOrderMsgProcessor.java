package com.qiyi.boss.message;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.CloudRocketMQMessageListener;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.Map;
import com.qiyi.boss.Constants;
import com.qiyi.boss.async.queue.AsyncTaskFactory;
import com.qiyi.boss.async.task.AbstractTask;
import com.qiyi.boss.async.task.AutoRenewConfirmTask;
import com.qiyi.boss.async.task.AutoRenewRefundTask;
import com.qiyi.boss.async.task.MonthlyRenewTask_Dut;
import com.qiyi.boss.component.AgreementHandler;
import com.qiyi.boss.component.AgreementHandlerFactory;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.outerinvoke.CommodityProxy;
import com.qiyi.boss.outerinvoke.result.CommodityInfo;
import com.qiyi.boss.service.AgreementOperateService;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.boss.service.PayTypeService;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.AgreementTemplateManager;
import com.qiyi.boss.service.impl.RefundHandleService;
import com.qiyi.boss.utils.AutoRenewConfig;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.EagleMeterReporter;
import com.qiyi.boss.utils.MessageMQUtils;
import com.qiyi.vip.trade.autorenew.utils.AccountChangeMsgTrafficSplitUtil;
import com.qiyi.vip.trade.autorenew.constants.TaskConstants;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.AsyncTask;
import static com.qiyi.boss.Constants.SOURCE_ACCOUNT_MESSAGE;
import static com.qiyi.boss.Constants.SOURCE_ORDER_COPILOT;
import static com.qiyi.boss.Constants.SOURCE_ORDER_MESSAGE;
import static com.qiyi.boss.constants.AgreementConstants.EXCLUDE_AGREEMENT_TYPES;
import static com.qiyi.boss.outerinvoke.result.CommodityInfo.DEDUCTION_MODEL_DIRECT;

/**
 * <AUTHOR>
 * @className PaidSignOrderMsgProcessor
 * @description
 * @date 2024/8/25
 **/
@Component
@Slf4j
@CloudRocketMQMessageListener(nickname = "paidSignOrderMsgProcessor", consumeMode = ConsumeMode.CONCURRENTLY)
public class PaidSignOrderMsgProcessor implements RocketMQListener<MessageExt> {

    @Resource
    private AgreementNoInfoManager agreementNoInfoManager;
    @Resource
    private AgreementOperateService agreementOperateService;
    @Resource
    private AgreementHandlerFactory agreementHandlerFactory;

    @Resource
    private AgreementTemplateManager agreementTemplateManager;
    @Resource
    private AutoRenewConfig autoRenewConfig;
    @Resource
    private CommodityProxy commodityProxy;
    @Resource
    private PayTypeService payTypeService;

    /**
     * 子类实现具体的消息处理逻辑
     */
    @Override
    public void onMessage(MessageExt messageExt) {
        String msgId = messageExt.getMsgId();
        log.info("[module:PaidSignOrderMsgProcessor] [enter] [msgId:{}]", msgId);
        
        if (MessageMQUtils.isPressureMsg(messageExt)) {
            return;
        }
        try {
            // 消息解析逻辑移到这里
            String msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
            log.info("[module:PaidSignOrderMsgProcessor] [enter] [msgId:{}] [msgBody:{}]", msgId, msgBody);
            Map<String, String> msgContent = JSON.parseObject(msgBody, new TypeReference<Map<String, String>>() {
            });
            AutorenewRequest autorenewRequest = AutorenewRequest.transferFromMessage(msgContent, msgId);
            autorenewRequest.setSource(SOURCE_ORDER_MESSAGE);
            handleOneMsg(autorenewRequest, msgId);
            log.info("[module:PaidSignOrderMsgProcessor] [success] [msgId:{}]", msgId);
        } catch (Exception e) {
            log.error("some error happened during process msg, msgId:{}", msgId, e);
            throw e;
        }
    }

    public void handleOneMsg(AutorenewRequest autorenewRequest, String msgId) {
        if (!validMsg(autorenewRequest)) {
            log.info("paidSignOrder not valid Msg, autorenewRequest:{}", autorenewRequest);
            return;
        }
        dealVIPAutoRenewMsg(autorenewRequest, msgId);
    }

    private void dealVIPAutoRenewMsg(AutorenewRequest request, String msgId) {
        String orderCode = request.getOrderCode();
        try {
            Integer dutType = request.getDutType();
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(dutType);
            if (agreementNoInfo == null) {
                log.info("agreementNoInfo is null msgId:{}", msgId);
                return;
            }
            Integer agreementType = agreementNoInfo.getType();
            if (request.getVipType() == null) {
                request.setVipType(agreementNoInfo.getVipType());
            }
            request.setAgreementType(agreementType);

            if (noNeedProcessAgreementType(request)) {
                log.info("noNeedProcessAgreementType msgId:{}", msgId);
                return;
            }
            if (newUpgradeMode(request.getSkuId())) {
                log.info("new upgrade mode, orderCode:{}, msgId:{}", request.getOrderCode(), msgId);
                request.setSourceVipType(null);
                request.setNewUpgradeMode(true);
            }
            EagleMeterReporter.incrCountOfMQConsumer("PaidSignOrderMsgProcessor");
            if (request.getStatus() == Constants.ORDER_STATUS_DELIVERED) {
                if (request.isAutoRenewRequest()) {
                    if (!payTypeService.isSupportSign(request.getPayType()) && request.isAutoRenewRequest()) {
                        request.setSignTransferCommon(true);
                    }
                    if (needProcess(request)) {
                        agreementOperateService.openBySignPay(request, agreementNoInfo);
                    } else {
                        log.info("AccountChangeMsg开关控制，跳过VIPAutoRenewRMQMsgProcessor处理, userId:{}, orderCode:{}, msgId:{}", request.getUserId(), request.getOrderCode(), msgId);
                    }
                } else if (request.isDutRequest()) {
                    handleDutOrder(request, agreementType, agreementNoInfo);
                }
            } else if (isValidRefundOrder(request) && autoRenewConfig.cancelAutoRenewWhenRefund(request.getUserId())) {
                makeRefundAsyncTask(request.getUserId(), request.getVipType(), null, request.getFc(), orderCode, Timestamp.from(Instant.now().plusSeconds(10)));
                log.info("delay process refundOrderMessage, orderCode:{}, msgId:{}", orderCode, msgId);
            }

        } catch (Exception e) {
            log.error("[PaidSignOrderMsgProcessor error] msgId:{}", msgId, e);
            /*处理失败后，交给异步任务处理*/
            Timestamp exectime = DateHelper.caculateTime(DateHelper.getDateTime(), 1, Constants.PRODUCT_PERIODUNIT_MINUTE);
            if (request.getStatus() == Constants.ORDER_STATUS_DELIVERED) {
                if (request.isAutoRenewRequest()) {
                    AbstractTask task = new MonthlyRenewTask_Dut(request);
                    AsyncTaskFactory.getInstance().insertIntoDB(task, AsyncTask.POOL_TYPE_AUTO_RENEW, TaskConstants.PRIORITY_NORMAL, exectime);
                } else if (request.isDutRequest()) {
                    AutoRenewConfirmTask task = new AutoRenewConfirmTask(request);
                    AsyncTaskFactory.getInstance().insertIntoDB(task, AsyncTask.POOL_TYPE_AUTO_RENEW_CONFIRM, AsyncTask.Task_PRIORITY_1, exectime);
                }
            }
            if (request.getStatus() == Constants.ORDER_STATUS_NEGATIVE_PAID && autoRenewConfig.cancelAutoRenewWhenRefund(request.getUserId())) {
                makeRefundAsyncTask(request.getUserId(), request.getVipType(), null, request.getFc(), orderCode, Timestamp.from(Instant.now().plusSeconds(10)));
                log.info("retry delay process refundOrderMessage, orderCode:{}, msgId:{}", orderCode, msgId);
            }
        }
    }

    private void makeRefundAsyncTask(Long userId, Long vipType, String priorityStr, String fc, String orderCode, Timestamp timerRunAt) {
        AbstractTask task = new AutoRenewRefundTask(userId, vipType, fc, orderCode);
        int priority = StringUtils.isBlank(priorityStr) ? TaskConstants.PRIORITY_NORMAL : Integer.parseInt(priorityStr);
        AsyncTaskFactory.getInstance().insertIntoDB(task, AsyncTask.POOL_TYPE_AUTO_RENEW, priority, timerRunAt);
    }


    private boolean isValidRefundOrder(AutorenewRequest autorenewRequest) {
        return autorenewRequest.getStatus() == Constants.ORDER_STATUS_NEGATIVE_PAID
            && RefundHandleService.isRefundAutoRenewOrder(autorenewRequest.getAutoRenew());
    }

    private void handleDutOrder(AutorenewRequest autorenewRequest, Integer agreementType, AgreementNoInfo agreementNoInfo) {
        AgreementTypeEnum agreementTypeEnum = AgreementTypeEnum.valueOf(agreementType);
        AgreementHandler agreementHandler = agreementHandlerFactory.getHandler(agreementTypeEnum);
        AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
        agreementHandler.doSomethingAfterDut(autorenewRequest, agreementTemplate);
    }

    public boolean noNeedProcessAgreementType(AutorenewRequest autorenewRequest) {
        Integer agreementType = autorenewRequest.getAgreementType();
        if (agreementType == null) {
            return true;
        }
        return EXCLUDE_AGREEMENT_TYPES.contains(agreementType) || (AgreementTypeEnum.commonAutoRenew(agreementType) && !agreementNoInfoManager.pureSignCommonAutoRenew(autorenewRequest));
    }

    private boolean validMsg(AutorenewRequest autorenewRequest) {
        if (StringUtils.isBlank(autorenewRequest.getOrderCode())
            || autorenewRequest.getProductType() == null
            || autorenewRequest.getPayType() == null
            || autorenewRequest.getDutType() == null
            || (autorenewRequest.getStatus() != Constants.ORDER_STATUS_DELIVERED
            && autorenewRequest.getStatus() != Constants.ORDER_STATUS_NEGATIVE_PAID)) {
            return false;
        }
        return true;
    }

    private boolean newUpgradeMode(String skuId) {
        if (StringUtils.isBlank(skuId)) {
            return false;
        }
        CommodityInfo commodityInfo = commodityProxy.queryCommodity(skuId);
        if (commodityInfo == null) {
            return false;
        }
        return ObjectUtils.equals(commodityInfo.getDeductionModel(), DEDUCTION_MODEL_DIRECT);
    }
    private boolean needProcess(AutorenewRequest autorenewRequest) {
        String source = autorenewRequest.getSource();
        if (SOURCE_ACCOUNT_MESSAGE.equals(source) || SOURCE_ORDER_COPILOT.equals(source)) {
            return true;
        }
        Integer dutType = autorenewRequest.getDutType();
        AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(dutType);
        if (agreementNoInfo != null && agreementNoInfo.isAppleChannel()) {
            return true;
        }

        if (BooleanUtils.isTrue(autorenewRequest.getSignTransferCommon())) {
            log.info("signTransferCommon is true, orderCode:{}", autorenewRequest.getOrderCode());
            return true;
        }
        return !AccountChangeMsgTrafficSplitUtil.shouldProcessByAccountChangeMsg(autorenewRequest.getUserId());
    }
}
