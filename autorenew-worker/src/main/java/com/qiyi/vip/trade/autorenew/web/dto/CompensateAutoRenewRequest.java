package com.qiyi.vip.trade.autorenew.web.dto;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 补偿订单自动续费开通请求参数
 * 
 * <AUTHOR>
 * @date 2024/12/28
 */
@Data
public class CompensateAutoRenewRequest {

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderCode;

    /**
     * 来源标识
     */
    @NotBlank(message = "来源标识不能为空")
    private String source;
}
