package com.qiyi.vip.trade.autorenew.web.controller;

import com.qiyi.boss.service.CompensateService;
import com.qiyi.boss.model.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

/**
 * 补偿控制器
 * 用于从订单角度补偿一些订单没有及时开通自动续费的情况
 * 
 * <AUTHOR>
 * @date 2024/12/28
 */
@RestController
@RequestMapping("/compensate")
@Slf4j
public class CompensateController {

    @Resource
    private CompensateService compensateService;

    /**
     * 补偿订单自动续费开通
     * 
     * @param orderCode 订单号
     * @param source 来源标识
     * @return 处理结果
     */
    @RequestMapping("/autoRenew")
    public BaseResponse<String> compensateAutoRenew(@RequestParam(value = "orderCode", required = false) String orderCode, @RequestParam(value = "source", required = false) String source) {
        log.info("开始补偿订单自动续费开通, orderCode:{}, source:{}", orderCode, source);
        // 参数校验
        if (StringUtils.isBlank(orderCode)) {
            log.warn("订单号为空, orderCode:{}, source:{}", orderCode, source);
            return BaseResponse.create(BaseResponse.CodeEnum.ERROR_PARAM.getCode(), "订单号不能为空");
        }
        
        if (StringUtils.isBlank(source)) {
            log.warn("来源标识为空, orderCode:{}, source:{}", orderCode, source);
            return BaseResponse.create(BaseResponse.CodeEnum.ERROR_PARAM.getCode(), "来源标识不能为空");
        }
        
        BaseResponse<String> result = compensateService.compensateAutoRenewByOrder(orderCode, source);
        log.info("补偿订单自动续费开通完成, orderCode:{}, source:{}, success:{}", orderCode, source, result.isSuccess());
        return result;
    }
}
