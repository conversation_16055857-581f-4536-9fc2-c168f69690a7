package com.qiyi.vip.trade.autorenew.web.controller;

import com.qiyi.boss.service.CompensateService;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.vip.trade.autorenew.web.dto.CompensateAutoRenewRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 补偿控制器
 * 用于从订单角度补偿一些订单没有及时开通自动续费的情况
 * 
 * <AUTHOR>
 * @date 2024/12/28
 */
@RestController
@RequestMapping("/compensate")
@Slf4j
public class CompensateController {

    @Resource
    private CompensateService compensateService;

    /**
     * 补偿订单自动续费开通
     *
     * @param request 补偿请求参数
     * @return 处理结果
     */
    @RequestMapping("/autoRenew")
    public BaseResponse<String> compensateAutoRenew(@Valid CompensateAutoRenewRequest request) {
        String orderCode = request.getOrderCode();
        String source = request.getSource();
        log.info("开始补偿订单自动续费开通, orderCode:{}, source:{}", orderCode, source);

        BaseResponse<String> result = compensateService.compensateAutoRenewByOrder(orderCode, source);
        log.info("补偿订单自动续费开通完成, orderCode:{}, source:{}, success:{}", orderCode, source, result.isSuccess());
        return result;
    }
}
