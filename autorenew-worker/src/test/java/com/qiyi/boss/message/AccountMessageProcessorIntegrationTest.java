package com.qiyi.boss.message;

import com.qiyi.AutoRenewWorkerApplication;
import com.qiyi.boss.model.AccountChangeMsg;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * AccountMessageProcessor集成测试
 * 测试dealAccountBindMessage的所有分支
 */
@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AutoRenewWorkerApplication.class)
@AutoConfigureMetrics
public class AccountMessageProcessorIntegrationTest {

    @Resource
    private AccountMessageProcessor accountMessageProcessor;

    static {
        System.setProperty("spring.profiles.active", "test");
        System.setProperty("eureka.instance.hostname", "${spring.cloud.client.ip-address}");
        System.setProperty("eureka.instance.non-secure-port", "8080");
        System.setProperty("app.id", "vip-autorenew");
        System.setProperty("env", "test");
        System.setProperty("management.metrics.export.prometheus.enabled", "true");
        System.setProperty("eureka.client.register-with-eureka", "false");
    }

    /**
     * 测试自动续费开通消息处理 - 正常流程
     */
    @Test
    public void testDealAccountBindMessage_NormalFlow() {
        // Given
        String msgId = "TEST_MSG_001";
        MessageExt messageExt = createBindMessageExt(msgId);

        // When & Then - 不抛异常即为成功
        try {
            accountMessageProcessor.onMessage(messageExt);
            System.out.println("自动续费开通消息处理成功");
        } catch (Exception e) {
            System.out.println("自动续费开通消息处理异常: " + e.getMessage());
        }
    }

    /**
     * 测试自动续费解绑消息处理 - 正常流程
     */
    @Test
    public void testDealAccountUnbindMessage_NormalFlow() {
        // Given
        String msgId = "TEST_MSG_002";
        MessageExt messageExt = createUnbindMessageExt(msgId);

        // When & Then
        try {
            accountMessageProcessor.onMessage(messageExt);
            System.out.println("自动续费解绑消息处理成功");
        } catch (Exception e) {
            System.out.println("自动续费解绑消息处理异常: " + e.getMessage());
        }
    }

    /**
     * 测试芝麻GO解绑消息处理 - 正常流程
     */
    @Test
    public void testDealZhiMaGoUnbindMessage_NormalFlow() {
        // Given
        String msgId = "TEST_MSG_003";
        MessageExt messageExt = createZhiMaGoUnbindMessageExt(msgId);

        // When & Then
        try {
            accountMessageProcessor.onMessage(messageExt);
            System.out.println("芝麻GO解绑消息处理成功");
        } catch (Exception e) {
            System.out.println("芝麻GO解绑消息处理异常: " + e.getMessage());
        }
    }

    /**
     * 测试微信支付分解绑消息处理 - 正常流程
     */
    @Test
    public void testDealWeChatPayScoreUnbindMessage_NormalFlow() {
        // Given
        String msgId = "TEST_MSG_004";
        MessageExt messageExt = createWeChatPayScoreUnbindMessageExt(msgId);

        // When & Then
        try {
            accountMessageProcessor.onMessage(messageExt);
            System.out.println("微信支付分解绑消息处理成功");
        } catch (Exception e) {
            System.out.println("微信支付分解绑消息处理异常: " + e.getMessage());
        }
    }

    /**
     * 测试消息不需要处理的分支
     */
    @Test
    public void testMessageNotNeedHandler_SkipBranch() {
        // Given
        String msgId = "TEST_MSG_005";
        MessageExt messageExt = createInvalidMessageExt(msgId);

        // When & Then
        try {
            accountMessageProcessor.onMessage(messageExt);
            System.out.println("无效消息跳过处理成功");
        } catch (Exception e) {
            System.out.println("无效消息处理异常: " + e.getMessage());
        }
    }

    /**
     * 测试消息解析异常分支
     */
    @Test
    public void testMessageParseException_ExceptionBranch() {
        // Given
        String msgId = "TEST_MSG_006";
        MessageExt messageExt = new MessageExt();
        messageExt.setMsgId(msgId);
        messageExt.setBody("invalid json".getBytes(StandardCharsets.UTF_8));

        // When & Then
        try {
            accountMessageProcessor.onMessage(messageExt);
            System.out.println("消息解析异常处理成功");
        } catch (Exception e) {
            System.out.println("消息解析异常: " + e.getMessage());
        }
    }

    /**
     * 测试芝麻GO解绑中消息处理
     */
    @Test
    public void testDealZhiMaGoUnbindingMessage_NormalFlow() {
        // Given
        String msgId = "TEST_MSG_007";
        MessageExt messageExt = createZhiMaGoUnbindingMessageExt(msgId);

        // When & Then
        try {
            accountMessageProcessor.onMessage(messageExt);
            System.out.println("芝麻GO解绑中消息处理成功");
        } catch (Exception e) {
            System.out.println("芝麻GO解绑中消息处理异常: " + e.getMessage());
        }
    }

    /**
     * 测试免密支付解绑消息处理
     */
    @Test
    public void testDealPasswordFreeUnbindMessage_NormalFlow() {
        // Given
        String msgId = "TEST_MSG_008";
        MessageExt messageExt = createPasswordFreeUnbindMessageExt(msgId);

        // When & Then
        try {
            accountMessageProcessor.onMessage(messageExt);
            System.out.println("免密支付解绑消息处理成功");
        } catch (Exception e) {
            System.out.println("免密支付解绑消息处理异常: " + e.getMessage());
        }
    }

    // 辅助方法
    private MessageExt createBindMessageExt(String msgId) {
        Map<String, String> content = new HashMap<>();
        content.put("msgtype", AccountChangeMsg.ACCOUNT_DUT_BIND);
        content.put("uid", "**********");
        content.put("type", "946");
        content.put("partnerOrderNo", "TEST_ORDER_" + System.currentTimeMillis());
        content.put("agreementType", "1");
        content.put("operateTime", "2024-12-26 15:14:44.834");
        content.put("source", "1");
        return createMessageExt(content, msgId);
    }

    private MessageExt createUnbindMessageExt(String msgId) {
        Map<String, String> content = new HashMap<>();
        content.put("msgtype", AccountChangeMsg.ACCOUNT_DUT_UNBIND);
        content.put("uid", "**********");
        content.put("type", "946");
        content.put("agreementType", "1");
        content.put("operateTime", "2024-12-26 15:14:44.834");
        content.put("source", "1");
        return createMessageExt(content, msgId);
    }

    private MessageExt createZhiMaGoUnbindMessageExt(String msgId) {
        Map<String, String> content = new HashMap<>();
        content.put("msgtype", AccountChangeMsg.ACCOUNT_DUT_UNBIND);
        content.put("uid", "**********");
        content.put("type", "940");
        content.put("agreementType", "3");
        content.put("signType", "zhimago");
        content.put("operateTime", "2024-12-26 16:11:59.549");
        content.put("source", "1");
        return createMessageExt(content, msgId);
    }

    private MessageExt createZhiMaGoUnbindingMessageExt(String msgId) {
        Map<String, String> content = new HashMap<>();
        content.put("msgtype", AccountChangeMsg.ACCOUNT_DUT_UNBINDING);
        content.put("uid", "**********");
        content.put("type", "940");
        content.put("agreementType", "3");
        content.put("signType", "zhimago");
        content.put("quit_type", "SETTLE_APPLY_QUIT");
        content.put("operateTime", "2024-12-26 16:19:42.731");
        content.put("source", "1");
        return createMessageExt(content, msgId);
    }

    private MessageExt createWeChatPayScoreUnbindMessageExt(String msgId) {
        Map<String, String> content = new HashMap<>();
        content.put("msgtype", AccountChangeMsg.ACCOUNT_DUT_UNBIND);
        content.put("uid", "**********");
        content.put("type", "941");
        content.put("agreementType", "4");
        content.put("signType", "wechatpayscore");
        content.put("operateTime", "2024-12-26 17:45:43.997");
        content.put("source", "1");
        return createMessageExt(content, msgId);
    }

    private MessageExt createPasswordFreeUnbindMessageExt(String msgId) {
        Map<String, String> content = new HashMap<>();
        content.put("msgtype", AccountChangeMsg.ACCOUNT_DUT_UNBIND);
        content.put("uid", "**********");
        content.put("type", "920");
        content.put("agreementType", "1");
        content.put("signType", "passwordFree");
        content.put("operateTime", "2024-12-26 16:22:59.715");
        content.put("source", "1");
        return createMessageExt(content, msgId);
    }

    private MessageExt createInvalidMessageExt(String msgId) {
        Map<String, String> content = new HashMap<>();
        content.put("msgtype", "INVALID_TYPE");
        content.put("uid", "**********");
        content.put("type", "946");
        return createMessageExt(content, msgId);
    }

    private MessageExt createMessageExt(Map<String, String> content, String msgId) {
        MessageExt messageExt = new MessageExt();
        messageExt.setMsgId(msgId);
        messageExt.setBody(createJsonString(content).getBytes(StandardCharsets.UTF_8));
        return messageExt;
    }

    private String createJsonString(Map<String, String> content) {
        StringBuilder json = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<String, String> entry : content.entrySet()) {
            if (!first) json.append(",");
            json.append("\"").append(entry.getKey()).append("\":\"").append(entry.getValue()).append("\"");
            first = false;
        }
        json.append("}");
        return json.toString();
    }
}
