package com.qiyi.boss.message;

import org.apache.commons.lang.RandomStringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import com.qiyi.AutoRenewWorkerApplication;
import com.qiyi.boss.service.AutorenewRequest;
import static org.junit.Assert.assertEquals;

/**
 * @Author: Lin Peihui
 * @Date: 2021/7/9
 */
@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AutoRenewWorkerApplication.class)
@AutoConfigureMetrics
public class VIPAutoRenewRMQMsgProcessorTest {

    static {
        System.setProperty("eureka.instance.hostname", "${spring.cloud.client.ip-address}");
        System.setProperty("eureka.instance.non-secure-port", "8080");
        System.setProperty("eureka.client.register-with-eureka", "false");
        System.setProperty("spring.profiles.active", "test");
        System.setProperty("app.id", "vip-autorenew");
        System.setProperty("env", "test");
        System.setProperty("management.metrics.export.prometheus.enabled", "true");
    }

    @Resource
    private VIPAutoRenewRMQMsgProcessor vipAutoRenewRMQMsgProcessor;
    @Resource
    VIPAutorenewConfirmRMQMsgProcessor confirmRMQMsgProcessor;
    @Resource
    PaidSignOrderMsgProcessor paidSignOrderMsgProcessor;

    @Test
    public void testHandleZhimaGoRefundMsg() throws Exception {
        String msgBody = "{\"payTime\":\"2021-07-09 18:22:37\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"2\",\"pid\":\"a47bac390c51df6a\",\"type\":\"1\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"439\",\"msgtype\":\"7\",\"productId\":\"41\",\"tradeNo\":\"8b8836946507ebb8\",\"fr_version\":\"\",\"tradeCode\":\"202107091055200500001\",\"settlementFee\":\"-5\",\"orderRealFee\":\"-5\",\"centerPayType\":\"ZHIMAGODUT\",\"sourceType\":\"\",\"fc\":\"a9e4fec171bb4c74\",\"aid\":\"null\",\"platformCode\":\"97ae2982356f69d8\",\"productSubtype\":\"1\",\"status\":\"6\",\"businessProperty\":\"{\\\"refundWay\\\":0}\",\"serviceCode\":\"lyksc7aq36aedndk\",\"orderFee\":\"-10\",\"couponSettlementFee\":\"0\",\"platform\":\"19\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"**********\",\"businessValues\":\"0\",\"centerCode\":\"2021070918050000101\",\"autoRenew\":\"2\",\"startTime\":\"2021-07-17 10:55:30\",\"thirdUid\":\"****************\",\"renewFlag\":\"1\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"-1\",\"beforePaidSign\":\"1\",\"beforeDeadline\":\"2021-07-17 10:55:30\",\"payAccount\":\"****************\",\"createTime\":\"2021-07-09 17:42:27\",\"payTypeCategory\":\"1\",\"userIp\":\"*************\",\"orderCode\":\"202107091742267300095\",\"payChannel\":\"1\",\"endTime\":\"2021-07-16 18:22:40\",\"centerPayService\":\"879\",\"gateway\":\"1\"}\n";
        MessageExt messageExt = new MessageExt();
        messageExt.setMsgId(RandomStringUtils.randomAlphanumeric(20));
        messageExt.setBody(msgBody.getBytes(StandardCharsets.UTF_8.displayName()));
        vipAutoRenewRMQMsgProcessor.onMessage(messageExt);
    }

    @Test
    public void confirmOrderFinishedMsg() throws Exception {
        String msgBody = "{\"payTime\":\"2021-11-23 17:43:42\",\"channel\":\"bd59b06a0a461c74\",\"chargeType\":\"1\",\"pid\":\"a09c674b4c141dc3\",\"type\":\"13\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"442\",\"msgtype\":\"7\",\"productId\":\"41\",\"tradeNo\":\"abb67f5c267f33b3\",\"fr_version\":\"\",\"settlementFee\":\"1\",\"renewType\":\"939\",\"orderRealFee\":\"0\",\"centerPayType\":\"WECHATPAYSCORECREATE\",\"sourceType\":\"\",\"fc\":\"8758cc1591dd0985\",\"aid\":\"null\",\"platformCode\":\"bb136ff4276771f3\",\"productSubtype\":\"1\",\"status\":\"1\",\"couponFee\":\"0\",\"serviceCode\":\"lyksc7aq36aedndk\",\"orderFee\":\"1\",\"couponSettlementFee\":\"0\",\"platform\":\"10\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"**********\",\"businessValues\":\"****************\",\"centerCode\":\"2021112363821001009\",\"autoRenew\":\"2\",\"startTime\":\"2021-11-23 17:48:32\",\"thirdUid\":\"oveuTjhyf61HS_bfx3SOZr4Jpnc0\",\"renewFlag\":\"1\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"1\",\"beforePaidSign\":\"0\",\"beforeDeadline\":\"2021-11-24 17:48:32\",\"payAccount\":\"oveuTjhyf61HS_bfx3SOZr4Jpnc0\",\"createTime\":\"2021-11-24 11:17:46\",\"payTypeCategory\":\"1\",\"userIp\":\"*************\",\"orderCode\":\"202111241117468700001\",\"payChannel\":\"2\",\"endTime\":\"2021-12-23 17:48:32\",\"centerPayService\":\"518\",\"gateway\":\"9793\"}";
        MessageExt messageExt = new MessageExt();
        messageExt.setMsgId(RandomStringUtils.randomAlphanumeric(20));
        messageExt.setBody(msgBody.getBytes(StandardCharsets.UTF_8.displayName()));
        confirmRMQMsgProcessor.onMessage(messageExt);
    }

    @Test
    public void testHandleFinished() throws Exception {
        String msgBody = "{\"payTime\":\"2022-02-28 20:17:18\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"pid\":\"9ff1a15abb9b50b8\",\"phoneNum\":\"***********\",\"type\":\"1\",\"deviceId\":\"tv_f2e43fbf302795b9e45dc0dc43956138_1570511449902_9038890275\",\"businessCode\":\"wxnsc8bw14aednlm\",\"payType\":\"384\",\"giftActCode\":\"main_9600211230151617293236,sub_9600220124185026044939\",\"msgtype\":\"7\",\"productId\":\"921\",\"tradeNo\":\"202202282017017766378\",\"fr_version\":\"enter_type=3&from=mobile_account&tvid=&aid=&c1=&qtcurl=mobile_account&block=mobile_account&v=12.2.2.143694&uuid=20170227120253510BIaKrwaM11642&u=tv_f2e43fbf302795b9e45dc0dc43956138_1570511449902&d=tv_f2e43fbf302795b9e45dc0dc43956138_1570511449902_9038890275&p2=3121&st=&hu=&fromForlog=2\",\"tradeCode\":\"4200001386202202287570024134\",\"settlementFee\":\"1500\",\"renewType\":\"19\",\"orderRealFee\":\"1500\",\"centerPayType\":\"WECHATPCDUTV4\",\"actCode\":\"main_9600211230151617293236,sub_9600220124185026044939\",\"sourceType\":\"\",\"fc\":\"8c79b98cc807447d\",\"aid\":\"null\",\"platformCode\":\"****************\",\"productSubtype\":\"5\",\"status\":\"1\",\"couponFee\":\"0\",\"serviceCode\":\"wxnsc8bw14aednlm\",\"orderFee\":\"1500\",\"couponSettlementFee\":\"0\",\"platform\":\"805\",\"fv\":\"b43f88d172c4b551\",\"uid\":\"**********\",\"businessValues\":\"0\",\"centerCode\":\"2022022890004825188\",\"autoRenew\":\"3\",\"startTime\":\"2022-02-28 20:17:18\",\"thirdUid\":\"omTBWt1dtvywAmfsPZqJof7Ww5-w\",\"renewFlag\":\"0\",\"productType\":\"5\",\"currencyUnit\":\"CNY\",\"amount\":\"1\",\"beforePaidSign\":\"0\",\"payAccount\":\"omTBWt1dtvywAmfsPZqJof7Ww5-w\",\"createTime\":\"2022-02-28 20:17:02\",\"payTypeCategory\":\"1\",\"orderCode\":\"202202282017017766378\",\"payChannel\":\"2\",\"endTime\":\"2022-03-28 23:59:59\",\"centerPayService\":\"479\",\"gateway\":\"5975\"}";
        MessageExt messageExt = new MessageExt();
        messageExt.setMsgId(RandomStringUtils.randomAlphanumeric(20));
        messageExt.setBody(msgBody.getBytes(StandardCharsets.UTF_8.displayName()));
        vipAutoRenewRMQMsgProcessor.onMessage(messageExt);
    }

    @Test
    public void agreementPaySign() throws Exception {
//        String msgBody = "{\"payTime\":\"2022-11-10 14:55:30\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"pid\":\"a0226bd958843452\",\"phoneNum\":\"c1e305c90cbc417032790e2f356bed5f\",\"type\":\"1\",\"deviceId\":\"b4d7c6547a5df712c5aea5e4d7cc3407110d\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"412\",\"giftActCode\":\"main_9600220524170456667005,sub_9600220524170729450633\",\"msgtype\":\"7\",\"productId\":\"4\",\"tradeNo\":\"20221109092734289279002591\",\"fr_version\":\"cellphoneModel=vivo+NEX+A&dfp=1418ca73bc2c0a48109c688fbbec6b910cca0aa414072a02e3e2a46023a4dd18ac&d=b4d7c6547a5df712c5aea5e4d7cc3407110d&k=20232202d774247f32bb7b36480500a1&v=13.10.5&aid=&fr=&test=&qylct=&qybdlct=&qyctxv=1&coordType=2&FromCasher=1&login=1_1&isPasswordFreePay=0&mod=CN&isAliPay=1&sid=n8pivlvd1ygkaqf7.0&hasShowedAgreement=true&bkt=no-send-redpacket;manual;856da2567b2dbedc;&e=5dc7b367aa654576bd26bc238e1d5c99&gatewayAbtest=gatewayGroupB&loginResultType=0&domainOrigin=FromGwDomain\",\"tradeCode\":\"2022110922001437901427618494\",\"settlementFee\":\"2200\",\"renewType\":\"946\",\"orderRealFee\":\"2200\",\"centerPayType\":\"ALIPAYSIGNV2\",\"actCode\":\"main_9600220524170456667005,sub_9600220524170729450633\",\"sourceType\":\"\",\"fc\":\"90f60e25e70c76a6\",\"aid\":\"null\",\"platformCode\":\"bb136ff4276771f3\",\"productSubtype\":\"1\",\"status\":\"1\",\"couponFee\":\"0\",\"businessProperty\":\"{\\\"orderExt\\\":\\\"{\\\\\\\"rs\\\\\\\":1,\\\\\\\"showBlock\\\\\\\":0,\\\\\\\"default\\\\\\\":0,\\\\\\\"amount\\\\\\\":1,\\\\\\\"userCrowd\\\\\\\":\\\\\\\"vip\\\\\\\",\\\\\\\"autorenew\\\\\\\":3,\\\\\\\"GW-Unity-Host\\\\\\\":\\\\\\\"api.iqiyi.com\\\\\\\",\\\\\\\"price\\\\\\\":2200,\\\\\\\"originalprice\\\\\\\":2200,\\\\\\\"pid\\\\\\\":\\\\\\\"a0226bd958843452\\\\\\\",\\\\\\\"sort\\\\\\\":3}\\\",\\\"payTypeActCode\\\":\\\"8d3c6a7906ff70e3,857b9832a0e3bb9e\\\"}\",\"serviceCode\":\"lyksc7aq36aedndk\",\"orderFee\":\"2200\",\"couponSettlementFee\":\"0\",\"platform\":\"10\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"**********\",\"businessValues\":\"0\",\"centerCode\":\"2022110990000323251\",\"autoRenew\":\"3\",\"startTime\":\"2022-11-10 15:00:00\",\"thirdUid\":\"****************\",\"renewFlag\":\"1\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"1\",\"beforePaidSign\":\"1\",\"beforeDeadline\":\"2022-11-08 23:59:59\",\"payAccount\":\"****************\",\"refer\":\"{\\\"actCode\\\":\\\"main_9600220524170456667005,sub_9600220524170729450633\\\",\\\"beforeDeadline\\\":\\\"2022-11-08 23:59:59\\\",\\\"beforePaidSign\\\":1,\\\"businessProperty\\\":{\\\"orderExt\\\":\\\"{\\\\\\\"rs\\\\\\\":1,\\\\\\\"showBlock\\\\\\\":0,\\\\\\\"default\\\\\\\":0,\\\\\\\"amount\\\\\\\":1,\\\\\\\"userCrowd\\\\\\\":\\\\\\\"vip\\\\\\\",\\\\\\\"autorenew\\\\\\\":3,\\\\\\\"GW-Unity-Host\\\\\\\":\\\\\\\"api.iqiyi.com\\\\\\\",\\\\\\\"price\\\\\\\":2200,\\\\\\\"originalprice\\\\\\\":2200,\\\\\\\"pid\\\\\\\":\\\\\\\"a0226bd958843452\\\\\\\",\\\\\\\"sort\\\\\\\":3}\\\",\\\"payTypeActCode\\\":\\\"8d3c6a7906ff70e3,857b9832a0e3bb9e\\\"},\\\"discountDetail\\\":{\\\"buyerPayAmount\\\":2020},\\\"merchantNo\\\":\\\"****************\\\",\\\"phoneNum\\\":\\\"c1e305c90cbc417032790e2f356bed5f\\\",\\\"suitABTestId\\\":\\\"[{\\\\\\\"abTestGroup\\\\\\\":\\\\\\\"Alipaytest02\\\\\\\",\\\\\\\"status\\\\\\\":1}]\\\",\\\"wechatBankCoupon\\\":{}}\",\"createTime\":\"2022-11-10 14:55:00\",\"payTypeCategory\":\"1\",\"orderCode\":\"20221108164750311481005835\",\"payChannel\":\"1\",\"endTime\":\"2022-12-09 23:59:59\",\"centerPayService\":\"497\",\"gateway\":\"14610\"}";
//        String msgBody = "{\"payTime\":\"2022-11-09 09:28:02\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"pid\":\"a0226bd958843452\",\"phoneNum\":\"c1e305c90cbc417032790e2f356bed5f\",\"type\":\"1\",\"deviceId\":\"b4d7c6547a5df712c5aea5e4d7cc3407110d\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"412\",\"giftActCode\":\"main_9600220524170456667005,sub_9600220524170729450633\",\"msgtype\":\"7\",\"productId\":\"4\",\"tradeNo\":\"20221109092734289279002591\",\"fr_version\":\"cellphoneModel=vivo+NEX+A&dfp=1418ca73bc2c0a48109c688fbbec6b910cca0aa414072a02e3e2a46023a4dd18ac&d=b4d7c6547a5df712c5aea5e4d7cc3407110d&k=20232202d774247f32bb7b36480500a1&v=13.10.5&aid=&fr=&test=&qylct=&qybdlct=&qyctxv=1&coordType=2&FromCasher=1&login=1_1&isPasswordFreePay=0&mod=CN&isAliPay=1&sid=n8pivlvd1ygkaqf7.0&hasShowedAgreement=true&bkt=no-send-redpacket;manual;856da2567b2dbedc;&e=5dc7b367aa654576bd26bc238e1d5c99&gatewayAbtest=gatewayGroupB&loginResultType=0&domainOrigin=FromGwDomain\",\"tradeCode\":\"2022110922001437901427618495\",\"settlementFee\":\"2200\",\"renewType\":\"2094601\",\"orderRealFee\":\"2200\",\"centerPayType\":\"ALIPAYSIGNV2\",\"actCode\":\"main_9600220524170456667005,sub_9600220524170729450633\",\"sourceType\":\"\",\"fc\":\"90f60e25e70c76a6\",\"aid\":\"null\",\"platformCode\":\"bb136ff4276771f3\",\"productSubtype\":\"1\",\"status\":\"1\",\"couponFee\":\"0\",\"businessProperty\":\"{\\\"orderExt\\\":\\\"{\\\\\\\"rs\\\\\\\":1,\\\\\\\"showBlock\\\\\\\":0,\\\\\\\"default\\\\\\\":0,\\\\\\\"amount\\\\\\\":1,\\\\\\\"userCrowd\\\\\\\":\\\\\\\"vip\\\\\\\",\\\\\\\"autorenew\\\\\\\":3,\\\\\\\"GW-Unity-Host\\\\\\\":\\\\\\\"api.iqiyi.com\\\\\\\",\\\\\\\"price\\\\\\\":2200,\\\\\\\"originalprice\\\\\\\":2200,\\\\\\\"pid\\\\\\\":\\\\\\\"a0226bd958843452\\\\\\\",\\\\\\\"sort\\\\\\\":3}\\\",\\\"payTypeActCode\\\":\\\"8d3c6a7906ff70e3,857b9832a0e3bb9e\\\"}\",\"serviceCode\":\"lyksc7aq36aedndk\",\"orderFee\":\"2200\",\"couponSettlementFee\":\"0\",\"platform\":\"10\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"**********\",\"businessValues\":\"0\",\"centerCode\":\"2022110990000323251\",\"autoRenew\":\"3\",\"startTime\":\"2022-11-09 09:28:02\",\"thirdUid\":\"****************\",\"renewFlag\":\"1\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"1\",\"beforePaidSign\":\"1\",\"beforeDeadline\":\"2022-11-08 23:59:59\",\"payAccount\":\"****************\",\"refer\":\"{\\\"actCode\\\":\\\"main_9600220524170456667005,sub_9600220524170729450633\\\",\\\"beforeDeadline\\\":\\\"2022-11-08 23:59:59\\\",\\\"beforePaidSign\\\":1,\\\"businessProperty\\\":{\\\"orderExt\\\":\\\"{\\\\\\\"rs\\\\\\\":1,\\\\\\\"showBlock\\\\\\\":0,\\\\\\\"default\\\\\\\":0,\\\\\\\"amount\\\\\\\":1,\\\\\\\"userCrowd\\\\\\\":\\\\\\\"vip\\\\\\\",\\\\\\\"autorenew\\\\\\\":3,\\\\\\\"GW-Unity-Host\\\\\\\":\\\\\\\"api.iqiyi.com\\\\\\\",\\\\\\\"price\\\\\\\":2200,\\\\\\\"originalprice\\\\\\\":2200,\\\\\\\"pid\\\\\\\":\\\\\\\"a0226bd958843452\\\\\\\",\\\\\\\"sort\\\\\\\":3}\\\",\\\"payTypeActCode\\\":\\\"8d3c6a7906ff70e3,857b9832a0e3bb9e\\\"},\\\"discountDetail\\\":{\\\"buyerPayAmount\\\":2020},\\\"merchantNo\\\":\\\"****************\\\",\\\"phoneNum\\\":\\\"c1e305c90cbc417032790e2f356bed5f\\\",\\\"suitABTestId\\\":\\\"[{\\\\\\\"abTestGroup\\\\\\\":\\\\\\\"Alipaytest02\\\\\\\",\\\\\\\"status\\\\\\\":1}]\\\",\\\"wechatBankCoupon\\\":{}}\",\"createTime\":\"2022-11-08 16:47:50\",\"payTypeCategory\":\"1\",\"orderCode\":\"20221108181824000036005855\",\"payChannel\":\"1\",\"endTime\":\"2022-12-09 23:59:59\",\"centerPayService\":\"497\",\"gateway\":\"14610\"}";
//        String msgBody = "{\"payTime\":\"2022-11-11 09:28:02\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"pid\":\"a0226bd958843452\",\"phoneNum\":\"c1e305c90cbc417032790e2f356bed5f\",\"type\":\"1\",\"deviceId\":\"b4d7c6547a5df712c5aea5e4d7cc3407110d\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"380\",\"giftActCode\":\"main_9600220524170456667005,sub_9600220524170729450633\",\"msgtype\":\"7\",\"productId\":\"4\",\"tradeNo\":\"20221109092734289279002591\",\"fr_version\":\"cellphoneModel=vivo+NEX+A&dfp=1418ca73bc2c0a48109c688fbbec6b910cca0aa414072a02e3e2a46023a4dd18ac&d=b4d7c6547a5df712c5aea5e4d7cc3407110d&k=20232202d774247f32bb7b36480500a1&v=13.10.5&aid=&fr=&test=&qylct=&qybdlct=&qyctxv=1&coordType=2&FromCasher=1&login=1_1&isPasswordFreePay=0&mod=CN&isAliPay=1&sid=n8pivlvd1ygkaqf7.0&hasShowedAgreement=true&bkt=no-send-redpacket;manual;856da2567b2dbedc;&e=5dc7b367aa654576bd26bc238e1d5c99&gatewayAbtest=gatewayGroupB&loginResultType=0&domainOrigin=FromGwDomain\",\"tradeCode\":\"2022110922001437901427618495\",\"settlementFee\":\"6300\",\"renewType\":\"944\",\"orderRealFee\":\"6300\",\"centerPayType\":\"WECHATAPPDUTV4\",\"actCode\":\"main_9600220524170456667005,sub_9600220524170729450633\",\"sourceType\":\"\",\"fc\":\"90f60e25e70c76a6\",\"aid\":\"null\",\"platformCode\":\"bb136ff4276771f3\",\"productSubtype\":\"1\",\"status\":\"1\",\"couponFee\":\"0\",\"businessProperty\":\"{\\\"orderExt\\\":\\\"{\\\\\\\"rs\\\\\\\":1,\\\\\\\"showBlock\\\\\\\":0,\\\\\\\"default\\\\\\\":0,\\\\\\\"amount\\\\\\\":3,\\\\\\\"userCrowd\\\\\\\":\\\\\\\"vip\\\\\\\",\\\\\\\"autorenew\\\\\\\":3,\\\\\\\"GW-Unity-Host\\\\\\\":\\\\\\\"api.iqiyi.com\\\\\\\",\\\\\\\"price\\\\\\\":6300,\\\\\\\"originalprice\\\\\\\":2200,\\\\\\\"pid\\\\\\\":\\\\\\\"a0226bd958843452\\\\\\\",\\\\\\\"sort\\\\\\\":3}\\\",\\\"payTypeActCode\\\":\\\"8d3c6a7906ff70e3,857b9832a0e3bb9e\\\"}\",\"serviceCode\":\"lyksc7aq36aedndk\",\"orderFee\":\"6300\",\"couponSettlementFee\":\"0\",\"platform\":\"10\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"**********\",\"businessValues\":\"0\",\"centerCode\":\"2022110990000323251\",\"autoRenew\":\"3\",\"startTime\":\"2022-11-09 09:28:02\",\"thirdUid\":\"****************\",\"renewFlag\":\"1\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"3\",\"beforePaidSign\":\"1\",\"beforeDeadline\":\"2022-11-08 23:59:59\",\"payAccount\":\"****************\",\"refer\":\"{\\\"actCode\\\":\\\"main_9600220524170456667005,sub_9600220524170729450633\\\",\\\"beforeDeadline\\\":\\\"2022-11-08 23:59:59\\\",\\\"beforePaidSign\\\":1,\\\"businessProperty\\\":{\\\"orderExt\\\":\\\"{\\\\\\\"rs\\\\\\\":1,\\\\\\\"showBlock\\\\\\\":0,\\\\\\\"default\\\\\\\":0,\\\\\\\"amount\\\\\\\":3,\\\\\\\"userCrowd\\\\\\\":\\\\\\\"vip\\\\\\\",\\\\\\\"autorenew\\\\\\\":3,\\\\\\\"GW-Unity-Host\\\\\\\":\\\\\\\"api.iqiyi.com\\\\\\\",\\\\\\\"price\\\\\\\":2200,\\\\\\\"originalprice\\\\\\\":2200,\\\\\\\"pid\\\\\\\":\\\\\\\"a0226bd958843452\\\\\\\",\\\\\\\"sort\\\\\\\":3}\\\",\\\"payTypeActCode\\\":\\\"8d3c6a7906ff70e3,857b9832a0e3bb9e\\\"},\\\"discountDetail\\\":{\\\"buyerPayAmount\\\":2020},\\\"merchantNo\\\":\\\"****************\\\",\\\"phoneNum\\\":\\\"c1e305c90cbc417032790e2f356bed5f\\\",\\\"suitABTestId\\\":\\\"[{\\\\\\\"abTestGroup\\\\\\\":\\\\\\\"Alipaytest02\\\\\\\",\\\\\\\"status\\\\\\\":1}]\\\",\\\"wechatBankCoupon\\\":{}}\",\"createTime\":\"2022-11-08 16:47:50\",\"payTypeCategory\":\"1\",\"orderCode\":\"20221108181824000036005855\",\"payChannel\":\"1\",\"endTime\":\"2022-12-09 23:59:59\",\"centerPayService\":\"497\",\"gateway\":\"14610\"}";

//        String msgBody = "{\"payTime\":\"2022-11-09 09:28:02\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"pid\":\"a0226bd958843452\",\"phoneNum\":\"c1e305c90cbc417032790e2f356bed5f\",\"type\":\"1\",\"deviceId\":\"b4d7c6547a5df712c5aea5e4d7cc3407110d\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"412\",\"giftActCode\":\"main_9600220524170456667005,sub_9600220524170729450633\",\"msgtype\":\"7\",\"productId\":\"4\",\"tradeNo\":\"20221109092734289279002591\",\"fr_version\":\"cellphoneModel=vivo+NEX+A&dfp=1418ca73bc2c0a48109c688fbbec6b910cca0aa414072a02e3e2a46023a4dd18ac&d=b4d7c6547a5df712c5aea5e4d7cc3407110d&k=20232202d774247f32bb7b36480500a1&v=13.10.5&aid=&fr=&test=&qylct=&qybdlct=&qyctxv=1&coordType=2&FromCasher=1&login=1_1&isPasswordFreePay=0&mod=CN&isAliPay=1&sid=n8pivlvd1ygkaqf7.0&hasShowedAgreement=true&bkt=no-send-redpacket;manual;856da2567b2dbedc;&e=5dc7b367aa654576bd26bc238e1d5c99&gatewayAbtest=gatewayGroupB&loginResultType=0&domainOrigin=FromGwDomain\",\"tradeCode\":\"2022110922001437901427618496\",\"settlementFee\":\"2200\",\"renewType\":\"2094602\",\"orderRealFee\":\"2200\",\"centerPayType\":\"ALIPAYSIGNV2\",\"actCode\":\"main_9600220524170456667005,sub_9600220524170729450633\",\"sourceType\":\"\",\"fc\":\"90f60e25e70c76a6\",\"aid\":\"null\",\"platformCode\":\"bb136ff4276771f3\",\"productSubtype\":\"1\",\"status\":\"1\",\"couponFee\":\"0\",\"businessProperty\":\"{\\\"orderExt\\\":\\\"{\\\\\\\"rs\\\\\\\":1,\\\\\\\"showBlock\\\\\\\":0,\\\\\\\"default\\\\\\\":0,\\\\\\\"amount\\\\\\\":1,\\\\\\\"userCrowd\\\\\\\":\\\\\\\"vip\\\\\\\",\\\\\\\"autorenew\\\\\\\":3,\\\\\\\"GW-Unity-Host\\\\\\\":\\\\\\\"api.iqiyi.com\\\\\\\",\\\\\\\"price\\\\\\\":2200,\\\\\\\"originalprice\\\\\\\":2200,\\\\\\\"pid\\\\\\\":\\\\\\\"a0226bd958843452\\\\\\\",\\\\\\\"sort\\\\\\\":3}\\\",\\\"payTypeActCode\\\":\\\"8d3c6a7906ff70e3,857b9832a0e3bb9e\\\"}\",\"serviceCode\":\"lyksc7aq36aedndk\",\"orderFee\":\"2200\",\"couponSettlementFee\":\"0\",\"platform\":\"10\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"**********\",\"businessValues\":\"0\",\"centerCode\":\"2022110990000323251\",\"autoRenew\":\"3\",\"startTime\":\"2022-11-09 09:28:02\",\"thirdUid\":\"****************\",\"renewFlag\":\"1\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"1\",\"beforePaidSign\":\"1\",\"beforeDeadline\":\"2022-11-08 23:59:59\",\"payAccount\":\"****************\",\"refer\":\"{\\\"actCode\\\":\\\"main_9600220524170456667005,sub_9600220524170729450633\\\",\\\"beforeDeadline\\\":\\\"2022-11-08 23:59:59\\\",\\\"beforePaidSign\\\":1,\\\"businessProperty\\\":{\\\"orderExt\\\":\\\"{\\\\\\\"rs\\\\\\\":1,\\\\\\\"showBlock\\\\\\\":0,\\\\\\\"default\\\\\\\":0,\\\\\\\"amount\\\\\\\":1,\\\\\\\"userCrowd\\\\\\\":\\\\\\\"vip\\\\\\\",\\\\\\\"autorenew\\\\\\\":3,\\\\\\\"GW-Unity-Host\\\\\\\":\\\\\\\"api.iqiyi.com\\\\\\\",\\\\\\\"price\\\\\\\":2200,\\\\\\\"originalprice\\\\\\\":2200,\\\\\\\"pid\\\\\\\":\\\\\\\"a0226bd958843452\\\\\\\",\\\\\\\"sort\\\\\\\":3}\\\",\\\"priceActCode\\\":\\\"9600210114142918104283\\\"},\\\"discountDetail\\\":{\\\"buyerPayAmount\\\":2020},\\\"merchantNo\\\":\\\"****************\\\",\\\"phoneNum\\\":\\\"c1e305c90cbc417032790e2f356bed5f\\\",\\\"suitABTestId\\\":\\\"[{\\\\\\\"abTestGroup\\\\\\\":\\\\\\\"Alipaytest02\\\\\\\",\\\\\\\"status\\\\\\\":1}]\\\",\\\"wechatBankCoupon\\\":{}}\",\"createTime\":\"2022-11-08 16:47:50\",\"payTypeCategory\":\"1\",\"orderCode\":\"20221108194423900089005856\",\"payChannel\":\"1\",\"endTime\":\"2022-12-09 23:59:59\",\"centerPayService\":\"497\",\"gateway\":\"14610\"}";
//        String msgBody = "{\"payTime\":\"2022-11-10 20:20:00\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"pid\":\"91de86ec2a858135\",\"phoneNum\":\"c1e305c90cbc417032790e2f356bed5f\",\"type\":\"1\",\"deviceId\":\"b4d7c6547a5df712c5aea5e4d7cc3407110d\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"412\",\"giftActCode\":\"main_9600220524170456667005,sub_9600220524170729450633\",\"msgtype\":\"7\",\"productId\":\"1726479\",\"tradeNo\":\"20221109092734289279002595\",\"fr_version\":\"cellphoneModel=vivo+NEX+A&dfp=1418ca73bc2c0a48109c688fbbec6b910cca0aa414072a02e3e2a46023a4dd18ac&d=b4d7c6547a5df712c5aea5e4d7cc3407110d&k=20232202d774247f32bb7b36480500a1&v=13.10.5&aid=&fr=&test=&qylct=&qybdlct=&qyctxv=1&coordType=2&FromCasher=1&login=1_1&isPasswordFreePay=0&mod=CN&isAliPay=1&sid=n8pivlvd1ygkaqf7.0&hasShowedAgreement=true&bkt=no-send-redpacket;manual;856da2567b2dbedc;&e=5dc7b367aa654576bd26bc238e1d5c99&gatewayAbtest=gatewayGroupB&loginResultType=0&domainOrigin=FromGwDomain\",\"tradeCode\":\"2022110922001437901427618495\",\"settlementFee\":\"990\",\"renewType\":\"2098801\",\"orderRealFee\":\"990\",\"centerPayType\":\"ALIPAYSIGNV2\",\"actCode\":\"main_9600220524170456667005,sub_9600220524170729450633\",\"sourceType\":\"1\",\"fc\":\"90f60e25e70c76a6\",\"aid\":\"null\",\"platformCode\":\"bb136ff4276771f3\",\"productSubtype\":\"58\",\"status\":\"1\",\"couponFee\":\"0\",\"businessProperty\":\"{\\\"orderExt\\\":\\\"{\\\\\\\"rs\\\\\\\":1,\\\\\\\"showBlock\\\\\\\":0,\\\\\\\"default\\\\\\\":0,\\\\\\\"amount\\\\\\\":1,\\\\\\\"userCrowd\\\\\\\":\\\\\\\"vip\\\\\\\",\\\\\\\"autorenew\\\\\\\":3,\\\\\\\"GW-Unity-Host\\\\\\\":\\\\\\\"api.iqiyi.com\\\\\\\",\\\\\\\"price\\\\\\\":2200,\\\\\\\"originalprice\\\\\\\":2200,\\\\\\\"pid\\\\\\\":\\\\\\\"a0226bd958843452\\\\\\\",\\\\\\\"sort\\\\\\\":3}\\\",\\\"payTypeActCode\\\":\\\"8d3c6a7906ff70e3,857b9832a0e3bb9e\\\"}\",\"serviceCode\":\"lyksc7aq36aedndk\",\"orderFee\":\"990\",\"couponSettlementFee\":\"0\",\"platform\":\"10\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"**********\",\"businessValues\":\"0\",\"centerCode\":\"2022110990000323255\",\"autoRenew\":\"3\",\"startTime\":\"2022-11-10 20:00:00\",\"thirdUid\":\"****************\",\"renewFlag\":\"1\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"30\",\"beforePaidSign\":\"1\",\"beforeDeadline\":\"2022-11-08 23:59:59\",\"payAccount\":\"****************\",\"refer\":\"{\\\"actCode\\\":\\\"main_9600220524170456667005,sub_9600220524170729450633\\\",\\\"beforeDeadline\\\":\\\"2022-11-08 23:59:59\\\",\\\"beforePaidSign\\\":1,\\\"businessProperty\\\":{\\\"orderExt\\\":\\\"{\\\\\\\"rs\\\\\\\":1,\\\\\\\"showBlock\\\\\\\":0,\\\\\\\"default\\\\\\\":0,\\\\\\\"amount\\\\\\\":1,\\\\\\\"userCrowd\\\\\\\":\\\\\\\"vip\\\\\\\",\\\\\\\"autorenew\\\\\\\":3,\\\\\\\"GW-Unity-Host\\\\\\\":\\\\\\\"api.iqiyi.com\\\\\\\",\\\\\\\"price\\\\\\\":2200,\\\\\\\"originalprice\\\\\\\":2200,\\\\\\\"pid\\\\\\\":\\\\\\\"a0226bd958843452\\\\\\\",\\\\\\\"sort\\\\\\\":3}\\\",\\\"payTypeActCode\\\":\\\"8d3c6a7906ff70e3,857b9832a0e3bb9e\\\"},\\\"discountDetail\\\":{\\\"buyerPayAmount\\\":2020},\\\"merchantNo\\\":\\\"****************\\\",\\\"phoneNum\\\":\\\"c1e305c90cbc417032790e2f356bed5f\\\",\\\"suitABTestId\\\":\\\"[{\\\\\\\"abTestGroup\\\\\\\":\\\\\\\"Alipaytest02\\\\\\\",\\\\\\\"status\\\\\\\":1}]\\\",\\\"wechatBankCoupon\\\":{}}\",\"createTime\":\"2022-11-10 19:59:00\",\"payTypeCategory\":\"1\",\"orderCode\":\"20221110202104500113005856\",\"payChannel\":\"1\",\"endTime\":\"2022-12-09 23:59:59\",\"centerPayService\":\"497\",\"gateway\":\"14610\"}";
        String msgBody = "{\"orderType\":\"MAIN_PROD\",\"payTime\":\"2024-07-10 14:41:00\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"pid\":\"a0226bd958843452\",\"phoneNum\":\"375fe6470c7cefef083bd6c0a876ac6d\",\"type\":\"16\",\"deviceId\":\"4e9b227e1c3aba57f627dda1321ac7a0110d\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"478\",\"msgtype\":\"7\",\"skuId\":\"sku_177079572728355936\",\"productId\":\"4\",\"tradeNo\":\"202407101440482838490093500002\",\"fr_version\":\"cellphoneModel=PFFM10&dfp=14e805cdb9455b42228e8ffe3844c58e718c6e678e4b96fc2ec7ef454bba9c2654&d=4e9b227e1c3aba57f627dda1321ac7a0110d&k=5d2157eabcea7a5a82c0ca8975063d43&v=15.6.6&aid=&fr=&test=&FromCasher=1&login=1_1&isPasswordFreePay=&mod=CN&isAliPay=1&sid=u4jo9jdi8647agc2.0&hasShowedAgreement=true&bkt=send-redpacket-2;coupon-algo-online;coupon-algo&e=7a8f23829fb14ae2&gatewayAbtest=gatewayGroupB&loginResultType=0&integral=60&diy_tag=f2ed9c20-2ddb-47cc-a87d-6319cb5f5371&domainOrigin=FromGwDomain\",\"tradeCode\":\"2024071022001495901427647149\",\"settlementFee\":\"2500\",\"renewType\":\"2109501\",\"orderRealFee\":\"1200\",\"centerPayType\":\"ALIPAYSIGNV2\",\"sourceType\":\"\",\"name\":\"黄金VIP会员连续包月\",\"fc\":\"979da95c1f5dd1f7\",\"aid\":\"null\",\"platformCode\":\"bb136ff4276771f3\",\"productSubtype\":\"1\",\"status\":\"1\",\"extraInfo\":\"{\\\"abTestGroup\\\":\\\"[{\\\\\\\"abTestGroup\\\\\\\":\\\\\\\"480#jichushiyanzu3,518#智能红包策略2\\\\\\\",\\\\\\\"status\\\\\\\":1}]\\\"}\",\"couponFee\":\"0\",\"businessProperty\":\"{\\\"orderExt\\\":\\\"{\\\\\\\"rs\\\\\\\":1,\\\\\\\"showBlock\\\\\\\":0,\\\\\\\"priceActCode\\\\\\\":\\\\\\\"1565108b2d614855\\\\\\\",\\\\\\\"userCrowd\\\\\\\":\\\\\\\"expiry_180\\\\\\\",\\\\\\\"topGiftList\\\\\\\":[{\\\\\\\"code\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"type\\\\\\\":0},{\\\\\\\"code\\\\\\\":\\\\\\\"sku_197774104155332665\\\\\\\",\\\\\\\"type\\\\\\\":1}],\\\\\\\"ruleUserCrowd\\\\\\\":\\\\\\\"1;expire_365,58;other,13;new,56;expire_180\\\\\\\",\\\\\\\"release_ltv12\\\\\\\":62.**************,\\\\\\\"test_ltv12\\\\\\\":62.**************,\\\\\\\"receiveStatus\\\\\\\":2}\\\",\\\"priceActCodeActual\\\":\\\"1565108b2d614855\\\",\\\"redPacketCode\\\":\\\"20240710144028331992102919936441\\\",\\\"redPacketPrice\\\":1300,\\\"redPacketBatchCode\\\":\\\"1565108b2d614855\\\"}\",\"serviceCode\":\"lyksc7aq36aedndk\",\"parentOrderCode\":\"20240710144048283849009350\",\"orderFee\":\"2500\",\"couponSettlementFee\":\"0\",\"platform\":\"10\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"**********\",\"businessValues\":\"0\",\"centerCode\":\"2024071090002342051\",\"autoRenew\":\"3\",\"startTime\":\"2024-07-10 14:41:01\",\"thirdUid\":\"****************\",\"renewFlag\":\"1\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"1\",\"beforePaidSign\":\"1\",\"skuAmount\":\"1\",\"beforeDeadline\":\"2023-03-29 23:59:59\",\"payAccount\":\"****************\",\"refer\":\"{\\\"beforeDeadline\\\":\\\"2023-03-29 23:59:59\\\",\\\"beforePaidSign\\\":1,\\\"businessProperty\\\":{\\\"orderExt\\\":\\\"{\\\\\\\"rs\\\\\\\":1,\\\\\\\"showBlock\\\\\\\":0,\\\\\\\"priceActCode\\\\\\\":\\\\\\\"1565108b2d614855\\\\\\\",\\\\\\\"userCrowd\\\\\\\":\\\\\\\"expiry_180\\\\\\\",\\\\\\\"topGiftList\\\\\\\":[{\\\\\\\"code\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"type\\\\\\\":0},{\\\\\\\"code\\\\\\\":\\\\\\\"sku_197774104155332665\\\\\\\",\\\\\\\"type\\\\\\\":1}],\\\\\\\"ruleUserCrowd\\\\\\\":\\\\\\\"1;expire_365,58;other,13;new,56;expire_180\\\\\\\",\\\\\\\"release_ltv12\\\\\\\":62.**************,\\\\\\\"test_ltv12\\\\\\\":62.**************,\\\\\\\"receiveStatus\\\\\\\":2}\\\",\\\"priceActCodeActual\\\":\\\"1565108b2d614855\\\",\\\"redPacketCode\\\":\\\"20240710144028331992102919936441\\\",\\\"redPacketPrice\\\":1300,\\\"redPacketBatchCode\\\":\\\"1565108b2d614855\\\"},\\\"discountDetail\\\":{\\\"buyerPayAmount\\\":1200},\\\"merchantNo\\\":\\\"****************\\\",\\\"phoneNum\\\":\\\"375fe6470c7cefef083bd6c0a876ac6d\\\",\\\"strategy\\\":\\\"VIP_PLUS\\\",\\\"systemProperty\\\":{\\\"traceId\\\":\\\"fb62c4fcef0306b201909b5f88b400ac\\\",\\\"spanId\\\":\\\"f2f9dce3e20936c2\\\",\\\"parentSpanId\\\":\\\"01909b5f88b400ac\\\"},\\\"wechatBankCoupon\\\":{}}\",\"createTime\":\"2024-07-10 14:40:49\",\"payTypeCategory\":\"1\",\"orderCode\":\"202407101440482838490093500002\",\"payChannel\":\"1\",\"endTime\":\"2024-08-10 23:59:59\",\"parentRealFee\":\"1200\",\"centerPayService\":\"\",\"gateway\":\"28505\"}";
        MessageExt messageExt = new MessageExt();
        messageExt.setMsgId(RandomStringUtils.randomAlphanumeric(20));
        messageExt.setBody(msgBody.getBytes(StandardCharsets.UTF_8.displayName()));
        vipAutoRenewRMQMsgProcessor.onMessage(messageExt);
    }

    @Test
    public void appleSignPay() throws Exception {
        String msgBody = "{\"payTime\":\"2023-03-14 21:44:51\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"pid\":\"a0226bd958843452\",\"phoneNum\":\"***********\",\"type\":\"1\",\"deviceId\":\"cdafae02c468302dc437127beee84c8b1112\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"304\",\"giftActCode\":\"iqiyi_vip_iphone_video_autorenew_12m_218yuan\",\"msgtype\":\"7\",\"skuId\":\"sku_177079571906272349\",\"productId\":\"4\",\"tradeNo\":\"202202171017580698876\",\"fr_version\":\"FromCasher=1&mod=CN&d=cdafae02c468302dc437127beee84c8b1112&v=13.1.5&k=8e48946f144759d86a50075555fd5862&login=1_1&dfp=8779941688ccb84ca5aba51cdbf69b4a14c1a47c7e940439def32fe27ed9681791\",\"tradeCode\":\"300001389244344\",\"settlementFee\":\"17800\",\"renewType\":\"2001612\",\"orderRealFee\":\"17800\",\"centerPayType\":\"APPLEIAPDUT\",\"actCode\":\"iqiyi_vip_iphone_video_autorenew_12m_218yuan\",\"expireTime\":\"2024-03-14 21:44:40.0\",\"sourceType\":\"\",\"fc\":\"9070f015eca9d7dc\",\"aid\":\"null\",\"platformCode\":\"bb35a104d95490f6\",\"productSubtype\":\"1\",\"status\":\"1\",\"couponFee\":\"0\",\"signOrderCode\":\"202202171017580698876\",\"serviceCode\":\"lyksc7aq36aedndk\",\"orderFee\":\"21800\",\"couponSettlementFee\":\"0\",\"platform\":\"12\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"1301433258\",\"centerCode\":\"2023031490003919298\",\"autoRenew\":\"3\",\"startTime\":\"2023-03-14 21:44:52\",\"thirdUid\":\"\",\"renewFlag\":\"1\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"12\",\"beforePaidSign\":\"1\",\"skuAmount\":\"1\",\"beforeDeadline\":\"2023-02-17 23:59:59\",\"refer\":\"{\\\"actCode\\\":\\\"iqiyi_vip_iphone_video_autorenew_12m_218yuan\\\",\\\"appId\\\":\\\"iqiyi_vip_iphone_video_autorenew_12m_218yuan\\\",\\\"beforeDeadline\\\":\\\"2023-02-17 23:59:59\\\",\\\"beforePaidSign\\\":1,\\\"bundleID\\\":\\\"com.qiyi.iphone\\\",\\\"businessProperty\\\":{},\\\"expireTime\\\":*************,\\\"merchantNo\\\":\\\"aiqiyi\\\",\\\"offerId\\\":\\\"autorenew12m178\\\",\\\"phoneNum\\\":\\\"***********\\\",\\\"signOrderCode\\\":\\\"202202171017580698876\\\",\\\"strategy\\\":\\\"VIP_PLUS\\\",\\\"wechatBankCoupon\\\":{}}\",\"createTime\":\"2023-03-14 21:44:50\",\"payTypeCategory\":\"1\",\"orderCode\":\"20230314214450882919003293\",\"payChannel\":\"9\",\"endTime\":\"2024-03-14 23:59:59\",\"centerPayService\":\"446\",\"gateway\":\"11401\"}";

        MessageExt messageExt = new MessageExt();
        messageExt.setMsgId(RandomStringUtils.randomAlphanumeric(20));
        messageExt.setBody(msgBody.getBytes(StandardCharsets.UTF_8.displayName()));
        vipAutoRenewRMQMsgProcessor.onMessage(messageExt);
    }

    @Test
    public void appleDut() throws Exception {
        String msgBody = "{\"payTime\":\"2023-03-14 21:44:51\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"pid\":\"a0226bd958843452\",\"phoneNum\":\"***********\",\"type\":\"1\",\"deviceId\":\"cdafae02c468302dc437127beee84c8b1112\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"304\",\"giftActCode\":\"iqiyi_vip_iphone_video_autorenew_12m_218yuan\",\"msgtype\":\"7\",\"skuId\":\"sku_177079571906272349\",\"productId\":\"4\",\"tradeNo\":\"202202171017580698876\",\"fr_version\":\"FromCasher=1&mod=CN&d=cdafae02c468302dc437127beee84c8b1112&v=13.1.5&k=8e48946f144759d86a50075555fd5862&login=1_1&dfp=8779941688ccb84ca5aba51cdbf69b4a14c1a47c7e940439def32fe27ed9681791\",\"tradeCode\":\"300001389244344\",\"settlementFee\":\"17800\",\"renewType\":\"2001612\",\"orderRealFee\":\"17800\",\"centerPayType\":\"APPLEIAPDUT\",\"actCode\":\"iqiyi_vip_iphone_video_autorenew_12m_218yuan\",\"expireTime\":\"2024-03-14 21:44:40.0\",\"sourceType\":\"\",\"fc\":\"9070f015eca9d7dc\",\"aid\":\"null\",\"platformCode\":\"bb35a104d95490f6\",\"productSubtype\":\"1\",\"status\":\"1\",\"couponFee\":\"0\",\"signOrderCode\":\"202202171017580698876\",\"serviceCode\":\"lyksc7aq36aedndk\",\"orderFee\":\"21800\",\"couponSettlementFee\":\"0\",\"platform\":\"12\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"1301433258\",\"centerCode\":\"2023031490003919298\",\"autoRenew\":\"2\",\"startTime\":\"2023-03-14 21:44:52\",\"thirdUid\":\"\",\"renewFlag\":\"1\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"12\",\"beforePaidSign\":\"1\",\"skuAmount\":\"1\",\"beforeDeadline\":\"2023-02-17 23:59:59\",\"refer\":\"{\\\"actCode\\\":\\\"iqiyi_vip_iphone_video_autorenew_12m_218yuan\\\",\\\"appId\\\":\\\"iqiyi_vip_iphone_video_autorenew_12m_218yuan\\\",\\\"beforeDeadline\\\":\\\"2023-02-17 23:59:59\\\",\\\"beforePaidSign\\\":1,\\\"bundleID\\\":\\\"com.qiyi.iphone\\\",\\\"businessProperty\\\":{},\\\"expireTime\\\":*************,\\\"merchantNo\\\":\\\"aiqiyi\\\",\\\"offerId\\\":\\\"autorenew12m178\\\",\\\"phoneNum\\\":\\\"***********\\\",\\\"signOrderCode\\\":\\\"202202171017580698876\\\",\\\"strategy\\\":\\\"VIP_PLUS\\\",\\\"wechatBankCoupon\\\":{}}\",\"createTime\":\"2023-03-14 21:44:50\",\"payTypeCategory\":\"1\",\"orderCode\":\"20230314214450882919003293\",\"payChannel\":\"9\",\"endTime\":\"2024-03-14 23:59:59\",\"centerPayService\":\"446\",\"gateway\":\"11401\"}";

        MessageExt messageExt = new MessageExt();
        messageExt.setMsgId(RandomStringUtils.randomAlphanumeric(20));
        messageExt.setBody(msgBody.getBytes(StandardCharsets.UTF_8.displayName()));
        confirmRMQMsgProcessor.onMessage(messageExt);
    }

    @Test
    public void testPaidSignOrderMsgProcessor() {
        AutorenewRequest request = new AutorenewRequest();
        request.setSkuId("sku_540174261717821496");
        request.setDutType(2101101);
        request.setAgreementType(1);
        request.setAutoRenew("3");
        boolean b = paidSignOrderMsgProcessor.noNeedProcessAgreementType(request);
    }
}
