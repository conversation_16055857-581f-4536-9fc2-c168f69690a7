<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
    </parent>

    <groupId>com.qiyi</groupId>
    <artifactId>vip-autorenew</artifactId>
    <version>2.3.65</version>
    <name>QIYI vip-autorenew</name>
    <packaging>pom</packaging>
    <url>http://www.qiyi.com</url>


    <modules>
        <module>autorenew-utils</module>
        <module>autorenew-core</module>
        <module>autorenew-api</module>
        <module>autorenew-worker</module>
        <module>autorenew-job</module>
        <module>autorenew-task</module>
        <module>autorenew-message</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <vip-utils.version>0.1.1</vip-utils.version>
        <vip-commons.version>1.0.91</vip-commons.version>
        <hutool-all.version>5.8.26</hutool-all.version>
        <dataservice-client.version>1.0.1</dataservice-client.version>
       <rocketmq.springboot.starter.version>2.1.0-iqiyi-qdbm-1.1</rocketmq.springboot.starter.version>
        <config-client.version>3.15.7</config-client.version>
        <rover.version>6.1.0-iqiyi-7</rover.version>
        <mysql.version>8.0.31</mysql.version>
        <hystrix.version>1.5.12</hystrix.version>
        <jackson2.databind.version>2.13.4</jackson2.databind.version>
        <slf4j.version>1.7.2</slf4j.version>
        <powermock.version>2.0.2</powermock.version>
        <sharding-jdbc.version>2.0.3</sharding-jdbc.version>
        <logback.version>1.2.3</logback.version>
        <order.dal.version>1.5.52</order.dal.version>
        <spring.orm.version>4.1.7.RELEASE</spring.orm.version>
        <hibernate.version>3.6.10.Final</hibernate.version>
        <smart-jedis.version>3.7.0-R.1.18-03</smart-jedis.version>
        <mybatis.version>2.3.0</mybatis.version>
        <curator.version>2.10.0</curator.version>
        <pay.info.client.version>1.3.30</pay.info.client.version>
        <retryer.version>2.0.0</retryer.version>
        <easyexcel.version>3.0.5</easyexcel.version>
        <uuid.center.version>1.0.7</uuid.center.version>
        <eagle.version>0.2.25-RELEASE</eagle.version>
        <vip-thread.version>1.0.7-RELEASE</vip-thread.version>
        <sentinel.version>1.8.0-iqiyi-4</sentinel.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.iqiyi.v</groupId>
                <artifactId>v-spring-cloud-netflix</artifactId>
                <version>v-1.4.2-boot-2.7.x</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
                <version>2.2.10.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-boot-starter-logging</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                </exclusions>
                <version>${mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.rholder</groupId>
                <artifactId>guava-retrying</artifactId>
                <version>${retryer.version}</version>
            </dependency>

            <dependency>
                <groupId>com.qiyi.vip</groupId>
                <artifactId>viptrade-pay-info-client</artifactId>
                <version>${pay.info.client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.36</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>1.2.5.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-core</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-annotations</artifactId>
                <version>3.5.6-Final</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-orm</artifactId>
                <version>${spring.orm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.vip.language.distribute</groupId>
                <artifactId>vip-language-distribute-client</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.qiyi.vip.trade</groupId>
                <artifactId>dataservice-client</artifactId>
                <version>${dataservice-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.qiyi.vip.commons</groupId>
                        <artifactId>vip-commons-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.qiyi.vip.commons</groupId>
                        <artifactId>vip-commons-web</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.iqiyi.config</groupId>
                <artifactId>config-client</artifactId>
                <version>${config-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${rover.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${rover.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.springboot.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>4.1.87.Final</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>3.15.5</version>
                <exclusions>
                    <exclusion>
                        <artifactId>netty-buffer</artifactId>
                        <groupId>io.netty</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>netty-codec</artifactId>
                        <groupId>io.netty</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>netty-codec-dns</artifactId>
                        <groupId>io.netty</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>netty-common</artifactId>
                        <groupId>io.netty</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>netty-handler</artifactId>
                        <groupId>io.netty</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>netty-resolver</artifactId>
                        <groupId>io.netty</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>netty-resolver-dns</artifactId>
                        <groupId>io.netty</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>netty-tcnative-boringssl-static</artifactId>
                        <groupId>io.netty</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>netty-transport</artifactId>
                        <groupId>io.netty</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.shardingjdbc</groupId>
                <artifactId>sharding-jdbc-core</artifactId>
                <version>${sharding-jdbc.version}</version>
            </dependency>
            <!--如果通过spring配置, 需要增加如下maven坐标-->
            <dependency>
                <groupId>io.shardingjdbc</groupId>
                <artifactId>sharding-jdbc-core-spring-namespace</artifactId>
                <version>${sharding-jdbc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.db</groupId>
                <artifactId>smart-jedis</artifactId>
                <version>${smart-jedis.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.prometheus</groupId>
                        <artifactId>simpleclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.iqiyi.config</groupId>
                        <artifactId>config-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.11.1</version>
            </dependency>

            <!-- 核心依赖，必须引入 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-core</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- 簇点链路功能 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-transport-simple-http</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- 配置中心动态规则管理 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-apollo</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- 对接全链路平台Prometheus指标监控 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-metric-prometheus</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- sentinel 热点参数限流必须引入 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-parameter-flow-control</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- sentinel切面，配合@SentinelResource注解使用 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-annotation-aspectj</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-apache-httpclient-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- Spring WebMvc jar引入 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-spring-webmvc-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.6</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jcl</artifactId>
                <version>5.1.8.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>3.1.0</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>1.8.10</version>
            </dependency>

            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.2.15</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.17.0</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.2</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.9.4</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.16.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.16.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>30.0-jre</version>
            </dependency>
            <dependency>
                <groupId>apache-httpclient</groupId>
                <artifactId>commons-httpclient</artifactId>
                <version>3.1</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.12</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.76</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.0</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson2.databind.version}</version>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.vip</groupId>
                <artifactId>vip-utils</artifactId>
                <version>${vip-utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qiyi.usercloud</groupId>
                <artifactId>enigma-uid</artifactId>
                <version>1.1.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.kit</groupId>
                <artifactId>http-client</artifactId>
                <version>1.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.qiyi.vip.commons</groupId>
                <artifactId>vip-commons-util</artifactId>
                <version>${vip-commons.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qiyi.vip.commons</groupId>
                <artifactId>vip-commons-dal</artifactId>
                <version>${vip-commons.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qiyi.vip.commons</groupId>
                <artifactId>vip-commons-web</artifactId>
                <version>${vip-commons.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>lombok</artifactId>
                        <groupId>org.projectlombok</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.springfox</groupId>
                        <artifactId>springfox-swagger-ui</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.springfox</groupId>
                        <artifactId>springfox-swagger2</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qiyi.vip.commons</groupId>
                <artifactId>vip-commons-component</artifactId>
                <version>${vip-commons.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.github.briandilley.jsonrpc4j</groupId>
                        <artifactId>jsonrpc4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-access</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.12</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.easymock</groupId>
                <artifactId>easymock</artifactId>
                <version>3.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>jstl</artifactId>
                <version>1.2</version>
            </dependency>
            <dependency>
                <groupId>taglibs</groupId>
                <artifactId>standard</artifactId>
                <version>1.1.2</version>
            </dependency>
            <dependency>
                <groupId>atg.taglib.json</groupId>
                <artifactId>json-taglib</artifactId>
                <version>0.4.1</version>
            </dependency>

            <dependency>
                <groupId>com.iqiyi.vip</groupId>
                <artifactId>order-dal</artifactId>
                <version>${order.dal.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-jdbc</artifactId>
                <version>1.0.9.RELEASE</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-client</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.vip</groupId>
                <artifactId>vip-uuid-center-starter</artifactId>
                <version>${uuid.center.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-test</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.v</groupId>
                <artifactId>v-spring-boot-starter-eagle</artifactId>
                <version>${eagle.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_pushgateway</artifactId>
                <version>0.15.0</version>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.vip</groupId>
                <artifactId>vip-threadpool-core</artifactId>
                <version>${vip-thread.version}</version>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.vip</groupId>
                <artifactId>commodity-spring-boot-starter</artifactId>
                <version>0.0.4-2-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce-core</artifactId>
                <version>6.1.10.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis-lettuce</artifactId>
                <version>2.7.3</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>3.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.db</groupId>
                <artifactId>mysql-dal-core</artifactId>
                <version>1.2.5</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <!-- 注意以下两项的参数配置 -->
                    <source>1.8</source>
                    <target>1.8</target>
                    <!-- 编译参数写在arg内 -->
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <!--  cobertura插件 ,取消不需要计算覆盖率的类  -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <configuration>
                    <instrumentation>
                        <excludes>
                            <exclude>**/entity/**/*.class</exclude>
                        </excludes>
                    </instrumentation>
                </configuration>
                <version>2.4</version>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.7.9</version>
                <configuration>
                    <address>*************</address>
                    <port>9100</port>
                    <reset>false</reset>
                    <append>true</append>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>iqiyi-maven-boss</id>
            <name>iqiyi-maven-boss</name>
            <url>http://jfrog.cloud.qiyi.domain/iqiyi-maven-boss</url>
        </repository>
        <repository>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>libs-release</id>
            <name>libs-release</name>
            <url>http://jfrog.cloud.qiyi.domain/libs-release</url>
        </repository>
        <repository>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <id>libs-snapshot</id>
            <name>libs-snapshot</name>
            <url>http://jfrog.cloud.qiyi.domain/libs-snapshot</url>
        </repository>
        <repository>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>cloudservice-releases</id>
            <name>cloudservice-releases</name>
            <url>http://jfrog.cloud.qiyi.domain:80/iqiyi-maven-cloudservice</url>
        </repository>
        <repository>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <id>cloudservice-snapshots</id>
            <name>cloudservice-snapshots</name>
            <url>http://jfrog.cloud.qiyi.domain:80/iqiyi-maven-cloudservice</url>
        </repository>
        <repository>
            <id>iqiyi-maven-cloudservice</id>
            <name>iqiyi-maven-cloudservice</name>
            <url>http://jfrog.cloud.qiyi.domain/iqiyi-maven-cloudservice</url>
        </repository>
        <repository>
            <id>iqiyi-jfrog</id>
            <url>http://jfrog.cloud.qiyi.domain/iqiyi-maven-cloudservice</url>
        </repository>
    </repositories>
</project>
