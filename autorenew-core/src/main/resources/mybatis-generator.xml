<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="mybatisGenerator" targetRuntime="MyBatis3">
        <commentGenerator>
            <!-- 是否不生成注释 -->
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <!--数据库连接 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="*********************************************************************************************"
                        userId="vip_test" password="rg_z_6UF)w=Y">
        </jdbcConnection>
        <!-- 分库连接串 -->
<!--        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"-->
<!--                        connectionURL="***********************************************************************************************"-->
<!--                        userId="bosstest" password="bosstest">-->
<!--        </jdbcConnection>-->

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false" />
        </javaTypeResolver>

        <!-- 生成model模型 -->
        <javaModelGenerator targetPackage="com.qiyi.vip.trade.autorenew.domain" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>
        <!--对应的xml mapper文件  -->
        <sqlMapGenerator targetPackage="sqlmap" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.qiyi.vip.trade.autorenew.mapper" targetProject="src/main/java">
            <property name="enableSubPackages" value="true" />
        </javaClientGenerator>

<!--        <table tableName="agreement_template" domainObjectName="AgreementTemplate" enableCountByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>-->
<!--        <table tableName="agreement_temp_price" domainObjectName="AgreementTempPrice" enableCountByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>-->
<!--        <table tableName="agreement_temp_settlement_rule" domainObjectName="AgreementTempSettlementRule" enableCountByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>-->
<!--        <table tableName="agreement_temp_pay_type" domainObjectName="AgreementTempPayType" enableCountByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>-->
<!--        <table tableName="agreement_dut_mkt" domainObjectName="AgreementDutMkt" enableCountByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>-->
<!--        <table tableName="agreement_cancel_restriction" domainObjectName="AgreementCancelRestriction" enableCountByExample="false"-->
<!--            enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>-->

        <table tableName="agreement_material" domainObjectName="AgreementMaterial" enableCountByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>
<!--        <table tableName="boss_dut_renew_set_log_000" domainObjectName="UserAgreementSetLogNew" enableCountByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>-->
<!--        <table tableName="boss_dut_renew_log_000" domainObjectName="UserAgreementRenewLogNew" enableCountByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>-->
<!--        <table tableName="user_agreement_operate_log_000" domainObjectName="UserAgreementOperateLog" enableCountByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>-->

    </context>
</generatorConfiguration>
