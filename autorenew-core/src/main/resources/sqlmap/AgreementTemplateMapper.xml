<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AgreementTemplateMapper">
  <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AgreementTemplate">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="source_vip_type" jdbcType="INTEGER" property="sourceVipType"/>
    <result column="vip_type" jdbcType="INTEGER" property="vipType" />
    <result column="pid" jdbcType="VARCHAR" property="pid" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="complete_order_pid" jdbcType="VARCHAR" property="completeOrderPid" />
    <result column="complete_order_skuid" jdbcType="VARCHAR" property="completeOrderSkuId" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="periods" jdbcType="INTEGER" property="periods" />
    <result column="discount_periods" jdbcType="INTEGER" property="discountPeriods" />
    <result column="period_duration" jdbcType="INTEGER" property="periodDuration" />
    <result column="period_unit" jdbcType="TINYINT" property="periodUnit" />
    <result column="pricing_strategy" jdbcType="TINYINT" property="pricingStrategy" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="attributes" jdbcType="VARCHAR" property="attributes" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="period_restriction_type" jdbcType="TINYINT" property="periodRestrictionType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, code, name, type, source_vip_type, vip_type, pid, sku_id
      , complete_order_pid, complete_order_skuid, amount, periods, discount_periods
      , period_duration, period_unit, pricing_strategy, status, attributes, create_time, update_time, period_restriction_type
  </sql>

  <select id="selectByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from agreement_template
    where code = #{code,jdbcType=VARCHAR}
  </select>

</mapper>