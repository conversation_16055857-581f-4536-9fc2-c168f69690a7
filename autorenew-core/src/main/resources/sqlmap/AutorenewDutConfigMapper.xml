<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AutorenewDutConfigMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AutorenewDutConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="job_type" jdbcType="TINYINT" property="jobType"/>
        <result column="source_vip_type" jdbcType="TINYINT" property="sourceVipType"/>
        <result column="vip_type" jdbcType="TINYINT" property="vipType"/>
        <result column="pcode" jdbcType="VARCHAR" property="pcode"/>
        <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
        <result column="service_code" jdbcType="VARCHAR" property="serviceCode"/>
        <result column="dut_types" jdbcType="VARCHAR" property="dutTypes"/>
        <result column="advance_days" jdbcType="INTEGER" property="advanceDays"/>
        <result column="exec_time" jdbcType="VARCHAR" property="execTime"/>
        <result column="exec_duration" jdbcType="INTEGER" property="execDuration"/>
        <result column="remind_exec_time" jdbcType="VARCHAR" property="remindExecTime"/>
        <result column="remind_exec_duration" jdbcType="INTEGER" property="remindExecDuration"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="agreement_type" jdbcType="TINYINT" property="agreementType"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, job_type, source_vip_type, vip_type, pcode, sku_id, service_code, dut_types, advance_days,
        exec_time, exec_duration, remind_exec_time, remind_exec_duration, status, create_time,
        update_time, agreement_type
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_dut_config
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="list" resultMap="BaseResultMap" parameterType="com.qiyi.vip.trade.autorenew.domain.AutorenewDutConfig">
        select
        <include refid="Base_Column_List"/>
        from autorenew_dut_config
        <where>
            <if test="jobType != null">
                and job_type = #{jobType}
            </if>
            <if test="sourceVipType != null">
                and source_vip_type = #{sourceVipType}
            </if>
            <if test="vipType != null">
                and vip_type = #{vipType}
            </if>
            <if test="pcode != null">
                and pcode = #{pcode}
            </if>
            <if test="skuId != null">
                and sku_id = #{skuId}
            </if>
            <if test="serviceCode != null">
                and service_code = #{serviceCode}
            </if>
            <if test="dutTypes != null">
                and dut_types = #{dutTypes}
            </if>
            <if test="advanceDays != null">
                and advance_days = #{advanceDays}
            </if>
            <if test="execTime != null">
                and exec_time = #{execTime}
            </if>
            <if test="execDuration != null">
                and exec_duration = #{execDuration}
            </if>
            <if test="remindExecTime != null">
                and remind_exec_time = #{remindExecTime}
            </if>
            <if test="remindExecDuration != null">
                and remind_exec_duration = #{remindExecDuration}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="agreementType != null">
                and agreement_type = #{agreementType}
            </if>
        </where>
    </select>

</mapper>