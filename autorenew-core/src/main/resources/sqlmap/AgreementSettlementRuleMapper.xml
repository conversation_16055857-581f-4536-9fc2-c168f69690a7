<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AgreementSettlementRuleMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AgreementSettlementRule">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="agreement_no" jdbcType="INTEGER" property="agreementNo"/>
        <result column="pid" jdbcType="VARCHAR" property="pid"/>
        <result column="exemption_period" jdbcType="INTEGER" property="exemptionPeriod"/>
        <result column="promise_periods" jdbcType="INTEGER" property="promisePeriods"/>
        <result column="within_period_settlement_strategy" jdbcType="TINYINT" property="withinPeriodSettlementStrategy"/>
        <result column="within_period_settlement_fee" jdbcType="INTEGER" property="withinPeriodSettlementFee"/>
        <result column="without_period_settlement_strategy" jdbcType="TINYINT" property="withoutPeriodSettlementStrategy"/>
        <result column="without_period_settlement_fee" jdbcType="INTEGER" property="withoutPeriodSettlementFee"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, agreement_no, pid, exemption_period, promise_periods, within_period_settlement_strategy,
        within_period_settlement_fee, without_period_settlement_strategy, without_period_settlement_fee,
        status, create_time, update_time
    </sql>
    <select id="selectByAgreementNo" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_settlement_rule
        where agreement_no = #{agreementNo,jdbcType=INTEGER}
    </select>

</mapper>