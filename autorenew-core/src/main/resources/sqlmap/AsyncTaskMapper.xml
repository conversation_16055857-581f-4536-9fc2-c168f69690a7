<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AsyncTaskMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AsyncTask">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="data" jdbcType="LONGVARCHAR" property="data"/>
        <result column="classname" jdbcType="VARCHAR" property="className"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="pooltype" jdbcType="BIGINT" property="poolType"/>
        <result column="priority" jdbcType="BIGINT" property="priority"/>
        <result column="inqueue" jdbcType="INTEGER" property="inQueue"/>
        <result column="timerrun_at" jdbcType="TIMESTAMP" property="timerRunAt"/>
        <result column="cronexpression" jdbcType="VARCHAR" property="cronExpression"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="task_ip" jdbcType="VARCHAR" property="taskIp"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <result column="support_delay" jdbcType="INTEGER" property="supportDelay"/>
    </resultMap>

    <sql id="Base_Column_List">
      id, classname, created_at, pooltype, priority, inqueue, timerrun_at, cronexpression,
      update_time, task_ip, `data`, `task_id`, support_delay
    </sql>

    <insert id="insertSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.AsyncTask" keyProperty="id" useGeneratedKeys="true">
        insert into boss_async_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="className != null">
                classname,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="poolType != null">
                pooltype,
            </if>
            <if test="priority != null">
                priority,
            </if>
            <if test="inQueue != null">
                inqueue,
            </if>
            <if test="timerRunAt != null">
                timerrun_at,
            </if>
            <if test="cronExpression != null">
                cronexpression,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="taskIp != null">
                task_ip,
            </if>
            <if test="data != null">
                data,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="supportDelay != null">
                support_delay,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="className != null">
                #{className,jdbcType=VARCHAR},
            </if>
            <if test="createdAt != null">
                #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="poolType != null">
                #{poolType,jdbcType=BIGINT},
            </if>
            <if test="priority != null">
                #{priority,jdbcType=BIGINT},
            </if>
            <if test="inQueue != null">
                #{inQueue,jdbcType=INTEGER},
            </if>
            <if test="timerRunAt != null">
                #{timerRunAt,jdbcType=TIMESTAMP},
            </if>
            <if test="cronExpression != null">
                #{cronExpression,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="taskIp != null">
                #{taskIp,jdbcType=VARCHAR},
            </if>
            <if test="data != null">
                #{data,jdbcType=LONGVARCHAR},
            </if>
            <if test="taskId != null">
                #{taskId},
            </if>
            <if test="supportDelay != null">
                #{supportDelay},
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
      delete from boss_async_task
      where id = #{id,jdbcType=BIGINT}
    </delete>

    <update id="updateByPrimaryKeySelective" parameterType="com.qiyi.vip.trade.autorenew.domain.AsyncTask">
        update boss_async_task
        <set>
            <if test="className != null">
                classname = #{className,jdbcType=VARCHAR},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="poolType != null">
                pooltype = #{poolType,jdbcType=BIGINT},
            </if>
            <if test="priority != null">
                priority = #{priority,jdbcType=BIGINT},
            </if>
            <if test="inQueue != null">
                inqueue = #{inQueue,jdbcType=INTEGER},
            </if>
            <if test="timerRunAt != null">
                timerrun_at = #{timerRunAt,jdbcType=TIMESTAMP},
            </if>
            <if test="cronExpression != null">
                cronexpression = #{cronExpression,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="taskIp != null">
                task_ip = #{taskIp,jdbcType=VARCHAR},
            </if>
            <if test="data != null">
                `data` = #{data,jdbcType=LONGVARCHAR},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="supportDelay != null">
                support_delay = #{supportDelay},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateAllAsyncTaskStatus" parameterType="java.lang.Integer">
      update boss_async_task set inqueue = 0, update_time=CURRENT_TIMESTAMP() where inqueue = 1 and pooltype = #{poolType}
    </update>

    <update id="batchDelayTask">
          update boss_async_task set timerrun_at = #{taskTimeRunAt}
          <where>
              id in
              <foreach collection="asyncTaskIds" item="taskId" open="(" close=")" separator=",">
                  #{taskId}
              </foreach>
          </where>
    </update>

    <update id="makeTaskProcessing" parameterType="java.lang.Long">
        update boss_async_task set update_time=CURRENT_TIMESTAMP(), inqueue = 1 where id = #{id} and inqueue = 0;
    </update>

    <update id="makeTaskUnProcess" parameterType="java.lang.Long">
        update boss_async_task set update_time=CURRENT_TIMESTAMP(), inqueue = 0 where id = #{id} and inqueue = 1;
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_async_task
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByTaskId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_async_task
        where task_id = #{taskId,jdbcType=VARCHAR}
    </select>

    <select id="listPageSelective" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_async_task
        <where>
            <if test="asyncTask.className != null">
                and classname = #{asyncTask.className}
            </if>
            <if test="asyncTask.createdAt != null">
                and created_at = #{asyncTask.createdAt}
            </if>
            <if test="asyncTask.poolType != null">
                and pooltype = #{asyncTask.poolType}
            </if>
            <if test="asyncTask.priority != null">
                and priority = #{asyncTask.priority}
            </if>
            <if test="asyncTask.inQueue != null">
                and inqueue = #{asyncTask.inQueue}
            </if>
            <if test="asyncTask.timerRunAt != null">
                and timerrun_at = #{asyncTask.timerRunAt}
            </if>
            <if test="asyncTask.cronExpression != null">
                and cronexpression = #{asyncTask.cronExpression}
            </if>
            <if test="asyncTask.updateTime != null">
                and update_time = #{asyncTask.updateTime}
            </if>
            <if test="asyncTask.taskIp != null">
                and task_ip = #{asyncTask.taskIp}
            </if>
            <if test="asyncTask.data != null">
                and `data` = #{asyncTask.data}
            </if>
            <if test="asyncTask.taskId != null">
                and task_id = #{asyncTask.taskId}
            </if>
        </where>
        order by id
        limit #{offSet}, #{pageSize}
    </select>

    <select id="getList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_async_task
        <where>
              <![CDATA[ timerrun_at <= CURRENT_TIMESTAMP() ]]>
          <if test="startTime != null">
              <![CDATA[ and created_at >= #{startTime} ]]>
          </if>
          <if test="endTime != null">
            <![CDATA[ and created_at <= #{endTime} ]]>
          </if>
          <if test="poolType != null">
            and pooltype = #{poolType}
          </if>
          <if test="inQueue != -1">
              and inqueue = #{inQueue}
          </if>
        </where>
        <if test="count > 0">
            limit #{count}
        </if>
    </select>

    <select id="getZhiMaGoTask" parameterType="map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_async_task
        where classname = 'com.qiyi.boss.async.task.AgreementRenewTask' and inqueue = 0
        <![CDATA[ and created_at >= #{startTime} ]]>
        <![CDATA[ and created_at < #{endTime} ]]>
        <![CDATA[ and timerrun_at >= #{endTime} ]]>
    </select>

    <select id="getZhiMaGoTaskByUid" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_async_task
        where classname = 'com.qiyi.boss.async.task.AgreementRenewTask' and inqueue = 0
        and data like concat('%userId=', #{userId}, '&amp;%')
    </select>

</mapper>