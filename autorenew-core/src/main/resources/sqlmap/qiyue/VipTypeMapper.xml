<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.qiyue.mapper.VipTypeMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.qiyue.domain.VipType">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="i18n" jdbcType="VARCHAR" property="i18n"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, description, code, i18n, create_time, update_time
    </sql>

    <insert id="insertSelective" parameterType="com.qiyi.vip.trade.qiyue.domain.VipType" keyProperty="id" useGeneratedKeys="true">
        insert into qiyue_vip_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                name,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="i18n != null">
                i18n,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="i18n != null">
                #{i18n,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from qiyue_vip_type
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <update id="updateByPrimaryKeySelective" parameterType="com.qiyi.vip.trade.qiyue.domain.VipType">
        update qiyue_vip_type
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="i18n != null">
                code = #{i18n,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from qiyue_vip_type
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getVipTypeByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from qiyue_vip_type
        where code = #{code}
    </select>

    <select id="getVipTypeById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from qiyue_vip_type
        where id = #{id}
    </select>
    <select id="getAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from qiyue_vip_type
    </select>
</mapper>