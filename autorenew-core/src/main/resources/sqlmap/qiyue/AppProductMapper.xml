<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.qiyue.mapper.AppProductMapper">
  <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.qiyue.domain.AppProduct">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="price" jdbcType="INTEGER" property="price" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="origin_price" jdbcType="INTEGER" property="originPrice" />
    <result column="platform_id" jdbcType="INTEGER" property="platformId" />
    <result column="bid" jdbcType="INTEGER" property="bid" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="auto_renew" jdbcType="INTEGER" property="autoRenew" />
    <result column="first_free" jdbcType="INTEGER" property="firstFree" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
    <result column="operator" jdbcType="BIGINT" property="operator" />
    <result column="code" jdbcType="VARCHAR" property="code" />
  </resultMap>

  <sql id="Base_Column_List">
    id, name, description, type, app_id, product_id, price, quantity, status, origin_price, 
    platform_id, bid, update_time, auto_renew, first_free, product_code, platform_code, 
    operator, code
  </sql>

  <insert id="insertSelective" parameterType="com.qiyi.vip.trade.qiyue.domain.AppProduct" useGeneratedKeys="true" keyProperty="id">
    insert into boss_app_product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="originPrice != null">
        origin_price,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="bid != null">
        bid,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="autoRenew != null">
        auto_renew,
      </if>
      <if test="firstFree != null">
        first_free,
      </if>
      <if test="productCode != null">
        product_code,
      </if>
      <if test="platformCode != null">
        platform_code,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="code != null">
        code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=INTEGER},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="originPrice != null">
        #{originPrice,jdbcType=INTEGER},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=INTEGER},
      </if>
      <if test="bid != null">
        #{bid,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="autoRenew != null">
        #{autoRenew,jdbcType=INTEGER},
      </if>
      <if test="firstFree != null">
        #{firstFree,jdbcType=INTEGER},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="platformCode != null">
        #{platformCode,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=BIGINT},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from boss_app_product
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <update id="updateByPrimaryKeySelective" parameterType="com.qiyi.vip.trade.qiyue.domain.AppProduct">
    update boss_app_product
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=INTEGER},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="originPrice != null">
        origin_price = #{originPrice,jdbcType=INTEGER},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=INTEGER},
      </if>
      <if test="bid != null">
        bid = #{bid,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="autoRenew != null">
        auto_renew = #{autoRenew,jdbcType=INTEGER},
      </if>
      <if test="firstFree != null">
        first_free = #{firstFree,jdbcType=INTEGER},
      </if>
      <if test="productCode != null">
        product_code = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="platformCode != null">
        platform_code = #{platformCode,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=BIGINT},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from boss_app_product
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="listByAppId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from boss_app_product
    where app_id = #{appId}
  </select>

</mapper>

