<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.qiyue.mapper.QiYuePlatformMapper">

    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.qiyue.domain.QiYuePlatform">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="corporation" jdbcType="VARCHAR" property="corporation"/>
        <result column="businessType" jdbcType="VARCHAR" property="businessType"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="tags" jdbcType="VARCHAR" property="tags"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, name, description, code, update_time, type, area, corporation, create_time
  </sql>

    <select id="getPlatformById" resultType="com.qiyi.vip.trade.qiyue.domain.QiYuePlatform">
        select <include refid="Base_Column_List"/> from qiyue_platform where id = #{id}
    </select>

    <select id="getPlatformByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from qiyue_platform where code = #{code, jdbcType=VARCHAR}
    </select>

</mapper>