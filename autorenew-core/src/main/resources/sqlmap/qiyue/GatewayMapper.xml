<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.qiyue.mapper.GatewayMapper">

    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.qiyue.domain.Gateway">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="create_time" jdbcType="DATE" property="createTime"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="channel_id" jdbcType="INTEGER" property="channelId"/>
        <result column="update_time" jdbcType="DATE" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, type, status, description, create_time, code, channel_id, update_time
    </sql>

    <select id="getGatewayById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from qiyue_gateway
        where id = #{id}
    </select>

    <select id="getGatewayByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from qiyue_gateway
        where code = #{code}
    </select>

</mapper>