<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.qiyue.mapper.QiyuePaymentTypeMapper">
  <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.qiyue.domain.QiyuePaymentType">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_chargeback" jdbcType="INTEGER" property="isChargeback" />
    <result column="is_chargeauto" jdbcType="INTEGER" property="isChargeauto" />
    <result column="is_background" jdbcType="INTEGER" property="isBackground" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="pay_center_code" jdbcType="VARCHAR" property="payCenterCode" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="is_support_sign" jdbcType="BIT" property="isSupportSign" />
    <result column="basic_pay_type_id" jdbcType="INTEGER" property="basicPayTypeId" />
    <result column="sign_pay_type_id" jdbcType="INTEGER" property="signPayTypeId" />
    <result column="pay_channel" jdbcType="INTEGER" property="payChannel" />
    <result column="pure_signing_pay_type_id" jdbcType="INTEGER" property="pureSigningPayTypeId" />
    <result column="properties" jdbcType="VARCHAR" property="properties" />
    <result column="is_support_password_free_sign" jdbcType="BIT" property="isSupportPasswordFreeSign" />
    <result column="refund_expire_offset" jdbcType="INTEGER" property="refundExpireOffset" />
    <result column="password_free_open_tips" jdbcType="VARCHAR" property="passwordFreeOpenTips" />
    <result column="dut_agreement_name" jdbcType="VARCHAR" property="dutAgreementName" />
    <result column="dut_agreement_url" jdbcType="VARCHAR" property="dutAgreementUrl" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, description, update_time, is_chargeback, is_chargeauto, is_background, 
    status, pay_center_code, type, is_support_sign, basic_pay_type_id, sign_pay_type_id, 
    pay_channel, pure_signing_pay_type_id, properties, is_support_password_free_sign, 
    refund_expire_offset, password_free_open_tips, dut_agreement_name, dut_agreement_url, 
    icon_url
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from qiyue_payment_type
    where id = #{id,jdbcType=INTEGER}
  </select>

</mapper>