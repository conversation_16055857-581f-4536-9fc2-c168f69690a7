<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.qiyue.mapper.BusinessMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.qiyue.domain.Business">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="introduction" jdbcType="VARCHAR" property="introduction"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="sign_key" jdbcType="VARCHAR" property="signKey"/>
        <result column="pay_return_url" jdbcType="VARCHAR" property="payReturnUrl"/>
        <result column="pay_notify_url" jdbcType="VARCHAR" property="payNotifyUrl"/>
        <result column="pay_page_desc" jdbcType="VARCHAR" property="payPageDesc"/>
        <result column="order_verify_url" jdbcType="VARCHAR" property="orderVerifyUrl"/>
        <result column="pay_center_partner_id" jdbcType="VARCHAR" property="payCenterPartnerId"/>
        <result column="partner_info" jdbcType="VARCHAR" property="partnerInfo"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, name, create_time, update_time, introduction, code, sign_key, pay_return_url, 
    pay_notify_url, pay_page_desc, order_verify_url, pay_center_partner_id, partner_info
  </sql>

    <select id="getBusinessById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from qiyue_business
        where id = #{id}
    </select>

    <select id="getBusinessByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from qiyue_business
        where code = #{code}
    </select>

</mapper>