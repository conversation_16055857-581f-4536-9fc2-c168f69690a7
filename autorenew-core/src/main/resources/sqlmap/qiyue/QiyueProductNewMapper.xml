<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.qiyue.mapper.QiyueProductNewMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.qiyue.domain.QiYueProductNew">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="price" jdbcType="INTEGER" property="price"/>
        <result column="period" jdbcType="INTEGER" property="period"/>
        <result column="period_unit" jdbcType="INTEGER" property="periodUnit"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="deadline" jdbcType="TIMESTAMP" property="deadline"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="support_type" jdbcType="VARCHAR" property="supportType"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="sub_type" jdbcType="INTEGER" property="subType"/>
        <result column="source_sub_type" jdbcType="INTEGER" property="sourceSubType"/>
        <result column="service_type" jdbcType="INTEGER" property="serviceType"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="time_type" jdbcType="INTEGER" property="timeType"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="support_exp" jdbcType="INTEGER" property="supportExp"/>
        <result column="original_price" jdbcType="INTEGER" property="originalPrice"/>
        <result column="source_vip_type_code" jdbcType="VARCHAR" property="sourceVipTypeCode"/>
        <result column="vip_type_code" jdbcType="VARCHAR" property="vipTypeCode"/>
        <result column="business_code" jdbcType="VARCHAR" property="businessCode"/>
        <result column="charge_type" jdbcType="INTEGER" property="chargeType"/>
        <result column="is_has_gift" jdbcType="INTEGER" property="isHasGift"/>
        <result column="currency_unit" jdbcType="VARCHAR" property="currencyUnit"/>
        <result column="pre_paid" jdbcType="TINYINT" property="prePaid"/>
        <result column="pay_page_desc" jdbcType="LONGVARCHAR" property="payPageDesc"/>
        <result column="h5_pay_page_desc" jdbcType="LONGVARCHAR" property="h5PayPageDesc"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, `name`, price, period, period_unit, `type`, status, deadline, url, support_type,
        code, sub_type, source_sub_type, service_type, update_time, time_type, area, support_exp,
        original_price, source_vip_type_code, vip_type_code, business_code, charge_type,
        is_has_gift, currency_unit, pre_paid, pay_page_desc, h5_pay_page_desc
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from qiyue_product_new
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="queryByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from qiyue_product_new
        where code = #{code}
    </select>

    <select id="queryQiyueProductsByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from qiyue_product_new
        <where>
            <if test="productIds != null and productIds.size() > 0">
                id in
                <foreach collection="productIds" item="productId" open="(" close=")" separator=",">
                    #{productId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryAllProduct" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from qiyue_product_new
    </select>

    <select id="queryByVipType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from qiyue_product_new
        <where>
            <if test="vipType != null">
                and sub_type = #{vipType}
            </if>
        </where>
    </select>

    <select id="selectRelatedDayProductCode" resultType="java.lang.String">
        <![CDATA[
        select code
        from qiyue_product_new bp
        join (select sub_type,source_sub_type from qiyue_product_new where code = #{code}) as t
        on (bp.sub_type = t.sub_type and ifnull(bp.source_sub_type,0) = ifnull(t.source_sub_type,0))
        where period_unit = 1 and period = 1 and status = 1 and deadline > now()
        order by id limit 1 ]]>
    </select>
</mapper>