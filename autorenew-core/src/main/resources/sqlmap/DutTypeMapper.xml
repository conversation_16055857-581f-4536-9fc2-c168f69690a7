<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.DutTypeMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.DutType">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="advance_days" jdbcType="DECIMAL" property="advanceDays"/>
        <result column="exec_time" jdbcType="VARCHAR" property="execTime"/>
        <result column="is_retry" jdbcType="INTEGER" property="isRetry"/>
        <result column="retry_strategy" jdbcType="VARCHAR" property="retryStrategy"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="pay_type" jdbcType="INTEGER" property="payType"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
      id, name, advance_days, exec_time, is_retry, retry_strategy, status, pay_type, update_time
    </sql>

    <insert id="insertSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.DutType" keyProperty="id" useGeneratedKeys="true">
        insert into boss_dut_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                name,
            </if>
            <if test="advanceDays != null">
                advance_days,
            </if>
            <if test="execTime != null">
                exec_time,
            </if>
            <if test="isRetry != null">
                is_retry,
            </if>
            <if test="retryStrategy != null">
                retry_strategy,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="payType != null">
                pay_type,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="advanceDays != null">
                #{advanceDays,jdbcType=DECIMAL},
            </if>
            <if test="execTime != null">
                #{execTime,jdbcType=VARCHAR},
            </if>
            <if test="isRetry != null">
                #{isRetry,jdbcType=INTEGER},
            </if>
            <if test="retryStrategy != null">
                #{retryStrategy,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="payType != null">
                #{payType,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
      delete from boss_dut_type
      where id = #{id,jdbcType=INTEGER}
    </delete>

    <update id="updateByPrimaryKeySelective" parameterType="com.qiyi.vip.trade.autorenew.domain.DutType">
        update boss_dut_type
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="advanceDays != null">
                advance_days = #{advanceDays,jdbcType=DECIMAL},
            </if>
            <if test="execTime != null">
                exec_time = #{execTime,jdbcType=VARCHAR},
            </if>
            <if test="isRetry != null">
                is_retry = #{isRetry,jdbcType=INTEGER},
            </if>
            <if test="retryStrategy != null">
                retry_strategy = #{retryStrategy,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="payType != null">
                pay_type = #{payType,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_type
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="findDutTypeByStatus" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_type
        where status = #{status}
    </select>

</mapper>