<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AutoRenewNodeLocationMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AutoRenewNodeLocation">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="location_key" jdbcType="VARCHAR" property="locationKey"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="platform" jdbcType="VARCHAR" property="platform"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="text" jdbcType="VARCHAR" property="text"/>
        <result column="sub_text" jdbcType="VARCHAR" property="subText"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="redirect_type" jdbcType="INTEGER" property="redirectType"/>
        <result column="redirect_url" jdbcType="VARCHAR" property="redirectUrl"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
      id, name, description, location_key, vip_type, platform, type, sort, url, text,
      sub_text, icon, redirect_type, redirect_url, status, create_time, update_time
    </sql>

    <insert id="insertSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.AutoRenewNodeLocation" keyProperty="id" useGeneratedKeys="true">
        insert into autorenew_node_location
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                name,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="locationKey != null">
                location_key,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="text != null">
                text,
            </if>
            <if test="subText != null">
                sub_text,
            </if>
            <if test="icon != null">
                icon,
            </if>
            <if test="redirectType != null">
                redirect_type,
            </if>
            <if test="redirectUrl != null">
                redirect_url,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="locationKey != null">
                #{locationKey,jdbcType=VARCHAR},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=INTEGER},
            </if>
            <if test="platform != null">
                #{platform,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="text != null">
                #{text,jdbcType=VARCHAR},
            </if>
            <if test="subText != null">
                #{subText,jdbcType=VARCHAR},
            </if>
            <if test="icon != null">
                #{icon,jdbcType=VARCHAR},
            </if>
            <if test="redirectType != null">
                #{redirectType,jdbcType=INTEGER},
            </if>
            <if test="redirectUrl != null">
                #{redirectUrl,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
      delete from autorenew_node_location
      where id = #{id,jdbcType=BIGINT}
    </delete>

    <update id="updateByPrimaryKeySelective" parameterType="com.qiyi.vip.trade.autorenew.domain.AutoRenewNodeLocation">
        update autorenew_node_location
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="locationKey != null">
                location_key = #{locationKey,jdbcType=VARCHAR},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=INTEGER},
            </if>
            <if test="platform != null">
                platform = #{platform,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="text != null">
                text = #{text,jdbcType=VARCHAR},
            </if>
            <if test="subText != null">
                sub_text = #{subText,jdbcType=VARCHAR},
            </if>
            <if test="icon != null">
                icon = #{icon,jdbcType=VARCHAR},
            </if>
            <if test="redirectType != null">
                redirect_type = #{redirectType,jdbcType=INTEGER},
            </if>
            <if test="redirectUrl != null">
                redirect_url = #{redirectUrl,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_node_location
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="listSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.AutoRenewNodeLocation" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_node_location
        <where>
            <if test="name != null">
                and name = #{name}
            </if>
            <if test="description != null">
                and description = #{description}
            </if>
            <if test="locationKey != null">
                and location_key = #{locationKey}
            </if>
            <if test="vipType != null">
                and vip_type = #{vipType}
            </if>
            <if test="vipType == null">
                and vip_type is null
            </if>
            <if test="platform != null">
                and platform = #{platform}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="sort != null">
                and sort = #{sort}
            </if>
            <if test="url != null">
                and url = #{url}
            </if>
            <if test="text != null">
                and text = #{text}
            </if>
            <if test="subText != null">
                and sub_text = #{subText}
            </if>
            <if test="icon != null">
                and icon = #{icon}
            </if>
            <if test="redirectType != null">
                and redirect_type = #{redirectType}
            </if>
            <if test="redirectUrl != null">
                and redirect_url = #{redirectUrl}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
        order by sort asc
    </select>

</mapper>