<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AutoRenewABTestMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AutoRenewABTest">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="dut_type" jdbcType="INTEGER" property="dutType"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="group_key" jdbcType="VARCHAR" property="groupKey"/>
        <result column="index_begin" jdbcType="INTEGER" property="indexBegin"/>
        <result column="index_end" jdbcType="INTEGER" property="indexEnd"/>
        <result column="event_type" jdbcType="INTEGER" property="eventType"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="valid_start_time" jdbcType="TIMESTAMP" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
      id, name, category, vip_type, dut_type, amount, group_key, index_begin, index_end, event_type,
      status, valid_start_time, valid_end_time, create_time, update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_abtest
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="list" parameterType="com.qiyi.vip.trade.autorenew.domain.AutoRenewABTest" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_abtest
        <where>
            <if test="name != null">
                and name = #{name}
            </if>
            <if test="category != null">
                and category = #{category}
            </if>
            <if test="vipType != null">
                and vip_type = #{vipType}
            </if>
            <if test="dutType != null">
                and dut_type = #{dutType}
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
            <if test="groupKey != null">
                and group_key = #{groupKey}
            </if>
            <if test="indexBegin != null">
                and index_begin = #{indexBegin}
            </if>
            <if test="indexEnd != null">
                and index_end = #{indexEnd}
            </if>
            <if test="eventType != null">
                and event_type = #{eventType}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>
</mapper>