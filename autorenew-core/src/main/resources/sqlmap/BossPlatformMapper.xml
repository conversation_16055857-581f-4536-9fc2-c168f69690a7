<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.BossPlatformMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.Platform">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="business_charge" jdbcType="VARCHAR" property="businessCharge"/>
        <result column="act_group" jdbcType="INTEGER" property="actGroup"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="longyuan_code" jdbcType="VARCHAR" property="longyuanCode"/>
        <result column="longyuan_code_new" jdbcType="VARCHAR" property="longyuanCodeNew"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="corporation" jdbcType="VARCHAR" property="corporation"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, description, code, business_type, business_charge, act_group, update_time,
        longyuan_code, longyuan_code_new, type, area, corporation
    </sql>

    <insert id="insertSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.Platform" useGeneratedKeys="true" keyProperty="id">
        insert into boss_platform_new
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                name,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="businessCharge != null">
                business_charge,
            </if>
            <if test="actGroup != null">
                act_group,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="longyuanCode != null">
                longyuan_code,
            </if>
            <if test="longyuanCodeNew != null">
                longyuan_code_new,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="area != null">
                area,
            </if>
            <if test="corporation != null">
                corporation,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="businessCharge != null">
                #{businessCharge,jdbcType=VARCHAR},
            </if>
            <if test="actGroup != null">
                #{actGroup,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="longyuanCode != null">
                #{longyuanCode,jdbcType=VARCHAR},
            </if>
            <if test="longyuanCodeNew != null">
                #{longyuanCodeNew,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="area != null">
                #{area,jdbcType=VARCHAR},
            </if>
            <if test="corporation != null">
                #{corporation,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from boss_platform_new
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <update id="updateByPrimaryKeySelective" parameterType="com.qiyi.vip.trade.autorenew.domain.Platform">
        update boss_platform_new
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="businessCharge != null">
                business_charge = #{businessCharge,jdbcType=VARCHAR},
            </if>
            <if test="actGroup != null">
                act_group = #{actGroup,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="longyuanCode != null">
                longyuan_code = #{longyuanCode,jdbcType=VARCHAR},
            </if>
            <if test="longyuanCodeNew != null">
                longyuan_code_new = #{longyuanCodeNew,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="area != null">
                area = #{area,jdbcType=VARCHAR},
            </if>
            <if test="corporation != null">
                corporation = #{corporation,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_platform_new
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getPlatformByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_platform_new
        where code = #{code}
    </select>

    <select id="listByLongYuanCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_platform_new
        where longyuan_code = #{longYuanCode}
    </select>

    <select id="listByLongYuanCodeNew" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_platform_new
        where longyuan_code_new = #{longYuanCodeNew}
    </select>
</mapper>
