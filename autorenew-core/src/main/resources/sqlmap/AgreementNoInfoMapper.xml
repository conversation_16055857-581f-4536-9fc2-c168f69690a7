<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AgreementNoInfoMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="template_code" jdbcType="VARCHAR" property="templateCode"/>
        <result column="default_no" jdbcType="TINYINT" property="defaultNo"/>
        <result column="source_vip_type" jdbcType="INTEGER" property="sourceVipType"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="pay_channel_name" jdbcType="VARCHAR" property="payChannelName"/>
        <result column="pay_channel_type" jdbcType="INTEGER" property="payChannelType"/>
        <result column="priority" jdbcType="TINYINT" property="priority"/>
        <result column="dut_type" jdbcType="INTEGER" property="dutType"/>
        <result column="dut_pay_type" jdbcType="INTEGER" property="dutPayType"/>
        <result column="change_amount" jdbcType="TINYINT" property="changeAmount"/>
        <result column="direct_cancel" jdbcType="TINYINT" property="directCancel"/>
        <result column="support_pure_sign" jdbcType="TINYINT" property="supportPureSign"/>
        <result column="cancel_autorenw_unbind" jdbcType="TINYINT" property="cancelAutorenwUnbind"/>
        <result column="valid_start_time" jdbcType="TIMESTAMP" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime"/>
        <result column="partner_id" jdbcType="VARCHAR" property="partnerId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, name, type, template_code, default_no, source_vip_type, vip_type, amount, pay_channel, pay_channel_name, pay_channel_type,
        priority, dut_type, dut_pay_type, change_amount, direct_cancel, support_pure_sign, cancel_autorenw_unbind,
        valid_start_time, valid_end_time, partner_id, status, create_time, update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_no_info
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByDutType" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_no_info
        where dut_type = #{dutType,jdbcType=INTEGER} and status = 1
    </select>

    <select id="selectByTemplateCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_no_info
        where template_code = #{templateCode,jdbcType=VARCHAR} and status = 1
    </select>

    <select id="selectAutoRenewAgreement" parameterType="map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_no_info
        where status = 1
        <if test="type != null">
            and type = #{type}
        </if>
        <if test="sourceVipType != null">
            and source_vip_type = #{sourceVipType}
        </if>
        <if test="vipType != null">
            and vip_type = #{vipType}
        </if>
        <if test="amount != null">
            and amount = #{amount}
        </if>
        <if test="payChannel != null">
            and pay_channel = #{payChannel}
        </if>
        <if test="priority != null">
            and priority = #{priority}
        </if>
        order by priority desc
    </select>

    <select id="selectAutoRenewAgreementNoWithoutUpgrade" parameterType="map" resultMap="BaseResultMap">
        select *
        from agreement_no_info
        where type = 1 and status = 1 and default_no = 1
        and source_vip_type is null
        and vip_type = #{vipType}
        and amount = #{amount}
        <if test="payChannel != null">
            and pay_channel = #{payChannel}
        </if>
        <if test="priority != null">
            and priority = #{priority}
        </if>
        order by priority desc
        limit 1
    </select>

</mapper>