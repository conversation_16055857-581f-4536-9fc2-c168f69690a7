<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.PayChannelTipMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.PayChannelTip">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="pay_channel_name" jdbcType="VARCHAR" property="payChannelName"/>
        <result column="i18n" jdbcType="VARCHAR" property="i18n"/>
    </resultMap>

    <sql id="Base_Column_List">
      id, pay_channel, pay_channel_name, i18n
    </sql>

    <select id="selectByPayChannel" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_paychannel_tip
        where pay_channel = #{payChannel,jdbcType=INTEGER} limit 1
    </select>
</mapper>