<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AgreementMaterialMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AgreementMaterial">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="agreement_no" jdbcType="INTEGER" property="agreementNo"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="detail_url" jdbcType="VARCHAR" property="detailUrl"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, agreement_no, description, detail_url, status, create_time, update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_material
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByAgreementNo" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_material
        where agreement_no = #{agreementNo,jdbcType=INTEGER} and status = 1
    </select>

</mapper>