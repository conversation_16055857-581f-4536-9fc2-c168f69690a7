<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AutoRenewRestrictionStrategyMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AutoRenewRestrictionStrategy">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="category" jdbcType="TINYINT" property="category"/>
        <result column="act_code" jdbcType="VARCHAR" property="actCode"/>
        <result column="duration" jdbcType="INTEGER" property="duration"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="valid_start_time" jdbcType="TIMESTAMP" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
      id, category, act_code, duration, status, valid_start_time, valid_end_time, create_time, update_time
    </sql>

    <insert id="insertSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.AutoRenewRestrictionStrategy" useGeneratedKeys="true" keyProperty="id">
        insert into autorenew_restriction_strategy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="category != null">
                category,
            </if>
            <if test="act_code != null">
                act_code,
            </if>
            <if test="duration != null">
                duration,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="validStartTime != null">
                valid_start_time,
            </if>
            <if test="validEndTime != null">
                valid_end_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="category != null">
                #{category,jdbcType=TINYINT},
            </if>
            <if test="actCode != null">
                #{actCode,jdbcType=VARCHAR},
            </if>
            <if test="duration != null">
                #{duration,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="validStartTime != null">
                #{validStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="validEndTime != null">
                #{validEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
      delete from autorenew_restriction_strategy
      where id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_restriction_strategy
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="list" parameterType="com.qiyi.vip.trade.autorenew.domain.AutoRenewRestrictionStrategy" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_restriction_strategy
        <where>
            <if test="category != null">
                and category = #{category}
            </if>
            <if test="actCode != null">
                and act_code = #{actCode}
            </if>
            <if test="duration != null">
                and duration = #{duration}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>

</mapper>