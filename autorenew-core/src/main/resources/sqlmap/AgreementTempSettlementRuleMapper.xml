<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AgreementTempSettlementRuleMapper">
  <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AgreementTempSettlementRule">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="agreement_no" jdbcType="INTEGER" property="agreementNo" />
    <result column="agreement_code" jdbcType="VARCHAR" property="agreementCode" />
    <result column="scene" jdbcType="VARCHAR" property="scene" />
    <result column="exemption_period" jdbcType="INTEGER" property="exemptionPeriod" />
    <result column="within_period_settlement_strategy" jdbcType="TINYINT" property="withinPeriodSettlementStrategy" />
    <result column="within_period_settlement_fee" jdbcType="INTEGER" property="withinPeriodSettlementFee" />
    <result column="beyond_period_settlement_strategy" jdbcType="TINYINT" property="beyondPeriodSettlementStrategy" />
    <result column="beyond_period_settlement_fee" jdbcType="INTEGER" property="beyondPeriodSettlementFee" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, agreement_no, agreement_code, scene, exemption_period,
    within_period_settlement_strategy, within_period_settlement_fee,
    beyond_period_settlement_strategy, beyond_period_settlement_fee,
    status, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from agreement_temp_settlement_rule
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectByAgreementCode" parameterType="string" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from agreement_temp_settlement_rule
    where agreement_code = #{agreementCode,jdbcType=VARCHAR} and status = 1
  </select>

  <select id="selectByAgreementNo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from agreement_temp_settlement_rule
    where agreement_no = #{agreementNo,jdbcType=INTEGER} and status = 1
  </select>
</mapper>