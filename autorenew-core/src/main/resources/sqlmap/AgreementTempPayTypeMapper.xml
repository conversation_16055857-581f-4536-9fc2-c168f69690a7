<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AgreementTempPayTypeMapper">
  <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AgreementTempPayType">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="agreement_no" jdbcType="INTEGER" property="agreementNo" />
    <result column="agreement_code" jdbcType="VARCHAR" property="agreementCode" />
    <result column="pay_channel" jdbcType="INTEGER" property="payChannel" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, agreement_no, agreement_code, pay_channel, pay_type, status, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from agreement_temp_pay_type
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectByAgreementNo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from agreement_temp_pay_type
    where agreement_no = #{agreementNo,jdbcType=INTEGER} and status = 1
    <if test="payChannel != null">
      and pay_channel = #{payChannel,jdbcType=INTEGER}
    </if>
  </select>

  <select id="selectByAgreementCode" parameterType="string" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from agreement_temp_pay_type
    where agreement_code = #{agreementCode,jdbcType=VARCHAR} and status = 1
    <if test="payChannel != null">
      and pay_channel = #{payChannel,jdbcType=INTEGER}
    </if>
  </select>
</mapper>