<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.directroute.DirectRouteDutUserDao">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.DutUserNew">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="auto_renew" jdbcType="INTEGER" property="autoRenew"/>
        <result column="sign_key" jdbcType="VARCHAR" property="signKey"/>
        <result column="agreement_code" jdbcType="VARCHAR" property="agreementCode"/>
        <result column="agreement_no" jdbcType="INTEGER" property="agreementNo"/>
        <result column="agreement_type" jdbcType="TINYINT" property="agreementType"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime"/>
        <result column="renew_price" jdbcType="INTEGER" property="renewPrice"/>
        <result column="currency_unit" jdbcType="VARCHAR" property="currencyUnit"/>
        <result column="pay_auto_renew" jdbcType="INTEGER" property="payAutoRenew"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="once_auto_renew" jdbcType="INTEGER" property="onceAutoRenew"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="return_url" jdbcType="VARCHAR" property="returnUrl"/>
        <result column="source_vip_type" jdbcType="INTEGER" property="sourceVipType"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="renew_count" jdbcType="TINYINT" property="renewCount"/>
        <result column="serial_renew_count" jdbcType="TINYINT" property="serialRenewCount"/>
        <result column="platform" jdbcType="BIGINT" property="platform"/>
        <result column="platform_code" jdbcType="VARCHAR" property="platformCode"/>
        <result column="interrupt_flag" jdbcType="INTEGER" property="interruptFlag"/>
        <result column="next_dut_time" jdbcType="TIMESTAMP" property="nextDutTime"/>
        <result column="pcode" jdbcType="VARCHAR" property="pcode"/>
        <result column="deadline" jdbcType="TIMESTAMP" property="deadline"/>
        <result column="vip_category" jdbcType="TINYINT" property="vipCategory"/>
        <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="remain_periods" jdbcType="INTEGER" property="remainPeriods"/>
        <result column="act_code" jdbcType="VARCHAR" property="actCode"/>
        <result column="act_total_periods" jdbcType="INTEGER" property="actTotalPeriods"/>
        <result column="contract_price" jdbcType="INTEGER" property="contractPrice"/>
        <result column="act_type" jdbcType="INTEGER" property="actType"/>
        <result column="sign_time" jdbcType="TIMESTAMP" property="signTime"/>
        <result column="time_zone" jdbcType="VARCHAR" property="timeZone"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , user_id, status, auto_renew, sign_key, agreement_code, agreement_no, agreement_type, type, operate_time, renew_price, currency_unit, pay_auto_renew,
    update_time, once_auto_renew, source, return_url, source_vip_type, vip_type, renew_count,
    serial_renew_count, platform, platform_code, interrupt_flag, next_dut_time, pcode, deadline, vip_category,
    order_code, amount, remain_periods, act_code, act_total_periods, contract_price,
    act_type,sign_time,time_zone
    </sql>
    <sql id="Simple_Column_List">
        id
        , user_id, status, auto_renew, sign_key, agreement_code, agreement_no, agreement_type, type, renew_price,
        source_vip_type, vip_type, platform, platform_code, next_dut_time, pcode, deadline, amount, sign_time
    </sql>


    <select id="getAutoRenewUsersWithExcludeTypes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${shardTblName}
        where
        <choose>
            <when test="agreementType == 1">
                <![CDATA[ deadline >= #{startTime} ]]> and <![CDATA[ deadline < #{endTime} ]]>
            </when>

            <otherwise>
                <![CDATA[ next_dut_time >= #{startTime} ]]> and <![CDATA[ next_dut_time < #{endTime} ]]>
            </otherwise>
        </choose>
        and vip_type = #{vipType} and auto_renew = #{autoRenew}
        <if test="agreementType != null">
            and agreement_type = #{agreementType}
        </if>
        <if test="autoRenew != null">
            and auto_renew = #{autoRenew}
        </if>
        <if test="sourceVipType == null">
            and source_vip_type is null
        </if>
        <if test="sourceVipType != null">
            and source_vip_type = #{sourceVipType}
        </if>
        <if test="excludeTypeList != null and excludeTypeList.size > 0">
            and type not in
            <foreach collection="excludeTypeList" item="dutType" open="(" close=")" separator=",">
                #{dutType}
            </foreach>
        </if>
        order by user_id
    </select>

<!--    <select id="pageQueryAutoRenewUsers" resultMap="BaseResultMap">-->
<!--        select-->
<!--        <include refid="Base_Column_List"/>-->
<!--        from ${shardTblName}-->
<!--        <where>-->
<!--            auto_renew = 1 and agreement_type = 1-->
<!--            <if test="lastId != null">-->
<!--                and id > lastId-->
<!--            </if>-->
<!--        </where>-->
<!--        order by id limit #{offset}, #{limit}-->
<!--    </select>-->

    <select id="getAutoRenewUsersByVipDeadline" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${shardTblName}
        where
        <choose>
            <when test="agreementType == 1">
                <![CDATA[ deadline >= #{startTime} ]]> and <![CDATA[ deadline < #{endTime} ]]>
            </when>

            <otherwise>
                <![CDATA[ next_dut_time >= #{startTime} ]]> and <![CDATA[ next_dut_time < #{endTime} ]]>
            </otherwise>
        </choose>
        and vip_type = #{vipType}
        and auto_renew = #{autoRenew}
        <if test="agreementType != null">
            and agreement_type  = #{agreementType}
        </if>
        and type in
        <foreach collection="dutTypeList" item="dutType" open="(" close=")" separator=",">
            #{dutType}
        </foreach>
        order by user_id
    </select>

    <select id="getDutUsersByNextDutTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${shardTblName}
        where <![CDATA[ next_dut_time >= #{startTime} ]]> and <![CDATA[ next_dut_time <= #{endTime} ]]>
        and vip_type = #{vipType}
        and auto_renew = #{autoRenew}
        <if test="agreementType != null">
            and agreement_type  = #{agreementType}
        </if>
        and type in
        <foreach collection="dutTypeList" item="dutType" open="(" close=")" separator=",">
            #{dutType}
        </foreach>
        order by user_id
    </select>

    <select id="getShardAsyncAutoRenewUsers" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${shardTblName}
        where <![CDATA[ deadline >= #{startTime} ]]> and <![CDATA[ deadline < #{endTime} ]]>
        and vip_type = #{vipType} and auto_renew = #{autoRenew}
        <if test="agreementType != null">
            and agreement_type  = #{agreementType}
        </if>
        <if test="includeTypes != null and includeTypes.size > 0 ">
            and type in
            <foreach collection="includeTypes" item="dutType" open="(" close=")" separator=",">
                #{dutType}
            </foreach>
        </if>
        <if test="excludeTypes != null and excludeTypes.size > 0 ">
            and type not in
            <foreach collection="excludeTypes" item="dutType" open="(" close=")" separator=",">
                #{dutType}
            </foreach>
        </if>
        order by user_id
    </select>

    <resultMap id="SimpleResultMap" type="com.qiyi.vip.trade.autorenew.domain.SimpleDutUserNew">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="auto_renew" jdbcType="INTEGER" property="autoRenew"/>
        <result column="sign_key" jdbcType="VARCHAR" property="signKey"/>
        <result column="agreement_code" jdbcType="VARCHAR" property="agreementCode"/>
        <result column="agreement_no" jdbcType="INTEGER" property="agreementNo"/>
        <result column="agreement_type" jdbcType="TINYINT" property="agreementType"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="renew_price" jdbcType="INTEGER" property="renewPrice"/>
        <result column="source_vip_type" jdbcType="INTEGER" property="sourceVipType"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="platform" jdbcType="BIGINT" property="platform"/>
        <result column="platform_code" jdbcType="VARCHAR" property="platformCode"/>
        <result column="next_dut_time" jdbcType="TIMESTAMP" property="nextDutTime"/>
        <result column="pcode" jdbcType="VARCHAR" property="pcode"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="deadline" jdbcType="TIMESTAMP" property="deadline"/>
        <result column="sign_time" jdbcType="TIMESTAMP" property="signTime"/>
    </resultMap>
    <select id="getByNextDutTimeRange" resultMap="SimpleResultMap">
        select
        <include refid="Simple_Column_List"/>
        from ${shardTblName}
        where next_dut_time >= #{startTime} and next_dut_time &lt; #{endTime}
        and agreement_type = #{agreementType}
        and vip_type = #{vipType} and auto_renew = #{autoRenew}
        <if test="sourceVipType == null">
            and source_vip_type is null
        </if>
        <if test="sourceVipType != null">
            and source_vip_type = #{sourceVipType}
        </if>
        order by user_id
    </select>

    <select id="getByAgreementTypeListAndNextDutTimeRange" resultMap="SimpleResultMap">
        select
        <include refid="Simple_Column_List"/>
        from ${shardTblName}
        where next_dut_time >= #{startTime} and next_dut_time &lt; #{endTime}
        and agreement_type in
        <foreach collection="agreementTypeList" open="(" close=")" item="agreementType" separator=",">
            #{agreementType}
        </foreach>
        and vip_type = #{vipType} and auto_renew = #{autoRenew}
        <if test="sourceVipType == null">
            and source_vip_type is null
        </if>
        <if test="sourceVipType != null">
            and source_vip_type = #{sourceVipType}
        </if>
        order by user_id
    </select>

    <select id="getByDeadlineRange" resultMap="SimpleResultMap">
        select
        <include refid="Simple_Column_List"/>
        from ${shardTblName}
        where deadline >= #{startTime} and deadline &lt; #{endTime}
        and agreement_type = #{agreementType}
        and vip_type = #{vipType} and auto_renew = #{autoRenew}
        <if test="sourceVipType == null">
            and source_vip_type is null
        </if>
        <if test="sourceVipType != null">
            and source_vip_type = #{sourceVipType}
        </if>
        order by user_id
    </select>
</mapper>