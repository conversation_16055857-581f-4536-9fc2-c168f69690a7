<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.IntroductoryPriceActMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.IntroductoryPriceAct">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="act_type" jdbcType="INTEGER" property="actType"/>
        <result column="act_code" jdbcType="VARCHAR" property="actCode"/>
        <result column="act_periods" jdbcType="INTEGER" property="actPeriods"/>
        <result column="act_price" jdbcType="INTEGER" property="actPrice"/>
        <result column="valid_start_time" jdbcType="TIMESTAMP" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime"/>
        <result column="user_list" jdbcType="VARCHAR" property="userList"/>
        <result column="pid" jdbcType="VARCHAR" property="pid"/>
        <result column="platform_list" jdbcType="VARCHAR" property="platformList"/>
        <result column="paytype_list" jdbcType="VARCHAR" property="paytypeList"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="act_name" jdbcType="VARCHAR" property="actName"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, update_time, create_time, act_type, act_code, act_periods, act_price, valid_start_time,
        valid_end_time, user_list, pid, platform_list, paytype_list, amount, act_name, priority,
        status
    </sql>

    <insert id="insertSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.IntroductoryPriceAct" useGeneratedKeys="true" keyProperty="id">
        insert into autorenew_introductory_price_act
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="actType != null">
                act_type,
            </if>
            <if test="actCode != null">
                act_code,
            </if>
            <if test="actPeriods != null">
                act_periods,
            </if>
            <if test="actPrice != null">
                act_price,
            </if>
            <if test="validStartTime != null">
                valid_start_time,
            </if>
            <if test="validEndTime != null">
                valid_end_time,
            </if>
            <if test="userList != null">
                user_list,
            </if>
            <if test="pid != null">
                pid,
            </if>
            <if test="platformList != null">
                platform_list,
            </if>
            <if test="paytypeList != null">
                paytype_list,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="actName != null">
                act_name,
            </if>
            <if test="priority != null">
                priority,
            </if>
            <if test="status != null">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="actType != null">
                #{actType,jdbcType=INTEGER},
            </if>
            <if test="actCode != null">
                #{actCode,jdbcType=VARCHAR},
            </if>
            <if test="actPeriods != null">
                #{actPeriods,jdbcType=INTEGER},
            </if>
            <if test="actPrice != null">
                #{actPrice,jdbcType=INTEGER},
            </if>
            <if test="validStartTime != null">
                #{validStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="validEndTime != null">
                #{validEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="userList != null">
                #{userList,jdbcType=VARCHAR},
            </if>
            <if test="pid != null">
                #{pid,jdbcType=VARCHAR},
            </if>
            <if test="platformList != null">
                #{platformList,jdbcType=VARCHAR},
            </if>
            <if test="paytypeList != null">
                #{paytypeList,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=INTEGER},
            </if>
            <if test="actName != null">
                #{actName,jdbcType=VARCHAR},
            </if>
            <if test="priority != null">
                #{priority,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from autorenew_introductory_price_act
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <update id="updateByPrimaryKeySelective" parameterType="com.qiyi.vip.trade.autorenew.domain.IntroductoryPriceAct">
        update autorenew_introductory_price_act
        <set>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="actType != null">
                act_type = #{actType,jdbcType=INTEGER},
            </if>
            <if test="actCode != null">
                act_code = #{actCode,jdbcType=VARCHAR},
            </if>
            <if test="actPeriods != null">
                act_periods = #{actPeriods,jdbcType=INTEGER},
            </if>
            <if test="actPrice != null">
                act_price = #{actPrice,jdbcType=INTEGER},
            </if>
            <if test="validStartTime != null">
                valid_start_time = #{validStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="validEndTime != null">
                valid_end_time = #{validEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="userList != null">
                user_list = #{userList,jdbcType=VARCHAR},
            </if>
            <if test="pid != null">
                pid = #{pid,jdbcType=VARCHAR},
            </if>
            <if test="platformList != null">
                platform_list = #{platformList,jdbcType=VARCHAR},
            </if>
            <if test="paytypeList != null">
                paytype_list = #{paytypeList,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=INTEGER},
            </if>
            <if test="actName != null">
                act_name = #{actName,jdbcType=VARCHAR},
            </if>
            <if test="priority != null">
                priority = #{priority,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_introductory_price_act
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="findPriceAct" parameterType="com.qiyi.boss.autorenew.dto.IntroductoryActDto" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_introductory_price_act
        where
        status = 1
        and pid = #{introductoryActDto.pid}
        and amount = #{introductoryActDto.amount}
        and act_price = #{introductoryActDto.fee}
        and <![CDATA[valid_start_time <= #{introductoryActDto.payTime} and valid_end_time > #{introductoryActDto.payTime} ]]>
    </select>
</mapper>