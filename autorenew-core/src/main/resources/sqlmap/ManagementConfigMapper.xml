<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.ManagementConfigMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.ManagementConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="amount" jdbcType="TINYINT" property="amount"/>
        <result column="original_price" jdbcType="INTEGER" property="originalPrice"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="valid_start_time" jdbcType="TIMESTAMP" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="agreement_type" jdbcType="TINYINT" property="agreementType"/>
    </resultMap>

    <sql id="Base_Column_List">
      id, vip_type, pay_channel, amount, original_price, status, valid_start_time, valid_end_time,
      create_time, update_time, agreement_type
    </sql>

    <insert id="insertSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.ManagementConfig" useGeneratedKeys="true" keyProperty="id">
        insert into autorenew_management_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="payChannel != null">
                pay_channel,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="originalPrice != null">
                original_price,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="validStartTime != null">
                valid_start_time,
            </if>
            <if test="validEndTime != null">
                valid_end_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vipType != null">
                #{vipType,jdbcType=INTEGER},
            </if>
            <if test="payChannel != null">
                #{payChannel,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=TINYINT},
            </if>
            <if test="originalPrice != null">
                #{originalPrice,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="validStartTime != null">
                #{validStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="validEndTime != null">
                #{validEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
      delete from autorenew_management_config
      where id = #{id,jdbcType=BIGINT}
    </delete>

    <update id="updateByPrimaryKeySelective" parameterType="com.qiyi.vip.trade.autorenew.domain.ManagementConfig">
        update autorenew_management_config
        <set>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=INTEGER},
            </if>
            <if test="payChannel != null">
                pay_channel = #{payChannel,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=TINYINT},
            </if>
            <if test="originalPrice != null">
                original_price = #{originalPrice,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="validStartTime != null">
                valid_start_time = #{validStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="validEndTime != null">
                valid_end_time = #{validEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_management_config
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="listSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.ManagementConfig" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_management_config
        <where>
            <![CDATA[ valid_start_time <= CURRENT_TIMESTAMP() and valid_end_time > CURRENT_TIMESTAMP() ]]>
            <if test="vipType != null">
                and vip_type = #{vipType}
            </if>
            <if test="payChannel != null">
                and pay_channel = #{payChannel}
            </if>
            <if test="payChannel == null">
                and pay_channel is null
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
            <if test="originalPrice != null">
                and original_price = #{originalPrice}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="agreementType != null">
                and agreement_type  = #{agreementType}
            </if>
        </where>
    </select>

</mapper>