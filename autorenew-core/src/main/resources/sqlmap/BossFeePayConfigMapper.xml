<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.BossFreePayConfigMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.FreePayConfig">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="act_code" jdbcType="VARCHAR" property="actCode"/>
        <result column="purpose" jdbcType="VARCHAR" property="purpose"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="compensate_day" jdbcType="INTEGER" property="compensateDay"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
      id, batch_no, act_code, purpose, vip_type, app_id, compensate_day, create_time, update_time
    </sql>

    <select id="getConfigSelective" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_free_pay_config
        <trim prefix="where" prefixOverrides="and | or">
            <if test="purpose != null">
                AND purpose = #{purpose}
            </if>
            <if test="vipType != null">
                AND vip_type = #{vipType}
            </if>
            <if test="appId != null">
                AND app_id = #{appId}
            </if>
        </trim>
    </select>

</mapper>