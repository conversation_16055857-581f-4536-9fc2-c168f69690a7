<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.dao.UserAgreementSetLogMapper">
  <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="operator" jdbcType="INTEGER" property="operator" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="sign_key" jdbcType="VARCHAR" property="signKey" />
    <result column="agreement_code" jdbcType="VARCHAR" property="agreementCode" />
    <result column="agreement_no" jdbcType="INTEGER" property="agreementNo"/>
    <result column="agreement_type" jdbcType="TINYINT" property="agreementType" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="push_channel" jdbcType="INTEGER" property="pushChannel" />
    <result column="gateway" jdbcType="INTEGER" property="gateway" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
    <result column="serial_renew_count" jdbcType="INTEGER" property="serialRenewCount" />
    <result column="vip_category" jdbcType="TINYINT" property="vipCategory" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="vip_type" jdbcType="INTEGER" property="vipType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, operator, operate_time, sign_key, agreement_code, agreement_no, agreement_type, type,
    description, update_time, push_channel, gateway, platform, platform_code, serial_renew_count, vip_category,
    amount, vip_type
  </sql>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from boss_dut_renew_set_log
    where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog" useGeneratedKeys="true" keyProperty="id">
    insert into boss_dut_renew_set_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="operateTime != null">
        operate_time,
      </if>
      <if test="signKey != null">
        sign_key,
      </if>
      <if test="agreementCode != null">
        agreement_code,
      </if>
      <if test="agreementNo != null">
        agreement_no,
      </if>
      <if test="agreementType != null">
        agreement_type,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="pushChannel != null">
        push_channel,
      </if>
      <if test="gateway != null">
        gateway,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="platformCode != null">
        platform_code,
      </if>
      <if test="serialRenewCount != null">
        serial_renew_count,
      </if>
      <if test="vipCategory != null">
        vip_category,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="vipType != null">
        vip_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=INTEGER},
      </if>
      <if test="operateTime != null">
        #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signKey != null">
        #{signKey,jdbcType=VARCHAR},
      </if>
      <if test="agreementCode != null">
        #{agreementCode,jdbcType=VARCHAR},
      </if>
      <if test="agreementNo != null">
        #{agreementNo,jdbcType=INTEGER},
      </if>
      <if test="agreementType != null">
        #{agreementType,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pushChannel != null">
        #{pushChannel,jdbcType=INTEGER},
      </if>
      <if test="gateway != null">
        #{gateway,jdbcType=INTEGER},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="platformCode != null">
        #{platformCode,jdbcType=VARCHAR},
      </if>
      <if test="serialRenewCount != null">
        #{serialRenewCount,jdbcType=INTEGER},
      </if>
      <if test="vipCategory != null">
        #{vipCategory,jdbcType=TINYINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="vipType != null">
        #{vipType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog">
        update boss_dut_renew_set_log
        <set>
            update_time = now(),
            <if test="operator != null">
                operator = #{operator,jdbcType=INTEGER},
            </if>
            <if test="operateTime != null">
                operate_time = #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="signKey != null">
                sign_key = #{signKey,jdbcType=VARCHAR},
            </if>
            <if test="agreementCode != null">
                agreement_code = #{agreementCode,jdbcType=VARCHAR},
            </if>
            <if test="agreementNo != null">
                agreement_no = #{agreementNo,jdbcType=INTEGER},
            </if>
            <if test="agreementType != null">
                agreement_type = #{agreementType,jdbcType=TINYINT},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="pushChannel != null">
                push_channel = #{pushChannel,jdbcType=INTEGER},
            </if>
            <if test="gateway != null">
                gateway = #{gateway,jdbcType=INTEGER},
            </if>
            <if test="platform != null">
                platform = #{platform,jdbcType=BIGINT},
            </if>
            <if test="platformCode != null">
                platform_code = #{platformCode,jdbcType=VARCHAR},
            </if>
            <if test="serialRenewCount != null">
                serial_renew_count = #{serialRenewCount,jdbcType=INTEGER},
            </if>
            <if test="vipCategory != null">
                vip_category = #{vipCategory,jdbcType=TINYINT},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id} and user_id = #{userId}
    </update>

  <select id="selectBySignKeyAndOptType" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from boss_dut_renew_set_log
    where user_id = #{userId,jdbcType=BIGINT}
    and sign_key = #{signKey,jdbcType=VARCHAR}
    and operator = #{operateType,jdbcType=TINYINT}
  </select>

    <select id="getByAgreementNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from boss_dut_renew_set_log
        where user_id = #{userId,jdbcType=BIGINT}
        and agreement_no = #{agreementNo,jdbcType=INTEGER}
        <if test="operator != null">
            and operator = #{operator,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectByDutType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_renew_set_log
        where user_id = #{userId,jdbcType=BIGINT}
        and type = #{dutType,jdbcType=INTEGER}
        <if test="operator != null">
            and operator = #{operator,jdbcType=INTEGER}
        </if>
    </select>

    <select id="getByAgreementTypeAndVipTypes" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_renew_set_log
        where user_id = #{userId,jdbcType=BIGINT}
        and agreement_type = #{agreementType,jdbcType=INTEGER}
        and vip_type in
        <foreach collection="vipTypes" item="vipType" open="(" close=")" separator=",">
            #{vipType,jdbcType=INTEGER}
        </foreach>
    </select>

</mapper>