<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.dao.AutorenewPreDutRecordDao">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AutorenewPreDutRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="dut_type" jdbcType="INTEGER" property="dutType"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="group_key" jdbcType="VARCHAR" property="groupKey"/>
        <result column="event_type" jdbcType="INTEGER" property="eventType"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="fee" jdbcType="INTEGER" property="fee"/>
        <result column="center_order_code" jdbcType="VARCHAR" property="centerOrderCode"/>
        <result column="error_code" jdbcType="VARCHAR" property="errorCode"/>
        <result column="contract_code" jdbcType="VARCHAR" property="contractCode"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, user_id, vip_type, dut_type, amount, group_key, event_type, status, fee, center_order_code, error_code, contract_code,
    create_time, update_time
    </sql>

    <insert id="insert" parameterType="com.qiyi.vip.trade.autorenew.domain.AutorenewPreDutRecord" keyProperty="id" useGeneratedKeys="true">
        insert into autorenew_pre_dut_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="dutType != null">
                dut_type,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="groupKey != null">
                group_key,
            </if>
            <if test="eventType != null">
                event_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="fee != null">
                fee,
            </if>
            <if test="centerOrderCode != null">
                center_order_code,
            </if>
            <if test="errorCode != null">
                error_code,
            </if>
            <if test="contractCode != null">
                contract_code,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=INTEGER},
            </if>
            <if test="dutType != null">
                #{dutType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=INTEGER},
            </if>
            <if test="groupKey != null">
                #{groupKey,jdbcType=VARCHAR},
            </if>
            <if test="eventType != null">
                #{eventType,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="fee != null">
                #{fee,jdbcType=INTEGER},
            </if>
            <if test="centerOrderCode != null">
                #{centerOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="errorCode != null">
                #{errorCode,jdbcType=VARCHAR},
            </if>
            <if test="contractCode != null">
                #{contractCode,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.qiyi.vip.trade.autorenew.domain.AutorenewPreDutRecord">
        update autorenew_pre_dut_record
        set vip_type = #{vipType,jdbcType=INTEGER},
            dut_type = #{dutType,jdbcType=INTEGER},
            amount = #{amount,jdbcType=INTEGER},
            group_key = #{groupKey,jdbcType=VARCHAR},
            event_type = #{eventType,jdbcType=VARCHAR},
            status = #{status,jdbcType=INTEGER},
            fee = #{fee,jdbcType=INTEGER},
            center_order_code = #{centerOrderCode,jdbcType=VARCHAR},
            error_code = #{errorCode,jdbcType=VARCHAR},
            contract_code = #{contractCode,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER} and user_id = #{userId}
    </update>
    <select id="selectByPrimaryKeyAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_pre_dut_record
        where id = #{id} and user_id = #{userId}
    </select>
    <select id="list" parameterType="com.qiyi.vip.trade.autorenew.domain.AutorenewPreDutRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_pre_dut_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="vipType != null">
                and vip_type = #{vipType}
            </if>
            <if test="dutType != null">
                and dut_type =#{dutType}
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
            <if test="groupKey != null">
                and group_key = #{groupKey}
            </if>
            <if test="eventType != null">
                and event_type = #{eventType}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="fee != null">
                and fee = #{fee}
            </if>
            <if test="centerOrderCode != null">
                and center_order_code = #{centerOrderCode}
            </if>
            <if test="errorCode != null">
                and error_code = #{errorCode}
            </if>
            <if test="contractCode != null">
                and contract_code = #{contractCode}
            </if>
            <if test="createTime != null">
                and create_time &gt; #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time &gt; #{updateTime}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>