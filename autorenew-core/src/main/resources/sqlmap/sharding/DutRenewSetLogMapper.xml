<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.dao.DutRenewSetLogDao">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="operator" jdbcType="INTEGER" property="operator"/>
        <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime"/>
        <result column="sign_key" jdbcType="VARCHAR" property="signKey" />
        <result column="agreement_code" jdbcType="VARCHAR" property="agreementCode" />
        <result column="agreement_no" jdbcType="INTEGER" property="agreementNo"/>
        <result column="agreement_type" jdbcType="TINYINT" property="agreementType" />
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="push_channel" jdbcType="INTEGER" property="pushChannel"/>
        <result column="gateway" jdbcType="INTEGER" property="gateway"/>
        <result column="platform" jdbcType="BIGINT" property="platform"/>
        <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
        <result column="serial_renew_count" jdbcType="INTEGER" property="serialRenewCount"/>
        <result column="vip_category" jdbcType="TINYINT" property="vipCategory"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
    </resultMap>

    <sql id="Base_Column_List">
      id, user_id, `operator`, operate_time, sign_key, agreement_code, agreement_no, agreement_type, `type`, description,
        update_time, push_channel, gateway, platform, platform_code, serial_renew_count, vip_category, vip_type, amount
    </sql>

    <insert id="insertSetLogSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog" keyProperty="id" useGeneratedKeys="true">
        insert into boss_dut_renew_set_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="operator != null">
                `operator`,
            </if>
            <if test="operateTime != null">
                operate_time,
            </if>
            <if test="signKey != null">
                sign_key,
            </if>
            <if test="agreementCode != null">
                agreement_code,
            </if>
            <if test="agreementNo != null">
                agreement_no,
            </if>
            <if test="agreementType != null">
                agreement_type,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="pushChannel != null">
                push_channel,
            </if>
            <if test="gateway != null">
                gateway,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="platformCode != null">
                platform_code,
            </if>
            <if test="serialRenewCount != null">
                serial_renew_count,
            </if>
            <if test="vipCategory != null">
                vip_category,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="amount != null">
                amount,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=INTEGER},
            </if>
            <if test="operateTime != null">
                #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="signKey != null">
                #{signKey,jdbcType=VARCHAR},
            </if>
            <if test="agreementCode != null">
                #{agreementCode,jdbcType=VARCHAR},
            </if>
            <if test="agreementNo != null">
                #{agreementNo,jdbcType=INTEGER},
            </if>
            <if test="agreementType != null">
                #{agreementType,jdbcType=TINYINT},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pushChannel != null">
                #{pushChannel,jdbcType=INTEGER},
            </if>
            <if test="gateway != null">
                #{gateway,jdbcType=INTEGER},
            </if>
            <if test="platform != null">
                #{platform,jdbcType=BIGINT},
            </if>
            <if test="platformCode != null">
                #{platformCode,jdbcType=VARCHAR},
            </if>
            <if test="serialRenewCount != null">
                #{serialRenewCount,jdbcType=INTEGER},
            </if>
            <if test="vipCategory != null">
                #{vipCategory,jdbcType=TINYINT},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeyAndUserIdSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog">
        update boss_dut_renew_set_log
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=INTEGER},
            </if>
            <if test="operateTime != null">
                operate_time = #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pushChannel != null">
                push_channel = #{pushChannel,jdbcType=INTEGER},
            </if>
            <if test="gateway != null">
                gateway = #{gateway,jdbcType=INTEGER},
            </if>
            <if test="platform != null">
                platform = #{platform,jdbcType=BIGINT},
            </if>
            <if test="platformCode != null">
                platform_code = #{platformCode,jdbcType=VARCHAR},
            </if>
            <if test="serialRenewCount != null">
                serial_renew_count = #{serialRenewCount,jdbcType=INTEGER},
            </if>
            <if test="vipCategory != null">
                vip_category = #{vipCategory,jdbcType=TINYINT},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id} and user_id = #{userId}
    </update>

    <select id="listSetLogSelective" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_set_log
        <where>
            1 = 1
            <if test="dutRenewSetLog.userId != null">
                and user_id = #{dutRenewSetLog.userId,jdbcType=BIGINT}
            </if>
            <if test="dutRenewSetLog.operator != null">
                and operator = #{dutRenewSetLog.operator,jdbcType=INTEGER}
            </if>
            <if test="dutRenewSetLog.operateTime != null">
                and operate_time = #{dutRenewSetLog.operateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="dutRenewSetLog.signKey != null">
                and sign_key = #{dutRenewSetLog.signKey,jdbcType=VARCHAR}
            </if>
            <if test="dutRenewSetLog.agreementCode != null">
                and agreement_code = #{dutRenewSetLog.agreementCode,jdbcType=VARCHAR}
            </if>
            <if test="dutRenewSetLog.agreementNo != null">
                and agreement_no = #{dutRenewSetLog.agreementNo,jdbcType=INTEGER}
            </if>
            <if test="dutRenewSetLog.agreementType != null">
                and agreement_type = #{dutRenewSetLog.agreementType,jdbcType=TINYINT}
            </if>
            <if test="dutRenewSetLog.type != null">
                and `type` = #{dutRenewSetLog.type,jdbcType=INTEGER}
            </if>
            <if test="dutRenewSetLog.description != null">
                and description = #{dutRenewSetLog.description,jdbcType=VARCHAR}
            </if>
            <if test="dutRenewSetLog.updateTime != null">
                and update_time = #{dutRenewSetLog.updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="dutRenewSetLog.pushChannel != null">
                and push_channel = #{dutRenewSetLog.pushChannel,jdbcType=INTEGER}
            </if>
            <if test="dutRenewSetLog.gateway != null">
                and gateway = #{dutRenewSetLog.gateway,jdbcType=INTEGER}
            </if>
            <if test="dutRenewSetLog.platform != null">
                and platform = #{dutRenewSetLog.platform,jdbcType=BIGINT}
            </if>
            <if test="dutRenewSetLog.platformCode != null">
                and platform_code = #{dutRenewSetLog.platformCode,jdbcType=VARCHAR}
            </if>
            <if test="dutRenewSetLog.serialRenewCount != null">
                and serial_renew_count = #{dutRenewSetLog.serialRenewCount,jdbcType=INTEGER}
            </if>
            <if test="dutRenewSetLog.vipCategory != null">
                and vip_category = #{dutRenewSetLog.vipCategory,jdbcType=TINYINT}
            </if>
            <if test="dutRenewSetLog.vipType != null">
                and vip_type = #{dutRenewSetLog.vipType,jdbcType=INTEGER}
            </if>
            <if test="dutRenewSetLog.amount != null">
                and amount = #{dutRenewSetLog.amount,jdbcType=INTEGER}
            </if>
        </where>
        order by ${orderByColumn} desc limit #{limit}
    </select>

    <select id="getDutRenewSetLogList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_set_log
        where user_id = #{userId} and operator = #{operator}
        <if test="agreementType != null">
            and agreement_type  = #{agreementType}
        </if>
        and `type` in
        <foreach collection="dutTypes" open="(" close=")" item="dutType" separator=",">
            #{dutType}
        </foreach>
        order by operate_time DESC
    </select>


    <select id="getAllDutRenewSetLogList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_set_log
        where user_id = #{userId}
        <if test="agreementType != null">
            and agreement_type  = #{agreementType}
        </if>
        and `type` in
        <foreach collection="dutTypes" open="(" close=")" item="dutType" separator=",">
            #{dutType}
        </foreach>

        <if test="excludeAgreementTypeList != null and excludeAgreementTypeList.size() > 0">
            and agreement_type not in
            <foreach collection="excludeAgreementTypeList" item="agreementType" open="(" close=")" separator=",">
                #{agreementType}
            </foreach>
        </if>
        order by operate_time DESC
    </select>

    <select id="queryDutSetLogsByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_set_log
        where user_id = #{userId}
        <if test="agreementType != null">
            and agreement_type  = #{agreementType}
        </if>
        <if test="operator != null">
            and operator = #{operator}
        </if>
        <if test="startTime != null">
            <![CDATA[ and operate_time >= #{startTime} ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ and operate_time <= #{endTime} ]]>
        </if>
    </select>

    <select id="pageQuery" resultMap="BaseResultMap" parameterType="com.qiyi.vip.trade.autorenew.vo.PageQuery">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_set_log
        where 1 = 1
        <if test="conditions != null">
            <foreach collection="conditions" item="condition">
                and ${condition.columnName} = #{condition.value}
            </foreach>
        </if>
        order by ${orderByCol} ${orderByDirection} limit #{offset}, #{limit}
    </select>

    <select id="countPageQuery" parameterType="com.qiyi.vip.trade.autorenew.vo.PageQuery" resultType="java.lang.Long">
        select count(*) from boss_dut_renew_set_log
        where 1 = 1
        <if test="conditions != null">
            <foreach collection="conditions" item="condition">
                and ${condition.columnName} = #{condition.value}
            </foreach>
        </if>
    </select>

    <select id="selectByPrimaryKeyAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_set_log
        where id = #{id} and user_id = #{userId}
    </select>
</mapper>