<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.dao.DutRenewLogDao">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.DutRenewLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="sign_key" jdbcType="VARCHAR" property="signKey" />
        <result column="agreement_code" jdbcType="VARCHAR" property="agreementCode" />
        <result column="agreement_no" jdbcType="INTEGER" property="agreementNo"/>
        <result column="agreement_type" jdbcType="TINYINT" property="agreementType" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
        <result column="trade_code" jdbcType="VARCHAR" property="tradeCode"/>
        <result column="operate_type" jdbcType="TINYINT" property="operateType" />
        <result column="status" jdbcType="DECIMAL" property="status"/>
        <result column="error_code" jdbcType="VARCHAR" property="errorCode"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="fee" jdbcType="INTEGER" property="fee"/>
        <result column="platform" jdbcType="BIGINT" property="platform"/>
        <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
        <result column="req_error_type" jdbcType="VARCHAR" property="reqErrorType"/>
        <result column="third_error_code" jdbcType="VARCHAR" property="thirdErrorCode"/>
        <result column="third_error_msg" jdbcType="VARCHAR" property="thirdErrorMsg"/>
        <result column="vip_category" jdbcType="TINYINT" property="vipCategory"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
    </resultMap>

    <sql id="Base_Column_List">
      id, user_id, sign_key, agreement_code, agreement_no, agreement_type, create_time, order_code, trade_code, operate_type,
        status, error_code, `type`, update_time, fee, platform, platform_code, req_error_type, third_error_code,
        third_error_msg, vip_category, vip_type, amount, description
    </sql>

    <insert id="insertRenewLogSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.DutRenewLog" keyProperty="id" useGeneratedKeys="true">
        insert into boss_dut_renew_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="signKey != null">
                sign_key,
            </if>
            <if test="agreementCode != null">
                agreement_code,
            </if>
            <if test="agreementNo != null">
                agreement_no,
            </if>
            <if test="agreementType != null">
                agreement_type,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="orderCode != null">
                order_code,
            </if>
            <if test="tradeCode != null">
                trade_code,
            </if>
            <if test="operateType != null">
                operate_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="errorCode != null">
                error_code,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="fee != null">
                fee,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="platformCode != null">
                platform_code,
            </if>
            <if test="reqErrorType != null">
                req_error_type,
            </if>
            <if test="thirdErrorCode != null">
                third_error_code,
            </if>
            <if test="thirdErrorMsg != null">
                third_error_msg,
            </if>
            <if test="vipCategory != null">
                vip_category,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="description != null">
                description,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="signKey != null">
                #{signKey,jdbcType=VARCHAR},
            </if>
            <if test="agreementCode != null">
                #{agreementCode,jdbcType=VARCHAR},
            </if>
            <if test="agreementNo != null">
                #{agreementNo,jdbcType=INTEGER},
            </if>
            <if test="agreementType != null">
                #{agreementType,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderCode != null">
                #{orderCode,jdbcType=VARCHAR},
            </if>
            <if test="tradeCode != null">
                #{tradeCode,jdbcType=VARCHAR},
            </if>
            <if test="operateType != null">
                #{operateType,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=DECIMAL},
            </if>
            <if test="errorCode != null">
                #{errorCode,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="fee != null">
                #{fee,jdbcType=INTEGER},
            </if>
            <if test="platform != null">
                #{platform,jdbcType=BIGINT},
            </if>
            <if test="platformCode != null">
                #{platformCode,jdbcType=VARCHAR},
            </if>
            <if test="reqErrorType != null">
                #{reqErrorType,jdbcType=VARCHAR},
            </if>
            <if test="thirdErrorCode != null">
                #{thirdErrorCode,jdbcType=VARCHAR},
            </if>
            <if test="thirdErrorMsg != null">
                #{thirdErrorMsg,jdbcType=VARCHAR},
            </if>
            <if test="vipCategory != null">
                #{vipCategory,jdbcType=TINYINT},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateRenewLogByPrimaryKeyAndUerIdSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.DutRenewLog">
        update boss_dut_renew_log
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderCode != null">
                order_code = #{orderCode,jdbcType=VARCHAR},
            </if>
            <if test="tradeCode != null">
                trade_code = #{tradeCode,jdbcType=VARCHAR},
            </if>
            <if test="operateType != null">
                operate_type = #{operateType,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=DECIMAL},
            </if>
            <if test="errorCode != null">
                error_code = #{errorCode,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="fee != null">
                fee = #{fee,jdbcType=INTEGER},
            </if>
            <if test="platform != null">
                platform = #{platform,jdbcType=BIGINT},
            </if>
            <if test="platformCode != null">
                platform_code = #{platformCode,jdbcType=VARCHAR},
            </if>
            <if test="reqErrorType != null">
                req_error_type = #{reqErrorType,jdbcType=VARCHAR},
            </if>
            <if test="thirdErrorCode != null">
                third_error_code = #{thirdErrorCode,jdbcType=VARCHAR},
            </if>
            <if test="thirdErrorMsg != null">
                third_error_msg = #{thirdErrorMsg,jdbcType=VARCHAR},
            </if>
            <if test="vipCategory != null">
                vip_category = #{vipCategory,jdbcType=TINYINT},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id, jdbcType=BIGINT} and user_id = #{userId}
    </update>
    <update id="updateUserAccountBindStatus" parameterType="map">
        update boss_dut_user_new
        set status = #{status, jdbcType=INTEGER}
        where user_id = #{userId,jdbcType=BIGINT} and type = #{dutType, jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKeyAndUserId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId, jdbcType=BIGINT}
    </select>

    <select id="getDutRenewLogByTime" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        where user_id = #{userId} and <![CDATA[ update_time > #{startTime} ]]>  and <![CDATA[ update_time < #{endTime} ]]>
        and status = #{status} and vip_category = #{vipCategory}
        and agreement_type = 1
        order by id desc
    </select>

    <select id="findDutRenewLogByTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        where user_id = #{userId} and <![CDATA[ create_time >= #{startTime} ]]>  and <![CDATA[ create_time <= #{endTime} ]]>
        and vip_type = #{vipType}
        <if test="agreementType != null">
            and agreement_type  = #{agreementType}
        </if>
    </select>

    <select id="getLastDutRenewLogAtTimeRange" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        where user_id = #{userId} and `type` = #{dutType} and vip_type = #{vipType}
        and <![CDATA[ update_time > #{startTime} ]]>  and <![CDATA[ update_time < #{endTime} ]]>
        <if test="agreementType != null">
            and agreement_type  = #{agreementType}
        </if>
        order by update_time desc limit 1
    </select>

    <select id="getRecentlyDutRenewLog" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        where user_id = #{userId} and `type` = #{dutType} and vip_type = #{vipType}
        order by update_time desc limit 1
    </select>

    <select id="getDutRenewLog" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        where user_id = #{userId} and <![CDATA[ update_time > #{startTime} ]]>  and <![CDATA[ update_time < #{endTime} ]]>
        and `type` in
        <foreach collection="dutTypeList" item="dutType" separator="," open="(" close=")">
            #{dutType}
        </foreach>
        <if test="status != null">
            and status = #{status}
        </if>
    </select>

    <select id="getDutRenewLogByOrderCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        <where>
            user_id = #{userId}
            <if test="orderCode != null">
                and order_code = #{orderCode}
            </if>
        </where>
    </select>

    <select id="getDutRenewLogByUpdateTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        where user_id = #{userId} and <![CDATA[ update_time >= #{startTime} ]]>  and <![CDATA[ update_time <= #{endTime} ]]>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="isInclude == true">
            and `type` in
            <foreach collection="dutTypeList" item="dutType" separator="," open="(" close=")">
                #{dutType}
            </foreach>
        </if>
        <if test="isInclude == false">
            and `type` not in
            <foreach collection="dutTypeList" item="dutType" separator="," open="(" close=")">
                #{dutType}
            </foreach>
        </if>
    </select>

    <select id="search" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        <where>
            1 = 1
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="startTime != null">
                and <![CDATA[ update_time > #{startTime} ]]>
            </if>
            <if test="endTime != null">
                and <![CDATA[ update_time < #{endTime} ]]>
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>

    <select id="pageQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        where 1 = 1
        <if test="pageQuery.conditions != null">
            <foreach collection="pageQuery.conditions" item="condition">
                and ${condition.columnName} = #{condition.value}
            </foreach>
        </if>
        <if test="excludeAgreementTypeList != null and excludeAgreementTypeList.size() > 0">
            and agreement_type not in
            <foreach collection="excludeAgreementTypeList" item="agreementType" open="(" close=")" separator=",">
                #{agreementType}
            </foreach>
        </if>
        order by ${pageQuery.orderByCol} ${pageQuery.orderByDirection} limit #{pageQuery.offset}, #{pageQuery.limit}
    </select>

    <select id="countPageQuery"  resultType="java.lang.Long">
        select count(*) from boss_dut_renew_log
        where 1 = 1
        <if test="pageQuery.conditions != null">
            <foreach collection="pageQuery.conditions" item="condition">
                and ${condition.columnName} = #{condition.value}
            </foreach>
        </if>
        <if test="excludeAgreementTypeList != null and excludeAgreementTypeList.size() > 0">
            and agreement_type not in
            <foreach collection="excludeAgreementTypeList" item="agreementType" open="(" close=")" separator=",">
                #{agreementType}
            </foreach>
        </if>
    </select>
</mapper>