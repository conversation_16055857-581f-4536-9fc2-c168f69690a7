<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.dao.DutUserNewDao">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.DutUserNew">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="auto_renew" jdbcType="INTEGER" property="autoRenew"/>
        <result column="sign_key" jdbcType="VARCHAR" property="signKey" />
        <result column="agreement_code" jdbcType="VARCHAR" property="agreementCode" />
        <result column="agreement_no" jdbcType="INTEGER" property="agreementNo"/>
        <result column="agreement_type" jdbcType="TINYINT" property="agreementType" />
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime"/>
        <result column="renew_price" jdbcType="INTEGER" property="renewPrice"/>
        <result column="currency_unit" jdbcType="VARCHAR" property="currencyUnit"/>
        <result column="pay_auto_renew" jdbcType="INTEGER" property="payAutoRenew"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="once_auto_renew" jdbcType="INTEGER" property="onceAutoRenew"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="return_url" jdbcType="VARCHAR" property="returnUrl"/>
        <result column="source_vip_type" jdbcType="INTEGER" property="sourceVipType"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="renew_count" jdbcType="INTEGER" property="renewCount"/>
        <result column="serial_renew_count" jdbcType="INTEGER" property="serialRenewCount"/>
        <result column="platform" jdbcType="BIGINT" property="platform"/>
        <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
        <result column="interrupt_flag" jdbcType="INTEGER" property="interruptFlag"/>
        <result column="next_dut_time" jdbcType="TIMESTAMP" property="nextDutTime"/>
        <result column="pcode" jdbcType="VARCHAR" property="pcode"/>
        <result column="deadline" jdbcType="TIMESTAMP" property="deadline"/>
        <result column="vip_category" jdbcType="TINYINT" property="vipCategory"/>
        <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="remain_periods" jdbcType="INTEGER" property="remainPeriods"/>
        <result column="act_code" jdbcType="VARCHAR" property="actCode"/>
        <result column="act_total_periods" jdbcType="INTEGER" property="actTotalPeriods"/>
        <result column="contract_price" jdbcType="INTEGER" property="contractPrice"/>
        <result column="act_type" jdbcType="INTEGER" property="actType"/>
        <result column="sign_time" jdbcType="TIMESTAMP" property="signTime"/>
        <result column="time_zone" jdbcType="VARCHAR" property="timeZone"/>
        <result column="ext" jdbcType="VARCHAR" property="ext"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, user_id, status, auto_renew, sign_key, agreement_code, agreement_no, agreement_type, type, operate_time, renew_price, currency_unit, pay_auto_renew,
    update_time, once_auto_renew, source, return_url, source_vip_type, vip_type, renew_count, 
    serial_renew_count, platform, platform_code, interrupt_flag, next_dut_time, pcode, deadline, vip_category,
    order_code, amount, remain_periods, act_code, act_total_periods, contract_price, 
    act_type,sign_time,time_zone, ext
    </sql>

    <insert id="insertDutUserSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.DutUserNew" keyProperty="id" useGeneratedKeys="true">
        insert into boss_dut_user_new
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="autoRenew != null">
                auto_renew,
            </if>
            <if test="signKey != null">
                sign_key,
            </if>
            <if test="agreementCode != null">
                agreement_code,
            </if>
            <if test="agreementNo != null">
                agreement_no,
            </if>
            <if test="agreementType != null">
                agreement_type,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="operateTime != null">
                operate_time,
            </if>
            <if test="renewPrice != null">
                renew_price,
            </if>
            <if test="currencyUnit != null">
                currency_unit,
            </if>
            <if test="payAutoRenew != null">
                pay_auto_renew,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="onceAutoRenew != null">
                once_auto_renew,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="returnUrl != null">
                return_url,
            </if>
            <if test="sourceVipType != null">
                source_vip_type,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="renewCount != null">
                renew_count,
            </if>
            <if test="serialRenewCount != null">
                serial_renew_count,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="platformCode != null">
                platform_code,
            </if>
            <if test="interruptFlag != null">
                interrupt_flag,
            </if>
            <if test="nextDutTime != null">
                next_dut_time,
            </if>
            <if test="pcode != null">
                pcode,
            </if>
            <if test="deadline != null">
                deadline,
            </if>
            <if test="vipCategory != null">
                vip_category,
            </if>
            <if test="orderCode != null">
                order_code,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="remainPeriods != null">
                remain_periods,
            </if>
            <if test="actCode != null">
                act_code,
            </if>
            <if test="actTotalPeriods != null">
                act_total_periods,
            </if>
            <if test="contractPrice != null">
                contract_price,
            </if>
            <if test="actType != null">
                act_type,
            </if>
            <if test="signTime != null">
                sign_time,
            </if>
            <if test="timeZone != null">
                time_zone,
            </if>
            <if test="ext != null">
                ext,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="autoRenew != null">
                #{autoRenew,jdbcType=INTEGER},
            </if>
            <if test="signKey != null">
                #{signKey,jdbcType=VARCHAR},
            </if>
            <if test="agreementCode != null">
                #{agreementCode,jdbcType=VARCHAR},
            </if>
            <if test="agreementNo != null">
                #{agreementNo,jdbcType=INTEGER},
            </if>
            <if test="agreementType != null">
                #{agreementType,jdbcType=TINYINT},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="operateTime != null">
                #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="renewPrice != null">
                #{renewPrice,jdbcType=INTEGER},
            </if>
            <if test="currencyUnit != null">
                #{currencyUnit,jdbcType=VARCHAR},
            </if>
            <if test="payAutoRenew != null">
                #{payAutoRenew,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="onceAutoRenew != null">
                #{onceAutoRenew,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                #{source,jdbcType=INTEGER},
            </if>
            <if test="returnUrl != null">
                #{returnUrl,jdbcType=VARCHAR},
            </if>
            <if test="sourceVipType != null">
                #{sourceVipType,jdbcType=INTEGER},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=INTEGER},
            </if>
            <if test="renewCount != null">
                #{renewCount,jdbcType=INTEGER},
            </if>
            <if test="serialRenewCount != null">
                #{serialRenewCount,jdbcType=INTEGER},
            </if>
            <if test="platform != null">
                #{platform,jdbcType=BIGINT},
            </if>
            <if test="platformCode != null">
                #{platformCode,jdbcType=VARCHAR},
            </if>
            <if test="interruptFlag != null">
                #{interruptFlag,jdbcType=INTEGER},
            </if>
            <if test="nextDutTime != null">
                #{nextDutTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pcode != null">
                #{pcode,jdbcType=VARCHAR},
            </if>
            <if test="deadline != null">
                #{deadline,jdbcType=TIMESTAMP},
            </if>
            <if test="vipCategory != null">
                #{vipCategory,jdbcType=TINYINT},
            </if>
            <if test="orderCode != null">
                #{orderCode,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=INTEGER},
            </if>
            <if test="remainPeriods != null">
                #{remainPeriods,jdbcType=INTEGER},
            </if>
            <if test="actCode != null">
                #{actCode,jdbcType=VARCHAR},
            </if>
            <if test="actTotalPeriods != null">
                #{actTotalPeriods,jdbcType=INTEGER},
            </if>
            <if test="contractPrice != null">
                #{contractPrice,jdbcType=INTEGER},
            </if>
            <if test="actType != null">
                #{actType,jdbcType=INTEGER},
            </if>
            <if test="signTime != null">
                #{signTime,jdbcType=TIMESTAMP},
            </if>
            <if test="timeZone != null">
                #{timeZone,jdbcType=VARCHAR},
            </if>
            <if test="ext != null">
                #{ext,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateDutUserByPrimaryKeyAndUserId" parameterType="com.qiyi.vip.trade.autorenew.domain.DutUserNew">
        update boss_dut_user_new
        set status = #{status,jdbcType=INTEGER},
            auto_renew = #{autoRenew,jdbcType=INTEGER},
            sign_key = #{signKey,jdbcType=VARCHAR},
            agreement_code = #{agreementCode,jdbcType=VARCHAR},
            agreement_no = #{agreementNo,jdbcType=INTEGER},
            agreement_type = #{agreementType,jdbcType=TINYINT},
            `type` = #{type,jdbcType=INTEGER},
            operate_time = #{operateTime,jdbcType=TIMESTAMP},
            renew_price = #{renewPrice,jdbcType=INTEGER},
            currency_unit = #{currencyUnit,jdbcType=VARCHAR},
            pay_auto_renew = #{payAutoRenew,jdbcType=INTEGER},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            once_auto_renew = #{onceAutoRenew,jdbcType=INTEGER},
            source = #{source,jdbcType=INTEGER},
            return_url = #{returnUrl,jdbcType=VARCHAR},
            source_vip_type = #{sourceVipType,jdbcType=INTEGER},
            vip_type = #{vipType,jdbcType=INTEGER},
            renew_count = #{renewCount,jdbcType=INTEGER},
            serial_renew_count = #{serialRenewCount,jdbcType=INTEGER},
            platform = #{platform,jdbcType=BIGINT},
            platform_code = #{platformCode,jdbcType=VARCHAR},
            interrupt_flag = #{interruptFlag,jdbcType=INTEGER},
            next_dut_time = #{nextDutTime,jdbcType=TIMESTAMP},
            pcode = #{pcode,jdbcType=VARCHAR},
            deadline = #{deadline,jdbcType=TIMESTAMP},
            vip_category = #{vipCategory,jdbcType=TINYINT},
            order_code = #{orderCode,jdbcType=VARCHAR},
            amount = #{amount,jdbcType=INTEGER},
            remain_periods = #{remainPeriods,jdbcType=INTEGER},
            act_code = #{actCode,jdbcType=VARCHAR},
            act_total_periods = #{actTotalPeriods,jdbcType=INTEGER},
            contract_price = #{contractPrice,jdbcType=INTEGER},
            act_type = #{actType,jdbcType=INTEGER},
            sign_time =   #{signTime,jdbcType=TIMESTAMP},
            time_zone =   #{timeZone,jdbcType=VARCHAR},
            ext =   #{ext,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER} and user_id = #{userId}
    </update>

    <update id="updateDutUserByPrimaryKeyAndUserIdSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.vo.DutUserNewVO">
        update boss_dut_user_new
        <set>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="autoRenew != null">
                auto_renew = #{autoRenew,jdbcType=INTEGER},
            </if>
            <if test="signKey != null">
                sign_key = #{signKey,jdbcType=VARCHAR},
            </if>
            <if test="agreementCode != null">
                agreement_code = #{agreementCode,jdbcType=VARCHAR},
            </if>
            <if test="agreementNo != null">
                agreement_no = #{agreementNo,jdbcType=INTEGER},
            </if>
            <if test="agreementType != null">
                agreement_type = #{agreementType,jdbcType=TINYINT},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="operateTime != null">
                operate_time = #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="renewPrice != null">
                renew_price = #{renewPrice,jdbcType=INTEGER},
            </if>
            <if test="currencyUnit != null">
                currency_unit = #{currencyUnit,jdbcType=VARCHAR},
            </if>
            <if test="payAutoRenew != null">
                pay_auto_renew = #{payAutoRenew,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="onceAutoRenew != null">
                once_auto_renew = #{onceAutoRenew,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                source = #{source,jdbcType=INTEGER},
            </if>
            <if test="returnUrl != null">
                return_url = #{returnUrl,jdbcType=VARCHAR},
            </if>
            <if test="sourceVipType != null">
                source_vip_type = #{sourceVipType,jdbcType=INTEGER},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=INTEGER},
            </if>
            <if test="renewCount != null">
                renew_count = #{renewCount,jdbcType=INTEGER},
            </if>
            <if test="serialRenewCount != null">
                serial_renew_count = #{serialRenewCount,jdbcType=INTEGER},
            </if>
            <if test="platform != null">
                platform = #{platform,jdbcType=BIGINT},
            </if>
            <if test="platformCode != null">
                platform_code = #{platformCode,jdbcType=VARCHAR},
            </if>
            <if test="interruptFlag != null">
                interrupt_flag = #{interruptFlag,jdbcType=INTEGER},
            </if>
            <if test="nextDutTime != null">
                next_dut_time = #{nextDutTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pcode != null">
                pcode = #{pcode,jdbcType=VARCHAR},
            </if>
            <if test="deadline != null">
                deadline = #{deadline,jdbcType=TIMESTAMP},
            </if>
            <if test="vipCategory != null">
                vip_category = #{vipCategory,jdbcType=TINYINT},
            </if>
            <if test="orderCode != null">
                order_code = #{orderCode,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=INTEGER},
            </if>
            <if test="remainPeriods != null">
                remain_periods = #{remainPeriods,jdbcType=INTEGER},
            </if>
            <if test="actCode != null">
                act_code = #{actCode,jdbcType=VARCHAR},
            </if>
            <if test="actTotalPeriods != null">
                act_total_periods = #{actTotalPeriods,jdbcType=INTEGER},
            </if>
            <if test="contractPrice != null">
                contract_price = #{contractPrice,jdbcType=INTEGER},
            </if>
            <if test="actType != null">
                act_type = #{actType,jdbcType=INTEGER},
            </if>
            <if test="signTime != null">
                sign_time = #{signTime,jdbcType=TIMESTAMP},
            </if>
            <if test="timeZone != null">
                time_zone = #{timeZone,jdbcType=VARCHAR},
            </if>
            <if test="ext != null">
                ext = #{ext,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id, jdbcType=BIGINT} and user_id = #{userId}
    </update>

    <update id="updateByUserIdAndVipType">
      update boss_dut_user_new set deadline = #{deadline}, vip_category = #{vipCategory}, update_time = #{updateTime}
      where user_id = #{userId} and vip_type = #{vipType}
    </update>

    <update id="updateDutUserDeadline">
      update boss_dut_user_new set deadline = #{deadline}, update_time = #{updateTime}
      where user_id = #{userId} and vip_type = #{vipType} and `type` = #{dutType}
        and deadline = #{originalDeadline}
    </update>

    <update id="participateByUser">
      update boss_dut_user_new
      set update_time = #{updateTime}
      , remain_periods = #{nowPeriods}
      , act_code = #{act.actCode}
      , act_total_periods = #{act.actPeriods}
      , renew_price = #{act.actPrice}
      , act_type = #{act.actType}
      , order_code = #{orderCode}
      , contract_price = #{contractPrice}
      where user_id = #{dutUserNew.userId} and vip_type = #{dutUserNew.vipType} and `type` = #{dutUserNew.type}
        and agreement_type = #{dutUserNew.agreementType}
    </update>

    <update id="changeActPeriodsByDutUser">
       update boss_dut_user_new
       set update_time = #{updateTime}, remain_periods = #{newPeriods}
       where user_id = #{dutUserNew.userId} and `type` = #{dutUserNew.type} and vip_type = #{dutUserNew.vipType}
        and agreement_type = #{dutUserNew.agreementType}
    </update>

    <update id="resetActPeriodsByDutUser">
       update boss_dut_user_new
       set update_time = #{updateTime}, remain_periods = 0, act_code = '', renew_price = #{originalPrice}, act_type = null
       where user_id = #{dutUserNew.userId} and `type` = #{dutUserNew.type} and vip_type = #{dutUserNew.vipType}
        and agreement_type = #{dutUserNew.agreementType}
    </update>

    <update id="executeUpdate">
      update boss_dut_user_new set update_time = #{updateTime}, ${setSql}
      where ${whereSql}
    </update>

    <update id="cancelAutoRenew">
        update boss_dut_user_new
        <set>
            update_time = #{updateTime},
            auto_renew = #{dutUserNewVO.autoRenew},
            once_auto_renew = #{dutUserNewVO.onceAutoRenew},
            serial_renew_count = #{dutUserNewVO.serialRenewCount},
            <if test="dutUserNewVO.actCode != null">
                act_code = #{dutUserNewVO.actCode},
            </if>
            <if test="dutUserNewVO.interruptFlag != null">
                interrupt_flag = #{dutUserNewVO.interruptFlag},
            </if>
            <if test="dutUserNewVO.operateTime != null">
                operate_time = #{dutUserNewVO.operateTime},
            </if>
            <if test="dutUserNewVO.nextDutTime != null">
                next_dut_time = #{dutUserNewVO.nextDutTime},
            </if>
            <if test="dutUserNewVO.renewPrice != null">
                renew_price = #{dutUserNewVO.renewPrice},
            </if>
            <if test="operationType == 1">
                status = 2, renew_price = null, next_dut_time = null,
            </if>
            <if test="originalActType == null || originalActType != 2">
                remain_periods = 0, act_total_periods = 0, act_code = '', contract_price = null, act_type = null,
            </if>
        </set>
        where id = #{dutUserNewVO.id, jdbcType=INTEGER} and user_id = #{dutUserNewVO.userId}
    </update>
    <update id="updateNextDutTime">
        update boss_dut_user_new
        set next_dut_time = #{nextDutTime}, update_time = now()
        where user_id = #{userId} and vip_type = #{vipType} and type = #{dutType} and agreement_type = #{agreementType}
    </update>

    <select id="listDutUserSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.vo.DutUserNewVO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_user_new
        <where>
            1 = 1
            <if test="userId != null">
                and user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="status != null">
                and status = #{status, jdbcType=INTEGER}
            </if>
            <if test="autoRenew != null">
                and auto_renew = #{autoRenew,jdbcType=INTEGER}
            </if>
            <if test="signKey != null">
                and sign_key = #{signKey,jdbcType=VARCHAR}
            </if>
            <if test="agreementCode != null">
                and agreement_code = #{agreementCode,jdbcType=VARCHAR}
            </if>
            <if test="agreementNo != null">
                and agreement_no = #{agreementNo,jdbcType=INTEGER}
            </if>
            <if test="agreementType != null">
                and agreement_type = #{agreementType,jdbcType=TINYINT}
            </if>
            <if test="type != null">
                and `type` = #{type,jdbcType=INTEGER}
            </if>
            <if test="operateTime != null">
                and operate_time = #{operateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="renewPrice != null">
                and renew_price = #{renewPrice,jdbcType=INTEGER}
            </if>
            <if test="currencyUnit != null">
                and currency_unit = #{currencyUnit,jdbcType=VARCHAR}
            </if>
            <if test="payAutoRenew != null">
                and pay_auto_renew = #{payAutoRenew,jdbcType=INTEGER}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="onceAutoRenew != null">
                and once_auto_renew = #{onceAutoRenew,jdbcType=INTEGER}
            </if>
            <if test="source != null">
                and source = #{source,jdbcType=INTEGER}
            </if>
            <if test="returnUrl != null">
                and return_url = #{returnUrl,jdbcType=VARCHAR}
            </if>
            <if test="sourceVipType != null">
                and source_vip_type = #{sourceVipType,jdbcType=INTEGER}
            </if>
            <if test="vipType != null">
                and vip_type = #{vipType,jdbcType=INTEGER}
            </if>
            <if test="renewCount != null">
                and renew_count = #{renewCount,jdbcType=INTEGER}
            </if>
            <if test="serialRenewCount != null">
                and serial_renew_count = #{serialRenewCount,jdbcType=INTEGER}
            </if>
            <if test="platform != null">
                and platform = #{platform,jdbcType=BIGINT}
            </if>
            <if test="platformCode != null">
                and platform_code = #{platformCode,jdbcType=VARCHAR}
            </if>
            <if test="interruptFlag != null">
                and interrupt_flag = #{interruptFlag,jdbcType=INTEGER}
            </if>
            <if test="nextDutTime != null">
                next_dut_time = #{nextDutTime,jdbcType=TIMESTAMP}
            </if>
            <if test="pcode != null">
                and pcode = #{pcode,jdbcType=VARCHAR}
            </if>
            <if test="deadline != null">
                and deadline = #{deadline,jdbcType=TIMESTAMP}
            </if>
            <if test="vipCategory != null">
                and vip_category = #{vipCategory,jdbcType=TINYINT}
            </if>
            <if test="orderCode != null">
                and order_code = #{orderCode,jdbcType=VARCHAR}
            </if>
            <if test="amount != null">
                and amount = #{amount,jdbcType=INTEGER}
            </if>
            <if test="remainPeriods != null">
                and remain_periods = #{remainPeriods,jdbcType=INTEGER}
            </if>
            <if test="actCode != null">
                and act_code = #{actCode,jdbcType=VARCHAR}
            </if>
            <if test="actTotalPeriods != null">
                and act_total_periods = #{actTotalPeriods,jdbcType=INTEGER}
            </if>
            <if test="contractPrice != null">
                and contract_price = #{contractPrice,jdbcType=INTEGER}
            </if>
            <if test="actType != null">
                and act_type = #{actType,jdbcType=INTEGER}
            </if>
            <if test="timestampRange != null and timeRangeColumn != null">
                and #{timeRangeColumn} between #{timestampRange.minimum} and #{timestampRange.maximum}
            </if>
        </where>
        <if test="sqlSortVOS != null and sqlSortVOS.size > 0">
          order by
          <foreach collection="sqlSortVOS" item="sortVO" separator=",">
              ${sortVO.column} ${sortVO.direction}
          </foreach>
        </if>
        <if test="offset != null and limit != null">
            limit #{offset}, #{limit}
        </if>
        <if test="offset == null and limit != null">
            limit #{limit}
        </if>
    </select>

    <select id="getAutoRenewUsersWithExcludeTypes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${shardTblName}
        where <![CDATA[ deadline >= #{startTime} ]]> and <![CDATA[ deadline < #{endTime} ]]>
        and vip_type = #{vipType} and auto_renew = #{autoRenew} and agreement_type = #{agreementType}
        <if test="sourceVipType == null">
            and source_vip_type is null
        </if>
        <if test="sourceVipType != null">
            and source_vip_type = #{sourceVipType}
        </if>
        <if test="excludeTypeList != null and excludeTypeList.size > 0">
            and type not in
            <foreach collection="excludeTypeList" item="dutType" open="(" close=")" separator=",">
                #{dutType}
            </foreach>
        </if>
        order by user_id
    </select>

    <select id="selectByPrimaryKeyAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_user_new
        where id = #{id} and user_id = #{userId}
    </select>

    <select id="countPageQuery" parameterType="com.qiyi.vip.trade.autorenew.vo.PageQuery" resultType="java.lang.Long">
        select count(*) from boss_dut_user_new
        where 1 = 1
        <if test="conditions != null">
            <foreach collection="conditions" item="condition">
                and ${condition.columnName} = #{condition.value}
            </foreach>
        </if>
    </select>

    <select id="pageQuery" resultMap="BaseResultMap" parameterType="com.qiyi.vip.trade.autorenew.vo.PageQuery">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_user_new
        where 1 = 1
        <if test="conditions != null">
            <foreach collection="conditions" item="condition">
                and ${condition.columnName} = #{condition.value}
            </foreach>
        </if>
        order by ${orderByCol} ${orderByDirection} limit #{offset}, #{limit}
    </select>

    <!--    批量查询签约关系-->
    <select id="listDutUserByVipTypes" resultMap="BaseResultMap">
       select <include refid="Base_Column_List"/>
       from boss_dut_user_new
       where user_id = #{userId}
        <if test="vipTypes != null and vipTypes.size() > 0">
            and vip_type in
            <foreach collection="vipTypes" item="vipType" open="(" close=")" separator=",">
                #{vipType}
            </foreach>
        </if>
        <if test="autoRenew != null">
            and auto_renew = #{autoRenew}
        </if>
        <if test="agreementType != null">
            and agreement_type = #{agreementType}
        </if>
    </select>
</mapper>