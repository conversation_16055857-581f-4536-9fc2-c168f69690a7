<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.dao.UserPasswordFreeDao">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.UserPasswordFree">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="dut_type" jdbcType="INTEGER" property="dutType"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, user_id, status, dut_type, pay_channel, create_time, update_time
    </sql>

    <insert id="insert" parameterType="com.qiyi.vip.trade.autorenew.domain.UserPasswordFree">
        insert into boss_user_password_free (user_id, status, dut_type, pay_channel, create_time, update_time)
        values ( #{userId}, #{status}, #{dutType}, #{payChannel}, NOW(), NOW() )
    </insert>

    <update id="update" parameterType="com.qiyi.vip.trade.autorenew.domain.UserPasswordFree">
        update boss_user_password_free
        set status = #{status},
            dut_type = #{dutType},
            pay_channel = #{payChannel},
            update_time = NOW()
        where id = #{id} and user_id = #{userId}
    </update>

    <select id="getByUserIdAndDutTypeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_user_password_free
        where user_id = #{userId}
        and dut_type in
        <foreach collection="dutTypeList" item="dutType" open="(" close=")" separator=",">
            #{dutType}
        </foreach>
    </select>

    <select id="getByUserIdAndDutType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_user_password_free
        where user_id = #{userId}
        and dut_type = #{dutType}
    </select>
    <select id="getByUserIdAndpayChannel" resultType="com.qiyi.vip.trade.autorenew.domain.UserPasswordFree">
        select
        <include refid="Base_Column_List"/>
        from boss_user_password_free
        where user_id = #{userId}
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="payChannel != null">
            and pay_channel = #{payChannel}
        </if>
        order by update_time
    </select>

    <select id="getUserOpenedList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_user_password_free
        where user_id = #{userId}
        and status = 1
    </select>


</mapper>