<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.dao.UserAgreementMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.DutUserNew">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="auto_renew" jdbcType="INTEGER" property="autoRenew"/>
        <result column="sign_key" jdbcType="VARCHAR" property="signKey"/>
        <result column="agreement_code" jdbcType="VARCHAR" property="agreementCode"/>
        <result column="agreement_no" jdbcType="INTEGER" property="agreementNo"/>
        <result column="agreement_type" jdbcType="TINYINT" property="agreementType"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime"/>
        <result column="renew_price" jdbcType="INTEGER" property="renewPrice"/>
        <result column="currency_unit" jdbcType="VARCHAR" property="currencyUnit"/>
        <result column="pay_auto_renew" jdbcType="INTEGER" property="payAutoRenew"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="once_auto_renew" jdbcType="INTEGER" property="onceAutoRenew"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="return_url" jdbcType="VARCHAR" property="returnUrl"/>
        <result column="source_vip_type" jdbcType="INTEGER" property="sourceVipType"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="renew_count" jdbcType="INTEGER" property="renewCount"/>
        <result column="serial_renew_count" jdbcType="INTEGER" property="serialRenewCount"/>
        <result column="platform" jdbcType="BIGINT" property="platform"/>
        <result column="platform_code" jdbcType="VARCHAR" property="platformCode"/>
        <result column="interrupt_flag" jdbcType="INTEGER" property="interruptFlag"/>
        <result column="vip_category" jdbcType="TINYINT" property="vipCategory"/>
        <result column="deadline" jdbcType="TIMESTAMP" property="deadline"/>
        <result column="pcode" jdbcType="VARCHAR" property="pcode"/>
        <result column="next_dut_time" jdbcType="TIMESTAMP" property="nextDutTime"/>
        <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="remain_periods" jdbcType="INTEGER" property="remainPeriods"/>
        <result column="act_code" jdbcType="VARCHAR" property="actCode"/>
        <result column="act_total_periods" jdbcType="INTEGER" property="actTotalPeriods"/>
        <result column="contract_price" jdbcType="INTEGER" property="contractPrice"/>
        <result column="act_type" jdbcType="INTEGER" property="actType"/>
        <result column="sign_time" jdbcType="TIMESTAMP" property="signTime"/>
        <result column="time_zone" jdbcType="VARCHAR" property="timeZone"/>
        <result column="ext" jdbcType="VARCHAR" property="ext"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, user_id, status, auto_renew, sign_key, agreement_code, agreement_no, agreement_type, type,
        operate_time, renew_price, currency_unit, pay_auto_renew, update_time, once_auto_renew,
        source, return_url, source_vip_type, vip_type, renew_count, serial_renew_count, platform, platform_code,
        interrupt_flag, vip_category, deadline, pcode, next_dut_time, order_code, amount,
        remain_periods, act_code, act_total_periods, contract_price, act_type, sign_time,
        time_zone, ext
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from boss_dut_user_new
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.qiyi.vip.trade.autorenew.domain.DutUserNew" useGeneratedKeys="true" keyProperty="id">
        insert into boss_dut_user_new
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="autoRenew != null">
                auto_renew,
            </if>
            <if test="signKey != null">
                sign_key,
            </if>
            <if test="agreementCode != null">
                agreement_code,
            </if>
            <if test="agreementNo != null">
                agreement_no,
            </if>
            <if test="agreementType != null">
                agreement_type,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="operateTime != null">
                operate_time,
            </if>
            <if test="renewPrice != null">
                renew_price,
            </if>
            <if test="currencyUnit != null">
                currency_unit,
            </if>
            <if test="payAutoRenew != null">
                pay_auto_renew,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="onceAutoRenew != null">
                once_auto_renew,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="returnUrl != null">
                return_url,
            </if>
            <if test="sourceVipType != null">
                source_vip_type,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="renewCount != null">
                renew_count,
            </if>
            <if test="serialRenewCount != null">
                serial_renew_count,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="platformCode != null">
                platform_code,
            </if>
            <if test="interruptFlag != null">
                interrupt_flag,
            </if>
            <if test="vipCategory != null">
                vip_category,
            </if>
            <if test="deadline != null">
                deadline,
            </if>
            <if test="pcode != null">
                pcode,
            </if>
            <if test="nextDutTime != null">
                next_dut_time,
            </if>
            <if test="orderCode != null">
                order_code,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="remainPeriods != null">
                remain_periods,
            </if>
            <if test="actCode != null">
                act_code,
            </if>
            <if test="actTotalPeriods != null">
                act_total_periods,
            </if>
            <if test="contractPrice != null">
                contract_price,
            </if>
            <if test="actType != null">
                act_type,
            </if>
            <if test="signTime != null">
                sign_time,
            </if>
            <if test="timeZone != null">
                time_zone,
            </if>
            <if test="ext != null">
                ext,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="autoRenew != null">
                #{autoRenew,jdbcType=INTEGER},
            </if>
            <if test="signKey != null">
                #{signKey,jdbcType=VARCHAR},
            </if>
            <if test="agreementCode != null">
                #{agreementCode,jdbcType=VARCHAR},
            </if>
            <if test="agreementNo != null">
                #{agreementNo,jdbcType=INTEGER},
            </if>
            <if test="agreementType != null">
                #{agreementType,jdbcType=TINYINT},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="operateTime != null">
                #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="renewPrice != null">
                #{renewPrice,jdbcType=INTEGER},
            </if>
            <if test="currencyUnit != null">
                #{currencyUnit,jdbcType=VARCHAR},
            </if>
            <if test="payAutoRenew != null">
                #{payAutoRenew,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="onceAutoRenew != null">
                #{onceAutoRenew,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                #{source,jdbcType=INTEGER},
            </if>
            <if test="returnUrl != null">
                #{returnUrl,jdbcType=VARCHAR},
            </if>
            <if test="sourceVipType != null">
                #{sourceVipType,jdbcType=INTEGER},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=INTEGER},
            </if>
            <if test="renewCount != null">
                #{renewCount,jdbcType=INTEGER},
            </if>
            <if test="serialRenewCount != null">
                #{serialRenewCount,jdbcType=INTEGER},
            </if>
            <if test="platform != null">
                #{platform,jdbcType=BIGINT},
            </if>
            <if test="platformCode != null">
                #{platformCode,jdbcType=VARCHAR},
            </if>
            <if test="interruptFlag != null">
                #{interruptFlag,jdbcType=INTEGER},
            </if>
            <if test="vipCategory != null">
                #{vipCategory,jdbcType=TINYINT},
            </if>
            <if test="deadline != null">
                #{deadline,jdbcType=TIMESTAMP},
            </if>
            <if test="pcode != null">
                #{pcode,jdbcType=VARCHAR},
            </if>
            <if test="nextDutTime != null">
                #{nextDutTime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderCode != null">
                #{orderCode,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=INTEGER},
            </if>
            <if test="remainPeriods != null">
                #{remainPeriods,jdbcType=INTEGER},
            </if>
            <if test="actCode != null">
                #{actCode,jdbcType=VARCHAR},
            </if>
            <if test="actTotalPeriods != null">
                #{actTotalPeriods,jdbcType=INTEGER},
            </if>
            <if test="contractPrice != null">
                #{contractPrice,jdbcType=INTEGER},
            </if>
            <if test="actType != null">
                #{actType,jdbcType=INTEGER},
            </if>
            <if test="signTime != null">
                #{signTime,jdbcType=TIMESTAMP},
            </if>
            <if test="timeZone != null">
                #{timeZone,jdbcType=VARCHAR},
            </if>
            <if test="ext != null">
                #{ext,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.qiyi.vip.trade.autorenew.domain.DutUserNew">
        update boss_dut_user_new
        <set>
            update_time = now(),
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="autoRenew != null">
                auto_renew = #{autoRenew,jdbcType=INTEGER},
            </if>
            <if test="signKey != null">
                sign_key = #{signKey,jdbcType=VARCHAR},
            </if>
            <if test="agreementCode != null">
                agreement_code = #{agreementCode,jdbcType=VARCHAR},
            </if>
            <if test="agreementNo != null">
                agreement_no = #{agreementNo,jdbcType=INTEGER},
            </if>
            <if test="agreementType != null">
                agreement_type = #{agreementType,jdbcType=TINYINT},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="operateTime != null">
                operate_time = #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="renewPrice != null">
                renew_price = #{renewPrice,jdbcType=INTEGER},
            </if>
            <if test="currencyUnit != null">
                currency_unit = #{currencyUnit,jdbcType=VARCHAR},
            </if>
            <if test="payAutoRenew != null">
                pay_auto_renew = #{payAutoRenew,jdbcType=INTEGER},
            </if>
            <if test="onceAutoRenew != null">
                once_auto_renew = #{onceAutoRenew,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                source = #{source,jdbcType=INTEGER},
            </if>
            <if test="returnUrl != null">
                return_url = #{returnUrl,jdbcType=VARCHAR},
            </if>
            <if test="sourceVipType != null">
                source_vip_type = #{sourceVipType,jdbcType=INTEGER},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=INTEGER},
            </if>
            <if test="renewCount != null">
                renew_count = #{renewCount,jdbcType=INTEGER},
            </if>
            <if test="serialRenewCount != null">
                serial_renew_count = #{serialRenewCount,jdbcType=INTEGER},
            </if>
            <if test="platform != null">
                platform = #{platform,jdbcType=BIGINT},
            </if>
            <if test="platformCode != null">
                platform_code = #{platformCode,jdbcType=VARCHAR},
            </if>
            <if test="interruptFlag != null">
                interrupt_flag = #{interruptFlag,jdbcType=INTEGER},
            </if>
            <if test="vipCategory != null">
                vip_category = #{vipCategory,jdbcType=TINYINT},
            </if>
            <if test="deadline != null">
                deadline = #{deadline,jdbcType=TIMESTAMP},
            </if>
            <if test="pcode != null">
                pcode = #{pcode,jdbcType=VARCHAR},
            </if>
            <if test="nextDutTime != null">
                next_dut_time = #{nextDutTime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderCode != null">
                order_code = #{orderCode,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=INTEGER},
            </if>
            <if test="remainPeriods != null">
                remain_periods = #{remainPeriods,jdbcType=INTEGER},
            </if>
            <if test="actCode != null">
                act_code = #{actCode,jdbcType=VARCHAR},
            </if>
            <if test="actTotalPeriods != null">
                act_total_periods = #{actTotalPeriods,jdbcType=INTEGER},
            </if>
            <if test="contractPrice != null">
                contract_price = #{contractPrice,jdbcType=INTEGER},
            </if>
            <if test="actType != null">
                act_type = #{actType,jdbcType=INTEGER},
            </if>
            <if test="signTime != null">
                sign_time = #{signTime,jdbcType=TIMESTAMP},
            </if>
            <if test="timeZone != null">
                time_zone = #{timeZone,jdbcType=VARCHAR},
            </if>
            <if test="ext != null">
                ext = #{ext,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </update>

    <update id="save" parameterType="com.qiyi.vip.trade.autorenew.domain.DutUserNew">
        update boss_dut_user_new
        set status = #{status,jdbcType=INTEGER},
        auto_renew = #{autoRenew,jdbcType=INTEGER},
        sign_key = #{signKey,jdbcType=VARCHAR},
        agreement_code = #{agreementCode,jdbcType=VARCHAR},
        agreement_no = #{agreementNo,jdbcType=INTEGER},
        agreement_type = #{agreementType,jdbcType=TINYINT},
        type = #{type,jdbcType=INTEGER},
        operate_time = #{operateTime,jdbcType=TIMESTAMP},
        renew_price = #{renewPrice,jdbcType=INTEGER},
        currency_unit = #{currencyUnit,jdbcType=VARCHAR},
        pay_auto_renew = #{payAutoRenew,jdbcType=INTEGER},
        update_time = now(),
        once_auto_renew = #{onceAutoRenew,jdbcType=INTEGER},
        source = #{source,jdbcType=INTEGER},
        return_url = #{returnUrl,jdbcType=VARCHAR},
        source_vip_type = #{sourceVipType,jdbcType=INTEGER},
        vip_type = #{vipType,jdbcType=INTEGER},
        renew_count = #{renewCount,jdbcType=INTEGER},
        serial_renew_count = #{serialRenewCount,jdbcType=INTEGER},
        platform = #{platform,jdbcType=BIGINT},
        platform_code = #{platformCode,jdbcType=VARCHAR},
        interrupt_flag = #{interruptFlag,jdbcType=INTEGER},
        vip_category = #{vipCategory,jdbcType=TINYINT},
        deadline = #{deadline,jdbcType=TIMESTAMP},
        pcode = #{pcode,jdbcType=VARCHAR},
        next_dut_time = #{nextDutTime,jdbcType=TIMESTAMP},
        order_code = #{orderCode,jdbcType=VARCHAR},
        amount = #{amount,jdbcType=INTEGER},
        remain_periods = #{remainPeriods,jdbcType=INTEGER},
        act_code = #{actCode,jdbcType=VARCHAR},
        act_total_periods = #{actTotalPeriods,jdbcType=INTEGER},
        contract_price = #{contractPrice,jdbcType=INTEGER},
        act_type = #{actType,jdbcType=INTEGER},
        sign_time = #{signTime,jdbcType=TIMESTAMP},
        time_zone = #{timeZone,jdbcType=VARCHAR},
        ext = #{ext,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </update>

    <update id="cancelAutoRenew" parameterType="map">
        update boss_dut_user_new
        <set>
            auto_renew = 0, once_auto_renew = 1, serial_renew_count = 0, interrupt_flag = 0
            , next_dut_time = null, renew_price = null
            <if test="unBind">
                , status = 0
            </if>
            <if test="actType == null || actType != 2">
                , remain_periods = 0, act_total_periods = 0, act_code = null, contract_price = null, act_type = null
            </if>
            , update_time = now()
        </set>
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </update>

    <update id="updateStatus" parameterType="map">
        update boss_dut_user_new
        set auto_renew = #{autoRenew,jdbcType=TINYINT}, update_time = now()
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </update>

    <update id="invalidOrFinishRecord" parameterType="map">
        update boss_dut_user_new
        set auto_renew = #{autoRenew,jdbcType=TINYINT},
            renew_price = null, serial_renew_count = 0,
            deadline = null, next_dut_time = null, status = 0,
            update_time = now()
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </update>

    <update id="updateNextDutTime" parameterType="map">
        update boss_dut_user_new
        set next_dut_time = #{nextDutTime,jdbcType=TIMESTAMP}, update_time = now()
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </update>

    <update id="updateNextDutTimeAndAgreementNo" parameterType="map">
        update boss_dut_user_new
        set next_dut_time = #{nextDutTime,jdbcType=TIMESTAMP}, agreement_no = #{agreementNo,jdbcType=INTEGER}, update_time = now()
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </update>

    <update id="updateNextDutTimeAndExt" parameterType="map">
        update boss_dut_user_new
        set next_dut_time = #{nextDutTime,jdbcType=TIMESTAMP}, ext = #{ext}, update_time = now()
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </update>

    <update id="updateNextDutTimeAndDeadline">
        update boss_dut_user_new
        set ext = #{ext}, update_time = now()
        <if test="nextDutTime != null">
            , next_dut_time = #{nextDutTime,jdbcType=TIMESTAMP}
        </if>
        <if test="deadline != null">
            , deadline = #{deadline,jdbcType=TIMESTAMP}
        </if>
        where user_id = #{userId,jdbcType=BIGINT}
        and id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateRenewCount" parameterType="map">
        update boss_dut_user_new
        set renew_count = #{renewCount,jdbcType=INTEGER}, serial_renew_count = #{serialRenewCount,jdbcType=INTEGER}, update_time = now()
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </update>

    <update id="incrementRenewCount" parameterType="map">
        update boss_dut_user_new
        set renew_count = renew_count + 1, serial_renew_count = serial_renew_count + 1, update_time = now()
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </update>

    <update id="incrementRenewCountAndResetNextDutTime" parameterType="map">
        update boss_dut_user_new
        set renew_count = renew_count + 1,
        serial_renew_count = serial_renew_count + 1,
        next_dut_time = null,
        update_time = now()
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </update>

    <update id="batchUpdateDeadline">
        update boss_dut_user_new set deadline = #{deadline,jdbcType=TIMESTAMP},update_time = now()
        where user_id = #{userId,jdbcType=BIGINT} and id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateUserAccountBindStatus" parameterType="map">
        update boss_dut_user_new
        set status = #{status, jdbcType=INTEGER}
        where user_id = #{userId,jdbcType=BIGINT} and type = #{dutType, jdbcType=INTEGER}
    </update>

    <update id="batchUpdateDeadlineAndNextDutTime">
        update boss_dut_user_new
        set deadline = #{deadline,jdbcType=TIMESTAMP},
            next_dut_time = #{nextDutTime,jdbcType=TIMESTAMP},
            update_time = now()
        where user_id = #{userId,jdbcType=BIGINT}
        and id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="batchUpdateNextDutTime">
        update boss_dut_user_new
        set next_dut_time = #{nextDutTime,jdbcType=TIMESTAMP},
        update_time = now()
        where user_id = #{userId,jdbcType=BIGINT}
        and
        id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_user_new
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </select>

    <select id="selectByAgreementNoAndVipType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_user_new
        where user_id = #{userId,jdbcType=BIGINT}
        and agreement_no = #{agreementNo,jdbcType=INTEGER}
        <if test="vipType != null">
            and vip_type = #{vipType,jdbcType=INTEGER}
        </if>
        <if test="autoRenew != null">
            and auto_renew = #{autoRenew,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectByAgreementNoAndDutType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_user_new
        where user_id = #{userId,jdbcType=BIGINT}
        <if test="agreementNo != null">
            and agreement_no = #{agreementNo,jdbcType=INTEGER}
        </if>
        <if test="dutType != null">
            and type = #{dutType,jdbcType=INTEGER}
        </if>
        <if test="sourceVipType != null">
            and source_vip_type = #{sourceVipType,jdbcType=INTEGER}
        </if>
        <if test="vipType != null">
            and vip_type = #{vipType,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectByDutTypeAndVipType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_user_new
        where user_id = #{userId,jdbcType=BIGINT}
        and type = #{dutType,jdbcType=INTEGER}
        and vip_type = #{vipType,jdbcType=INTEGER}
    </select>

    <select id="selectByVipType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_user_new
        where user_id = #{userId,jdbcType=BIGINT}
        and vip_type = #{vipType,jdbcType=INTEGER}
        <if test="sourceVipType != null">
            and source_vip_type = #{sourceVipType,jdbcType=INTEGER}
        </if>
        <if test="autoRenew != null">
            and auto_renew = #{autoRenew,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectEffectByUid" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_user_new
        where user_id = #{userId,jdbcType=BIGINT} and auto_renew != 0
    </select>

    <select id="selectByAgreementTypeAndVipType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_user_new
        where user_id = #{userId,jdbcType=BIGINT}
        and agreement_type = #{agreementType,jdbcType=TINYINT}
        <if test="vipType != null">
            and vip_type = #{vipType,jdbcType=INTEGER}
        </if>
        <if test="autoRenew != null">
            and auto_renew = #{autoRenew,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectByExcludeAgreementTypesAndVipTypes" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_user_new
        where user_id = #{userId,jdbcType=BIGINT}
        <if test="excludeAgreementTypeList != null and excludeAgreementTypeList.size() > 0">
            and agreement_type not in
            <foreach collection="excludeAgreementTypeList" item="agreementType" open="(" close=")" separator=",">
                #{agreementType}
            </foreach>
        </if>
        <if test="vipTypes != null and vipTypes.size() > 0 ">
            and vip_type in
            <foreach collection="vipTypes" item="vipType" open="(" close=")" separator=",">
                #{vipType}
            </foreach>
        </if>
        <if test="autoRenew != null">
            and auto_renew = #{autoRenew,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectByAgreementInfo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_user_new
        where user_id = #{userId,jdbcType=BIGINT}
        <if test="agreementNo != null">
            and agreement_no = #{agreementNo,jdbcType=INTEGER}
        </if>
        <if test="agreementTypeList != null and agreementTypeList.size() > 0">
            and agreement_type in
            <foreach collection="agreementTypeList" item="agreementType" open="(" close=")" separator=",">
                #{agreementType}
            </foreach>
        </if>
        <if test="agreementStatusList != null and agreementStatusList.size() > 0">
            and auto_renew in
            <foreach collection="agreementStatusList" item="agreementStatus" open="(" close=")" separator=",">
                #{agreementStatus}
            </foreach>
        </if>
    </select>

    <select id="selectBySignKey" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_user_new
        where user_id = #{userId,jdbcType=BIGINT}
        and sign_key = #{signKey,jdbcType=VARCHAR}
    </select>

    <select id="getUserByAllAgreements" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_user_new
        where user_id = #{userId,jdbcType=BIGINT}
        <if test="autoRenew != null">
            and auto_renew = #{autoRenew,jdbcType=INTEGER}
        </if>
    </select>

</mapper>