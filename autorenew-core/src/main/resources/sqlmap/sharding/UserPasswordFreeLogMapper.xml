<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.dao.UserPasswordFreeLogDao">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.UserPasswordFreeLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="dut_type" jdbcType="INTEGER" property="dutType"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="sign_type" jdbcType="INTEGER" property="signType"/>
        <result column="operation" jdbcType="INTEGER" property="operation"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="ext_info" jdbcType="VARCHAR" property="extInfo"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, user_id, dut_type, pay_channel, operation, source, create_time, update_time
    </sql>

    <insert id="insert" parameterType="com.qiyi.vip.trade.autorenew.domain.UserPasswordFreeLog">
        insert into boss_user_password_free_log (user_id, dut_type, pay_channel, sign_type, operation, source, ext_info, create_time, update_time)
        values ( #{userId}, #{dutType}, #{payChannel}, #{signType}, #{operation}, #{source}, #{extInfo},NOW(), NOW())
    </insert>

</mapper>