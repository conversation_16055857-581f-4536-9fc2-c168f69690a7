<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.dao.UserAgreementRenewLogMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.DutRenewLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="sign_key" jdbcType="VARCHAR" property="signKey"/>
        <result column="agreement_code" jdbcType="VARCHAR" property="agreementCode"/>
        <result column="agreement_no" jdbcType="INTEGER" property="agreementNo"/>
        <result column="agreement_type" jdbcType="TINYINT" property="agreementType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
        <result column="trade_code" jdbcType="VARCHAR" property="tradeCode"/>
        <result column="operate_type" jdbcType="TINYINT" property="operateType" />
        <result column="status" jdbcType="DECIMAL" property="status"/>
        <result column="error_code" jdbcType="VARCHAR" property="errorCode"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="fee" jdbcType="INTEGER" property="fee"/>
        <result column="platform" jdbcType="INTEGER" property="platform"/>
        <result column="platform_code" jdbcType="VARCHAR" property="platformCode"/>
        <result column="req_error_type" jdbcType="VARCHAR" property="reqErrorType"/>
        <result column="third_error_code" jdbcType="VARCHAR" property="thirdErrorCode"/>
        <result column="third_error_msg" jdbcType="VARCHAR" property="thirdErrorMsg"/>
        <result column="vip_category" jdbcType="TINYINT" property="vipCategory"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, user_id, sign_key, agreement_code, agreement_no, agreement_type, create_time, order_code,
        trade_code, operate_type, status, error_code, type, update_time, fee, platform, platform_code,
        req_error_type, third_error_code, third_error_msg, vip_category, vip_type, amount, description
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from boss_dut_renew_log
        where id = #{id,jdbcType=BIGINT}
          and user_id = #{userId,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.qiyi.vip.trade.autorenew.domain.DutRenewLog" useGeneratedKeys="true" keyProperty="id">
        insert into boss_dut_renew_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="signKey != null">
                sign_key,
            </if>
            <if test="agreementCode != null">
                agreement_code,
            </if>
            <if test="agreementNo != null">
                agreement_no,
            </if>
            <if test="agreementType != null">
                agreement_type,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="orderCode != null">
                order_code,
            </if>
            <if test="tradeCode != null">
                trade_code,
            </if>
            <if test="operateType != null">
                operate_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="errorCode != null">
                error_code,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="fee != null">
                fee,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="platformCode != null">
                platform_code,
            </if>
            <if test="reqErrorType != null">
                req_error_type,
            </if>
            <if test="thirdErrorCode != null">
                third_error_code,
            </if>
            <if test="thirdErrorMsg != null">
                third_error_msg,
            </if>
            <if test="vipCategory != null">
                vip_category,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="description != null">
                description,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="signKey != null">
                #{signKey,jdbcType=VARCHAR},
            </if>
            <if test="agreementCode != null">
                #{agreementCode,jdbcType=VARCHAR},
            </if>
            <if test="agreementNo != null">
                #{agreementNo,jdbcType=INTEGER},
            </if>
            <if test="agreementType != null">
                #{agreementType,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderCode != null">
                #{orderCode,jdbcType=VARCHAR},
            </if>
            <if test="tradeCode != null">
                #{tradeCode,jdbcType=VARCHAR},
            </if>
            <if test="operateType != null">
                #{operateType,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=DECIMAL},
            </if>
            <if test="errorCode != null">
                #{errorCode,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="fee != null">
                #{fee,jdbcType=INTEGER},
            </if>
            <if test="platform != null">
                #{platform,jdbcType=INTEGER},
            </if>
            <if test="platformCode != null">
                #{platformCode,jdbcType=VARCHAR},
            </if>
            <if test="reqErrorType != null">
                #{reqErrorType,jdbcType=VARCHAR},
            </if>
            <if test="thirdErrorCode != null">
                #{thirdErrorCode,jdbcType=VARCHAR},
            </if>
            <if test="thirdErrorMsg != null">
                #{thirdErrorMsg,jdbcType=VARCHAR},
            </if>
            <if test="vipCategory != null">
                #{vipCategory,jdbcType=TINYINT},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.qiyi.vip.trade.autorenew.domain.DutRenewLog">
        update boss_dut_renew_log
        <set>
            update_time = now(),
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="orderCode != null">
                order_code = #{orderCode,jdbcType=VARCHAR},
            </if>
            <if test="tradeCode != null">
                trade_code = #{tradeCode,jdbcType=VARCHAR},
            </if>
            <if test="operateType != null">
                operate_type = #{operateType,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=DECIMAL},
            </if>
            <if test="errorCode != null">
                error_code = #{errorCode,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="fee != null">
                fee = #{fee,jdbcType=INTEGER},
            </if>
            <if test="platform != null">
                platform = #{platform,jdbcType=INTEGER},
            </if>
            <if test="platformCode != null">
                platform_code = #{platformCode,jdbcType=VARCHAR},
            </if>
            <if test="reqErrorType != null">
                req_error_type = #{reqErrorType,jdbcType=VARCHAR},
            </if>
            <if test="thirdErrorCode != null">
                third_error_code = #{thirdErrorCode,jdbcType=VARCHAR},
            </if>
            <if test="thirdErrorMsg != null">
                third_error_msg = #{thirdErrorMsg,jdbcType=VARCHAR},
            </if>
            <if test="vipCategory != null">
                vip_category = #{vipCategory,jdbcType=TINYINT},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </select>

    <select id="selectByOrderCode" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        where user_id = #{userId,jdbcType=BIGINT} and order_code = #{orderCode,jdbcType=VARCHAR}
    </select>
    <select id="listRecordBySignKey" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        where user_id = #{userId,jdbcType=BIGINT}
        and sign_key = #{signKey,jdbcType=VARCHAR}
        <if test="operateType != null">
            and operate_type = #{operateType,jdbcType=TINYINT}
        </if>
    </select>

    <select id="calcRenewLogCount" resultType="int">
        select count(1)
        from boss_dut_renew_log
        where user_id = #{userId,jdbcType=BIGINT}
        <if test="agreementNos != null and agreementNos.size() > 0">
            and agreement_no in
            <foreach collection="agreementNos" item="agreementNo" open="(" close=")" separator=",">
                #{agreementNo}
            </foreach>
        </if>
        <if test="operateType != null">
            and operate_type = #{operateType,jdbcType=TINYINT}
        </if>
        <if test="status != null">
            and status = #{status,jdbcType=DECIMAL}
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
    </select>

    <select id="getLatestRecordBySignKey" parameterType="map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        where user_id = #{userId,jdbcType=BIGINT}
        and sign_key = #{signKey,jdbcType=VARCHAR}
        order by create_time desc
        limit 1
    </select>

    <select id="getLatestRecordBySignKeyAndOperateType" parameterType="map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        where user_id = #{userId,jdbcType=BIGINT}
        and sign_key = #{signKey,jdbcType=VARCHAR}
        and operate_type = #{operateType,jdbcType=TINYINT}
        order by create_time desc
        limit 1
    </select>

    <select id="getLatestRecordByAgreementTypeAndVipTypes" parameterType="map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        where user_id = #{userId,jdbcType=BIGINT}
        and agreement_type = #{agreementType,jdbcType=VARCHAR}
        and vip_type = #{vipType,jdbcType=INTEGER}
        order by create_time desc
        limit 1
    </select>

    <select id="getRecordByAgreementTypeAndVipTypes" parameterType="map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_dut_renew_log
        where user_id = #{userId,jdbcType=BIGINT}
        and agreement_type = #{agreementType,jdbcType=VARCHAR}
        and vip_type = #{vipType,jdbcType=INTEGER}
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
    </select>

</mapper>