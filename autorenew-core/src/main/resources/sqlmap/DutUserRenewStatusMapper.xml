<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.DutUserRenewStatusMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.DutUserRenewStatus">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="renew_count" jdbcType="TINYINT" property="renewCount"/>
        <result column="serial_renew_count" jdbcType="TINYINT" property="serialRenewCount"/>
        <result column="sms_remind" jdbcType="INTEGER" property="smsRemind"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="wechat_remind" jdbcType="INTEGER" property="wechatRemind"/>
    </resultMap>

    <sql id="Base_Column_List">
      id, user_id, renew_count, serial_renew_count, sms_remind, update_time, wechat_remind
    </sql>

    <insert id="insertSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.DutUserRenewStatus" useGeneratedKeys="true" keyProperty="id">
        insert into boss_dut_user_renew_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="renewCount != null">
                renew_count,
            </if>
            <if test="serialRenewCount != null">
                serial_renew_count,
            </if>
            <if test="smsRemind != null">
                sms_remind,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="wechatRemind != null">
                wechat_remind,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="renewCount != null">
                #{renewCount,jdbcType=TINYINT},
            </if>
            <if test="serialRenewCount != null">
                #{serialRenewCount,jdbcType=TINYINT},
            </if>
            <if test="smsRemind != null">
                #{smsRemind,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="wechatRemind != null">
                #{wechatRemind,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
      delete from boss_dut_user_renew_status
      where id = #{id,jdbcType=INTEGER}
    </delete>

    <update id="updateByPrimaryKeySelective" parameterType="com.qiyi.vip.trade.autorenew.domain.DutUserRenewStatus">
        update boss_dut_user_renew_status
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="renewCount != null">
                renew_count = #{renewCount,jdbcType=TINYINT},
            </if>
            <if test="serialRenewCount != null">
                serial_renew_count = #{serialRenewCount,jdbcType=TINYINT},
            </if>
            <if test="smsRemind != null">
                sms_remind = #{smsRemind,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="wechatRemind != null">
                wechat_remind = #{wechatRemind,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="incrementRenewCount" parameterType="map">
        update boss_dut_user_renew_status
        set renew_count = renew_count + 1,
          serial_renew_count = serial_renew_count + 1,
          update_time = now()
        where user_id = #{userId,jdbcType=BIGINT}
    </update>

    <update id="resetAutoRenewStatus" parameterType="map">
        update boss_dut_user_renew_status
        set serial_renew_count = 0,
            sms_remind = 0, wechat_remind = 0, update_time = now()
        where user_id = #{userId,jdbcType=BIGINT}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_user_renew_status
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getDutUserRenewStatus" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_dut_user_renew_status
        where user_id = #{userId} limit 1;
    </select>
</mapper>