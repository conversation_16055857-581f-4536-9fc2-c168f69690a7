<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AutoRenewUpgradeConfigMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AutoRenewUpgradeConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="source_vip_type" jdbcType="TINYINT" property="sourceVipType"/>
        <result column="target_vip_type" jdbcType="TINYINT" property="targetVipType"/>
        <result column="month_product_code" jdbcType="VARCHAR" property="monthProductCode"/>
        <result column="month_sku_id" jdbcType="VARCHAR" property="monthSkuId"/>
        <result column="month_renew_price" jdbcType="INTEGER" property="monthRenewPrice"/>
        <result column="day_product_code" jdbcType="VARCHAR" property="dayProductCode"/>
        <result column="day_sku_id" jdbcType="VARCHAR" property="daySkuId"/>
        <result column="day_renew_price" jdbcType="INTEGER" property="dayRenewPrice"/>
        <result column="final_product_code" jdbcType="VARCHAR" property="finalProductCode"/>
        <result column="final_sku_id" jdbcType="VARCHAR" property="finalSkuId"/>
        <result column="final_renew_price" jdbcType="INTEGER" property="finalRenewPrice"/>
        <result column="priority" jdbcType="TINYINT" property="priority"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
      id, source_vip_type, target_vip_type, month_product_code, month_sku_id, month_renew_price,
        day_product_code, day_sku_id, day_renew_price
        , final_product_code, final_sku_id, final_renew_price, priority, status, update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_upgrade_config
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="listSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.AutoRenewUpgradeConfig" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_upgrade_config
        <where>
            <if test="sourceVipType != null">
                and source_vip_type = #{sourceVipType}
            </if>
            <if test="targetVipType != null">
                and target_vip_type = #{targetVipType}
            </if>
            <if test="monthProductCode != null">
                and month_product_code = #{monthProductCode}
            </if>
            <if test="monthSkuId != null">
                and month_sku_id = #{monthSkuId}
            </if>
            <if test="monthRenewPrice != null">
                and month_renew_price = #{monthRenewPrice}
            </if>
            <if test="dayProductCode != null">
                and day_product_code = #{dayProductCode}
            </if>
            <if test="daySkuId != null">
                and day_sku_id = #{daySkuId}
            </if>
            <if test="dayRenewPrice != null">
                and day_renew_price = #{dayRenewPrice}
            </if>
            <if test="finalProductCode != null">
                and final_product_code = #{finalProductCode}
            </if>
            <if test="finalSkuId != null">
                and final_sku_id = #{finalSkuId}
            </if>
            <if test="finalRenewPrice != null">
                and final_renew_price = #{finalRenewPrice}
            </if>
            <if test="priority != null">
                and priority = #{priority}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <select id="getByVipType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_upgrade_config
        where source_vip_type = #{sourceVipType} and target_vip_type = #{targetVipType} and status = #{status};
    </select>
</mapper>