<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AutoRenewDutTypeMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dut_type" jdbcType="INTEGER" property="dutType"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="agreement_type" jdbcType="TINYINT" property="agreementType"/>
        <result column="source_vip_type" jdbcType="INTEGER" property="sourceVipType"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="pay_channel_name" jdbcType="VARCHAR" property="payChannelName"/>
        <result column="pay_channel_type" jdbcType="INTEGER" property="payChannelType"/>
        <result column="dut_pay_type" jdbcType="INTEGER" property="dutPayType"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="cancel_autorenw_unbind" jdbcType="TINYINT" property="cancelAutorenwUnbind"/>
        <result column="change_amount" jdbcType="TINYINT" property="changeAmount"/>
        <result column="direct_cancel" jdbcType="TINYINT" property="directCancel"/>
        <result column="support_pure_sign" jdbcType="TINYINT" property="supportPureSign"/>
        <result column="priority" jdbcType="TINYINT" property="priority"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="partner_id" jdbcType="VARCHAR" property="partnerId"/>
        <result column="business_code" jdbcType="VARCHAR" property="businessCode"/>
        <result column="valid_start_time" jdbcType="TIMESTAMP" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
      id, dut_type, name, agreement_type, source_vip_type, vip_type, pay_channel, pay_channel_name, pay_channel_type, dut_pay_type,
      product_code, cancel_autorenw_unbind, change_amount, direct_cancel, support_pure_sign, priority, status,
      partner_id, business_code, valid_start_time, valid_end_time, create_time, update_time
    </sql>

    <select id="selectByDutType" resultType="com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType">
        select <include refid="Base_Column_List"/>
        from autorenew_dut_type
        where dut_type = #{dutType} and status = 1
    </select>

    <select id="selectByVipType" resultType="com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType">
        select <include refid="Base_Column_List"/>
        from autorenew_dut_type
        where vip_type = #{vipType} and status = 1
        <if test="agreementType != null">
            and agreement_type = #{agreementType}
        </if>
    </select>

    <select id="selectByVipTypeAndExcludeAgreementTypes" resultType="com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType">
        select <include refid="Base_Column_List"/>
        from autorenew_dut_type
        where vip_type = #{vipType} and status = 1
        <if test="excludeAgreementTypeList != null and excludeAgreementTypeList.size() > 0">
            and agreement_type not in
            <foreach collection="excludeAgreementTypeList" item="agreementType" open="(" close=")" separator=",">
                #{agreementType}
            </foreach>
        </if>
    </select>

    <select id="list" parameterType="com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from autorenew_dut_type
        <where>
            <if test="dutType != null">
                and dut_type = #{dutType}
            </if>
            <if test="name != null">
                and name = #{name}
            </if>
            <if test="agreementType != null">
                and agreement_type = #{agreementType}
            </if>
            <if test="sourceVipType != null">
                and source_vip_type = #{sourceVipType}
            </if>
            <if test="vipType != null">
                and vip_type = #{vipType}
            </if>
            <if test="payChannel != null">
                and pay_channel = #{payChannel}
            </if>
            <if test="payChannelName != null">
                and pay_channel_name = #{payChannelName}
            </if>
            <if test="payChannelType != null">
                and pay_channel_type = #{payChannelType}
            </if>
            <if test="dutPayType != null">
                and dut_pay_type = #{dutPayType}
            </if>
            <if test="productCode != null">
                and product_code = #{productCode}
            </if>
            <if test="cancelAutorenwUnbind != null">
                and cancel_autorenw_unbind = #{cancelAutorenwUnbind}
            </if>
            <if test="changeAmount != null">
                and change_amount = #{changeAmount}
            </if>
            <if test="directCancel != null">
                and direct_cancel = #{directCancel}
            </if>
            <if test="supportPureSign != null">
                and support_pure_sign = #{supportPureSign}
            </if>
            <if test="priority != null">
                and priority = #{priority}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="partnerId != null">
                and partner_id = #{partnerId}
            </if>
        </where>
        order by priority desc
    </select>

    <select id="selectByVipTypeAndPayChannel" resultType="com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType">
        select <include refid="Base_Column_List"/>
        from autorenew_dut_type
        where vip_type = #{vipType} and status = 1
        <if test="agreementType != null">
            and agreement_type  = #{agreementType}
        </if>
        <if test="sourceVipType != null">
            and source_vip_type = #{sourceVipType}
        </if>
        <if test="sourceVipType == null">
            and source_vip_type is null
        </if>
        <if test="payChannel != null">
            and pay_channel = #{payChannel}
        </if>
        <if test="priority != null">
            and priority = #{priority}
        </if>
        order by priority desc
    </select>


    <select id="listAutoRenewDutTypeByVipTypes" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from autorenew_dut_type
        where status = 1 and vip_type in
        <foreach collection="vipTypes" item="vipType" open="(" close=")" separator=",">
            #{vipType}
        </foreach>
        <if test="excludeAgreementTypeList != null and excludeAgreementTypeList.size() > 0">
            and agreement_type not in
            <foreach collection="excludeAgreementTypeList" item="agreementType" open="(" close=")" separator=",">
                #{agreementType}
            </foreach>
        </if>
    </select>

</mapper>