<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenewtidb.mapper.AutoRenewSetLogMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenewtidb.domain.AutoRenewSetLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="operator" jdbcType="INTEGER" property="operator"/>
        <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime"/>
        <result column="sign_key" jdbcType="VARCHAR" property="signKey" />
        <result column="agreement_no" jdbcType="INTEGER" property="agreementNo"/>
        <result column="agreement_type" jdbcType="TINYINT" property="agreementType" />
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="reason_id" jdbcType="INTEGER" property="reasonId"/>
        <result column="other_reason" jdbcType="VARCHAR" property="otherReason"/>
        <result column="scene" jdbcType="VARCHAR" property="scene"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="push_channel" jdbcType="INTEGER" property="pushChannel"/>
        <result column="gateway" jdbcType="INTEGER" property="gateway"/>
        <result column="platform" jdbcType="BIGINT" property="platform"/>
        <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
        <result column="serial_renew_count" jdbcType="TINYINT" property="serialRenewCount"/>
        <result column="vip_category" jdbcType="TINYINT" property="vipCategory"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>

    </resultMap>

    <sql id="Base_Column_List">
      id, user_id, `operator`, operate_time, sign_key, agreement_no, agreement_type, `type`, description,
        update_time, push_channel, gateway, platform, platform_code, serial_renew_count, vip_category, vip_type, amount
    </sql>

    <select id="queryKeFuCancelDutSetLogs" resultMap="BaseResultMap">
        select user_id, operate_time, vip_type, amount, type, reason_id, other_reason, scene
        from boss_dut_renew_set_log
        where 1 = 1
        <if test="operator != null">
            and operator = #{operator}
        </if>
        <if test="scene != null">
            and scene = #{scene}
        </if>
        <if test="startTime != null">
            <![CDATA[ and operate_time >= #{startTime} ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ and operate_time <= #{endTime} ]]>
        </if>
    </select>
</mapper>