<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenewtidb.mapper.AccountDutUserMapper">
  <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AccountDutUser">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="DECIMAL" property="userId" />
    <result column="status" jdbcType="DECIMAL" property="status" />
    <result column="type" jdbcType="BIGINT" property="type" />
  </resultMap>

    <sql id="Base_Column_List">
        id, user_id, status, type
    </sql>

    <select id="getByIdRange" resultType="com.qiyi.vip.trade.autorenew.domain.AccountDutUser" parameterType="map">
        select <include refid="Base_Column_List"/>
        from account_dut_user
        where id between #{startIndex} and #{endIndex}
    </select>

</mapper>