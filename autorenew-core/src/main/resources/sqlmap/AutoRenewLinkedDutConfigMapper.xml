<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AutoRenewLinkedDutConfigMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AutoRenewLinkedDutConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="job_type" jdbcType="TINYINT" property="jobType"/>
        <result column="source_vip_type" jdbcType="TINYINT" property="sourceVipType"/>
        <result column="vip_type" jdbcType="TINYINT" property="vipType"/>
        <result column="agreement_type" jdbcType="TINYINT" property="agreementType"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="pay_channel_type" jdbcType="INTEGER" property="payChannelType"/>
        <result column="offset" jdbcType="INTEGER" property="offset"/>
        <result column="retry_interval_day" jdbcType="INTEGER" property="retryIntervalDay"/>
        <result column="retry_interval_min" jdbcType="INTEGER" property="retryIntervalMin"/>
        <result column="need_notify" jdbcType="INTEGER" property="needNotify"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="valid_start_time" jdbcType="TIMESTAMP" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, job_type, source_vip_type, vip_type, category, pay_channel, pay_channel_type,
        offset, retry_interval_day, retry_interval_min, need_notify, status, valid_start_time,
        valid_end_time, create_time, update_time, agreement_type
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from autorenew_linked_dut_config
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.AutoRenewLinkedDutConfig"
            useGeneratedKeys="true" keyProperty="id">
        insert into autorenew_linked_dut_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                name,
            </if>
            <if test="jobType != null">
                job_type,
            </if>
            <if test="sourceVipType != null">
                source_vip_type,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="payChannel != null">
                pay_channel,
            </if>
            <if test="payChannelType != null">
                pay_channel_type,
            </if>
            <if test="offset != null">
                offset,
            </if>
            <if test="retryIntervalDay != null">
                retry_interval_day,
            </if>
            <if test="retryIntervalMin != null">
                retry_interval_min,
            </if>
            <if test="needNotify != null">
                need_notify,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="validStartTime != null">
                valid_start_time,
            </if>
            <if test="validEndTime != null">
                valid_end_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="jobType != null">
                #{jobType,jdbcType=TINYINT},
            </if>
            <if test="sourceVipType != null">
                #{sourceVipType,jdbcType=TINYINT},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=TINYINT},
            </if>
            <if test="category != null">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="payChannel != null">
                #{payChannel,jdbcType=INTEGER},
            </if>
            <if test="payChannelType != null">
                #{payChannelType,jdbcType=INTEGER},
            </if>
            <if test="offset != null">
                #{offset,jdbcType=INTEGER},
            </if>
            <if test="retryIntervalDay != null">
                #{retryIntervalDay,jdbcType=INTEGER},
            </if>
            <if test="retryIntervalMin != null">
                #{retryIntervalMin,jdbcType=INTEGER},
            </if>
            <if test="needNotify != null">
                #{needNotify,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="validStartTime != null">
                #{validStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="validEndTime != null">
                #{validEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.qiyi.vip.trade.autorenew.domain.AutoRenewLinkedDutConfig">
        update autorenew_linked_dut_config
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="jobType != null">
                job_type = #{jobType,jdbcType=TINYINT},
            </if>
            <if test="sourceVipType != null">
                source_vip_type = #{sourceVipType,jdbcType=TINYINT},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=TINYINT},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="payChannel != null">
                pay_channel = #{payChannel,jdbcType=INTEGER},
            </if>
            <if test="payChannelType != null">
                pay_channel_type = #{payChannelType,jdbcType=INTEGER},
            </if>
            <if test="offset != null">
                offset = #{offset,jdbcType=INTEGER},
            </if>
            <if test="retryIntervalDay != null">
                retry_interval_day = #{retryIntervalDay,jdbcType=INTEGER},
            </if>
            <if test="retryIntervalMin != null">
                retry_interval_min = #{retryIntervalMin,jdbcType=INTEGER},
            </if>
            <if test="needNotify != null">
                need_notify = #{needNotify,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="validStartTime != null">
                valid_start_time = #{validStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="validEndTime != null">
                valid_end_time = #{validEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_linked_dut_config
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="listLinkedDutConfig" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_linked_dut_config
        <where>
            <if test="jobType != null">
                and job_type = #{jobType}
            </if>
            <if test="vipType != null">
                and vip_type = #{vipType}
            </if>
            <if test="sourceVipType != null">
                and source_vip_type = #{sourceVipType}
            </if>
            <if test="payChannel != null">
                and pay_channel = #{payChannel}
            </if>
            <if test="category != null">
                and category = #{category}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="agreementType != null">
                and agreement_type = #{agreementType}
            </if>
            and <![CDATA[valid_start_time <= current_time() and valid_end_time > current_time() ]]>
        </where>
    </select>
</mapper>