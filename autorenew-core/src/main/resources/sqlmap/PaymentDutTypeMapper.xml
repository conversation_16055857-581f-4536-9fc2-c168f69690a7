<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.PaymentDutTypeMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.PaymentDutType">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="pay_type" jdbcType="INTEGER" property="payType"/>
        <result column="dut_type" jdbcType="INTEGER" property="dutType"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="pay_channel_type" jdbcType="TINYINT" property="payChannelType"/>
        <result column="service_code" jdbcType="VARCHAR" property="serviceCode"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="amount" jdbcType="TINYINT" property="amount"/>
        <result column="source_vip_type" jdbcType="TINYINT" property="sourceVipType"/>
        <result column="vip_type" jdbcType="TINYINT" property="vipType"/>
        <result column="act_code" jdbcType="VARCHAR" property="actCode"/>
        <result column="renew_price" jdbcType="INTEGER" property="renewPrice"/>
        <result column="valid_start_time" jdbcType="TIMESTAMP" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, pay_type, dut_type, pay_channel, pay_channel_type, service_code, create_time,
        update_time, amount, source_vip_type, vip_type, act_code, renew_price, valid_start_time,
        valid_end_time
      </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_payment_dut_type
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getDutTypeWithSourceVipType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_payment_dut_type
        <where>
            <![CDATA[valid_start_time <= CURRENT_TIMESTAMP() and valid_end_time > CURRENT_TIMESTAMP() and vip_type = #{vipType} and amount = #{amount} ]]>
            <if test="payType != null">
                and pay_type = #{payType}
            </if>
            <if test="payChannel != null">
                and pay_channel = #{payChannel}
            </if>
            <if test="sourceVipType != null">
                and source_vip_type = #{sourceVipType}
            </if>
            <if test="sourceVipType == null">
                and source_vip_type is null
            </if>
            <if test="actCode != null">
                and act_code = #{actCode}
            </if>
            <if test="actCode == null">
                and act_code is null
            </if>
        </where>
    </select>

    <select id="getRenewPrice" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_payment_dut_type
        <where>
            <![CDATA[valid_start_time <= CURRENT_TIMESTAMP() and valid_end_time > CURRENT_TIMESTAMP() ]]>
            and dut_type = #{dutType} and amount = #{amount}
            <if test="actCode != null and actCode != '' ">
                and act_code = #{actCode}
            </if>
            <if test="actCode == null">
                and act_code is null
            </if>
            <if test="vipType != null">
                and vip_type = #{vipType}
            </if>
        </where>
    </select>

    <select id="getRenewPriceNew" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from boss_payment_dut_type
        where <![CDATA[valid_start_time <= CURRENT_TIMESTAMP() and valid_end_time > CURRENT_TIMESTAMP() ]]>
        and vip_type = #{vipType} and amount = #{amount}
        and dut_type in
        <foreach collection="dutTypes" item="dutType" open="(" separator="," close=")">
             #{dutType}
        </foreach>
        <if test="actCode != null and actCode != '' ">
            and act_code = #{actCode}
        </if>
        <if test="actCode == null">
            and act_code is null
        </if>
    </select>

    <select id="getWechatAmountByDutType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_payment_dut_type
        where  dut_type = #{dutType}
    </select>

    <select id="getDutTypeByViptypeAndAmountExcludeActCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_payment_dut_type
        where <![CDATA[ valid_start_time <= CURRENT_TIMESTAMP() and valid_end_time > CURRENT_TIMESTAMP() ]]> and vip_type = #{vipType} and amount = #{amount} and act_code is null
    </select>

    <select id="listUniqDutTypes" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT dut_type FROM boss_payment_dut_type WHERE service_code = #{serviceCode};
    </select>

    <select id="getDutTypeByActCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM boss_payment_dut_type WHERE act_code = #{actCode}  limit 1;
    </select>
    <select id="getDutTypeByPayType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM boss_payment_dut_type where pay_type = #{payType}
        and vip_type is null
    </select>
    <select id="getPayTypeByPayChannel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM boss_payment_dut_type
        where pay_channel = #{pay_channel}
        <if test="vip_type != null">
            and vip_type = #{vip_type}
        </if>
        order by create_time DESC
    </select>
    <select id="getDutTypeByPayTypeAndPayChannel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_payment_dut_type
        <where>
            <if test="payType != null">
                and pay_type = #{payType}
            </if>
            <if test="vipType != null">
                and vip_type = #{vipType}
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
        </where>
    </select>

</mapper>