<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AutoRenewGracePeriodRecordMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AutoRenewGracePeriodRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="dut_type" jdbcType="INTEGER" property="dutType"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="original_trade_no" jdbcType="VARCHAR" property="originalTradeNo"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
      id, user_id, order_code, vip_type, dut_type, app_id, original_trade_no, amount, create_time,
      update_time
    </sql>

    <insert id="insertSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.AutoRenewGracePeriodRecord"
            useGeneratedKeys="true" keyProperty="id">
        insert into autorenew_grace_period_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="orderCode != null">
                order_code,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="dutType != null">
                dut_type,
            </if>
            <if test="appId != null">
                app_id,
            </if>
            <if test="originalTradeNo != null">
                original_trade_no,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="orderCode != null">
                #{orderCode,jdbcType=VARCHAR},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=INTEGER},
            </if>
            <if test="dutType != null">
                #{dutType,jdbcType=INTEGER},
            </if>
            <if test="appId != null">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="originalTradeNo != null">
                #{originalTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
      delete from autorenew_grace_period_record
      where id = #{id,jdbcType=BIGINT}
    </delete>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.qiyi.vip.trade.autorenew.domain.AutoRenewGracePeriodRecord">
        update autorenew_grace_period_record
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="orderCode != null">
                order_code = #{orderCode,jdbcType=VARCHAR},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=INTEGER},
            </if>
            <if test="dutType != null">
                dut_type = #{dutType,jdbcType=INTEGER},
            </if>
            <if test="appId != null">
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="originalTradeNo != null">
                original_trade_no = #{originalTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_grace_period_record
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="find" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_grace_period_record
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="appId != null">
                and app_id = #{appId}
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
            <if test="createTime != null">
                and create_time &gt; #{createTime}
            </if>
        </where>
    </select>
</mapper>