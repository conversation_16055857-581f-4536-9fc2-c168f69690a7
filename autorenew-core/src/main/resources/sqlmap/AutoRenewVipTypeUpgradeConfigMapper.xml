<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.AutoRenewVipTypeUpgradeConfigMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.AutoRenewVipTypeUpgradeConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="upgrade_group" jdbcType="INTEGER" property="upgradeGroup"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="agreement_type" jdbcType="TINYINT" property="agreementType"/>
    </resultMap>

    <sql id="Base_Column_List">
      id, vip_type, description, upgrade_group, priority, update_time, area, agreement_type
    </sql>

    <insert id="insertSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.AutoRenewVipTypeUpgradeConfig" keyProperty="id" useGeneratedKeys="true">
        insert into autorenew_viptype_upgrade_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="description != null">
                description,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="upgradeGroup != null">
                upgrade_group,
            </if>
            <if test="priority != null">
                priority,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="agreementType != null">
                agreement_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=INTEGER},
            </if>
            <if test="upgradeGroup != null">
                #{upgradeGroup,jdbcType=INTEGER},
            </if>
            <if test="priority != null">
                #{priority,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="agreementType != null">
                #{agreementType,jdbcType=INTEGER}
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
      delete from autorenew_viptype_upgrade_config
      where id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_viptype_upgrade_config
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="listSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.AutoRenewVipTypeUpgradeConfig" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_viptype_upgrade_config
        <where>
            <if test="description != null">
                and description = #{description}
            </if>
            <if test="vipType != null">
                and vip_type = #{vipType}
            </if>
            <if test="upgradeGroup != null">
                and upgrade_group = #{upgradeGroup}
            </if>
            <if test="priority != null">
                and priority = #{priority}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="agreementType != null">
                and agreement_type = #{agreementType}
            </if>
        </where>
    </select>

    <select id="listUpgradeConfigByArea" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from autorenew_viptype_upgrade_config
        where area = #{area}
    </select>
</mapper>