<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.trade.autorenew.mapper.CommonAutoRenewDutConfigMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.trade.autorenew.domain.CommonAutoRenewDutConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="pcode" jdbcType="VARCHAR" property="pcode"/>
        <result column="next_id" jdbcType="BIGINT" property="nextId"/>
        <result column="advance_hours" jdbcType="INTEGER" property="advanceHours"/>
        <result column="interval" jdbcType="INTEGER" property="interval"/>
        <result column="retry_offset" jdbcType="INTEGER" property="retryOffset"/>
        <result column="pay_channel" jdbcType="TINYINT" property="payChannel"/>
        <result column="pay_channel_type" jdbcType="TINYINT" property="payChannelType"/>
        <result column="order_fc" jdbcType="VARCHAR" property="orderFc"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="valid_start_time" jdbcType="TIMESTAMP" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, category, vip_type, pcode, next_id, advance_hours, `interval`, retry_offset,
        pay_channel, pay_channel_type, order_fc, status, valid_start_time, valid_end_time, create_time, update_time
    </sql>

    <insert id="insertSelective" parameterType="com.qiyi.vip.trade.autorenew.domain.CommonAutoRenewDutConfig" useGeneratedKeys="true" keyProperty="id">
        insert into common_autorenew_dut_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                name,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="pcode != null">
                pcode,
            </if>
            <if test="nextId != null">
                next_id,
            </if>
            <if test="advanceHours != null">
                advance_hours,
            </if>
            <if test="interval != null">
                interval,
            </if>
            <if test="retryOffset != null">
                retry_offset,
            </if>
            <if test="payChannel != null">
                pay_channel,
            </if>
            <if test="payChannelType != null">
                pay_channel_type,
            </if>
            <if test="orderFc != null">
                order_fc,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="validStartTime != null">
                valid_start_time,
            </if>
            <if test="validEndTime != null">
                valid_end_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=INTEGER},
            </if>
            <if test="pcode != null">
                #{pcode,jdbcType=VARCHAR},
            </if>
            <if test="nextId != null">
                #{nextId,jdbcType=BIGINT},
            </if>
            <if test="advanceHours != null">
                #{advanceHours,jdbcType=INTEGER},
            </if>
            <if test="interval != null">
                #{interval,jdbcType=INTEGER},
            </if>
            <if test="retryOffset != null">
                #{retryOffset,jdbcType=INTEGER},
            </if>
            <if test="payChannel != null">
                #{payChannel,jdbcType=TINYINT},
            </if>
            <if test="payChannelType != null">
                #{payChannelType,jdbcType=TINYINT},
            </if>
            <if test="orderFc != null">
                #{orderFc,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="validStartTime != null">
                #{validStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="validEndTime != null">
                #{validEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from common_autorenew_dut_config
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.qiyi.vip.trade.autorenew.domain.CommonAutoRenewDutConfig">
        update common_autorenew_dut_config
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=INTEGER},
            </if>
            <if test="pcode != null">
                pcode = #{pcode,jdbcType=VARCHAR},
            </if>
            <if test="nextId != null">
                next_id = #{nextId,jdbcType=BIGINT},
            </if>
            <if test="advanceHours != null">
                advance_hours = #{advanceHours,jdbcType=INTEGER},
            </if>
            <if test="interval != null">
                interval = #{interval,jdbcType=INTEGER},
            </if>
            <if test="retryOffset != null">
                retry_offset = #{retryOffset,jdbcType=INTEGER},
            </if>
            <if test="payChannel != null">
                pay_channel = #{payChannel,jdbcType=TINYINT},
            </if>
            <if test="payChannelType != null">
                pay_channel_type = #{payChannelType,jdbcType=TINYINT},
            </if>
            <if test="orderFc != null">
                order_fc = #{orderFc,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="validStartTime != null">
                valid_start_time = #{validStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="validEndTime != null">
                valid_end_time = #{validEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from common_autorenew_dut_config
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from common_autorenew_dut_config
        where <![CDATA[ valid_start_time <= CURRENT_TIMESTAMP() and valid_end_time > CURRENT_TIMESTAMP() ]]>
        and status = #{status}
        <if test="vipType != null">
            and vip_type = #{vipType}
        </if>
        <if test="category != null">
            and category = #{category}
        </if>
        <if test="payChannel != null">
            and pay_channel = #{payChannel}
        </if>
    </select>

</mapper>