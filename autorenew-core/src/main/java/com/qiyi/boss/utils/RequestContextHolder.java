package com.qiyi.boss.utils;

/**
 * <AUTHOR> k<PERSON><PERSON><PERSON><PERSON>
 * DateTime: 2017/11/13 下午3:21
 * Mail:kong<PERSON><EMAIL>   
 * Description: desc
 */
public class RequestContextHolder {

    private static final ThreadLocal<RequestContext> REQUEST_CONTEXT = new ThreadLocal<RequestContext>(){
        @Override
        protected RequestContext initialValue() {
            return new RequestContext();
        }
    };

    public static RequestContext getRequestContext() {
        return REQUEST_CONTEXT.get();
    }

    public static void setRequestContext(RequestContext requestContext){
        REQUEST_CONTEXT.set(requestContext);
    }

    public static void cleanRequestContext() {
        REQUEST_CONTEXT.remove();
    }

}
