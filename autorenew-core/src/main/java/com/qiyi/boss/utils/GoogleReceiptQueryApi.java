package com.qiyi.boss.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.base.Predicate;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;

/**
 * @author: zhangtengfei01
 * @date: 2020/10/27 16:38
 * @desc: 谷歌查询票据接口API
 */
@Slf4j
@Service
public class GoogleReceiptQueryApi {

    private static final String signKey = AppConfig.getProperty("account.unbind.key");
    private static final Set<String> SKIPPED_SIGN_PARAMS = Sets.newHashSet("sign", "sign_type", "ip", "cip");
    // 对应接口文档 http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
    private static final String GOOGLE_RECEIPT_QUERY_URL = AppConfig.getProperty("google.receipt.query.url");

    @Resource(name = "payCenterClient")
    private RestTemplate receiptQueryClient;


    /**
     * 谷歌查询票据接口，根据支付中心订单号和uid查询谷歌
     * 接口文档：http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
     *
     * @param centerOrderCode
     * @param uid
     * @return
     */
    public Long getExpireTime(String centerOrderCode, Long uid) {
        log.info("getExpireTime enter , centerCode:{}, uid:{}", centerOrderCode, uid);
        SortedMap<String, String> param = new TreeMap<>();
        param.put("userId", String.valueOf(uid));
        param.put("orderCode", centerOrderCode);
        param.put("partner", "qiyue_global");
        param.put("sign", doSign(param));
        String url = GOOGLE_RECEIPT_QUERY_URL + Joiner.on("&").withKeyValueSeparator("=").join(param);
        log.info("getExpireTime url:{}", url);
        ResponseEntity<String> returnValue = null;
        try {
            returnValue = receiptQueryClient.exchange(url, HttpMethod.POST, null, new ParameterizedTypeReference<String>() {
            });
        } catch (Exception ex) {
            log.error("error occurs when getExpireTime, centerCode:{}, uid:{}", centerOrderCode, uid, ex);
            return null;
        }
        JSONObject result = JSON.parseObject(returnValue.getBody());
        log.info("getExpireTime centerCode:{}, uid:{}, result:{}", centerOrderCode, uid, result);
        String success = result.getString("is_success");
        if (success.equals("T")) {
            return result.getJSONObject("data").getLong("expiryTimeMillis");
        }
        return null;
    }


    private static String generateParamsString(Map<String, String> parameters, final Set<String> skippedParams) {
        Map<String, String> filteredParams = Maps.filterEntries(parameters, new Predicate<Map.Entry<String, String>>() {
            @Override
            public boolean apply(Map.Entry<String, String> paramEntry) {
                return !skippedParams.contains(paramEntry.getKey().toLowerCase())
                        && StringUtils.isNoneBlank(paramEntry.getValue());
            }
        });
        return Joiner.on("&").withKeyValueSeparator("=").join(filteredParams);
    }

    public static String doSign(SortedMap<String, String> parameters) {
        String paramsString = generateParamsString(parameters, SKIPPED_SIGN_PARAMS);
        String needToBeSigned = paramsString + signKey;
        return EncodeUtils.MD5(needToBeSigned, "UTF-8");
    }
}
