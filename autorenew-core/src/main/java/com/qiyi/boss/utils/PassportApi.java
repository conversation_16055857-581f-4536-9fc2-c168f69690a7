package com.qiyi.boss.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.function.Function;

import com.qiyi.boss.Constants;
import com.qiyi.boss.dto.NotifyPassportUserLogOffReqDto;
import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.model.UidInfo;
import com.qiyi.boss.model.UserInfo;
import com.qiyi.boss.outerinvoke.BaseApi;
import com.iqiyi.kit.http.client.util.HttpClients;
import com.iqiyi.kit.http.client.util.QueryUrlProcessor;

import static com.qiyi.vip.trade.dataservice.client.response.Response.SUCCESS_CODE;

/**
 * Created by IntelliJ IDEA.
 * <AUTHOR>
 * Date: 15-5-29
 * Time: 上午11:13
 * To change this template use File | Settings | File Templates.
 */
@Component
public class PassportApi extends BaseApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(PassportApi.class);

    private static final String GET_USER_INFO_BYUSERNAME_URL = AppConfig.getProperty("passport.queryUserByUsername.url");
    private static final String GET_USER_INFO_BYUID_URL = AppConfig.getProperty("passport.queryUserByUid.url");
    private static final String BATCH_USER_INFO_URL = AppConfig.getProperty("passport.batchQueryUserByUids.url");
    private static final String GET_USER_INFO_BY_AUTH_COOKIE_URL = AppConfig.getProperty("passport.profileInfo.url");
    private static final String AUTH_COOKIE_KEY = AppConfig.getProperty("passport.authCookie.key");
    private static final String PASSPORT_CLOSE_ACCOUNT_VERIFICATION_URL = AppConfig.getProperty("passport.closeaccout.status.verification.url");
    /**
     * 接口wiki: http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
     */
    private static final String GET_UID_BY_AUTH_COOKIE_URL = AppConfig.getProperty("passport.inner.user.uid.info.url");

    private static final String PASSPORT_SECRET_KEY = "vo0ifxgp45xxTfgr4acdse";
    private static final String REQ_PARAM_AGENT_TYPE = "agenttype";
    private static final String REQ_PARAM_AGENT_TYPE_VALUE = "167";
    private static final String REQ_PARAM_TIMESTAMP = "timestamp";
    private static final String REQ_PARAM_SOURCE = "source";
    public static final String REQ_PARAM_SOURCE_VALUE = "vipTrade";
    private static final String REQ_PARAM_FIELDS = "fields";
    private static final String REQ_PARAM_PRIVATE = "private";

    private QueryUrlProcessor urlParamSigner = new PassportSigner();

    private ParameterizedTypeReference<Map<String, Object>> stringObjMapTypeRef =
            new ParameterizedTypeReference<Map<String, Object>>() {
            };

    @Resource(name = "passportHttpClient")
    private RestTemplate passportClient;

    /**
     * 根据用户名获取用户ID
     * @param username 用户名
     * @return 用户ID
     */
    @SuppressWarnings("unchecked")
    public Long getUserIdByUsername(String username) {
        ResponseEntity<Map<String, Object>> responseEntity = new QueryUserInfoCommand(null, username, null,GET_USER_INFO_BYUSERNAME_URL).execute();
        if (isHttpFailed(responseEntity)) {
            return null;
        }
        Map<String, Object> data = getDataNode(responseEntity);
        String uid = MapUtils.getString(data, "uid", "");
        if (StringUtils.isBlank(uid)) {
            return null;
        }

        return Long.valueOf(uid);
    }

    public boolean isAccountClosed(Long uid) {
        ResponseEntity<Map<String, Object>> responseEntity = new QueryUserInfoCommand(uid, null, null, GET_USER_INFO_BYUID_URL).execute();
        if (isHttpFailed(responseEntity)) {
            LOGGER.warn("Get User by identifier {} failed. Response Body -> {}", uid, responseEntity != null ? responseEntity.getBody() : "");
            return false;
        }
        Map<String, Object> body = responseEntity.getBody();
        return body != null && ("P00100".equals(body.get("code")) || "P00108".equals(body.get("code")));
    }

    /**
     * Passport回调接口：http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
     * @param reqDto
     * @return
     */
    public boolean failNotifyPassportUserLogOffJudgementResult(NotifyPassportUserLogOffReqDto reqDto) {
        LOGGER.info("notifyPassportUserLogOffJudgementResult:{}", reqDto);
        BaseResponse response = new NotifyPassportLogOffJudgementResultCommand(reqDto).execute();
        return response != null && BaseResponse.CodeEnum.OUTER_SERVICE_ERROR.getCode().equals(response.getCode());
    }

    public UserInfo getVipUserWithOpenIdsByUid(Long uid) {
        ResponseEntity<Map<String, Object>> responseEntity = new BatchQueryUserInfoCommand(Collections.singletonList(uid)).execute();
        if (isHttpFailed(responseEntity)) {
            return null;
        }
        return toUserInfoWithOpenIds(uid, responseEntity);
    }

    /**
     * 根据用户的Cookie信息获取用户信息
     * wiki地址：http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
     */
    @SuppressWarnings("unchecked")
    public UserInfo getUserByPassport(HttpServletRequest request) {
        String ticket = getTicket(request);
        //IOS客户端7.3-7.4版本P00001传值前端存在bug,如果用户未登录 P00001传值成了P00001=(null),这里需要特殊处理一下
        if (StringUtils.isBlank(ticket) || "null".equals(ticket) || "(null)".equals(ticket)) {
            return null;
        }

        ResponseEntity<Map<String, Object>> responseEntity = new QueryUserInfoByAuthCookieRetryCommand(ticket).execute();
        if (isHttpFailed(responseEntity)) {
            LOGGER.warn("[module:auth] [action:authenticate] [ticket:{}]", ticket);
            return null;
        }
        LOGGER.debug("[module:auth] [action:authenticate] [step:passport] [content:{}]", responseEntity);

        JsonResult result = JSON.parseObject(JSON.toJSONString(responseEntity.getBody()), JsonResult.class);

        if (!SUCCESS_CODE.equals(result.getCode())) {
            LOGGER.warn("[module:auth] [action:authenticate] [body:{}] [ticket:{}]", responseEntity.getBody(), ticket);
            return null;
        }
        Map<String, Object> data = (Map<String, Object>) result.getData();
        UserInfo userInfo = buildPassportUserInfo(data);

        LOGGER.info("[module:auth] [action:authenticate] [step:exit] [ip:{}][authCookie:{}] [uid:{}]",
            TempIpUtil.getAndDiffIp(request), ticket, userInfo.getId());
        return userInfo;
    }

    private UserInfo buildPassportUserInfo(Map<String, Object> resultData) {
        Map userInfoData = MapUtils.getMap(resultData,"userinfo");
        UserInfo userInfo = new UserInfo();
        userInfo.setId(MapUtils.getLong(userInfoData, "uid"));
        userInfo.setEmail(MapUtils.getString(userInfoData, "email"));
        userInfo.setName(MapUtils.getString(userInfoData, "nickname"));
        userInfo.setPhone(MapUtils.getString(userInfoData, "phone"));
        userInfo.setSource(MapUtils.getInteger(userInfoData, "source"));
        //设置头像
        userInfo.setIcon(MapUtils.getString(userInfoData, "icon"));
        return userInfo;
    }

    public static String getTicket(HttpServletRequest request) {
        //先从提交的参数中取P00001，如果没有则从Cookie中取
        String p00001 = request.getParameter(Constants.PASSPORT_USERID_COOKIE_KEY);
        if (p00001 == null || p00001.trim().length() == 0) {
            p00001 = RequestUtils.getCookieValue(request, AUTH_COOKIE_KEY);
        }
        return p00001;
    }

    static class PassportSigner implements QueryUrlProcessor {
        @Override
        public String process(String originalUrl, Map<String, Object> reqParams) {
            SortedMap<String, Object> sortedParams = new TreeMap<>(reqParams);
            StringBuilder sb = new StringBuilder();
            for (String key : sortedParams.keySet()) {
                String val = sortedParams.get(key).toString();
                sb.append(key).append("=").append(StringUtils.defaultIfEmpty(val, "")).append("|");
            }
            String sign = DigestUtils.md5Hex(sb.append(PASSPORT_SECRET_KEY).toString());
            return originalUrl + "&sign=" + sign;
        }
    }

    private ResponseEntity<Map<String, Object>> doGet(String baseUrl, Object id, Map<String, Object> requestParams) {
        String url = HttpClients.buildQueryUrl(baseUrl, requestParams, urlParamSigner);

        ResponseEntity<Map<String, Object>> responseEntity;
        try {
            responseEntity = passportClient.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    stringObjMapTypeRef
            );
        } catch (RestClientException e) {
            LOGGER.error("RestTemplate request remote service error! baseUrl={}, id={}, params={}, error={}", baseUrl, id, requestParams, e.getMessage());
            return new ResponseEntity<>(HttpStatus.REQUEST_TIMEOUT);
        }

        if (isHttpFailed(responseEntity) || isBusinessFailed(responseEntity)) {
            LOGGER.warn("Get User by identifier {}, params={} failed. Response Body -> {}", id, requestParams, responseEntity.getBody());
            return null;
        }
        return responseEntity;
    }

    @SuppressWarnings("unchecked")
    private UserInfo toUserInfoWithOpenIds(Long uid, ResponseEntity<Map<String, Object>> responseEntity) {
        UserInfo userInfo = new UserInfo();
        Map<String, Object> data = getDataNode(responseEntity);
        if (MapUtils.isEmpty(data) || CollectionUtils.isEmpty((List<Object>) MapUtils.getObject(data, "data"))) {
            return null;
        }
        ArrayList<Map<String,Object>> userList  = (ArrayList<Map<String, Object>>) MapUtils.getObject(data, "data");
            for (Map<String,Object> user: userList) {
                if (uid.toString().equals(getUserInfoProperty(user, "uid"))){
                    userInfo.setId(Long.parseLong(getUserInfoProperty(user, "uid")));
                    userInfo.setName(getUserInfoProperty(user, "nickname"));
                    userInfo.setPhone(getUserInfoProperty(user, "phone"));
                    userInfo.setEmail(getUserInfoProperty(user, "email"));
                }
            }
            return userInfo;
    }

    private String getUserInfoProperty(Map<String,Object> resultMap, String propertyName) {
        return MapUtils.getString(resultMap, propertyName);
    }

    private Map<String, Object> buildBasicRequestParams() {
        Map<String, Object> requestParams = Maps.newHashMap();
        requestParams.put(REQ_PARAM_AGENT_TYPE, REQ_PARAM_AGENT_TYPE_VALUE);
        requestParams.put(REQ_PARAM_TIMESTAMP, String.valueOf(System.currentTimeMillis()));
        requestParams.put(REQ_PARAM_SOURCE, REQ_PARAM_SOURCE_VALUE);
        return requestParams;

    }

    private boolean isBusinessFailed(ResponseEntity<Map<String, Object>> responseEntity) {

        String resultCode = MapUtils.getString(responseEntity.getBody(),"code");
        return !"A00000".equals(resultCode) && !"P00108".equals(resultCode) && !"P00100".equals(resultCode);
    }

    private static boolean isHttpFailed(ResponseEntity<Map<String, Object>> responseEntity) {
        return responseEntity == null || responseEntity.getStatusCode().value() != HttpStatus.OK.value();
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> getDataNode(ResponseEntity<Map<String, Object>> responseEntity) {
        Map<String, Object> responseData = responseEntity.getBody();
        if (MapUtils.isEmpty(responseData)) {
            return Collections.emptyMap();
        }
        Object object = MapUtils.getObject(responseData, "data", null);
        if (object != null) {
            if (object instanceof LinkedHashMap) {
                return (Map<String, Object>)  object;
            }
            //此处是为了兼容国际站与主站接口返回数据格式的差异
            else {
                return responseData;
            }
        }
        return Collections.emptyMap();
    }

    class QueryUserInfoCommand extends SimpleHystrixCommand<ResponseEntity<Map<String, Object>>> {
        private Long userId;
        private String nickName;
        private String userName;
        private String queryUrl;

        QueryUserInfoCommand(Long userId, String userName, String nickName, String queryUrl) {
            super("PassportApi", 20, 490);
            this.userId = userId;
            this.userName = userName;
            this.nickName = nickName;
            this.queryUrl = queryUrl;
        }

        @Override
        protected ResponseEntity<Map<String, Object>> run() {
            return queryUserInfo(userId, userName, nickName, queryUrl);
        }

        @Override
        protected ResponseEntity<Map<String, Object>> getFallback() {
            return queryUserInfo(userId, userName, nickName, queryUrl);
        }
    }

    class BatchQueryUserInfoCommand extends SimpleHystrixCommand<ResponseEntity<Map<String, Object>>> {

        private List<Long> userIds;

        BatchQueryUserInfoCommand(List<Long> userIds) {
            super("PassportApi", 20, 490);
            this.userIds = userIds;
        }

        @Override
        protected ResponseEntity<Map<String, Object>> run() {
            return batchQueryUserInfos(userIds);
        }

        @Override
        protected ResponseEntity<Map<String, Object>> getFallback() {
            return batchQueryUserInfos(userIds);
        }
    }

    class NotifyPassportLogOffJudgementResultCommand extends SimpleHystrixCommand<BaseResponse>{
        private NotifyPassportUserLogOffReqDto reqDto;

        NotifyPassportLogOffJudgementResultCommand(NotifyPassportUserLogOffReqDto reqDto){
            super("NotifyPassport", 20, 490);
            this.reqDto = reqDto;
        }

        @Override
        protected BaseResponse run() throws Exception {
            return notifyPassportLogOffJudgementResult(reqDto);
        }

        @Override
        protected BaseResponse getFallback() {
            return notifyPassportLogOffJudgementResult(reqDto);
        }
    }



    public class QueryUserInfoByAuthCookieRetryCommand extends SimpleHystrixCommand<ResponseEntity<Map<String, Object>>> {

        private String authCookie;

        QueryUserInfoByAuthCookieRetryCommand(String authCookie) {
            super("PassportApi", 20, 490);
            this.authCookie = authCookie;
        }

        @Override
        protected ResponseEntity<Map<String, Object>> run() {
            return queryUserInfoByAutoCookie(authCookie);
        }

        @Override
        protected ResponseEntity<Map<String, Object>> getFallback() {
            Throwable exception = getFailedExecutionException();
            LOGGER.error("query userInfo from passport error!", exception);
            return new QueryUserInfoByAuthCookieCommand(authCookie).execute();
        }
    }

    public class QueryUserInfoByAuthCookieCommand extends SimpleHystrixCommand<ResponseEntity<Map<String, Object>>> {

        private String authCookie;

        QueryUserInfoByAuthCookieCommand(String authCookie) {
            super("PassportApi", 20, 490);
            this.authCookie = authCookie;
        }

        @Override
        protected ResponseEntity<Map<String, Object>> run() {
            return queryUserInfoByAutoCookie(authCookie);
        }

        @Override
        protected ResponseEntity<Map<String, Object>> getFallback() {
            Throwable exception = getFailedExecutionException();
            LOGGER.error("query userInfo from passport retry error", exception);
            return null;
        }
    }


    private BaseResponse notifyPassportLogOffJudgementResult(NotifyPassportUserLogOffReqDto reqDto) {
        Map<String, String> requestParams = new HashMap<>();
        requestParams.put("uid", String.valueOf(reqDto.getUid()));
        requestParams.put("result", String.valueOf(reqDto.getResult()));
        requestParams.put("details", reqDto.getDetails());
        requestParams.put("type", String.valueOf(reqDto.getType()));
        requestParams.put("subType", String.valueOf(reqDto.getSubType()));
        Long timestamp = reqDto.getTimestamp();
        requestParams.put("timestamp", String.valueOf(timestamp));
        requestParams.put(REQ_PARAM_SOURCE, REQ_PARAM_SOURCE_VALUE);
        String passportSign = SignatureUtil.sign(requestParams, PASSPORT_SECRET_KEY);
        String requestUrl = PASSPORT_CLOSE_ACCOUNT_VERIFICATION_URL + "?" + REQ_PARAM_SOURCE + "=" + REQ_PARAM_SOURCE_VALUE + "&timestamp=" + timestamp + "&sign=" + passportSign;
        return doPostByFormDataWithUrlVariables(passportClient, requestUrl, requestParams, "回调passport通知是否可注销", new ParameterizedTypeReference<BaseResponse<Map<String, Object>>>() {
        });
    }



    private ResponseEntity<Map<String, Object>> queryUserInfo(Long userId, String userName, String nickName, String queryUrl) {
        Map<String, Object> requestParams = buildBasicRequestParams();
        if (userId != null) {
            requestParams.put("uid", userId);
        }
        if (StringUtils.isNotBlank(userName)) {
            requestParams.put("username", userName);
        }
        if (StringUtils.isNotBlank(nickName)) {
            requestParams.put("nickname", nickName);
        }
        return doGet(queryUrl, userName, requestParams);
    }

    private ResponseEntity<Map<String, Object>>  batchQueryUserInfos(List<Long> userIds) {
        Map<String, Object> requestParams = buildBasicRequestParams();
        if (CollectionUtils.isNotEmpty(userIds)) {
            requestParams.put("uids", Joiner.on(",").skipNulls().join(userIds));
            requestParams.put(REQ_PARAM_FIELDS, REQ_PARAM_PRIVATE);
        }
        return doGet(BATCH_USER_INFO_URL, userIds, requestParams);
    }

    private ResponseEntity<Map<String, Object>> queryUserInfoByAutoCookie(String authCookie) {
        Map<String, Object> requestParams = buildBasicRequestParams();
        requestParams.put(REQ_PARAM_FIELDS, REQ_PARAM_PRIVATE);
        requestParams.put("authcookie", authCookie);
        return doGet(GET_USER_INFO_BY_AUTH_COOKIE_URL, authCookie, requestParams);
    }

    private Function<Map<String, Object>, BaseResponse<UidInfo>> getUidByAuthCookieRunFunc = param ->
            doGet(passportClient, GET_UID_BY_AUTH_COOKIE_URL,
                    param,
                    "根据authCookie查询uid",
                    new ParameterizedTypeReference<BaseResponse<UidInfo>>() {});
    private Function<Map<String, Object>, BaseResponse<UidInfo>> getUidByAuthCookieFallbackFunc = param ->
            BaseResponse.create(BaseResponse.CodeEnum.OUTER_SERVICE_ERROR);

    /**
     * 根据authCookie查询uid信息
     * @param authCookie
     * @return
     */
    public UidInfo getUidByAuthCookie(String authCookie) {
        if (StringUtils.isBlank(authCookie)) {
            return null;
        }
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("authCookie", authCookie);
        requestParams.put("source", REQ_PARAM_SOURCE_VALUE);
        requestParams.put("timestamp", System.currentTimeMillis());
        requestParams.put("sign", SignatureUtil.generatePassportSign(requestParams, PASSPORT_SECRET_KEY));
        BaseResponse<UidInfo> response = new CommonHystrixCommand<>(
                HystrixCommandPropsEnum.PASSPORT_GET_UID_BY_AUTH_COOKIE,
                getUidByAuthCookieRunFunc,
                getUidByAuthCookieFallbackFunc,
                requestParams).execute();
        if (response.isFailure()) {
            LOGGER.error("getUidByAuthCookie failure, params: {}", JacksonUtils.toJsonString(requestParams));
            return null;
        }
        return response.getData();
    }
}
