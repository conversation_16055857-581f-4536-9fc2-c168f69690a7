package com.qiyi.boss.utils;

import com.qiyi.boss.Constants;

/**
 * Created by IntelliJ IDEA.
 * ABTest工具类
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * Date: 2020-6-25
 * Time: 17:30
 */
public class ABTestUtils {

	public static final String SUFFIX = "!@#s$fd^as%g^g&h*s";
	public static final int AB_TEST_TOTAL_CAPACITY = 100;

	public static Integer getIndex(String sample) {
		return Math.abs(EncodeUtils.MD5(sample + SUFFIX, Constants.DEFAULT_CHARSET).hashCode() % AB_TEST_TOTAL_CAPACITY);
	}

}
