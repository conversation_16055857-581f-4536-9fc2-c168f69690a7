package com.qiyi.boss.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.qiyi.boss.Constants;
import com.qiyi.boss.dto.AccountBindInfo;
import com.qiyi.boss.dto.AccountResponse;
import com.qiyi.boss.dto.AccountUnbindReqDto;
import com.qiyi.boss.dto.AccountUnbindRespDto;
import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.outerinvoke.BaseApi;
import com.iqiyi.kit.http.client.util.HttpClients;

import static com.qiyi.vip.trade.dataservice.client.response.Response.SUCCESS_CODE;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class DutAccountApi extends BaseApi {

    private static String key = AppConfig.getProperty("account.dutquery.key");

    private static String unbindKey = AppConfig.getProperty("account.unbind.key");

    private static String ACCOUNT_UNBIND_URL = AppConfig.getProperty("account.unSign.url");

    /**
     * <a href="https://iq.feishu.cn/wiki/KYodwIrPNiMvoekeJGIcQ5QWnkd">批量解绑接口文档</a>
     */
    @Value("${account.batch.unbind.url}")
    private String accountBatchUnbindUrl;
    @Value("${qiyi.pay.key}")
    private String qiyiPayKey;

    @Resource(name = "accountHttpClient")
    private RestTemplate accountHttpClient;
    @Resource(name = "accountBatchUnbindHttpClient")
    private RestTemplate accountBatchUnbindHttpClient;

    public Map<Integer, String> cancelDutAccountBind(Map<String, String> data) {
        return new CancelDutBindCommand(data).execute();
    }

    public boolean cancelDutAccountBind(AccountUnbindReqDto accountUnbindReqDto) {
        Map<String,String> params = Maps.newHashMap();
        String sign = EncodeUtils.MD5(accountUnbindReqDto.getType() + "" + accountUnbindReqDto.getUserId() + unbindKey, Charset.defaultCharset().name());
        params.put("uid",accountUnbindReqDto.getUserId().toString());
        params.put("type",accountUnbindReqDto.getType().toString());
        params.put("sign", sign);
        log.info("[Url:{}] [Params:{}]", AppConfig.getProperty("account.unSign.url"), params);
        Map<Integer, String> resultMap = new CancelDutBindCommand(params).execute();
        log.info("[Url:{}] [Params:{}] [Results:{}]", AppConfig.getProperty("account.unSign.url"), params, resultMap);
        String result = resultMap.get(resultMap.keySet().iterator().next());

        return StringUtils.isNotBlank(result) && Objects.equals( "A00000", JSONObject.parseObject(result).getString("code"));
    }

    /**
     * 根据用户id查询账户中心绑定的信息
     * @param userId 用户ID
     * @return listDutTypes of {@link AccountBindInfo}
     */
    public Optional<AccountResponse> queyrAccountBindInfo(Long userId) {
        return new QueryAccountBindInfoCommand(userId).execute();
    }

    public Optional<AccountResponse> queryAccountBindInfo(Long userId, Integer type) {
        return new QueryAccountBindInfoCommand(userId, type).execute();
    }

    public Optional<AccountResponse> queyrAccountBindInfo(Long userId, Integer type, String partner, String partnerOrderNo) {
        return new QueryAccountBindInfoCommand(userId, type, partner, partnerOrderNo).execute();
    }

    class CancelDutBindCommand extends SimpleHystrixCommand<Map<Integer, String>> {

        private Map<String, String> data;

        CancelDutBindCommand(Map<String, String> data) {
            super("AccountApi", 20, 490);
            this.data = data;
        }

        @Override
        protected Map<Integer, String> run() {
            return HttpClientUtil.getInstance().doPost(AppConfig.getProperty("account.unSign.url"), data);
        }

        @Override
        protected Map<Integer, String> getFallback() {
            Map<Integer, String> result = Maps.newHashMap();
            result.put(HttpStatus.REQUEST_TIMEOUT.value(), null);
            return result;
        }
    }

    private class QueryAccountBindInfoCommand extends SimpleHystrixCommand<Optional<AccountResponse>> {

        private Long userId;

        private Integer type;

        private String partner;

        private String partnerOrderNo;

        QueryAccountBindInfoCommand(Long userId) {
            super("AccountApi", 20, 490);
            this.userId = userId;
        }

        QueryAccountBindInfoCommand(Long userId, Integer type) {
            super("AccountApi", 20, 500);
            this.userId = userId;
            this.type = type;
        }

        QueryAccountBindInfoCommand(Long userId, Integer type, String partner, String partnerOrderNo) {
            super("AccountApi", 20, 500);
            this.userId = userId;
            this.type = type;
            this.partner = partner;
            this.partnerOrderNo = partnerOrderNo;
        }

        @Override
        protected Optional<AccountResponse> run() {
            return getDutBindInfo(userId, type, partner, partnerOrderNo);
        }

        @Override
        protected Optional<AccountResponse> getFallback() {
            return getDutBindInfo(userId, type, partner, partnerOrderNo);
        }
    }

    private Optional<AccountResponse> getDutBindInfo(Long userId, Integer dutType, String partner, String partnerOrderNo) {
        Map<String, String> configs = Maps.newHashMap();
        configs.put("uid", String.valueOf(userId));
        if (dutType != null) {
            configs.put("type", String.valueOf(dutType));
        }
        configs.put("version", "1.0");
        if (StringUtils.isNotBlank(partner)) {
            configs.put("partner", partner);
        }
        if (StringUtils.isNotBlank(partnerOrderNo)) {
            configs.put("partnerOrderNo", partnerOrderNo);
        }
        configs.put("sign", PayUtils.signMessageRequest(configs, key));

        try {
            String url = AppConfig.getProperty("account.dutquery.url") + "?"
                    + Joiner.on("&").withKeyValueSeparator("=").join(configs);
            ResponseEntity<AccountResponse> responseEntity = accountHttpClient.exchange(
                url, HttpMethod.POST, null, new ParameterizedTypeReference<AccountResponse>() {}
            );
            AccountResponse body = responseEntity.getBody();
            log.info("getDutBindInfo, url:{}, result: {}", url, JacksonUtils.toJsonString(body));
            if (body != null && SUCCESS_CODE.equals(body.getCode())) {
                return Optional.ofNullable(body);
            }
        } catch (RestClientException e) {
            log.error("An exception is thrown when get User by userId={}, type={}, error={}", userId, dutType, e.getMessage());
            return Optional.empty();
        }
        return Optional.empty();
    }

    private Function<Map<String, Object>, BaseResponse<AccountUnbindRespDto>> accountUnbindRunFunc = param ->
            doGet(accountHttpClient, ACCOUNT_UNBIND_URL, param,
                "请求账户中心解绑接口",
                new ParameterizedTypeReference<BaseResponse<AccountUnbindRespDto>>() {});
    private Function<Map<String, Object>, BaseResponse<AccountUnbindRespDto>> accountUnbindFallbackFunc = param ->
            BaseResponse.create(BaseResponse.CodeEnum.OUTER_SERVICE_ERROR);

    /**
     * 请求账户中户中心解绑
     * @param accountUnbindReqDto
     * @return
     */
    public boolean unbindAccount(AccountUnbindReqDto accountUnbindReqDto) {
        Map<String, Object> params = Maps.newHashMap();
        String sign = EncodeUtils.MD5(accountUnbindReqDto.getType() + "" + accountUnbindReqDto.getUserId() + unbindKey, Charset.defaultCharset().name());
        params.put("uid",accountUnbindReqDto.getUserId());
        params.put("type",accountUnbindReqDto.getType());
        params.put("sign", sign);
        BaseResponse<AccountUnbindRespDto> response = new CommonHystrixCommand<>(
                HystrixCommandPropsEnum.ACCOUNT_UNBIND,
                accountUnbindRunFunc,
                accountUnbindFallbackFunc,
                params).execute();
        String code = response.getCode();
        boolean success = BaseResponse.CodeEnum.isSuccess(code) || "W00000".equals(code) || "ACC008".equals(code);
        if (!success) {
            log.error("请求账户中心解绑失败，result: {}", JacksonUtils.toJsonString(response));
        }
        return success;
    }



    /**
     * 请求账户中户中心批量解绑
     */
    public boolean batchUnbindAccount(Long userId, List<Integer> dutTypes) {
        return batchUnbindAccount(userId, dutTypes, null);
    }

    public boolean batchUnbindAccount(Long userId, List<Integer> dutTypes, String bundleID) {
        if (CollectionUtils.isEmpty(dutTypes)) {
            return true;
        }
        String dutTypeStr = dutTypes.stream().map(String::valueOf).collect(Collectors.joining(","));
        Map<String, Object> params = Maps.newHashMap();
        params.put("userId", userId);
        params.put("types", dutTypeStr);
        params.put("partner", Constants.DEFAULT_PARTNER);
        if (StringUtils.isNotBlank(bundleID)) {
            params.put("bundleId", bundleID);
        }
        String sign = PayUtils.generateSignWithObjValue(params, qiyiPayKey);
        params.put("sign", sign);
        String urlWithParam = HttpClients.buildQueryUrl(accountBatchUnbindUrl, params);
        Callable<BaseResponse<String>> batchUnbindInvokeCallable = () -> doBatchUnbindAccount(urlWithParam);
        BaseResponse<String> baseResponse = RetryerUtils.invokeWithOnceRetry(batchUnbindInvokeCallable, "batchUnbindAccount");
        if (baseResponse == null) {
            return true;
        }
        String code = baseResponse.getCode();
        return BaseResponse.CodeEnum.isSuccess(code) || "W00000".equals(code) || "ACC008".equals(code);
    }

    private BaseResponse<String> doBatchUnbindAccount(String urlWithParam) {
        StopWatch stopWatch = StopWatch.createStarted();
        log.info("请求账户中心批量解绑接口-start，url: {}", urlWithParam);
        ResponseEntity<BaseResponse<String>> responseEntity = null;
        try {
            responseEntity = accountBatchUnbindHttpClient.exchange(
                urlWithParam, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResponse<String>>() {});
        } catch (Exception e) {
            log.error("请求账户中心批量解绑接口-失败，url: {}", urlWithParam, e);
            throw e;
        }
        BaseResponse<String> responseBody = responseEntity.getBody();
        log.info("请求账户中心批量解绑接口-end，url: {}, cost: {}, result: {}", urlWithParam, stopWatch.getTime(), JacksonUtils.toJsonString(responseBody));
        return responseBody;
    }

}
