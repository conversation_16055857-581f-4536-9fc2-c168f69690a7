package com.qiyi.boss.utils;

import com.qiyi.boss.Constants;
import com.qiyi.boss.dto.UserUpgradeInfo;
import com.qiyi.boss.enums.UpgradeStageEnum;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewUpgradeConfig;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * Created at: 2022-08-04
 *
 * <AUTHOR>
 */
public class UpgradeAutoRenewUtils {

    /**
     * 计算用户升级信息
     */
    public static UserUpgradeInfo calcUpgradeInfo(DutUserNew dutUserNew, AutoRenewUpgradeConfig autoRenewUpgradeConfig, Integer upgradeDays) {
        Integer amount = null == dutUserNew.getAmount() ? Constants.AMOUNT_OF_COMMON_AUTORENEW : dutUserNew.getAmount();
        String pid;
        String skuId;
        Integer renewPrice;
        UpgradeStageEnum upgradeStage;
        if (!autoRenewUpgradeConfig.getFinalProductCode().equals(dutUserNew.getPcode())) {
            if (UpgradeAutoRenewUtils.dutByMonth(upgradeDays)) {
                upgradeStage = UpgradeStageEnum.FIRST_STAGE;
                amount = Constants.AMOUNT_OF_COMMON_AUTORENEW;
                pid = autoRenewUpgradeConfig.getMonthProductCode();
                skuId = autoRenewUpgradeConfig.getMonthSkuId();
                renewPrice = amount * autoRenewUpgradeConfig.getMonthRenewPrice();
            } else if (UpgradeAutoRenewUtils.dutByDay(upgradeDays)) {
                upgradeStage = UpgradeStageEnum.SECOND_STAGE;
                amount = upgradeDays;
                pid = autoRenewUpgradeConfig.getDayProductCode();
                skuId = autoRenewUpgradeConfig.getDaySkuId();
                renewPrice = amount * autoRenewUpgradeConfig.getDayRenewPrice();
            } else {
                upgradeStage = UpgradeStageEnum.FINAL_STAGE;
                pid = autoRenewUpgradeConfig.getFinalProductCode();
                skuId = autoRenewUpgradeConfig.getFinalSkuId();
                renewPrice = amount * autoRenewUpgradeConfig.getFinalRenewPrice();
            }
        } else {
            upgradeStage = UpgradeStageEnum.FINAL_STAGE;
            pid = autoRenewUpgradeConfig.getFinalProductCode();
            skuId = autoRenewUpgradeConfig.getFinalSkuId();
            renewPrice = amount * autoRenewUpgradeConfig.getFinalRenewPrice();
        }
        return UserUpgradeInfo.builder()
            .upgradeStage(upgradeStage)
            .pid(pid)
            .skuId(skuId)
            .amount(amount)
            .renewPrice(renewPrice)
            .build();
    }

    /**
     * 校验是否按月代扣
     * @param amount 可升级天数
     */
    private static boolean dutByMonth(int amount) {
        return amount >= DateHelper.daysInCurrentMonth();
    }

    /**
     * 校验是否按天代扣
     * @param amount 可升级天数
     */
    private static boolean dutByDay(int amount) {
        return amount > 0 && amount < DateHelper.daysInCurrentMonth();
    }

}
