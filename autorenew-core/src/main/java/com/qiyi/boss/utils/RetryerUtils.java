package com.qiyi.boss.utils;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

import com.qiyi.boss.exception.BizException;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.model.BaseResponse.CodeEnum;

/**
 * <AUTHOR>
 * @date 2021/12/27 下午 02:36
 */
@Slf4j
public class RetryerUtils {
    /**
     *
     * @param callable
     * @param <T>
     * @return
     */
    public static <T> BaseResponse<T> invokeWithOnceRetry(Callable<BaseResponse<T>> callable, String remoteServiceName) {
        Retryer<BaseResponse<T>> retryer = RetryerBuilder
            .<BaseResponse<T>>newBuilder()
            //抛出runtime异常、checked异常时都会重试，但是抛出error不会重试。
            .retryIfException()
            //重调策略
            .withWaitStrategy(WaitStrategies.fixedWait(150, TimeUnit.MILLISECONDS))
            //尝试次数
            .withStopStrategy(StopStrategies.stopAfterAttempt(2))
            .build();
        try {
            BaseResponse<T> response = retryer.call(callable);
            log.info("invoke {} success, response:{}", remoteServiceName, response);
            return response;
        } catch (Exception e) {
            log.error("invoke {} error", remoteServiceName, e);
            throw BizException.newException(CodeEnum.OUTER_SERVICE_ERROR);
        }
    }

}
