package com.qiyi.boss.utils;

import com.iqiyi.solar.config.client.CloudConfig;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Objects;

/**
 * 应用配置,对应配置中心文件:
 * boss_web_config 线上环境
 * boss_web_config_test 测试环境
 * Created by <PERSON><PERSON><PERSON> on 2016/9/19.
 */
@Component
public class AutoRenewConfig {

    @Resource
    private CloudConfig cloudConfig;

    /**
     * autorenew-task服务器数量
     */
    public Integer getAutoRenewTaskServerNum() {
        return cloudConfig.getIntProperty("autorenewTaskServerNum", 4);
    }

    /**
     * 碎片服务接口的接口
     */
    public String getHttpFragmentServiceUrl() {
        return cloudConfig.getProperty("httpFragmentServiceUrl", "");
    }

    /**
     * 退款取消自动续费开关
     */
    public boolean isRefundCancelAutorenew() {
        return cloudConfig.getBooleanProperty("isRefundCancelAutorenew", true);
    }

    public boolean cancelAutoRenewWhenRefund(Long userId) {
        String[] uidWhiteArrays = cloudConfig.getArrayProperty("not.cancel.autorenew.when.refund.uids", ",", new String[]{});
        return Arrays.stream(uidWhiteArrays).noneMatch(uid -> Objects.equals(uid, userId.toString()));
    }

    /**
     * 切换支付中心新的签名key
     */
    public boolean isUseNewPayCenterSignKey() {
        return cloudConfig.getBooleanProperty("isUseNewPayCenterSignKey", false);
    }

    /**
     * 是否需要处理苹果宽限期
     */
    public boolean isNeedDealGracePeriod(String switchName) {
        return cloudConfig.getBooleanProperty(switchName, true);
    }

    /**
     * 是否开启微信预扣费通知
     */
    public boolean needWechatPreDutRemind() {
        return cloudConfig.getBooleanProperty("needWechatPreDutRemind", false);
    }

    /**
     * 微信预扣费通知是否开启分流
     */
    public boolean needABTestOfWechatPreDutRemind() {
        return cloudConfig.getBooleanProperty("needABTestOfWechatPreDutRemind", false);
    }

    /**
     * 微信预扣费通知可重试错误码
     */
    public String wechatPreDutRemindRetryErrorCodes() {
        return cloudConfig.getProperty("wechatPreDutRemindRetryErrorCodes", "");
    }

    /**
     * 判断当前平台是否返回下次可领福利信息
     * @param platform
     */
    public boolean needShowReceivableGiftInfo(String platform){
        String[] showReceivableGiftInfoPlatforms = cloudConfig.getArrayProperty("marketing.show.ReceivableGiftInfo.platform", ",", null);
        return ArrayUtils.contains(showReceivableGiftInfoPlatforms, platform);
    }

    public boolean dutByNewProcess() {
        return cloudConfig.getBooleanProperty("dutByNewProcess", false);
    }

    public String dutByNewProcessUserIds() {
        return cloudConfig.getProperty("dutByNewProcessUserIds", "");
    }

    public Integer routeByUserIdRange() {
        return cloudConfig.getIntProperty("routeByUserIdRange", -1);
    }

    public String routeByDutTypes() {
        return cloudConfig.getProperty("routeByDutTypes", "");
    }

    public boolean dutSupportUidWhiteList() {
        return cloudConfig.getBooleanProperty("dutSupportWhiteList", false);
    }

    public String dutUidWhiteList() {
        return cloudConfig.getProperty("dutUidWhiteList", "");
    }

    public boolean useNewPayCenterKey() {
        return cloudConfig.getBooleanProperty("useNewPayCenterKey", false);
    }

    public Boolean autorenewMessageSearchAliPayAccountId() {
        return cloudConfig.getBooleanProperty("autorenew.message.searchAliPayAccountId.fromOrders",false);
    }

}
