package com.qiyi.boss.utils;

import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import com.qiyi.boss.Constants;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;

/**
 * <AUTHOR>
 * DateTime: 2019/12/09 下午15:14
 * Mail:<EMAIL>
 * Description: 纯签约工具类
 */
public class PureSignUtils {

    public static String getWechatPureSignPayType(HttpServletRequest httpServletRequest) {

        if (isWechatMiniProgram(httpServletRequest)) {
            return Constants.WECHAT_MINI_SIGN;
        }

        if (isWechat(httpServletRequest)) {
            return Constants.WECHAT_V3_SIGN;
        }

        if (isIqiyiGphone(httpServletRequest)) {
            return Constants.WECHAT_PURE_PAY_TYPE;
        }

        return Constants.WECHAT_SIGN_H5;
    }

    public static List<String> genPureSignPayTypeList(HttpServletRequest httpServletRequest) {
        return genPureSignPayTypeList(httpServletRequest, null);
    }

    /**
     * 生成纯签约支付类型列表，支持支付渠道过滤
     *
     * @param httpServletRequest HTTP请求对象
     * @param payChannel 支付渠道过滤参数，1=支付宝，2=微信，null=不过滤
     * @return 支付类型列表
     */
    public static List<String> genPureSignPayTypeList(HttpServletRequest httpServletRequest, Integer payChannel) {
        List<String> pureSignList = Lists.newArrayList();

        boolean isAli = isAliApp(httpServletRequest);
        boolean isWx = isWechat(httpServletRequest);

        // 微信支付类型判断：如果不是支付宝环境 且 (没有指定渠道 或 指定了微信渠道)
        if (!isAli && (payChannel == null || payChannel == PaymentDutType.PAY_CHANNEL_WECHAT)) {
            pureSignList.add(getWechatPureSignPayType(httpServletRequest));
        }

        // 支付宝支付类型判断：如果不是微信环境 且 (没有指定渠道 或 指定了支付宝渠道)
        if (!isWx && (payChannel == null || payChannel == PaymentDutType.PAY_CHANNEL_ALIPAY)) {
            pureSignList.add(Constants.ALI_PURE_PAY_TYPE);
        }

        return pureSignList;
    }

    /** 微信小程序 */
    private static boolean isWechatMiniProgram(HttpServletRequest servletRequest) {
        String userAgent = servletRequest.getHeader("user-agent");
        return userAgent != null
                && userAgent.toLowerCase().contains("micromessenger")
                && userAgent.toLowerCase().contains("miniprogram");
    }

    /** 微信H5 */
    private static boolean isWechat(HttpServletRequest servletRequest) {
        String userAgent = servletRequest.getHeader("user-agent");
        return userAgent != null
                && userAgent.toLowerCase().contains("micromessenger");
    }

    /** 爱奇艺安卓APP */
    private static boolean isIqiyiGphone(HttpServletRequest servletRequest) {
        return StringUtils.isEmpty(servletRequest.getHeader("user-agent"))
                || StringUtils.isEmpty(servletRequest.getHeader("referer"));
    }

    /** 支付宝 */
    private static boolean isAliApp(HttpServletRequest servletRequest) {
        String userAgent = servletRequest.getHeader("user-agent");
        return userAgent != null
                && userAgent.toLowerCase().contains("aliapp");
    }

}
