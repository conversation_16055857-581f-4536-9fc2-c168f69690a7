package com.qiyi.boss.utils;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import java.util.Map;
import com.qiyi.boss.Constants;
import com.qiyi.boss.autorenew.dto.MessageSenderDto;
import com.qiyi.vip.trade.autorenew.mq.MsgSenderService;

@Slf4j
public class MessageMQUtils {

    /**
     * true:压测模式 false:非压测模式
     */
    private static final String PRESSURE_TEST_MODE = "Pressure-Test-Mode";

	public static void sendMessage(MessageSenderDto dto) {

		long sendStart = System.currentTimeMillis();
		Map<String, String> dataMap = Maps.newHashMap();
		try {
            String msgType = Constants.MESSAGE_TYPE_AUTO_RENEW_RECHARGE_SMS_REMIND;
            dataMap.put("msgtype", msgType);
			dataMap.put("uid", String.valueOf(dto.getUserId()));
			dataMap.put("dut_type", String.valueOf(dto.getDutType()));
            dataMap.put("agreement_no", String.valueOf(dto.getAgreementNo()));
			dataMap.put("type_name", StringUtils.isEmpty(dto.getPayChannelName()) ? "" : dto.getPayChannelName());
            dataMap.put("pay_channel", String.valueOf(dto.getPayChannel()));
			dataMap.put("vip_type", String.valueOf(dto.getVipType()));
			dataMap.put("failed_type", StringUtils.isEmpty(dto.getFailedType()) ? "" : dto.getFailedType());
			log.info("Running autorenew retry, begin to send MQ for params:{}", dataMap);
			//发送消息到会员营销
			boolean isSuccess = ApplicationContextUtil.getBean(MsgSenderService.class).sendAutorenewRemindMsg(dataMap, msgType);
			long sendEnd = System.currentTimeMillis();
			log.info("End to run  for userId:{} with params:{}, and the result is:{}, cost:{}ms.]",
					dto.getUserId(), dataMap, isSuccess, sendEnd - sendStart);

		} catch (Exception e) {
			log.error("Running autorenew retry, send MQ meets an error for params:{}]", dataMap, e);
		}
	}

    /**
     * 是否为压测消息
     * @param messageExt 消息实体
     * @return true:是压测消息、不需要处理，false:非压测消息、需要处理
     */
    public static boolean isPressureMsg(MessageExt messageExt) {
        String pressureTest = messageExt.getUserProperty(PRESSURE_TEST_MODE);
        return Boolean.parseBoolean(pressureTest);
    }
}
