package com.qiyi.boss.utils;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.config.PropertyPlaceholderConfigurer;

import java.util.Properties;

/**
 * Created by IntelliJ IDEA.
 * <AUTHOR>
 * <AUTHOR>
 * Date: 13-7-19
 * Time: 下午3:20
 * To change this template use File | Settings | File Templates.
 */
public class MyPropertyPlaceholderConfigurer extends PropertyPlaceholderConfigurer {

    @Override
    protected void processProperties(
            ConfigurableListableBeanFactory beanFactory, Properties props) throws BeansException {
        String password = props.getProperty("jdbc.password");
        props.setProperty("jdbc.password", Encode.decode(password));
        String masterPassword = props.getProperty("master.jdbc.password");
        props.setProperty("master.jdbc.password", Encode.decode(masterPassword));
        String slavePassword = props.getProperty("slave.jdbc.password");
        props.setProperty("slave.jdbc.password", Encode.decode(slavePassword));

        String qiyueUrl = props.getProperty("qiyue.jdbc.url");
        props.setProperty("qiyue.jdbc.url", Encode.decode(qiyueUrl));
        String qiyueUserName = props.getProperty("qiyue.jdbc.username");
        props.setProperty("qiyue.jdbc.username", Encode.decode(qiyueUserName));
        String qiyuePassword = props.getProperty("qiyue.jdbc.password");
        props.setProperty("qiyue.jdbc.password", Encode.decode(qiyuePassword));

        super.processProperties(beanFactory, props);
    }
}
