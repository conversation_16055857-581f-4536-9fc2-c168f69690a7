package com.qiyi.boss.utils;

import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * Created at: 2021-09-17
 *
 * <AUTHOR>
 */
@Slf4j
public class HystrixFutureHelper {

    public static <T> T getFutureResult(Future<T> future, T defaultValue, HystrixCommandPropsEnum commandProps) {
        return getFutureResult(future, null, defaultValue, commandProps);
    }

    /**
     *
     * @param future
     * @param timeout 毫秒
     * @param defaultValue
     */
    public static <T> T getFutureResult(Future<T> future, Integer timeout, T defaultValue, HystrixCommandPropsEnum commandProps) {
        if (future == null) {
            return defaultValue;
        }
        try {
            T result = timeout != null ? future.get(timeout, TimeUnit.MILLISECONDS) : future.get();
            return result == null ? defaultValue : result;
        } catch (Exception e) {
            log.error("getFutureResult occur exception, hystrix GroupKey: {}, commandKey: {}", commandProps.getGroupKey(), commandProps.getCommandKey(), e);
            return defaultValue;
        }
    }

}
