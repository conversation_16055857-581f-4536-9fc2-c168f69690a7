package com.qiyi.boss.utils.lock;

import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @version 12-3-1 - 下午12:05
 */

public class SysnKeyLock extends ReentrantLock {
	/**
	 *
	 */
	private static final long serialVersionUID = 1478211960393710925L;

	String key;

	public SysnKeyLock(String key) {
		super();
		this.key = key;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

}
