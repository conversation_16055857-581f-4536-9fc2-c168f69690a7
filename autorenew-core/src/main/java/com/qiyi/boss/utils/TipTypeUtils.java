package com.qiyi.boss.utils;

import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;

/**
 * Created at: 2021-10-11
 *
 * <AUTHOR>
 */
public class TipTypeUtils {

    public static boolean isNewMobilePayChannel(Integer payChannel) {
        return payChannel == PaymentDutType.PAY_CHANNEL_CUCC
                || payChannel == PaymentDutType.PAY_CHANNEL_CMCC
                || payChannel == PaymentDutType.PAY_CHANNEL_CTCC;
    }

}
