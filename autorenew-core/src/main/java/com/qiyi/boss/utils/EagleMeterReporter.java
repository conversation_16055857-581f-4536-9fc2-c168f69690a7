package com.qiyi.boss.utils;

import org.apache.commons.lang3.StringUtils;

import com.iqiyi.v.eagle.EagleMonitor;
import com.iqiyi.v.eagle.bo.EagleParam;

/**
 * Created at: 2022-11-05
 *
 * <AUTHOR>
 */
public class EagleMeterReporter {

    public static void incrCountOfApiInvoke(String url){
        if (StringUtils.isBlank(url)) {
            return;
        }
        EagleMonitor.counterInc(new EagleParam("api_invoke_count_total").tag("url", url));
    }

    public static void incrCountOfMQConsumer(String consumer){
        if (StringUtils.isBlank(consumer)) {
            return;
        }
        EagleMonitor.counterInc(new EagleParam("mq_consumer_count_total").tag("consumer", consumer));
    }

    public static void incrCountOfOptAutoRenew(String url, Integer op){
        if (StringUtils.isBlank(url)) {
            return;
        }
        String opValue = op == null ? "null" : op.toString();
        EagleMonitor.counterInc(new EagleParam("opt_auto_renew_count_total").tag("url", url).tag("op", opValue));
    }

    /**
     * 缺少可替换的协议编号
     */
    public static void incrMissReplaceableAgreementNo(Integer dutType, Integer amount, Short priority){
        EagleMonitor.counterInc(new EagleParam("miss_replaceable_agreement_no")
            .tag("dutType", dutType.toString())
            .tag("amount", amount.toString())
            .tag("priority", priority.toString())
        );
    }

    /**
     * 记录订单属性不一致的情况
     * @param skuId 商品ID
     * @param propertyName 不一致的属性名
     * @param propertyValue 不一致的属性值
     */
    public static void incrOrderPropertyInconsistency(String skuId, String propertyName, String propertyValue) {
        EagleMonitor.counterInc(new EagleParam("order_property_inconsistency")
            .tag("sku_id", StringUtils.defaultString(skuId, "null"))
            .tag("property", propertyName)
            .tag("property_value", propertyValue)
        );
    }
}
