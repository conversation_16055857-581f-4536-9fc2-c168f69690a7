package com.qiyi.boss.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import com.qiyi.boss.dto.CouponSkuInfo;
import com.qiyi.boss.dto.FirstDutInfo;
import com.qiyi.boss.dto.ManagementRenewInfoVO;
import com.qiyi.boss.dto.RenewInfoVO;
import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.boss.model.PayCenterCoupon;
import com.qiyi.boss.outerinvoke.result.CouponContext;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;

/**
 * @author: guojing
 * @date: 2025/7/3 17:38
 */
@Slf4j
public class DutCouponUtils {

    public static void calcAndSetPriceAfterCoupon(List<ManagementRenewInfoVO> autoRenewList, Future<List<CouponContext>> userCouponListFuture, List<Integer> cannotUseCouponPayChannels) {
        List<CouponContext> couponContexts = HystrixFutureHelper.getFutureResult(userCouponListFuture,
            Collections.emptyList(), HystrixCommandPropsEnum.COUPON_PROXY_QUERY);
        if (CollectionUtils.isEmpty(couponContexts)) {
            return;
        }

        // 分离站内券和站外券
        List<CouponContext> onSiteCoupons = couponContexts.stream()
            .filter(couponContext -> !Boolean.TRUE.equals(couponContext.getCoupon().getOffSite()))
            .filter(couponContext -> CollectionUtils.isNotEmpty(couponContext.getSkuInfoSet()))
            .collect(Collectors.toList());
        
        List<CouponContext> offSiteCoupons = couponContexts.stream()
            .filter(couponContext -> Boolean.TRUE.equals(couponContext.getCoupon().getOffSite()))
            .collect(Collectors.toList());
        
        // 站内券按skuId分组
        Map<Set<String>, List<CouponContext>> skuIdSetToCouponMap = onSiteCoupons.stream()
            .collect(Collectors.groupingBy(couponContext ->
                couponContext.getSkuInfoSet().stream()
                    .map(CouponSkuInfo::getSkuId)
                    .collect(Collectors.toSet()))
            );
        for (ManagementRenewInfoVO renewInfoVO : autoRenewList) {
            RenewInfoVO autoRenewInfo = renewInfoVO.getAutoRenewInfo();
            String priceAfterCoupon = calcPriceAfterCoupon(autoRenewInfo, skuIdSetToCouponMap, offSiteCoupons, cannotUseCouponPayChannels);
            if (StringUtils.isNotBlank(priceAfterCoupon)) {
                autoRenewInfo.setPriceAfterCoupon(priceAfterCoupon);
            }
        }
    }

    private static String calcPriceAfterCoupon(RenewInfoVO renewInfoVO, Map<Set<String>, List<CouponContext>> skuIdSetToCouponMap, List<CouponContext> offSiteCoupons, List<Integer> cannotUseCouponPayChannels) {
        if (renewInfoVO == null) {
            return null;
        }
        FirstDutInfo firstDutInfo = renewInfoVO.getFirstDutInfo();
        if (firstDutInfo == null || CollectionUtils.isEmpty(renewInfoVO.getPayTypeInfo())) {
            return null;
        }
        Integer payChannel = renewInfoVO.getPayTypeInfo().get(0).getPayChannel();
        if (cannotUseCouponPayChannels.contains(payChannel)) {
            return null;
        }

        String skuId = firstDutInfo.getSkuId();
        LocalDate nextDutDay = LocalDate.parse(renewInfoVO.getDoPayTime());
        Integer agreementType = renewInfoVO.getAgreementType();
        Integer renewPrice = firstDutInfo.getFirstRenewPrice();
        
        // 收集所有可用的券进行统一比较
        List<CouponContext> allAvailableCoupons = new ArrayList<>();
        
        // 添加匹配skuId的站内券
        if (skuId != null && MapUtils.isNotEmpty(skuIdSetToCouponMap)) {
            for (Map.Entry<Set<String>, List<CouponContext>> entry : skuIdSetToCouponMap.entrySet()) {
                Set<String> skuIdSet = entry.getKey();
                if (skuIdSet.contains(skuId)) {
                    allAvailableCoupons.addAll(entry.getValue());
                }
            }
        }
        
        // 添加站外券
        if (CollectionUtils.isNotEmpty(offSiteCoupons)) {
            allAvailableCoupons.addAll(offSiteCoupons);
        }
        
        // 统一选择最优券
        if (CollectionUtils.isNotEmpty(allAvailableCoupons)) {
            PayCenterCoupon coupon = selectBestCoupon(allAvailableCoupons, renewPrice, nextDutDay, agreementType, payChannel);
            if (coupon != null) {
                log.info("selected coupon:{}", coupon);
                Integer couponFee = coupon.getFee();
                int feeAfterCoupon = renewPrice - couponFee;
                if (feeAfterCoupon >= 0) {
                    return NumberFormatUtils.formatToStr(feeAfterCoupon, "#.#");
                } else {
                    log.error("券后金额小于0，coupon:{}, renewPrice:{}", coupon, renewPrice);
                    return "0.0";
                }
            }
        }
        return null;
    }

    /**
     * 从券列表中选择最优券
     * @param couponContexts 券上下文列表
     * @param renewPrice 续费价格
     * @param dutDay 代扣日期
     * @param agreementType 协议类型
     * @param payChannel 支付渠道
     * @return 最优券，如果没有可用券则返回null
     */
    public static PayCenterCoupon selectBestCoupon(List<CouponContext> couponContexts,
                                                   Integer renewPrice, 
                                                   LocalDate dutDay, 
                                                   Integer agreementType, 
                                                   Integer payChannel) {
        if (CollectionUtils.isEmpty(couponContexts)) {
            return null;
        }

        return couponContexts.stream()
            .filter(couponContext -> matchCouponRule(couponContext, agreementType, payChannel))
            .filter(couponContext -> isCouponValid(couponContext, renewPrice, dutDay))
            .map(CouponContext::getCoupon)
            .filter(c -> StringUtils.isBlank(c.getConsumeOrderCode()))
            .sorted(createCouponComparator())
            .findFirst()
            .orElse(null);
    }

    /**
     * 创建券排序比较器
     * 排序规则：
     * 1. 优先面额更大的券
     * 2. 如面额相同，优先使用有效期更近的券
     * 3. 如以上都相同，优先使用站外续费立减券
     * 4. 之后随机（通过券码字符串排序实现稳定的伪随机）
     */
    public static Comparator<PayCenterCoupon> createCouponComparator() {
        return Comparator
            .comparing(PayCenterCoupon::getFee, Comparator.reverseOrder()) // 1. 面额降序
            .thenComparing(PayCenterCoupon::getEndTime) // 2. 有效期升序（更近的优先）
            .thenComparing(coupon -> Boolean.TRUE.equals(coupon.getOffSite()) ? 0 : 1) // 3. 站外券优先
            .thenComparing(PayCenterCoupon::getCode); // 4. 券码排序实现稳定的伪随机
    }
    
    /**
     * 验证券是否有效（过期时间和门槛价判断）
     * @param couponContext 券上下文
     * @param renewPrice 续费价格
     * @param dutDay 代扣日期
     * @return 券是否有效
     */
    public static boolean isCouponValid(CouponContext couponContext, Integer renewPrice, LocalDate dutDay) {
        PayCenterCoupon coupon = couponContext.getCoupon();
        if (coupon == null) {
            return false;
        }

        // 过期时间判断
        if (!coupon.timeValid(dutDay)) {
            return false;
        }
        // 门槛价判断
        if (renewPrice != null && coupon.getTransactionMinimum() != null && Boolean.TRUE.equals(coupon.getOffSite()) && renewPrice < coupon.getTransactionMinimum()) {
            return false;
        }

        return true;
    }

    /**
     * 匹配券规则
     * @param couponContext 券上下文
     * @param agreementType 协议类型
     * @param payChannel 支付渠道
     * @return 是否匹配规则
     */
    public static boolean matchCouponRule(CouponContext couponContext, Integer agreementType, Integer payChannel) {
        PayCenterCoupon coupon = couponContext.getCoupon();
        if (coupon == null) {
            return false;
        }

        // 如果券是站外的，需要能获取到channelDiscountTokenConfig（不是null）&& 同渠道
        if (Boolean.TRUE.equals(coupon.getOffSite())) {
            String discountToken = CloudConfigUtil.getChannelDiscountToken(agreementType, payChannel);
            boolean hasToken = StringUtils.isNotBlank(discountToken);
            Integer mappedPayChannel = CloudConfigUtil.getPayChannelByThirdChannel(coupon.getThirdChannel());
            boolean sameChannel = ObjectUtils.equals(payChannel, mappedPayChannel);
            boolean result = hasToken && sameChannel;
            log.info("站外券matchCouponRule匹配结果: {}, hasToken:{}, sameChannel:{}, code:{}", result, hasToken, sameChannel, coupon.getCode());
            return result;
        } else {
            // 如果券是站内的，则需要SkuInfoSet不为空，过滤逻辑由券代理系统实现
            return CollectionUtils.isNotEmpty(couponContext.getSkuInfoSet());
        }
    }
}
