package com.qiyi.boss.utils;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created at: 2021-06-24
 *
 * <AUTHOR>
 */
public class SignKeyGenerator {

    /**
     * 最大自增数
     */
    private static final int MAX_INCR_NUM = 999;

    private static final AtomicInteger INCR_NUM = new AtomicInteger(0);

    public static String getSignKey(Long userId, Integer agreementType) {
        String time = DateHelper.getDateStringByPattern(DateHelper.getCurrentTime(), "yyyyMMddHHmmssSSS");
        String uidPart = String.format("%02d", userId % 100);
        if (INCR_NUM.get() > MAX_INCR_NUM) {
            INCR_NUM.set(0);
        }
        String agreementTypeStr = String.format("%02d", agreementType);
        String currNum = String.format("%03d", INCR_NUM.get());
        INCR_NUM.incrementAndGet();
        return time + uidPart + agreementTypeStr + currNum;
    }

}
