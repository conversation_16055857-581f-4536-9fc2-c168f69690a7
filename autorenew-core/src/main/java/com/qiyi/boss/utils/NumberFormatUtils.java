package com.qiyi.boss.utils;

import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * Created at: 2020-10-29
 *
 * <AUTHOR>
 */
public class NumberFormatUtils {

    /**
     * 格式化数字
     * @param number 单位：分
     * @param pattern
     * @return 返回的数值单位为元
     */
    public static String formatToStr(Integer number, String pattern) {
        if (number == null) {
            return "0";
        }
        DecimalFormat df = new DecimalFormat(pattern);
        df.setRoundingMode(RoundingMode.HALF_UP);
        return df.format(number * 1.0 / 100);
    }

    /**
     * 分转为元
     * @param number
     */
    public static double formatNumber(Integer number) {
        if (number == null) {
            return 0;
        }
        DecimalFormat df = new DecimalFormat("#.##");
        df.setRoundingMode(RoundingMode.HALF_UP);
        return Double.parseDouble(df.format(number * 1.0 / 100));
    }

}
