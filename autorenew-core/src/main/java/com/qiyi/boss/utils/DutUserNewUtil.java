package com.qiyi.boss.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Set;

import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.SimpleDutUserNew;

/**
 * User: kongwenqiang
 * DateTime: 2017/9/6 下午6:45
 * Mail:<EMAIL>   
 * Description: desc
 */
public class DutUserNewUtil {

    public static List<DutUserNew> distinctByUserId(List<DutUserNew> dutUserNewList) {
        if (CollectionUtils.isEmpty(dutUserNewList)) {
            return dutUserNewList;
        }
        Set<Long> userIdSet = Sets.newHashSet();

        List<DutUserNew> resultDutUserList = Lists.newLinkedList();
        for (DutUserNew dutUserNew : dutUserNewList) {
            if (!userIdSet.contains(dutUserNew.getUserId())){
                resultDutUserList.add(dutUserNew);
                userIdSet.add(dutUserNew.getUserId());
            }
        }
        return resultDutUserList;
    }

    public static List<SimpleDutUserNew> distinctByUserIdNew(List<SimpleDutUserNew> dutUserNewList) {
        if (CollectionUtils.isEmpty(dutUserNewList)) {
            return dutUserNewList;
        }
        Set<Long> userIdSet = Sets.newHashSet();

        List<SimpleDutUserNew> resultDutUserList = Lists.newLinkedList();
        for (SimpleDutUserNew dutUserNew : dutUserNewList) {
            if (!userIdSet.contains(dutUserNew.getUserId())){
                resultDutUserList.add(dutUserNew);
                userIdSet.add(dutUserNew.getUserId());
            }
        }
        return resultDutUserList;
    }

}
