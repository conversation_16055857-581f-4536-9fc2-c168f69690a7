package com.qiyi.boss.utils;

import com.google.common.base.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

import com.qiyi.boss.Constants;
import com.qiyi.boss.param.QiyiParam;

/**
 * Created by IntelliJ IDEA.
 * User: lishunlong
 * Date: 11-2-7
 * Time: 上午11:23
 * 用于处理HTTP请求的工具类
 */
@Slf4j
public class RequestUtils {

    public static final String AUTH_COOKIE_KEY = "P00001";


    /**
     * 获取客户端IP地址，支持proxy
     * @param req HttpServletRequest
     * @return IP地址
     */
    public static String getRemoteAddr(HttpServletRequest req) {
        String ip = req.getHeader("X-Forwarded-For");
        if(StringUtils.isNotBlank(ip)){
            String[] ips = StringUtils.split(ip,',');
            if(ips!=null){
                for(String tmpip : ips){
                    if(StringUtils.isBlank(tmpip))
                        continue;
                    tmpip = tmpip.trim();
                    if(isIPAddr(tmpip) && !IpUtils.isInternalIp(tmpip)){
                        return tmpip.trim();
                    }
                }
            }
        }
        ip = req.getHeader("x-real-ip");
        if(isIPAddr(ip))
            return ip;
        ip = req.getRemoteAddr();
        if(ip.indexOf('.')==-1)
            ip = "127.0.0.1";
        return ip;
    }

    /**
     * 获取COOKIE的值
     * @param request HttpServletRequest
     * @param name Cookie的名称
     * @return CookieValue or null
     */
    public static String getCookieValue(HttpServletRequest request, String name) {
        Cookie[] cookies = request.getCookies();
        if(cookies == null) return null;
        for (Cookie ck : cookies) {
            if (StringUtils.equalsIgnoreCase(name,ck.getName()))
                return ck.getValue();
        }
        return null;
    }

    /**
     * 判断字符串是否是一个IP地址
     * @param addr 字符串
     * @return true:IP地址，false：非IP地址
     */
    public static boolean isIPAddr(String addr){
        if(StringUtils.isEmpty(addr))
            return false;
        String[] ips = StringUtils.split(addr, '.');
        if(ips.length != 4)
            return false;
        try{
            int ipa = Integer.parseInt(ips[0]);
            int ipb = Integer.parseInt(ips[1]);
            int ipc = Integer.parseInt(ips[2]);
            int ipd = Integer.parseInt(ips[3]);
            return ipa >= 0 && ipa <= 255 && ipb >= 0 && ipb <= 255 && ipc >= 0
                    && ipc <= 255 && ipd >= 0 && ipd <= 255;
        }catch(Exception e){}
        return false;
    }


    /**
     * 解析错误返回默认值 -1
     */
    public static Function<String, Integer> transToIntFunc = new Function<String, Integer>() {
        @Override
        public Integer apply(String input) {
            try {
                return Integer.parseInt(input.trim());
            } catch (Exception e) {
                return -1;
            }
        }
    };

    public static boolean hasAnyNullObject(Object... objects){
        for (Object obj : objects){
            if( obj ==null){
                return true;
            }
        }
        return false;
    }
    public static String getTicket(HttpServletRequest request) {
        //先从提交的参数中取P00001，如果没有则从Cookie中取
        String p00001 = request.getParameter(Constants.PASSPORT_USERID_COOKIE_KEY);
        if (p00001 == null || p00001.trim().length() == 0) {
            p00001 = RequestUtils.getCookieValue(request, AUTH_COOKIE_KEY);
        }
        return p00001;
    }

    public static boolean isBusinessFailed(ResponseEntity<Map<String, Object>> responseEntity) {
        Map<String, Object> body = responseEntity.getBody();
        if (MapUtils.isEmpty(body)) {
            return true;
        }
        String code = MapUtils.getString(body, "code", null);
        return StringUtils.isBlank(code) || !"A00000".equals(code) || MapUtils.getObject(body, "data", null) == null;
    }

    public static boolean isHttpFailed(ResponseEntity<Map<String, Object>> responseEntity) {
        return responseEntity == null || responseEntity.getStatusCode().value() != HttpStatus.OK.value();
    }

    public static boolean validateSign(Map<String, String> params, String key) {
        String sign = params.get("sign");
        params.remove("sign");
        String signKey = getKey(key);
        String mysign = getMySign(params, signKey);
        return sign.equals(mysign);
    }

    /**
     * 为测试录制回放做的需插桩方法
     * @param signKey
     */
    public static String getKey(String signKey){
        return signKey;
    }

    private static String getMySign(Map<String, String> params, String key) {
        //把数组所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串
        String prestr = PayUtils.createLinkString(params);
        //把拼接后的字符串再与安全校验码直接连接起来
        prestr = prestr + key;
        String sign = EncodeUtils.MD5(prestr, QiyiParam.CHARSET_UTF8);
        log.info("[result:{" + sign + "}][beforeSign:{" + prestr + "}]");
        return sign;
    }

    public static String getUserIp(HttpServletRequest servletRequest) {
        return com.qiyi.boss.utils.RequestUtils.getRemoteAddr(servletRequest);
    }

}