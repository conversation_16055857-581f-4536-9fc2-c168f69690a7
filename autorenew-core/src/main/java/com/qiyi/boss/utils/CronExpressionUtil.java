package com.qiyi.boss.utils;

import java.sql.Timestamp;

/**
 * Created by IntelliJ IDEA.
 * User: bfhong
 * Date: 12-4-10
 * Time: 下午7:15
 * To change this template use File | Settings | File Templates.
 */
public class CronExpressionUtil {

    public static Timestamp getOwnNextValidTime(String ownExpression, Timestamp timestamp) throws Exception {
        String[] expressions = ownExpression.split("\\*");
        if (expressions.length != 2) {
            throw new Exception("OwnExpression is Parse Error:" + ownExpression);
        }
        int offset = Integer.parseInt(expressions[1]);
        int period = Integer.parseInt(expressions[0]);
        Timestamp nexttime = DateHelper.caculateTime(timestamp, offset, period);
        return nexttime;
    }
}
