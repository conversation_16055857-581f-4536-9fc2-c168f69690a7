package com.qiyi.boss.utils;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

import com.qiyi.boss.model.PromotionResponse;
import com.iqiyi.kit.http.client.util.HttpClients;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2019-2-18
 * Time: 15:16
 */
@Component
@Slf4j
public class PromotionProcessApi {

	private static Logger logger = LoggerFactory.getLogger(PromotionProcessApi.class);

	private static String key = AppConfig.getProperty("promotion.process.key");
	private static String url = AppConfig.getProperty("promotion.process.url");

	@Resource(name = "promotionCloudClient")
	private RestTemplate promotionCloudClient;

	public boolean canBuy(Long userId) {
		try {
			Optional<PromotionResponse> promotionResponse = new PromotionProcessCommand(userId).execute();
			if (promotionResponse.isPresent()) {
				logger.info("Request url:<{}> for uid:{}, and the response code:{}, msg:{}, data:{}.",
						url, userId, promotionResponse.get().getCode(), promotionResponse.get().getMsg(), promotionResponse.get().getData());
				return promotionResponse.get().canBuy();
			}
		} catch (Exception e) {
			logger.error("[PromotionProcessApi] [canBuy] [uid:{}] [error:{}]", userId, e.getMessage());
		}

		return true;
	}

	private class PromotionProcessCommand extends SimpleHystrixCommand<Optional<PromotionResponse>> {

		private Long userId;

		PromotionProcessCommand(Long userId) {
			super("promotion", 10, 500);
			this.userId = userId;
		}

		@Override
		protected Optional<PromotionResponse> run() {
			return process(userId);
		}

		@Override
		protected Optional<PromotionResponse> getFallback() {
			return process(userId);
		}
	}

	private Optional<PromotionResponse> process(Long userId) {
		Map<String, Object> params = Maps.newHashMap();
		params.put("promotionCode", "student_month_card_count_24_limit");
		params.put("_channel", "vip-auto-renew");
		params.put("uid", String.valueOf(userId));
		String sign = EncodeUtils.MD5(SignatureUtil.createLinkString(params) + key, "UTF-8");
		params.put("sign", sign);

		logger.info("Request url:<{}> with params:{}.", url, params);

		try {
			return Optional.of(promotionCloudClient.getForObject(HttpClients.buildQueryUrl(url, params), PromotionResponse.class));
		} catch (RestClientException e) {
			logger.error("Request url:<{}> error, params:{}, error:{}", url,  params, e.getMessage());
			return Optional.empty();
		}
	}
}
