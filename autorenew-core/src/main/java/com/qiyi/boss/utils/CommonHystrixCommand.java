package com.qiyi.boss.utils;

import com.netflix.hystrix.*;
import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.function.Function;

@Slf4j
public class CommonHystrixCommand<P, R> extends HystrixCommand<R> {

    private static final int DEFAULT_THREAD_POOL_CORE_SIZE = 30;

    private String retryCommandKey;
    private P param;
    private Function<P, R> runFunc;
    private Function<P, R> fallbackFunc;
    private boolean needRetry;

    public CommonHystrixCommand(HystrixCommandPropsEnum hystrixProps, Function<P, R> runFunc, Function<P, R> fallbackFunc, P param) {
        this(hystrixProps, runFunc, fallbackFunc, param, false);
    }

    public CommonHystrixCommand(HystrixCommandPropsEnum hystrixProps, Function<P, R> runFunc, Function<P, R> fallbackFunc, P param, boolean needRetry) {
        this(hystrixProps.getGroupKey(),
                hystrixProps.getCommandKey(),
                runFunc,
                fallbackFunc,
                param,
                needRetry,
                DEFAULT_THREAD_POOL_CORE_SIZE,
                CloudConfigUtil.getHystrixTimeout(hystrixProps.getCommandKey())
        );
        this.retryCommandKey = hystrixProps.getRetryCommandKey();
    }

    public CommonHystrixCommand(HystrixCommandPropsEnum hystrixProps, Function<P, R> runFunc, Function<P, R> fallbackFunc, P param, int threadNumber, int timeoutInMillis) {
        this(hystrixProps, runFunc, fallbackFunc, param, threadNumber, timeoutInMillis, false);
    }

    public CommonHystrixCommand(HystrixCommandPropsEnum hystrixProps, Function<P, R> runFunc, Function<P, R> fallbackFunc, P param, int threadNumber, int timeoutInMillis, boolean needRetry) {
        this(hystrixProps.getGroupKey(),
            hystrixProps.getCommandKey(),
            runFunc,
            fallbackFunc,
            param,
            needRetry,
            threadNumber,
            timeoutInMillis
        );
        this.retryCommandKey = hystrixProps.getRetryCommandKey();
    }

    private CommonHystrixCommand(String groupKey, String commandKey, Function<P, R> runFunc, Function<P, R> fallbackFunc, P param, boolean needRetry, int threadNumber, int timeoutInMillis) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey(groupKey))
                .andCommandKey(HystrixCommandKey.Factory.asKey(commandKey))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionTimeoutInMilliseconds(timeoutInMillis)
                        .withCircuitBreakerRequestVolumeThreshold(20)
                        .withCircuitBreakerErrorThresholdPercentage(50)
                )
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withCoreSize(threadNumber)
                )
        );
        this.param = param;
        this.runFunc = runFunc;
        this.fallbackFunc = fallbackFunc;
        this.needRetry = needRetry;
    }

    @Override
    protected R run() throws Exception {
        return runFunc.apply(param);
    }

    @Override
    protected R getFallback() {
        log.error("执行Hystrix fallback逻辑, groupKey: {}, commandKey: {}, run cost: {}, executionTimeoutInMilliseconds: {}, param: {}",
                getCommandGroup().name(),
                getCommandKey().name(),
                getExecutionTimeInMilliseconds(),
                getProperties().executionTimeoutInMilliseconds().get(),
                JacksonUtils.toJsonString(param),
                getExecutionException());
        if (!needRetry || StringUtils.isBlank(retryCommandKey)) {
            return fallbackFunc.apply(param);
        }
        String groupKey = getCommandGroup().name();
        if (this.isCircuitBreakerOpen()) {
            log.error("CircuitBreaker Opened, groupKey: {}, commandKey: {}", groupKey, getCommandKey().name(), getExecutionException());
            return fallbackFunc.apply(param);
        }
        int retryTimeout = CloudConfigUtil.getHystrixTimeout(retryCommandKey);
        return CommonHystrixCommand.newRetryCommand(groupKey, retryCommandKey, runFunc, fallbackFunc, param, retryTimeout).execute();
    }

    private static <P, R> CommonHystrixCommand<P, R> newRetryCommand(String groupKey, String commandKey, Function<P, R> runFunc, Function<P, R> fallbackFunc, P param, int timeoutInMillis) {
        return new CommonHystrixCommand<>(groupKey,
                commandKey,
                runFunc,
                fallbackFunc,
                param,
                false,
                DEFAULT_THREAD_POOL_CORE_SIZE,
                timeoutInMillis
        );
    }
}
