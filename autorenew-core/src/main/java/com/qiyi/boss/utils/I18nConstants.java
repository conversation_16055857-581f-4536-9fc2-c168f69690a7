package com.qiyi.boss.utils;

import com.google.common.collect.Maps;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;

import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2019-5-5
 * Time: 18:02
 */
public class I18nConstants {

	/**
	 *  收银台购买自动续费商品时提示信息
	 */
	public static final String I18N_KEY_ONCE_AUTO_RENEW_TIP = "VIP_1567754558257_156";
	public static final String I18N_KEY_AUTO_RENEWING_TIP = "VIP_1567754558302_865";
	public static final String I18N_KEY_AUTO_RENEWING_TW_TIP = "VIP_1567754558215_126";


	/**
	 * 续费时长单位多语言key.
	 */
	public static final String I18N_KEY_MONTH = "VIP_1557974606839_685";
	public static final String I18N_KEY_WEEK = "VIP_1557974607057_82";
	public static final String I18N_KEY_DAY = "autorenew_length_day";
	public static final String I18N_KEY_SEASON = "VIP_1557974607106_744";
	public static final String I18N_KEY_YEAR = "VIP_1557974607150_238";

	/**
	 * Google/iOS自动续费管理页取消提示文案多语言key.
	 */
	public static final String I18N_KEY_APPLE_CANCEL_TIPS = "VIP_1557974607502_951";
	public static final String I18N_KEY_APPLE_CANCEL_METHOD = "VIP_1557975093631_483";
	public static final String I18N_KEY_APPLE_MOTHOD_TIPS = "VIP_1557974607535_160";
	public static final String I18N_KEY_GOOGLE_CANCEL_TIPS = "VIP_1557974607569_168";

	/**
	 * 续费时机提示文案多语言key.
	 */
	public static final String I18N_KEY_DUT_TIME_TIPS_IOS = "VIP_1557974607467_931";
	public static final String I18N_KEY_DUT_TIME_TIPS_GOOGLE = "VIP_1557974607467_931";
	public static final String I18N_KEY_DUT_TIME_TIPS_NORMAL = "VIP_1557974607430_482";

	/**
	 * 自动续费协议url多语言key
	 */
	public static final String I18N_KEY_CONTRACT_URL = "VIP_1557974607606_257";

	/**
	 * 支付渠道名称多语言key.
	 */
	public static final Map<Integer, String> PAY_CHANNEL_KEY_MAP = Maps.newHashMap();
	static {
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_ALIPAY, "VIP_1557974607395_201");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_IAP, "VIP_1557974607192_697");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_GOOGLE_BILLING, "VIP_1557974609313_526");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_PAYPAL, "VIP_1557974607316_420");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_CREDITCARD, "VIP_1557974607274_211");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_MASTERCARD, "VIP_1557974607357_417");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_GCASH, "VIP_1574911921879_203");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_SPGATEWAY, "VIP_1585565961267_347");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_FORTUMO, "VIP_1585932657191_342");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_DANA, "VIP_1589191613758_881");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_TRUEMONEY, "VIP_1591691652918_512");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_CODAPAY, "VIP_1591691619467_320");


		// 以下为待定
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_WECHAT, "i18n_pay_channel_key_wechat");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_BAIDUPAY, "i18n_pay_channel_key_baidupay");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_GASH, "i18n_pay_channel_key_gash");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_MOBILE, "i18n_pay_channel_key_mobile");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_QIDOU, "i18n_pay_channel_key_qidou");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_QIYI_WALLET, "i18n_pay_channel_key_qiyi_wallet");
		PAY_CHANNEL_KEY_MAP.put(PaymentDutType.PAY_CHANNEL_BANKCARD, "i18n_pay_channel_key_bankcard");
	}

	/**
	 * 将货币单位换算成为货币符号.
	 */
	public final static Map<String, String> CURRENCY_UNIT_MAP = Maps.newHashMap();

	static {
		CURRENCY_UNIT_MAP.put(null, "");
		CURRENCY_UNIT_MAP.put("CNY", "¥");
		CURRENCY_UNIT_MAP.put("TWD", "NT$");
		CURRENCY_UNIT_MAP.put("USD", "$");
		CURRENCY_UNIT_MAP.put("CAD", "C$");
		CURRENCY_UNIT_MAP.put("THB", "฿");
		CURRENCY_UNIT_MAP.put("PHP", "₱");
		CURRENCY_UNIT_MAP.put("MYR", "RM");
		CURRENCY_UNIT_MAP.put("LAK", "₭");
		CURRENCY_UNIT_MAP.put("IDR", "Rp");
		CURRENCY_UNIT_MAP.put("KHR", "៛");
		CURRENCY_UNIT_MAP.put("BND", "B$");
		CURRENCY_UNIT_MAP.put("VND", "₫");
		CURRENCY_UNIT_MAP.put("SGD", "S$");
		CURRENCY_UNIT_MAP.put("MMK", "MMK");
		CURRENCY_UNIT_MAP.put("MOP", "MOP$");
		CURRENCY_UNIT_MAP.put("HKD", "HK$");

		// 以下为待定
		CURRENCY_UNIT_MAP.put("PKR", "₨");
		CURRENCY_UNIT_MAP.put("BHD", "ب.د");
		CURRENCY_UNIT_MAP.put("ILS", "₪");
		CURRENCY_UNIT_MAP.put("TRY", "₤");
		CURRENCY_UNIT_MAP.put("INR", "₨");
		CURRENCY_UNIT_MAP.put("KPW", "₩");
		CURRENCY_UNIT_MAP.put("GEL", "ლ");
		CURRENCY_UNIT_MAP.put("KZT", "Т");
		CURRENCY_UNIT_MAP.put("KRW", "₩");
		CURRENCY_UNIT_MAP.put("QAR", "ر.ق");
		CURRENCY_UNIT_MAP.put("KWD", "د.ك");
		CURRENCY_UNIT_MAP.put("LBP", "ل.ل");
		CURRENCY_UNIT_MAP.put("MVR", "ރ.");
		CURRENCY_UNIT_MAP.put("BDT", "৳");
		CURRENCY_UNIT_MAP.put("MNT", "₮");
		CURRENCY_UNIT_MAP.put("AMD", "դր.");
		CURRENCY_UNIT_MAP.put("GEL", "ლ");
		CURRENCY_UNIT_MAP.put("RUB", "р.");
		CURRENCY_UNIT_MAP.put("NPR", "₨");
		CURRENCY_UNIT_MAP.put("JPY", "¥");
		CURRENCY_UNIT_MAP.put("SAR", "ر.س");
		CURRENCY_UNIT_MAP.put("LKR", "ரூ");
		CURRENCY_UNIT_MAP.put("TJS", "ЅМ");
		CURRENCY_UNIT_MAP.put("TMM", "m");
		CURRENCY_UNIT_MAP.put("SYP", "ل.س");
		CURRENCY_UNIT_MAP.put("AMD", "դր.");
		CURRENCY_UNIT_MAP.put("YER", "﷼");
		CURRENCY_UNIT_MAP.put("IQD", "ع.د");
		CURRENCY_UNIT_MAP.put("IRR", "﷼");
		CURRENCY_UNIT_MAP.put("ILS", "₪");
		CURRENCY_UNIT_MAP.put("INR", "₨");
		CURRENCY_UNIT_MAP.put("GBP", "£");
		CURRENCY_UNIT_MAP.put("JOD", "د.ا");
		CURRENCY_UNIT_MAP.put("GEL", "ლ");
		CURRENCY_UNIT_MAP.put("RUB", "р.");
		CURRENCY_UNIT_MAP.put("ALL", "L");
		CURRENCY_UNIT_MAP.put("EEK", "KR");
		CURRENCY_UNIT_MAP.put("BYR", "Br");
		CURRENCY_UNIT_MAP.put("EUR", "€");
		CURRENCY_UNIT_MAP.put("ISK", "kr");
		CURRENCY_UNIT_MAP.put("BAM", "КМ");
		CURRENCY_UNIT_MAP.put("BGN", "лв");
		CURRENCY_UNIT_MAP.put("PLN", "zł");
		CURRENCY_UNIT_MAP.put("DKK", "kr");
		CURRENCY_UNIT_MAP.put("RUB", "р.");
		CURRENCY_UNIT_MAP.put("DKK", "kr");
		CURRENCY_UNIT_MAP.put("CZK", "Kč");
		CURRENCY_UNIT_MAP.put("HRK", "kn");
		CURRENCY_UNIT_MAP.put("CSD", "ДИН");
		CURRENCY_UNIT_MAP.put("LVL", "Ls");
		CURRENCY_UNIT_MAP.put("CHF", "Fr");
		CURRENCY_UNIT_MAP.put("LTL", "Lt");
		CURRENCY_UNIT_MAP.put("MKD", "ден");
		CURRENCY_UNIT_MAP.put("MTL", "₤");
		CURRENCY_UNIT_MAP.put("MDL", "L");
		CURRENCY_UNIT_MAP.put("NOK", "kr");
		CURRENCY_UNIT_MAP.put("SEK", "kr");
		CURRENCY_UNIT_MAP.put("CHF", "Fr");
		CURRENCY_UNIT_MAP.put("CSD", "ДИН");
		CURRENCY_UNIT_MAP.put("SKK", "Sk");
		CURRENCY_UNIT_MAP.put("TRY", "₤");
		CURRENCY_UNIT_MAP.put("UAH", "₴");
		CURRENCY_UNIT_MAP.put("HUF", "Ft");
		CURRENCY_UNIT_MAP.put("GBP", "£");
		CURRENCY_UNIT_MAP.put("GIP", "£");
		CURRENCY_UNIT_MAP.put("DZD", "د.ج");
		CURRENCY_UNIT_MAP.put("EGP", "ج.م");
		CURRENCY_UNIT_MAP.put("AOA", "Kz");
		CURRENCY_UNIT_MAP.put("XOF", "Fr");
		CURRENCY_UNIT_MAP.put("BWP", "P");
		CURRENCY_UNIT_MAP.put("XOF", "Fr");
		CURRENCY_UNIT_MAP.put("BIF", "Fr");
		CURRENCY_UNIT_MAP.put("XAF", "Fr");
		CURRENCY_UNIT_MAP.put("XOF", "Fr");
		CURRENCY_UNIT_MAP.put("CVE", "$");
		CURRENCY_UNIT_MAP.put("GMD", "D");
		CURRENCY_UNIT_MAP.put("XAF", "Fr");
		CURRENCY_UNIT_MAP.put("CDF", "Fr");
		CURRENCY_UNIT_MAP.put("GHC", "₵");
		CURRENCY_UNIT_MAP.put("ZWD", "$");
		CURRENCY_UNIT_MAP.put("XAF", "Fr");
		CURRENCY_UNIT_MAP.put("DJF", "Fr");
		CURRENCY_UNIT_MAP.put("GNF", "Fr");
		CURRENCY_UNIT_MAP.put("XOF", "Fr");
		CURRENCY_UNIT_MAP.put("XAF", "Fr");
		CURRENCY_UNIT_MAP.put("KMF", "Fr");
		CURRENCY_UNIT_MAP.put("XOF", "Fr");
		CURRENCY_UNIT_MAP.put("KES", "Sh");
		CURRENCY_UNIT_MAP.put("LSL", "L");
		CURRENCY_UNIT_MAP.put("ZAR", "R");
		CURRENCY_UNIT_MAP.put("LRD", "$");
		CURRENCY_UNIT_MAP.put("LYD", "ل.د");
		CURRENCY_UNIT_MAP.put("RWF", "Fr");
		CURRENCY_UNIT_MAP.put("MWK", "MK");
		CURRENCY_UNIT_MAP.put("XOF", "Fr");
		CURRENCY_UNIT_MAP.put("MRO", "UM");
		CURRENCY_UNIT_MAP.put("MUR", "₨");
		CURRENCY_UNIT_MAP.put("MAD", "د.م.");
		CURRENCY_UNIT_MAP.put("MZN", "MTn");
		CURRENCY_UNIT_MAP.put("MZM", "MT");
		CURRENCY_UNIT_MAP.put("NAD", "$");
		CURRENCY_UNIT_MAP.put("ZAR", "R");
		CURRENCY_UNIT_MAP.put("ZAR", "R");
		CURRENCY_UNIT_MAP.put("XOF", "Fr");
		CURRENCY_UNIT_MAP.put("NGN", "₦");
		CURRENCY_UNIT_MAP.put("SHP", "£");
		CURRENCY_UNIT_MAP.put("STD", "Db");
		CURRENCY_UNIT_MAP.put("XOF", "Fr");
		CURRENCY_UNIT_MAP.put("SCR", "₨");
		CURRENCY_UNIT_MAP.put("SLL", "Le");
		CURRENCY_UNIT_MAP.put("SOS", "Sh");
		CURRENCY_UNIT_MAP.put("SZL", "L");
		CURRENCY_UNIT_MAP.put("TZS", "Sh");
		CURRENCY_UNIT_MAP.put("TND", "د.ت");
		CURRENCY_UNIT_MAP.put("UGX", "Sh");
		CURRENCY_UNIT_MAP.put("MAD", "د.م.");
		CURRENCY_UNIT_MAP.put("ZMK", "ZK");
		CURRENCY_UNIT_MAP.put("XAF", "Fr");
		CURRENCY_UNIT_MAP.put("XCD", "$");
		CURRENCY_UNIT_MAP.put("BSD", "$");
		CURRENCY_UNIT_MAP.put("BBD", "$");
		CURRENCY_UNIT_MAP.put("BZD", "$");
		CURRENCY_UNIT_MAP.put("BMD", "$");
		CURRENCY_UNIT_MAP.put("PAB", "B/.");
		CURRENCY_UNIT_MAP.put("XCD", "$");
		CURRENCY_UNIT_MAP.put("DOP", "$");
		CURRENCY_UNIT_MAP.put("CRC", "₡");
		CURRENCY_UNIT_MAP.put("CUC", "$");
		CURRENCY_UNIT_MAP.put("CUP", "$");
		CURRENCY_UNIT_MAP.put("XCD", "$");
		CURRENCY_UNIT_MAP.put("HTG", "G");
		CURRENCY_UNIT_MAP.put("HNL", "L");
		CURRENCY_UNIT_MAP.put("KYD", "$");
		CURRENCY_UNIT_MAP.put("SVC", "₡");
		CURRENCY_UNIT_MAP.put("MXN", "$");
		CURRENCY_UNIT_MAP.put("XCD", "$");
		CURRENCY_UNIT_MAP.put("NIO", "C$");
		CURRENCY_UNIT_MAP.put("XCD", "$");
		CURRENCY_UNIT_MAP.put("TTD", "$");
		CURRENCY_UNIT_MAP.put("GTQ", "Q");
		CURRENCY_UNIT_MAP.put("JMD", "$");
		CURRENCY_UNIT_MAP.put("ARS", "$");
		CURRENCY_UNIT_MAP.put("AWG", "ƒ");
		CURRENCY_UNIT_MAP.put("BOB", "Bs.");
		CURRENCY_UNIT_MAP.put("BRL", "R$");
		CURRENCY_UNIT_MAP.put("PYG", "₲");
		CURRENCY_UNIT_MAP.put("PEN", "S/.");
		CURRENCY_UNIT_MAP.put("COP", "$");
		CURRENCY_UNIT_MAP.put("GYD", "$");
		CURRENCY_UNIT_MAP.put("FKP", "£");
		CURRENCY_UNIT_MAP.put("SRD", "$");
		CURRENCY_UNIT_MAP.put("VEB", "Bs");
		CURRENCY_UNIT_MAP.put("UYU", "$");
		CURRENCY_UNIT_MAP.put("CLP", "$");
		CURRENCY_UNIT_MAP.put("AUD", "$");
		CURRENCY_UNIT_MAP.put("PGK", "K");
		CURRENCY_UNIT_MAP.put("FJD", "$");
		CURRENCY_UNIT_MAP.put("XPF", "Fr");
		CURRENCY_UNIT_MAP.put("ANG", "ƒ");
		CURRENCY_UNIT_MAP.put("AUD", "$");
		CURRENCY_UNIT_MAP.put("NZD", "$");
		CURRENCY_UNIT_MAP.put("GBP", "£");
		CURRENCY_UNIT_MAP.put("AUD", "$");
		CURRENCY_UNIT_MAP.put("NZD", "$");
		CURRENCY_UNIT_MAP.put("WST", "T");
		CURRENCY_UNIT_MAP.put("SBD", "$");
		CURRENCY_UNIT_MAP.put("TOP", "T$");
		CURRENCY_UNIT_MAP.put("SHP", "£");
		CURRENCY_UNIT_MAP.put("AUD", "$");
		CURRENCY_UNIT_MAP.put("VUV", "Vt");
		CURRENCY_UNIT_MAP.put("XPF", "Fr");
	}
}

