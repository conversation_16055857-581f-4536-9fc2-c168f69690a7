package com.qiyi.boss.utils;

import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;

/**
 * <AUTHOR>
 * @param <R>
 */
public abstract class SimpleHystrixCommand<R> extends HystrixCommand<R> {

    private static final int DEFAULT_THREAD_POOL_CORE_SIZE = 30;

	public SimpleHystrixCommand(String groupName, int threadNumber, int timeoutInMillis) {
		super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey(groupName))
				.andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
						.withCoreSize(threadNumber)
				)
				.andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
						.withExecutionTimeoutInMilliseconds(timeoutInMillis)
				)
		);
	}

    public SimpleHystrixCommand(String groupName, String commandName, int timeoutInMillis) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey(groupName))
            .andCommandKey(HystrixCommandKey.Factory.asKey(commandName))
            .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                .withCoreSize(DEFAULT_THREAD_POOL_CORE_SIZE)
            )
            .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                .withExecutionTimeoutInMilliseconds(timeoutInMillis)
            )
        );
    }

    public SimpleHystrixCommand(String groupName, String commandName, int threadNumber, int timeoutInMillis) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey(groupName))
            .andCommandKey(HystrixCommandKey.Factory.asKey(commandName))
            .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                .withCoreSize(threadNumber)
            )
            .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                .withExecutionTimeoutInMilliseconds(timeoutInMillis)
            )
        );
    }

}