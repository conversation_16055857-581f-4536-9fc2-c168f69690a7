package com.qiyi.boss.utils;


import com.qiyi.boss.service.impl.FacadeManager;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 * <AUTHOR> lishunlong
 * Date: 11-2-9
 * Time: 上午11:03
 * To change this template use File | Settings | File Templates.
 */
@Component
@Lazy(value = false)
public class ApplicationContextUtil implements ApplicationContextAware {
    private static ApplicationContext context = null;

    private ApplicationContextUtil() {
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    public static ApplicationContextUtil getInstance() {
        return (ApplicationContextUtil) context.getBean("applicationContextUtil");
    }


    public FacadeManager getFacadeManager() {
        return (FacadeManager) context.getBean("facadeManager");
    }

    public static Object getBean(String name) {
        return context.getBean(name);
    }

    public static <T> T getBean(Class<T> classz) {
        return context.getBean(classz);
    }

    public static <T> T getBean(String name, Class<T> classz) {
        return context.getBean(name, classz);
    }

    public static <T> List<T> getBeansOfType(Class<T> classz) {
        Map<String, T> beansOfType = context.getBeansOfType(classz);
        return MapUtils.isNotEmpty(beansOfType) ? new ArrayList<>(beansOfType.values()) : null;
    }

    public static ApplicationContext getApplicationContext() {
        return context;
    }
}
