package com.qiyi.boss.component;

import com.qiyi.boss.dto.AgreementOptDto;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.enums.PricingStrategyEnum;
import com.qiyi.boss.model.AccountChangeMsg;
import com.qiyi.boss.model.RenewPriceDto;
import com.qiyi.boss.outerinvoke.result.PayCenterAppleDutInfoResult;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * Created at: 2022-06-01
 *
 * <AUTHOR>
 */
public interface AgreementHandler {

    AgreementTypeEnum getAgreementType();

    /**
     * 获取代扣金额
     */
    RenewPriceDto getRenewPrice(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, PricingStrategyEnum pricingStrategy);

    /**
     * 纯签约之后执行某些动作
     * @param dutUserNew
     */
    void doSomethingAfterPureSign(DutUserNew dutUserNew);

    /**
     * 签约支付方式开通自动续费
     * @param autorenewRequest
     * @param agreementNoInfo
     * @param agreementTemplate
     */
    DutUserNew openBySignPay(AutorenewRequest autorenewRequest, AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate);

    void unbindAccount(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo);

    /**
     * 用户主动取消协议
     * @param dutUserNew
     * @param agreementNoInfo
     * @param agreementOptDto
     */
    void cancelAgreement(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, AgreementOptDto agreementOptDto);

    /**
     * 协议期满
     * @param dutUserNew
     * @param agreementNoInfo
     * @param operateType
     */
    void doSomethingWhenAgreementExpiration(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, OperateTypeEnum operateType);

    /**
     * 代扣后执行某些动作
     */
    DutRenewLog doSomethingAfterDut(AutorenewRequest autorenewRequest, AgreementTemplate agreementTemplate);

    /**
     * 账户中心用户绑定关系变更
     * @param accountChangeMsg
     * @param autoRenewDutType
     */
    void consumeAccountChangeMsg(AccountChangeMsg accountChangeMsg, AutoRenewDutType autoRenewDutType);

    void resetOpenedNextDutTime(AutorenewRequest autorenewRequest, DutUserNew openedDutUser);

    void setOpeningNextDutTime(AutorenewRequest autorenewRequest, DutUserNew openingDutUser, DutUserNew needUpdateDutUserNew, AgreementTemplate agreementTemplate, PayCenterAppleDutInfoResult appleSignInfo);

}
