package com.qiyi.boss.component;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import com.qiyi.boss.enums.AgreementTypeEnum;

/**
 * Created at: 2022-06-01
 *
 * <AUTHOR>
 */
@Slf4j
@Lazy(value = false)
@Component
public class AgreementHandlerFactory implements InitializingBean, ApplicationContextAware {

    private ApplicationContext applicationContext;

    private static final Map<AgreementTypeEnum, AgreementHandler> agreementTypeToHandlerMap = Maps.newEnumMap(AgreementTypeEnum.class);

    @Resource
    private AutoRenewHandler autoRenewHandler;

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("AgreementHandlerFactory-init start!");
        applicationContext.getBeansOfType(AgreementHandler.class).values()
            .forEach(handler -> {
                log.info("AgreementHandlerFactory-init:register handler {}  for {} agreement",
                    handler.getClass().getSimpleName(), handler.getAgreementType());
                agreementTypeToHandlerMap.put(handler.getAgreementType(), handler);
            });
        log.info("AgreementHandlerFactory-init end!");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public AgreementHandler getHandler(AgreementTypeEnum agreementType) {
        if (agreementType == null) {
            return autoRenewHandler;
        }
        AgreementHandler agreementHandler = agreementTypeToHandlerMap.get(agreementType);
        return agreementHandler != null ? agreementHandler : autoRenewHandler;
    }

}
