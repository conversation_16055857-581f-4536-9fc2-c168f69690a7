package com.qiyi.boss.component;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

import com.qiyi.boss.async.queue.AsyncTaskFactory;
import com.qiyi.boss.async.task.AgreementRenewTask;
import com.qiyi.boss.dto.AgreementOptDto;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.model.UserAgreementContext;
import com.qiyi.boss.outerinvoke.result.PayCenterAppleDutInfoResult;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.AsyncTask;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.SimpleDutUserNew;

/**
 * Created at: 2022-06-01
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ZhiMaGoHandler extends AbstractAgreementHandler {

    @Override
    public AgreementTypeEnum getAgreementType() {
        return AgreementTypeEnum.ZHIMA_GO;
    }

    @Override
    public void doSomethingAfterPureSign(DutUserNew dutUserNew) {
        //签约之后立即发起代扣
        SimpleDutUserNew simpleDutUserNew = SimpleDutUserNew.buildFromDutUserNew(dutUserNew);
        AgreementRenewTask task = AgreementRenewTask.buildFromDutUserNew(simpleDutUserNew);
        AsyncTaskFactory.getInstance().insertIntoDB(task, 0, AsyncTask.Task_PRIORITY_1, DateHelper.getDateTime());
    }

    @Override
    public DutUserNew openBySignPay(AutorenewRequest autorenewRequest, AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate) {
        Timestamp nextDutTime = agreementTemplate.calcZhiMaGoNextDutTimeAfterSignPay(autorenewRequest.getPayTime());
        nextDutTime = agreementTemplate.getPeriods() > 1 ? nextDutTime : null;
        autorenewRequest.setExpireTime(nextDutTime);
        UserAgreementContext userAgreementContext = new UserAgreementContext(agreementNoInfo, agreementTemplate);
        return userAgreementService.openBySignPay(autorenewRequest, userAgreementContext);
    }

    @Override
    public void cancelAgreement(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, AgreementOptDto agreementOptDto) {
        unbindAccount(dutUserNew, agreementNoInfo);
    }

    @Override
    public void doSomethingWhenAgreementExpiration(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, OperateTypeEnum operateType) {
        //无需处理，由支付宝侧发起结算流程
    }

    @Override
    public DutRenewLog doSomethingAfterDut(AutorenewRequest autorenewRequest, AgreementTemplate agreementTemplate) {
        DutRenewLog dutRenewLog = super.doSomethingAfterDut(autorenewRequest, agreementTemplate);
        if (dutRenewLog == null || !dutRenewLog.isUpdated()) {
            return dutRenewLog;
        }
        DutUserNew dutUserNew = userAgreementMapper.selectBySignKey(dutRenewLog.getUserId(), dutRenewLog.getSignKey());
        userAgreementService.incrementRenewCount(dutUserNew.getId(), dutUserNew.getUserId());
        return dutRenewLog;
    }

    @Override
    public void resetOpenedNextDutTime(AutorenewRequest autorenewRequest, DutUserNew openedDutUser) {
    }

    @Override
    public void setOpeningNextDutTime(AutorenewRequest autorenewRequest, DutUserNew openingDutUser, DutUserNew needUpdateDutUserNew, AgreementTemplate agreementTemplate, PayCenterAppleDutInfoResult appleSignInfo) {
    }
}
