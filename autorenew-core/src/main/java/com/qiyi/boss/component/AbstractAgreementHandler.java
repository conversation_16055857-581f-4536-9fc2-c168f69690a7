package com.qiyi.boss.component;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import com.qiyi.boss.dto.AccountUnbindReqDto;
import com.qiyi.boss.enums.PricingStrategyEnum;
import com.qiyi.boss.exception.BizException;
import com.qiyi.boss.model.AccountChangeMsg;
import com.qiyi.boss.model.BaseResponse.CodeEnum;
import com.qiyi.boss.model.RenewPriceDto;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.utils.DutAccountApi;
import com.qiyi.vip.trade.autorenew.dao.UserAgreementMapper;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * Created at: 2022-06-02
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractAgreementHandler implements AgreementHandler {

    @Resource
    RenewPriceCalculatorFactory renewPriceCalculatorFactory;
    @Resource
    protected DutAccountApi dutAccountApi;
    @Resource
    UserAgreementMapper userAgreementMapper;
    @Resource
    UserAgreementService userAgreementService;

    @Override
    public RenewPriceDto getRenewPrice(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, PricingStrategyEnum pricingStrategy) {
        AbstractPricingStrategy strategy = renewPriceCalculatorFactory.getStrategy(pricingStrategy);
        return strategy.buildRenewPrice(dutUserNew, agreementNoInfo.getTemplateCode());
    }

    @Override
    public void unbindAccount(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo) {
        AccountUnbindReqDto accountUnbindReqDto = new AccountUnbindReqDto(dutUserNew.getUserId(), agreementNoInfo.getDutType());
        boolean unbindSuccess = dutAccountApi.unbindAccount(accountUnbindReqDto);
        if (!unbindSuccess) {
            throw BizException.newException(CodeEnum.ACCOUNT_UNBIND_EXCEPTION);
        }
    }

    @Override
    public DutRenewLog doSomethingAfterDut(AutorenewRequest autorenewRequest, AgreementTemplate agreementTemplate) {
        Long userId = autorenewRequest.getUserId();
        String orderCode = autorenewRequest.getOrderCode();
        return userAgreementService.updateDutUserNewAndLog(userId, orderCode);
    }

    @Override
    public void consumeAccountChangeMsg(AccountChangeMsg accountChangeMsg, AutoRenewDutType autoRenewDutType) {

    }

}
