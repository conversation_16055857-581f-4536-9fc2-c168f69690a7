package com.qiyi.boss.component;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.qiyi.boss.Constants;
import com.qiyi.boss.autorenew.dto.DutSuccessMsgReqDto;
import com.qiyi.boss.autorenew.enumerate.ActTypeEnum;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.dto.AgreementOptDto;
import com.qiyi.boss.dto.FamilyBindRelation;
import com.qiyi.boss.enums.AgreementStatusEnum;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.enums.PricingStrategyEnum;
import com.qiyi.boss.model.RenewPriceDto;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.outerinvoke.CustomVipInfoClient;
import com.qiyi.boss.outerinvoke.VipChargeApi;
import com.qiyi.boss.outerinvoke.result.MultiVipInfoResult;
import com.qiyi.boss.outerinvoke.result.PayCenterAppleDutInfoResult;
import com.qiyi.boss.outerinvoke.result.UsersVipVipInfoResult;
import com.qiyi.boss.service.AutoRenewService;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.boss.service.PayCenterAppleSignManager;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.AgreementTemplateManager;
import com.qiyi.boss.service.impl.AutoRenewUpgradeConfigManager;
import com.qiyi.boss.service.impl.DutPriceActServiceImpl;
import com.qiyi.boss.service.impl.UserAgreementLogManager;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.JacksonUtils;
import com.qiyi.boss.utils.SignKeyGenerator;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewUpgradeConfig;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew.ExtInfo;
import com.qiyi.vip.trade.autorenew.repository.DutUserRepository;

/**
 * Created at: 2022-06-01
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AutoRenewHandler extends AbstractAgreementHandler {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Resource
    AgreementNoInfoManager agreementNoInfoManager;
    @Resource
    AutoRenewUpgradeConfigManager autoRenewUpgradeConfigManager;
    @Resource
    DutUserRepository dutUserRepository;
    @Resource
    AutoRenewService autoRenewService;
    @Resource
    DutPriceActServiceImpl dutPriceActService;
    @Resource
    PayCenterAppleSignManager payCenterAppleSignManager;
    @Resource
    UserAgreementLogManager userAgreementLogManager;
    @Resource
    private AgreementTemplateManager agreementTemplateManager;
    @Resource
    private RenewPriceCalculatorFactory renewPriceCalculatorFactory;
    @Resource
    private VipChargeApi vipChargeApi;
    @Resource
    private CustomVipInfoClient customVipInfoClient;

    @Override
    public AgreementTypeEnum getAgreementType() {
        return AgreementTypeEnum.AUTO_RENEW;
    }

    @Override
    public RenewPriceDto getRenewPrice(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, PricingStrategyEnum pricingStrategy) {
        AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
        //非正式协议只能签约支付购买，使用次数需算上首笔签约支付的
        if (agreementNoInfo.notDefaultNo()) {
            Integer successDutLogCount = userAgreementLogManager.getSuccessDutLogCount(dutUserNew, agreementNoInfo, agreementTemplate);
            if (successDutLogCount == null) {
                if (agreementNoInfo.thirdDut()) {
                    successDutLogCount = dutUserNew.getSerialRenewCount();
                } else {
                    successDutLogCount = userAgreementLogManager.successDutLogCount(dutUserNew.getUserId(), agreementNoInfo.getTemplateCode(), null);
                }
            }
            AbstractPricingStrategy priceCalculatorStrategy = renewPriceCalculatorFactory.getStrategy(pricingStrategy);
            RenewPriceDto renewPriceDto = priceCalculatorStrategy.getDiscountPrice(agreementNoInfo.getTemplateCode(), successDutLogCount);
            //兼容逻辑，价格从协议迁移到协议模版
            if (renewPriceDto == null) {
                renewPriceDto = priceCalculatorStrategy.getDiscountPrice(dutUserNew.getAgreementNo(), successDutLogCount);
            }
            return renewPriceDto;
        }
        return super.getRenewPrice(dutUserNew, agreementNoInfo, pricingStrategy);
    }

    @Override
    public void doSomethingAfterPureSign(DutUserNew dutUserNew) { }

    @Override
    public DutUserNew openBySignPay(AutorenewRequest autorenewRequest, AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate) {
        return autoRenewService.openBySignPay(autorenewRequest, agreementNoInfo, agreementTemplate);
    }

    @Override
    public void cancelAgreement(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, AgreementOptDto agreementOptDto) {
        if (!agreementNoInfo.supportUnBindWhenCancelAutoRenew()) {
            return;
        }
        unbindAccount(dutUserNew, agreementNoInfo);
    }

    @Override
    public void doSomethingWhenAgreementExpiration(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, OperateTypeEnum operateType) { }

    @Override
    public DutRenewLog doSomethingAfterDut(AutorenewRequest autorenewRequest, AgreementTemplate agreementTemplate) {
        Long userId = autorenewRequest.getUserId();
        Long vipType = autorenewRequest.getVipType();
        Integer agreementNo = autorenewRequest.getDutType();
        AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
        Integer dutType = agreementNoInfo.getDutType();
        Short priority = agreementNoInfo.getPriority();
        dealUpgrade(agreementNoInfo, autorenewRequest, priority);
        DutUserNew dutUserNew = userAgreementService.getByAgreementNoOrDutType(userId, agreementNo, dutType, vipType);
        if (dutUserNew == null && !autorenewRequest.isAppleOrder()) {
            log.error("找不到对应的签约记录! AutoRenewRequest={}", autorenewRequest);
            return null;
        }

        PayCenterAppleDutInfoResult appleSignInfo = null;
        if (autorenewRequest.isAppleOrder()) {
            appleSignInfo = payCenterAppleSignManager.getSpecifiedSignInfo(userId, autorenewRequest.getActCode());
        }
        //苹果签约关系为空时，保存签约关系
        Integer agreementType = autorenewRequest.getAgreementType();
        if (dutUserNew == null) {
            if (Constants.QIYUE_ORDER_AUTORENEW_DUT.equals(autorenewRequest.getAutoRenew())) {
                autorenewRequest.setAutoRenew(Constants.QIYUE_ORDER_AUTORENEW_FIRST_LARGE);
            }
            List<DutUserNew> openedDutUsers = userAgreementService.getByAgreementTypeAndVipType(userId, AgreementTypeEnum.valueOf(agreementType), vipType, AgreementStatusEnum.VALID);
            autoRenewService.doRepeatOpening(autorenewRequest.generateVipUser(), agreementNoInfo, autorenewRequest.getOpeningAmount(),
                openedDutUsers, autorenewRequest.getFc(), autorenewRequest.getFv(), autorenewRequest);
            dutUserNew = autoRenewService.openByAutoRenewRequest(agreementNoInfo, agreementTemplate, autorenewRequest,
                OperateSceneEnum.OPEN_APPLE_REOPEN, appleSignInfo);
        } else {
            dealSameVipAndAgreementType(autorenewRequest);
            updateDutUserByAutoRenewRequest(dutUserNew, autorenewRequest, agreementTemplate, agreementNoInfo, appleSignInfo);
        }
        DutRenewLog dutRenewLog = null;
        // 苹果IAP和PayPal 由苹果和PayPal扣费,无需更新续费日志
        if (!agreementNoInfo.isAppleChannel() && !agreementNoInfo.isPayPalChannel()) {
            dutRenewLog = userAgreementService.updateDutUserNewAndLog(userId, autorenewRequest.getOrderCode());
        }
        userAgreementService.incrementRenewCount(dutUserNew.getId(), dutUserNew.getUserId());
        DutSuccessMsgReqDto dutSuccessMsgReqDto = DutSuccessMsgReqDto.buildFromAutoRenewRequest(autorenewRequest);
        dutSuccessMsgReqDto.setDutType(agreementNoInfo.getDutType());
        dutPriceActService.changeVipSubscriptionActStatus(dutSuccessMsgReqDto);
        return dutRenewLog;
    }

    private void dealSameVipAndAgreementType(AutorenewRequest autorenewRequest) {
        Integer agreementType = autorenewRequest.getAgreementType();
        if (!AgreementTypeEnum.jointType(agreementType)) {
            log.info("no need setNextDutTime, autorenewRequest:{}", autorenewRequest);
            return;
        }
        Long userId = autorenewRequest.getUserId();
        Long vipType = autorenewRequest.getVipType();
        Integer dutType = autorenewRequest.getDutType();
        List<DutUserNew> openedNoAppleDutUsers = userAgreementService.getByAgreementTypeAndVipType(userId, AgreementTypeEnum.valueOf(agreementType), vipType, AgreementStatusEnum.VALID)
            .stream()
            .filter(d -> !ObjectUtils.equals(d.getAgreementNo(), dutType))
            .filter(d -> agreementNoInfoManager.getById(d.getAgreementNo()) != null && !agreementNoInfoManager.getById(d.getAgreementNo()).isAppleChannel())
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(openedNoAppleDutUsers)) {
            return;
        }
        for (DutUserNew dutUserNew : openedNoAppleDutUsers) {
            resetOpenedNextDutTime(autorenewRequest, dutUserNew);
        }
    }

    @Override
    public void resetOpenedNextDutTime(AutorenewRequest autorenewRequest, DutUserNew openedDutUser) {
        Integer agreementType = openedDutUser.getAgreementType();
        if (!AgreementTypeEnum.jointType(agreementType)) {
            log.info("openedDutUser: {} no need setNextDutTime, autorenewRequest :{}", openedDutUser.getUserId(), autorenewRequest);
            return;
        }
        Integer agreementNo = openedDutUser.getAgreementNo();
        AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
        if (agreementNoInfo == null || agreementNoInfo.isAppleChannel()) {
            return;
        }
        String orderCode = autorenewRequest.getOrderCode();
        AgreementTemplate openAgreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
        ExtInfo extInfo = openedDutUser.parseExt();
        // 此处需要对订单进行幂等
        if (extInfo == null || validOrderCode(extInfo, orderCode)) {
            Timestamp nextDutTime = openedDutUser.getNextDutTime();
            if (nextDutTime == null || nextDutTime.before(autorenewRequest.getPayTime())) {
                // 其他签约关系没有更新
                nextDutTime = autorenewRequest.getPayTime();
            }
            // 基于首次签约或代扣计算下次代扣时间
            Timestamp endTime = openAgreementTemplate.calcNextEndTimeNew(autorenewRequest, nextDutTime);
            openedDutUser.setNextDutTime(endTime);
            openedDutUser.setExtLastOrderCode(orderCode);
            setNoAppleMaxDutTime(autorenewRequest, endTime);
            userAgreementService.updateNextDutTimeAndExt(openedDutUser);
        }
    }

    @Override
    public void setOpeningNextDutTime(AutorenewRequest autorenewRequest, DutUserNew openingDutUser, DutUserNew needUpdateDutUserNew, AgreementTemplate openingAgreementTemplate, PayCenterAppleDutInfoResult appleSignInfo) {
        Integer agreementType = autorenewRequest.getAgreementType();
        if (!AgreementTypeEnum.jointType(agreementType) && !AgreementTypeEnum.familyType(agreementType)) {
            log.info("no need setOpeningNextDutTime");
            return;
        }
        if (AgreementTypeEnum.jointType(agreementType)) {
            setJointTypeNextDutTime(autorenewRequest, openingDutUser, needUpdateDutUserNew, openingAgreementTemplate, appleSignInfo);
            return;
        }
        if (AgreementTypeEnum.familyType(agreementType)) {
            setFamilyTypeNextDutTime(autorenewRequest, openingDutUser, appleSignInfo);
        }
    }

    private void setFamilyTypeNextDutTime(AutorenewRequest autorenewRequest, DutUserNew openingDutUser, PayCenterAppleDutInfoResult appleSignInfo) {
        String orderCode = autorenewRequest.getOrderCode();
        if (!autorenewRequest.isAppleOrder() && autorenewRequest.isDutRequest()) {
            log.info("not set familyTypeNextDutTime for non apple dutOrder:{}", orderCode);
            return;
        }
        ExtInfo extInfo = openingDutUser.parseExt();
        Long userId = autorenewRequest.getUserId();
        Long vipType = autorenewRequest.getVipType();
        // 幂等
        if (extInfo != null && orderCode.equals(extInfo.getLastOrderCode())) {
            log.warn("sign order is duplicate, orderCode:{}, openingDutUser:{}", orderCode, openingDutUser);
            return;
        }
        FamilyBindRelation bindRelation = vipChargeApi.queryBindRelationBySourceUid(userId);
        Timestamp expireTime = autorenewRequest.getExpireTime();
        boolean appleOrder = autorenewRequest.isAppleOrder();
        if (appleOrder && expireTime != null) {
            // 苹果签约关系无需关注被绑定用户到期时间
            openingDutUser.setBindUidInfo(orderCode, bindRelation, null);
            openingDutUser.setNextDutTime(expireTime);
            return;
        }
        if (appleOrder && appleSignInfo != null) {
            // 苹果签约关系无需关注被绑定用户到期时间
            openingDutUser.setBindUidInfo(orderCode, bindRelation, null);
            openingDutUser.setNextDutTime(appleSignInfo.getExpiresDate());
        }

        Long targetUid = null;
        String sourceUidDeadline = null;
        String targetUidDeadline = null;

        if (bindRelation != null && bindRelation.getTargetUid() != null) {
            targetUid = bindRelation.getTargetUid();
            List<MultiVipInfoResult> multiVipInfoResults = customVipInfoClient.batchQueryUserVipInfo(Lists.newArrayList(userId, targetUid), Lists.newArrayList(vipType), null);
            sourceUidDeadline = extractUidDeadline(multiVipInfoResults, userId, vipType);
            targetUidDeadline = extractUidDeadline(multiVipInfoResults, targetUid, vipType);
            log.info("bindRelation is not null, sourceUid:{}, sourceUidDeadline:{}, targetUid:{}, targetUidDeadline:{}", userId, sourceUidDeadline, targetUid, targetUidDeadline);
        } else {
            VipUser vipInfo = customVipInfoClient.getVipInfoNew(userId, vipType);
            if (vipInfo != null && vipInfo.getDeadline() != null) {
                sourceUidDeadline = DATE_TIME_FORMATTER.format(vipInfo.getDeadline().toLocalDateTime());
                log.info("bindRelation is null, sourceUid:{}, sourceUidDeadline:{}", userId, sourceUidDeadline);
            }
        }
        if (sourceUidDeadline == null && targetUidDeadline == null) {
            log.info("sourceUidDeadline and targetUidDeadline are both null, sourceUid:{}, targetUid:{}", userId, targetUid);
            openingDutUser.setBindUidInfo(orderCode, bindRelation, targetUidDeadline);
            return;
        }
        Timestamp sourceUidTimestamp = DateHelper.getTimestamp(sourceUidDeadline);
        Timestamp targetUidTimestamp = DateHelper.getTimestamp(targetUidDeadline);

        Timestamp smallerDeadlineNonNullValue = FamilyBindRelation.getSmallerDeadlineNonNullValue(sourceUidTimestamp != null ? sourceUidTimestamp.getTime() : null, targetUidTimestamp != null ? targetUidTimestamp.getTime() : null);
        log.info("smallerDeadlineNonNullValue:{}, sourceUidTimestamp:{}, targetUidTimestamp:{}", smallerDeadlineNonNullValue, sourceUidTimestamp, targetUidTimestamp);
        openingDutUser.setNextDutTime(smallerDeadlineNonNullValue);
        openingDutUser.setBindUidInfo(orderCode, bindRelation, targetUidDeadline);
    }



    private static String extractUidDeadline(List<MultiVipInfoResult> multiVipInfoResults, Long userId, Long vipType) {
        if (CollectionUtils.isEmpty(multiVipInfoResults)) {
            return null;
        }
        return multiVipInfoResults.stream()
            .filter(m -> ObjectUtils.equals(m.getUid(), userId))
            .flatMap(m -> Optional.ofNullable(m.getDetail()).map(List::stream).orElseGet(Stream::empty))
            .filter(m -> ObjectUtils.equals(vipType, m.getVipType()))
            .findFirst()
            .map(UsersVipVipInfoResult::getDeadline)
            .orElse(null);
    }

    private static void setJointTypeNextDutTime(AutorenewRequest autorenewRequest, DutUserNew openingDutUser, DutUserNew needUpdateDutUserNew, AgreementTemplate openingAgreementTemplate, PayCenterAppleDutInfoResult appleSignInfo) {
        ExtInfo extInfo = openingDutUser.parseExt();
        String orderCode = autorenewRequest.getOrderCode();
        if (extInfo != null && orderCode.equals(extInfo.getLastOrderCode())) {
            log.warn("sign order is duplicate");
            return;
        }
        Timestamp expireTime = autorenewRequest.getExpireTime();
        if (autorenewRequest.isAppleOrder() && expireTime != null) {
            openingDutUser.setExtLastOrderCode(orderCode);
            openingDutUser.setNextDutTime(expireTime);
            if (needUpdateDutUserNew != null) {
                needUpdateDutUserNew.setExtLastOrderCode(orderCode);
                needUpdateDutUserNew.setNextDutTime(expireTime);
            }
            return;
        }

        if (appleSignInfo != null) {
            openingDutUser.setExtLastOrderCode(orderCode);
            openingDutUser.setNextDutTime(appleSignInfo.getExpiresDate());
            if (needUpdateDutUserNew != null) {
                needUpdateDutUserNew.setExtLastOrderCode(orderCode);
                needUpdateDutUserNew.setNextDutTime(appleSignInfo.getExpiresDate());
            }
            return;
        }

        Timestamp payTime = autorenewRequest.getPayTime();
        Timestamp startTime = null;
        Timestamp nextDutTime = openingDutUser.getNextDutTime();
        Timestamp noAppleMaxNextDutTime = autorenewRequest.getNoAppleMaxNextDutTime();
        if (noAppleMaxNextDutTime != null && (nextDutTime == null || noAppleMaxNextDutTime.after(nextDutTime))) {
            openingDutUser.setExtLastOrderCode(orderCode);
            openingDutUser.setNextDutTime(noAppleMaxNextDutTime);
            if (needUpdateDutUserNew != null) {
                needUpdateDutUserNew.setExtLastOrderCode(orderCode);
                needUpdateDutUserNew.setNextDutTime(noAppleMaxNextDutTime);
            }
            return;
        }

        if (noAppleMaxNextDutTime == null) {
            startTime = nextDutTime == null || nextDutTime.before(payTime) ? payTime : nextDutTime;
        }
        openingDutUser.setExtLastOrderCode(orderCode);
        Timestamp endTime = openingAgreementTemplate.calcNextEndTimeNew(autorenewRequest, startTime);
        openingDutUser.setNextDutTime(endTime);
        if (needUpdateDutUserNew != null) {
            needUpdateDutUserNew.setExtLastOrderCode(orderCode);
            needUpdateDutUserNew.setNextDutTime(endTime);
        }
    }

    private static void setNoAppleMaxDutTime(AutorenewRequest autorenewRequest, Timestamp endTime) {
        if (autorenewRequest.getNoAppleMaxNextDutTime() == null || autorenewRequest.getNoAppleMaxNextDutTime().before(endTime)) {
            autorenewRequest.setNoAppleMaxNextDutTime(endTime);
        }
    }


    private static boolean validOrderCode(ExtInfo extInfo, String orderCode) {
        String lastOrderCode = extInfo.getLastOrderCode();
        if (StringUtils.isBlank(lastOrderCode)) {
            return true;
        }
        return !ObjectUtils.equals(lastOrderCode, orderCode) && lastOrderCode.compareTo(orderCode) < 0;
    }

    /**
     * 处理自动续费升级逻辑
     */
    private void dealUpgrade(AgreementNoInfo agreementNoInfo, AutorenewRequest autorenewRequest, Short priority) {
        Long sourceVipType = agreementNoInfo.getSourceVipType();
        if (sourceVipType == null) {
            return;
        }
        AutoRenewUpgradeConfig autoRenewUpgradeConfig = autoRenewUpgradeConfigManager.getByVipType(
            sourceVipType, autorenewRequest.getVipType(), priority);
        if (autoRenewUpgradeConfig == null || !autoRenewUpgradeConfig.getFinalProductCode().equals(autorenewRequest.getPid())) {
            return;
        }
        Integer agreementType = autorenewRequest.getAgreementType();
        List<DutUserNew> userNews = userAgreementService.getByAgreementTypeAndVipType(autorenewRequest.getUserId(),
            AgreementTypeEnum.valueOf(agreementType), autoRenewUpgradeConfig.getSourceVipType(), autorenewRequest.getVipType(), null);
        for (DutUserNew dutUserNew : userNews) {
            if (autoRenewUpgradeConfig.getFinalProductCode().equals(dutUserNew.getPcode())) {
                return;
            }
            dutUserNew.setRenewPrice(autoRenewUpgradeConfig.getFinalRenewPrice());
            dutUserNew.setPcode(autoRenewUpgradeConfig.getFinalProductCode());
            dutUserRepository.save(dutUserNew);
        }
    }

    /**
     * 更新签约价、下次代扣时间等信息
     */
    private void updateDutUserByAutoRenewRequest(DutUserNew dutUserNew, AutorenewRequest autoRenewRequest,
        AgreementTemplate agreementTemplate, AgreementNoInfo agreementNoInfo, PayCenterAppleDutInfoResult appleSignInfo) {
        DutUserNew needUpdateDutUserNew = DutUserNew.builder()
            .id(dutUserNew.getId())
            .userId(dutUserNew.getUserId())
            .renewCount(dutUserNew.getRenewCount())
            .serialRenewCount(dutUserNew.getSerialRenewCount())
            .build();
        setOpeningNextDutTime(autoRenewRequest, dutUserNew, needUpdateDutUserNew, agreementTemplate, appleSignInfo);
        if (agreementNoInfo.notDefaultNo()) {
            int successDutLogCount = 0;
            if (agreementNoInfo.thirdDut()) {
                successDutLogCount = dutUserNew.getSerialRenewCount();
            } else {
                successDutLogCount = userAgreementLogManager.successDutLogCount(dutUserNew.getUserId(), agreementNoInfo.getTemplateCode(), null);
                ExtInfo extInfo = dutUserNew.parseExt();
                extInfo.setTemplateRenewCount(successDutLogCount + 1);
                String ext = JacksonUtils.toJsonString(extInfo);
                needUpdateDutUserNew.setExt(ext);
            }
            // 优惠协议每次开通都刷新 或 是第三方渠道 取签约关系上的连续代扣次数
            Integer dutLogCount = agreementTemplate.needRestrictPeriod() || agreementNoInfo.thirdDut() ? successDutLogCount : userAgreementLogManager.getSuccessDutLogCount(dutUserNew, agreementNoInfo, agreementTemplate);
            PricingStrategyEnum pricingStrategy = PricingStrategyEnum.valueOf(agreementTemplate.getPricingStrategy());
            AbstractPricingStrategy priceCalculatorStrategy = renewPriceCalculatorFactory.getStrategy(pricingStrategy);
            RenewPriceDto renewPriceDto = priceCalculatorStrategy.getDiscountPrice(agreementNoInfo.getTemplateCode(), dutLogCount + 1);
            //兼容逻辑，价格从协议迁移到协议模版
            if (renewPriceDto == null) {
                renewPriceDto = priceCalculatorStrategy.getDiscountPrice(dutUserNew.getAgreementNo(), dutLogCount + 1);
            }
            needUpdateDutUserNew.setRenewPrice(renewPriceDto.getFee());
            needUpdateDutUserNew.setContractPrice(renewPriceDto.getOriginalPrice());
        }
        if (autoRenewRequest.getExpireTime() != null) {
            needUpdateDutUserNew.setNextDutTime(autoRenewRequest.getExpireTime());
        }
        if (appleSignInfo != null) {
            needUpdateDutUserNew.setSignTime(appleSignInfo.getRecentSubscriptionStartDate());
            needUpdateDutUserNew.setNextDutTime(appleSignInfo.getExpiresDate());
        }

        //苹果签约关系为老模式时，填充协议号到签约关系上
        DutRenewSetLog needUpdateSetLog = null;
        if (autoRenewRequest.isAppleOrder() && dutUserNew.isAutoRenewUser() && dutUserNew.getAgreementNo() == null) {
            String signKey = SignKeyGenerator.getSignKey(dutUserNew.getUserId(), agreementNoInfo.getType());
            needUpdateDutUserNew.setAgreementNo(agreementNoInfo.getId());
            needUpdateDutUserNew.setSignKey(signKey);
            DutRenewSetLog openedSetLog = userAgreementLogManager.getRecentlySetLog(dutUserNew.getUserId(), dutUserNew.getType(), DutRenewSetLog.RENEW_SET);
            needUpdateSetLog = DutRenewSetLog.builder()
                .id(openedSetLog.getId())
                .userId(openedSetLog.getUserId())
                .agreementNo(agreementNoInfo.getId())
                .signKey(signKey)
                .build();
        }

        //如果用户当前为非自动续费状态且代扣渠道为谷歌，恢复其自动续费状态
        if (dutUserNew.getAutoRenew() == DutUserNew.RENEW_NOT_AUTO) {
            if (autoRenewRequest.isGoogleBillingOrder() || autoRenewRequest.isAppleOrder()) {
                // 特别说明：谷歌自动续费订单恢复自动续费，autorenew 2 -> 3，解决autoRenew为2时将季、年和周等非月卡amount修改为1无法获取renewPrice的问题
                if (Constants.QIYUE_ORDER_AUTORENEW_DUT.equals(autoRenewRequest.getAutoRenew())) {
                    autoRenewRequest.setAutoRenew(Constants.QIYUE_ORDER_AUTORENEW_FIRST_LARGE);
                }
                OperateSceneEnum operateScene = autoRenewRequest.isAppleOrder() ? OperateSceneEnum.OPEN_APPLE_REOPEN : OperateSceneEnum.OPEN_GOOGLEREOPEN;
                autoRenewService.openByAutoRenewRequest(agreementNoInfo, agreementTemplate, autoRenewRequest, operateScene, appleSignInfo);
                return;
            }
        }

        if (dutUserNew.isAutoRenewUser() && CloudConfigUtil.enableStudentDutTimesFix() && dutUserNew.getVipType() == Constants.VIP_USER_STUDENT) {
            if (dutUserNew.getActType() == null) {
                needUpdateDutUserNew.setActType(ActTypeEnum.STOP_AFTER_X.getValue());
                needUpdateDutUserNew.setActTotalPeriods(24);
                int remainPeriods = 24 - dutUserNew.getSerialRenewCount();
                needUpdateDutUserNew.setRemainPeriods(remainPeriods < 0 ? 24 : remainPeriods);
            }
        }

        userAgreementService.updateAutoRenewAfterDut(needUpdateDutUserNew, needUpdateSetLog);
    }

}
