package com.qiyi.boss.component;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.dto.AgreementOptDto;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.enums.PricingStrategyEnum;
import com.qiyi.boss.exception.BizException;
import com.qiyi.boss.model.RenewPriceDto;
import com.qiyi.boss.outerinvoke.result.PayCenterAppleDutInfoResult;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.boss.service.impl.RenewTaskComponent;
import com.qiyi.boss.service.impl.UserAgreementLogManager;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * Created at: 2022-06-01
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WeChatPayScoreHandler extends AbstractAgreementHandler {

    @Resource
    RenewTaskComponent renewTaskComponent;
    @Resource
    UserAgreementLogManager userAgreementLogManager;

    @Override
    public AgreementTypeEnum getAgreementType() {
        return AgreementTypeEnum.WECHAT_PAY_SCORE;
    }

    @Override
    public RenewPriceDto getRenewPrice(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, PricingStrategyEnum pricingStrategy) {
        RenewPriceDto renewPriceDto = super.getRenewPrice(dutUserNew, agreementNoInfo, pricingStrategy);
        renewPriceDto.setRealFee(0);
        return renewPriceDto;
    }

    @Override
    public void doSomethingAfterPureSign(DutUserNew dutUserNew) {
        renewTaskComponent.genWeChatPayScoreCreateTask(dutUserNew);
    }

    @Override
    public DutUserNew openBySignPay(AutorenewRequest autorenewRequest, AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate) {
        throw BizException.newParamException("微信先看后付不支持签约支付开通自动续费");
    }

    @Override
    public void cancelAgreement(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, AgreementOptDto agreementOptDto) {
        Long userId = dutUserNew.getUserId();
        if (dutUserNew.cancelPendingStatus() || dutUserNew.invalid()) {
            log.info("Not need to cancel wechat pay score. userId: {}, dutUserNew status:{}", userId, dutUserNew.getAutoRenew());
            return;
        }
        boolean hasUnCompleteOrder = userAgreementLogManager.hasUnCompleteOrder(userId, dutUserNew.getSignKey());
        //无未完结订单直接解约，否则更新协议状态为待解决
        if (!hasUnCompleteOrder) {
            unbindAccount(dutUserNew, agreementNoInfo);
        } else {
            userAgreementService.updateToCancelPending(dutUserNew, agreementOptDto);
        }
    }

    @Override
    public void doSomethingWhenAgreementExpiration(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, OperateTypeEnum operateType) {
        if (operateType == OperateTypeEnum.COMPLETE_ORDER) {
            return;
        }
        AgreementOptDto agreementOptDto = AgreementOptDto.buildFromDutUserNew(dutUserNew, agreementNoInfo.getPayChannel(), OperateSceneEnum.CANCEL_AGREEMENT_PERIODS_REACHED);
        cancelAgreement(dutUserNew, agreementNoInfo, agreementOptDto);
    }

    @Override
    public DutRenewLog doSomethingAfterDut(AutorenewRequest autorenewRequest, AgreementTemplate agreementTemplate) {
        DutRenewLog dutRenewLog = super.doSomethingAfterDut(autorenewRequest, agreementTemplate);
        if (dutRenewLog == null || !dutRenewLog.isUpdated()) {
            return dutRenewLog;
        }

        DutUserNew dutUserNew = userAgreementMapper.selectBySignKey(dutRenewLog.getUserId(), dutRenewLog.getSignKey());
        OperateTypeEnum operateType = OperateTypeEnum.parseValue(dutRenewLog.getOperateType());
        //微信支付分0元创单支付成功后，需要将nextDutTime设置为创单的权益结束时间，以便本次服务到期后发起订单完结。
        if (operateType == OperateTypeEnum.DUT) {
            userAgreementMapper.updateNextDutTime(dutUserNew.getId(), dutUserNew.getUserId(), autorenewRequest.getDeadline());
        }
        //结单完成后设置下次续费时间为空
        if (operateType == OperateTypeEnum.COMPLETE_ORDER) {
            userAgreementService.incrementRenewCountAndResetNextDutTime(dutUserNew.getId(), dutUserNew.getUserId());
        }
        return dutRenewLog;
    }

    @Override
    public void resetOpenedNextDutTime(AutorenewRequest autorenewRequest, DutUserNew openedDutUser) {

    }

    @Override
    public void setOpeningNextDutTime(AutorenewRequest autorenewRequest, DutUserNew openingDutUser, DutUserNew needUpdateDutUserNew, AgreementTemplate agreementTemplate, PayCenterAppleDutInfoResult appleSignInfo) {

    }

}
