package com.qiyi.boss.component;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Map;

import com.qiyi.boss.enums.PricingStrategyEnum;

/**
 * Created at: 2022-06-20
 *
 * <AUTHOR>
 */
@Slf4j
@Lazy(value = false)
@Component
public class RenewPriceCalculatorFactory implements InitializingBean, ApplicationContextAware {

    private ApplicationContext applicationContext;

    private static final Map<PricingStrategyEnum, AbstractPricingStrategy> pricingStrategyToStrategyMap = Maps.newEnumMap(PricingStrategyEnum.class);

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("RenewPriceCalculatorFactory-init start!");
        applicationContext.getBeansOfType(AbstractPricingStrategy.class).values()
            .forEach(strategy -> {
                log.info("RenewPriceCalculatorFactory-init:register handler {}  for PricingStrategy:{}",
                    strategy.getClass().getSimpleName(), strategy.getPricingStrategy());
                pricingStrategyToStrategyMap.put(strategy.getPricingStrategy(), strategy);
            });
        log.info("RenewPriceCalculatorFactory-init end!");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public AbstractPricingStrategy getStrategy(PricingStrategyEnum pricingStrategy) {
        return pricingStrategyToStrategyMap.get(pricingStrategy);
    }

}
