package com.qiyi.boss.component;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.UUID;

import com.qiyi.boss.Constants;
import com.qiyi.boss.dto.AgreementSignReqParam;
import com.qiyi.boss.model.AgreementPureSignContext;
import com.qiyi.boss.service.impl.QiyuePaymentTypeManager;
import com.qiyi.boss.utils.AppConfig;
import com.qiyi.boss.utils.EncodeUtils;
import com.qiyi.boss.utils.PayUtils;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.constants.ParamConstants;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTempPayType;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;

/**
 * <AUTHOR>
 * @date 2021/7/15 16:15
 */
@Component
public class AgreementPureSignComponent {

    private static final String QIYI_INTER_DUT_BIND_URL = AppConfig.getProperty("inter.pay.dut.bind.url");
    private static final String QIYI_DUT_BIND_NOTIFY_URL = AppConfig.getProperty("pay.dut.bind.notify.url");

    @Resource
    QiyuePaymentTypeManager paymentTypeManager;

    /**
     * 生成纯签url
     * @param pureSignContext
     * @param signReqParam
     * @return
     */
    public String genPureSignUrl(AgreementPureSignContext pureSignContext, AgreementSignReqParam signReqParam, String displayAccount) {
        AgreementNoInfo agreementNoInfo = pureSignContext.getAgreementNoInfo();
        AgreementTemplate agreementTemplate = pureSignContext.getAgreementTemplate();
        AgreementTempPayType agreementTempPayType = pureSignContext.getAgreementTempPayType();
        Long userId = signReqParam.getUid();
        String platform = signReqParam.getPlatform();
        String returnUrl = signReqParam.getReturnUrl();
        Long vipType = agreementTemplate.getVipType();
        Integer pureSignPayType = agreementTempPayType.getPayType();
        String payCenterCode = getPayCenterCodeByPayType(pureSignPayType);
        Integer dutType = agreementNoInfo.getDutType();
        Integer payChannel = agreementNoInfo.getPayChannel();

        String partner = CloudConfigUtil.getPartnerByVipType(vipType);
        String notifyUrl = QIYI_DUT_BIND_NOTIFY_URL;
        String uuid = UUID.randomUUID().toString();
        Map<String, String> reqMap = Maps.newHashMap();
        reqMap.put("request_serial", uuid);
        reqMap.put("uid", userId.toString());
        reqMap.put("partner", partner);
        reqMap.put("platform_code", platform);
        reqMap.put("notify_url", notifyUrl);
        reqMap.put("version", "1.0");
        reqMap.put("charset", "UTF-8");
        reqMap.put("pay_type", payCenterCode);

        String extendParamStr = "";
        if (payChannel == PaymentDutType.PAY_CHANNEL_ALIPAY) {
            extendParamStr = buildAliPayExtendParam(dutType);
        }
        if (payChannel == PaymentDutType.PAY_CHANNEL_WECHAT) {
            extendParamStr = buildWeChatExtendParam(dutType, displayAccount);
        }

        StringBuilder sb = new StringBuilder(QIYI_INTER_DUT_BIND_URL)
            .append("?request_serial=").append(uuid)
            .append("&uid=").append(userId)
            .append("&partner=").append(partner)
            .append("&platform_code=").append(platform)
            .append("&notify_url=").append(notifyUrl)
            .append("&version=1.0")
            .append("&charset=UTF-8")
            .append("&pay_type=").append(payCenterCode)
            .append("&extend_params=").append(EncodeUtils.urlEncode(extendParamStr));

        reqMap.put("extend_params", extendParamStr);
        Map<String, String> extraCommonParamsMap = buildExtraCommonParamsMap(signReqParam);
        if (MapUtils.isNotEmpty(extraCommonParamsMap)) {
            String extraCommonParamsStr = Joiner.on("&").withKeyValueSeparator("=").join(extraCommonParamsMap);
            reqMap.put("extra_common_params", extraCommonParamsStr);
            sb.append("&extra_common_params=").append(EncodeUtils.urlEncode(extraCommonParamsStr));
        }
        if (StringUtils.isNotBlank(returnUrl)) {
            reqMap.put("return_url", returnUrl);
            sb.append("&return_url=").append(EncodeUtils.urlEncode(returnUrl));
        }
        sb.append("&sign=").append(PayUtils.signMessageRequest(reqMap, AppConfig.getProperty("qiyi.pay.key")));
        return sb.toString();
    }

    private String getPayCenterCodeByPayType(Integer payType) {
        return paymentTypeManager.getPayCenterCodeByIdFromPayInfo(payType);
    }

    /**
     * 构建支付宝extend_param
     * @param dutType
     */
    private String buildAliPayExtendParam(Integer dutType) {
        return "subParam_channel=ALIPAYAPP&subParam_schemaOfAddress=alipays&subParam_dutType=" + dutType;
    }

    /**
     * 构建微信的extend_param
     * @param dutType
     * @param displayAccount
     */
    private String buildWeChatExtendParam(Integer dutType, String displayAccount) {
        return "subParam_dutType=" + dutType + "&subParam_displayAccount=" + displayAccount;
    }

    private Map<String, String> buildExtraCommonParamsMap(AgreementSignReqParam signReqParam) {
        String fc = signReqParam.getFc();
        String fv = signReqParam.getFv();
        Map<String, String> extraCommonParamsMap = Maps.newHashMap();
        if (StringUtils.isNotBlank(fc)) {
            extraCommonParamsMap.put("fc", fc);
        }
        if (StringUtils.isNotBlank(fv)) {
            extraCommonParamsMap.put("fv", fv);
        }
        if (signReqParam.getAgreementNo() != null) {
            extraCommonParamsMap.put(ParamConstants.AGREEMENT_NO, signReqParam.getAgreementNo().toString());
        }
        return extraCommonParamsMap;
    }

}
