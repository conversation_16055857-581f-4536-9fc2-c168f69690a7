package com.qiyi.boss.component;

import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.qiyi.boss.Constants;
import com.qiyi.boss.dto.CancelTipInfo;
import com.qiyi.boss.dto.FirstDutInfo;
import com.qiyi.boss.dto.KeyValuePair;
import com.qiyi.boss.dto.PayTypeInfoVO;
import com.qiyi.boss.dto.RenewInfoVO;
import com.qiyi.boss.dto.RenewVipInfoVO;
import com.qiyi.boss.outerinvoke.result.PartnerUserSignRecordQueryResult;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.NumberFormatUtils;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;

/**
 * Created at: 2021-09-18
 *
 * <AUTHOR>
 */
public class NewMobileRenewComponent {

    private static final Map<Integer, Map.Entry<Integer, String>> newMobilePayChannelTipMap = new HashMap<>();

    static {
        newMobilePayChannelTipMap.put(PaymentDutType.PAY_CHANNEL_CUCC, Maps.immutableEntry(PaymentDutType.PAY_CHANNEL_CUCC, "联通话费支付"));
        newMobilePayChannelTipMap.put(PaymentDutType.PAY_CHANNEL_CMCC, Maps.immutableEntry(PaymentDutType.PAY_CHANNEL_CMCC, "移动话费支付"));
        newMobilePayChannelTipMap.put(PaymentDutType.PAY_CHANNEL_CTCC, Maps.immutableEntry(PaymentDutType.PAY_CHANNEL_CTCC, "电信话费支付"));
    }

    public static List<Integer> NEW_MOBILE_PAY_CHANNEL_LIST = Arrays.asList(
        PaymentDutType.PAY_CHANNEL_CUCC,
        PaymentDutType.PAY_CHANNEL_CMCC,
        PaymentDutType.PAY_CHANNEL_CTCC
    );

    public static RenewInfoVO buildAutoRenewInfo(PartnerUserSignRecordQueryResult partnerUserSignRecord, String vipTypeName, RenewVipInfoVO vipInfo,
        Timestamp deadline, boolean newVersion) {
        Integer amount = partnerUserSignRecord.getAmount();
        Integer recordRenewPrice = partnerUserSignRecord.getRenewPrice();
        String productName = getProductName(vipTypeName, amount, newVersion);

        FirstDutInfo firstDutInfo = FirstDutInfo.builder()
            .firstDutType(0)
            .firstRenewPrice(recordRenewPrice)
            .build();
        RenewInfoVO renewInfoVO = RenewInfoVO.builder()
            .deadline(deadline == null ? null : DateHelper.getDateStringByPattern(deadline, "yyyy-MM-dd"))
            .productName(productName)
            .allDutTypeDutTimeTips(CloudConfigUtil.managementPageAllDutTimeTips(null, null))
            .price(NumberFormatUtils.formatToStr(recordRenewPrice, "#.#"))
            .firstDutInfo(firstDutInfo)
            .payTypeInfo(Collections.singletonList(buildNewMobilePayInfo(partnerUserSignRecord)))
            .ruleTips("1.以下是您开通自动续费的全部支付方式，会员到期前我们将按照从上到下的顺序依次尝试为您扣除下一周期" +
                "的会员费用，并为您自动续费VIP；\n\n2.您可以在这里添加（苹果设备暂不支持）或删除支付方式，删除后不再通过该支付方式扣款。")
            .supportToAddPayTypes(Collections.emptyList())
            .payInfoExplanation("实际续费日期、续费金额以运营商实际扣费为准")
            .build();
        vipInfo.setAmount(amount);
        vipInfo.setPid(partnerUserSignRecord.getProductCode());
        return renewInfoVO;
    }

    private static String getProductName(String vipTypeName, Integer amount, boolean newVersion) {
        if (!newVersion) {
            return vipTypeName + amount + "个月";
        }
        return vipTypeName + MapUtils.getString(Constants.amountNameMap(), amount,amount + "个月");
    }

    public static PayTypeInfoVO buildNewMobilePayInfo(PartnerUserSignRecordQueryResult partnerUserSignRecord) {
        String cancelTip = "根据运营商规定，您需要自行发送短信或拨打客服电话取消续费。当月取消下月不再扣费。";
        String methodTip = "取消方法:\r\n移动：您可以发送短信“0000”至10086查询并根据菜单指引办理取消自动续费，或拨打10086取消。\n\n"
                + "联通：您可以拨打联通客服4000600611或10010取消。";
        Map<String, String> cancelTips = Maps.newLinkedHashMap();
        cancelTips.put("cancelTips", cancelTip);
        cancelTips.put("methodTips", methodTip);
        CancelTipInfo cancelTipInfo = CancelTipInfo.builder()
            .cancelTips(cancelTip)
            .methodTips(methodTip)
            .build();
        Integer payChannel = partnerUserSignRecord.getPayChannel();
        return PayTypeInfoVO.builder()
            .cancelTips(cancelTipInfo)
            .tipType(KeyValuePair.from(newMobilePayChannelTipMap.get(payChannel)))
            .dutType(Collections.emptyList())
            .dutTimeTips(null)
            .payChannel(payChannel)
            .signTime(partnerUserSignRecord.getSignTime())
            .build();
    }

    public static Map<String, Object> buildOpenFcInfo(PartnerUserSignRecordQueryResult partnerUserSignRecord) {
        Map<String, Object> openFcInfo = Maps.newHashMap();
        openFcInfo.put("type", 0);
        openFcInfo.put("amount", partnerUserSignRecord.getAmount());
        openFcInfo.put("payChannel", partnerUserSignRecord.getPayChannel());
        openFcInfo.put("priority", 1);
        if (StringUtils.isNotBlank(partnerUserSignRecord.getFc())) {
            openFcInfo.put("fc", partnerUserSignRecord.getFc());
        }
        openFcInfo.put("orderCode", null);
        return openFcInfo;
    }


}
