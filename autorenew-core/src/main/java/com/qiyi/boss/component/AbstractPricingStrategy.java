package com.qiyi.boss.component;

import com.qiyi.boss.enums.PricingStrategyEnum;
import com.qiyi.boss.model.RenewPriceDto;
import com.qiyi.vip.trade.autorenew.domain.AgreementTempPrice;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * Created at: 2021-07-01
 *
 * <AUTHOR>
 */
public abstract class AbstractPricingStrategy {

    protected abstract PricingStrategyEnum getPricingStrategy();

    /**
     * 查询协议价格
     */
    public abstract AgreementTempPrice getPriceInfo(Integer agreementNo, Integer periodNo);

    public abstract AgreementTempPrice getPriceInfo(String agreementCode, Integer periodNo);

    /**
     * 获取折扣价格
     * @param agreementNo
     * @param renewCount
     */
    public abstract RenewPriceDto getDiscountPrice(Integer agreementNo, Integer renewCount);

    public abstract RenewPriceDto getDiscountPrice(String agreementCode, Integer renewCount);

    /**
     * 构建代扣时的续费价格
     */
    public abstract RenewPriceDto buildRenewPrice(DutUserNew dutUserNew);

    public abstract RenewPriceDto buildRenewPrice(DutUserNew dutUserNew, String agreementCode);

}
