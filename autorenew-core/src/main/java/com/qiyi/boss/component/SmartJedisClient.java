package com.qiyi.boss.component;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Collections;
import com.qiyi.boss.utils.JacksonUtils;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.iqiyi.smartjedis.SmartJedis;

/**
 * Created at: 2020-12-02
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SmartJedisClient {


    @Resource
    SmartJedis smartJedis;

    public SmartJedisClient(SmartJedis smartJedis) {
        this.smartJedis = smartJedis;
    }

    public String getString(String key) {
        String value = null;
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            value = smartJedis.get(key);
            log.info("Get Cache {}={} success, cost:{}", key, value, stopWatch.getTime());
        } catch (Exception e) {
            log.error("Get Cache {} failed, cost: {}", key, stopWatch.getTime(), e);
        }
        return value;
    }

    public <T> T get(String key, TypeReference<T> type) {
        return get(key, type, false);
    }

    public boolean exists(String key) {
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            Boolean exists = smartJedis.exists(key);
            log.info("Cache exists {}={} success, cost:{}", key, exists, stopWatch.getTime());
            return BooleanUtils.isTrue(exists);
        } catch (Exception e) {
            log.error("Cache exists {} failed, cost: {}", key, stopWatch.getTime(), e);
            return false;
        }
    }

    public <T> T get(String key, TypeReference<T> type, boolean throwException) {
        StopWatch stopWatch = StopWatch.createStarted();
        String jsonStr = null;
        try {
            jsonStr = smartJedis.get(key);
            log.info("Get Cache {}={} success, cost:{}", key, jsonStr, stopWatch.getTime());
        } catch (Exception e) {
            log.error("Get Cache {} failed, cost: {}", key, stopWatch.getTime(), e);
            if (throwException) {
                throw e;
            }
        }
        return JacksonUtils.parseObject(jsonStr, type);
    }

    public void setex(String key, String value, int cacheTimeSeconds) {
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            smartJedis.setex(key, cacheTimeSeconds, value);
            log.info("Set Cache {}={} success, cacheTimeSeconds:{}, cost:{}", key, value, cacheTimeSeconds, stopWatch.getTime());
            boolean needCheckRedis = CloudConfigUtil.needCheckRedis();
            if (needCheckRedis) {
                String result = smartJedis.get(key);
                log.info("Check Cache key {} value {}", key, result);
            }
        } catch (Exception e) {
            log.error("Set Cache {}={} failed, cacheTimeSeconds:{}, cost: {}", key, value, cacheTimeSeconds, stopWatch.getTime(), e);
        }
    }

    public void delete(String key) {
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            smartJedis.del(key);
            log.info("Del Cache {} success, cost:{}", key, stopWatch.getTime());
        } catch (Exception e) {
            log.error("Del Cache {} failed, cost: {}", key, stopWatch.getTime(), e);
        }
    }

    public Object execLuaScript(String scriptString, String key, String arg) {
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            return smartJedis.eval(scriptString, Collections.singletonList(key), Collections.singletonList(arg));
        } catch (Exception e) {
            log.error("execLuaScript {} failed, cost: {}", key, stopWatch.getTime(), e);
            return null;
        }
    }

//    public Object execLuaScriptOld(String scriptString, String key, String arg) {
//        StopWatch stopWatch = StopWatch.createStarted();
//        try {
//            return smartJedis.getShardByName("viptrade-autorenew").getService().apply(new JedisOperation<Object>() {
//                @Override
//                public Object perform(Jedis jedis) {
//                    return jedis.eval(scriptString, Collections.singletonList(key), Collections.singletonList(arg));
//                }
//
//                @Override
//                public boolean shouldRunOnMaster() {
//                    return true;
//                }
//
//                @Override
//                public String getMethodName() {
//                    return "eval";
//                }
//            });
//        } catch (Exception e) {
//            log.error("execLuaScript {} failed, cost: {}", key, stopWatch.getTime(), e);
//            return null;
//        }
//    }

}
