package com.qiyi.boss.component;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import com.qiyi.boss.enums.PricingStrategyEnum;
import com.qiyi.boss.model.RenewPriceDto;
import com.qiyi.boss.service.impl.AgreementTemplateManager;
import com.qiyi.vip.trade.autorenew.domain.AgreementTempPrice;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * 固定单价 价格策略
 * Created at: 2021-07-01
 *
 * <AUTHOR>
 */
@Component
public class FixedPriceStrategy extends AbstractPricingStrategy {

    @Resource
    AgreementTemplateManager agreementTemplateManager;

    @Override
    protected PricingStrategyEnum getPricingStrategy() {
        return PricingStrategyEnum.FIXED;
    }

    @Override
    public AgreementTempPrice getPriceInfo(Integer agreementNo, Integer periodNo) {
        return agreementTemplateManager.getDefaultPrice(agreementNo);
    }

    @Override
    public AgreementTempPrice getPriceInfo(String agreementCode, Integer periodNo) {
        return agreementTemplateManager.getDefaultPriceByCode(agreementCode);
    }

    @Override
    public RenewPriceDto getDiscountPrice(Integer agreementNo, Integer renewCount) {
        AgreementTempPrice defaultPrice = agreementTemplateManager.getDefaultPrice(agreementNo);
        return new RenewPriceDto(defaultPrice.getPrice(), defaultPrice.getPrice());
    }

    @Override
    public RenewPriceDto getDiscountPrice(String agreementCode, Integer renewCount) {
        AgreementTempPrice defaultPrice = agreementTemplateManager.getDefaultPriceByCode(agreementCode);
        return new RenewPriceDto(defaultPrice.getPrice(), defaultPrice.getPrice());
    }

    @Override
    public RenewPriceDto buildRenewPrice(DutUserNew dutUserNew) {
        if (dutUserNew.getRenewPrice() != null) {
            return new RenewPriceDto(dutUserNew.getRenewPrice(), dutUserNew.getRenewPrice());
        }
        AgreementTempPrice defaultPrice = agreementTemplateManager.getDefaultPrice(dutUserNew.getAgreementNo());
        return new RenewPriceDto(defaultPrice.getPrice(), defaultPrice.getPrice());
    }

    @Override
    public RenewPriceDto buildRenewPrice(DutUserNew dutUserNew, String agreementCode) {
        if (dutUserNew.getRenewPrice() != null) {
            return new RenewPriceDto(dutUserNew.getRenewPrice(), dutUserNew.getRenewPrice());
        }
        AgreementTempPrice defaultPrice = agreementTemplateManager.getDefaultPriceByCode(agreementCode);
        if (defaultPrice == null) {
            defaultPrice = agreementTemplateManager.getDefaultPrice(dutUserNew.getAgreementNo());
        }
        return new RenewPriceDto(defaultPrice.getPrice(), defaultPrice.getPrice());
    }
}
