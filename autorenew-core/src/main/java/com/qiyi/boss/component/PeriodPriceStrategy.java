package com.qiyi.boss.component;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.enums.PricingStrategyEnum;
import com.qiyi.boss.model.RenewPriceDto;
import com.qiyi.boss.service.impl.AgreementTemplateManager;
import com.qiyi.vip.trade.autorenew.domain.AgreementTempPrice;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * 分期定价 价格获取策略
 * Created at: 2021-07-01
 *
 * <AUTHOR>
 */
@Component
public class PeriodPriceStrategy extends AbstractPricingStrategy {

    @Resource
    AgreementTemplateManager agreementTemplateManager;

    @Override
    protected PricingStrategyEnum getPricingStrategy() {
        return PricingStrategyEnum.PERIOD;
    }

    @Override
    public AgreementTempPrice getPriceInfo(Integer agreementNo, Integer periodNo) {
        List<AgreementTempPrice> priceList = agreementTemplateManager.getAgreementPrice(agreementNo);
        if (CollectionUtils.isEmpty(priceList)) {
            return null;
        }
        AgreementTempPrice price = priceList.stream()
            .filter(item -> Objects.equals(periodNo, item.getPeriodNo()))
            .findFirst().orElse(null);
        if (price != null) {
            return price;
        }
        return priceList.stream()
            .filter(item -> item.getPeriodNo() == null || item.getPeriodNo() == 0)
            .findFirst().orElse(null);
    }

    @Override
    public AgreementTempPrice getPriceInfo(String agreementCode, Integer periodNo) {
        List<AgreementTempPrice> priceList = agreementTemplateManager.getPriceByCode(agreementCode);
        if (CollectionUtils.isEmpty(priceList)) {
            return null;
        }
        AgreementTempPrice price = priceList.stream()
            .filter(item -> Objects.equals(periodNo, item.getPeriodNo()))
            .findFirst().orElse(null);
        if (price != null) {
            return price;
        }
        return priceList.stream()
            .filter(item -> item.getPeriodNo() == null || item.getPeriodNo() == 0)
            .findFirst().orElse(null);
    }

    @Override
    public RenewPriceDto getDiscountPrice(Integer agreementNo, Integer renewCount) {
        Integer nextDutPeriod = renewCount + 1;
        List<AgreementTempPrice> priceList = agreementTemplateManager.getAgreementPrice(agreementNo);
        if (CollectionUtils.isEmpty(priceList)) {
            return null;
        }
        Map<Integer, AgreementTempPrice> periodNoToPriceMap = priceList.stream()
            .collect(Collectors.toMap(AgreementTempPrice::getPeriodNo, v -> v));
        AgreementTempPrice nextDutPrice = periodNoToPriceMap.get(nextDutPeriod);
        AgreementTempPrice normalPrice = periodNoToPriceMap.get(0);
        Integer originalPrice = normalPrice.getPrice();
        Integer renewPrice = nextDutPrice != null ? nextDutPrice.getPrice() : originalPrice;
        return new RenewPriceDto(renewPrice, originalPrice);
    }

    @Override
    public RenewPriceDto getDiscountPrice(String agreementCode, Integer renewCount) {
        Integer nextDutPeriod = renewCount + 1;
        List<AgreementTempPrice> priceList = agreementTemplateManager.getPriceByCode(agreementCode);
        if (CollectionUtils.isEmpty(priceList)) {
            return null;
        }
        Map<Integer, AgreementTempPrice> periodNoToPriceMap = priceList.stream()
            .collect(Collectors.toMap(AgreementTempPrice::getPeriodNo, v -> v));
        AgreementTempPrice nextDutPrice = periodNoToPriceMap.get(nextDutPeriod);
        AgreementTempPrice normalPrice = periodNoToPriceMap.get(0);
        Integer originalPrice = normalPrice.getPrice();
        Integer renewPrice = nextDutPrice != null ? nextDutPrice.getPrice() : originalPrice;
        return new RenewPriceDto(renewPrice, originalPrice);
    }

    @Override
    public RenewPriceDto buildRenewPrice(DutUserNew dutUserNew) {
        boolean openedBySignPay = OperateSceneEnum.isSignPayScene(dutUserNew.getOperateSceneExt());
        int periodNo = openedBySignPay ? dutUserNew.getSerialRenewCount() + 2 : dutUserNew.getSerialRenewCount() + 1;
        AgreementTempPrice priceInfo = this.getPriceInfo(dutUserNew.getAgreementNo(), periodNo);
        return priceInfo != null ? new RenewPriceDto(priceInfo.getPrice(), priceInfo.getOriginalPrice()) : null;
    }

    @Override
    public RenewPriceDto buildRenewPrice(DutUserNew dutUserNew, String agreementCode) {
        boolean openedBySignPay = OperateSceneEnum.isSignPayScene(dutUserNew.getOperateSceneExt());
        int periodNo = openedBySignPay ? dutUserNew.getSerialRenewCount() + 2 : dutUserNew.getSerialRenewCount() + 1;
        AgreementTempPrice priceInfo = this.getPriceInfo(agreementCode, periodNo);
        if (priceInfo == null) {
            priceInfo = this.getPriceInfo(dutUserNew.getAgreementNo(), periodNo);
        }
        return priceInfo != null ? new RenewPriceDto(priceInfo.getPrice(), priceInfo.getOriginalPrice()) : null;
    }

}
