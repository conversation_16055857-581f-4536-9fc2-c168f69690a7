package com.qiyi.boss.constants;

import com.google.common.collect.Lists;
import java.util.List;
import com.qiyi.boss.enums.AgreementTypeEnum;

/**
 * <AUTHOR>
 * @className AgreementConstants
 * @description
 * @date 2024/8/15
 **/
public class AgreementConstants {


    public static final List<Integer> EXCLUDE_AGREEMENT_TYPES = Lists.newArrayList(AgreementTypeEnum.ZHIMA_GO.getValue(), AgreementTypeEnum.WECHAT_PAY_SCORE.getValue(), AgreementTypeEnum.PASSWORD_FREE.getValue());

    public static final List<Integer> COMMON_AUTORENEW_AGREEMENT_TYPES = Lists.newArrayList(AgreementTypeEnum.AUTO_RENEW.getValue(), AgreementTypeEnum.QQ_JOINT_PACKAGE.getValue(), AgreementTypeEnum.KUGOU_JOINT_PACKAGE.getValue(), AgreementTypeEnum.FAMILY_CARD_PACKAGE.getValue(), AgreementTypeEnum.ALIPAY_DEDUCTION_PACKAGE.getValue(), AgreementTypeEnum.WECHAT_DEDUCTION_PACKAGE.getValue(), AgreementTypeEnum.PHONE_CREDIT_DEDUCTION_PACKAGE.getValue());

}
