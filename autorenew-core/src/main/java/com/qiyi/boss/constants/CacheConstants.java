package com.qiyi.boss.constants;

import org.apache.commons.lang.math.RandomUtils;

/**
 * Created at: 2020-12-02
 *
 * <AUTHOR>
 */
public class CacheConstants {

    public static final int ONE_MONTH_IN_SECONDS = 35 * 24 * 60 * 60 + RandomUtils.nextInt(3 * 60 * 60);

    /**
     * 立减优惠模板信息的缓存key
     */
    private static final String DUT_DISCOUNT_TEMPLATE_CACHE_KEY = "dut_discount_temp:%s";
    /**
     * 用户所有立减优惠信息的缓存key
     */
    private static final String USER_DUT_DISCOUNTS_CACHE_KEY = "user_dut_discounts:%s";

    public static String buildDutDiscountTemplateCacheKey(String batchNo) {
        return String.format(CacheConstants.DUT_DISCOUNT_TEMPLATE_CACHE_KEY, batchNo);
    }

    public static String buildUserDutDiscountsCacheKey(Long userId) {
        return String.format(CacheConstants.USER_DUT_DISCOUNTS_CACHE_KEY, userId);
    }

    public static String bulidAliPayAsyncDutTaskOrderCacheKey(Long userId, Long vipType) {
        return String.format("aliPayAsync_%s_%s", userId, vipType);
    }

}
