package com.qiyi.boss.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @auther: guojing
 * @date: 2023/8/26 14:35
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelSignInfo {
    /**
     * 协议编号
     */
    private Integer agreementNo;
    /**
     * 协议名称
     */
    private String agreementName;
    /**
     * 代扣方式
     */
    private Integer dutType;
    /**
     * 一次签约的唯一标识
     */
    private String signKey;
    /**
     * 签约时间
     */
    private String signTime;
    /**
     * 签约时长
     */
    private Integer amount;
    /**
     * 续费价格，单位：元
     */
    private Double renewPrice;
    /**
     * 支付渠道
     */
    private Integer payChannel;
    /**
     * 支付渠道名称
     */
    private String payChannelName;
    /**
     * 平台名称
     */
    private String platformName;

}
