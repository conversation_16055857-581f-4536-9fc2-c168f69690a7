package com.qiyi.boss.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import java.util.Map;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @className UidAllRenewInfo
 * @description
 * @date 2025/5/12
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UidAllRenewInfo {

    private List<DutUserNew> renewStatus;

    private List<DutRenewSetLog> setLog;

    private List<DutRenewLog> renewLog;

    // 解释 renewStatus 对象属性的 Map
    private Map<String, String> renewStatusFieldDesc;

    // 解释 setLog 对象属性的 Map
    private Map<String, String> setLogFieldDesc;

    // 解释 renewLog 对象属性的 Map
    private Map<String, String> renewLogFieldDesc;

    // 解释 renewStatus 对象属性的静态 Map
    private static final Map<String, String> RENEW_STATUS_FIELD_DESC = new HashMap<String, String>() {{
        put("userId", "用户ID");
        put("signKey", "签约唯一标识");
        put("agreementNo", "协议编号");
        put("agreementType", "协议类型(1:自动续费,2:芝麻GO,3:微信先看后付,4:免密支付,5:双连包-QQ音乐,6:双连包-酷狗,7:亲情卡,8:双连包-支付宝立减金,9:双连包-微信立减金)");
        put("status", "绑定状态");
        put("autoRenew", "自动续费状态(-1:未签约,0:已失效,1:生效中,2:自动续费时长切换,4:结算中,5:履约完成,6:待解约)");
        put("signTime", "签约时间");
        put("operateTime", "操作时间");
        put("platform", "平台ID");
        put("platformCode", "平台代码");
        put("deadline", "到期时间");
        put("pcode", "产品代码");
        put("amount", "签约时长");
        put("orderCode", "订单号");
        put("nextDutTime", "下次代扣时间");
        put("serialRenewCount", "当前签约关系第几次续费");
        put("renewPrice", "续费价格");
        put("contractPrice", "合约价格");
        put("vipType", "会员类型");
        put("type", "代扣方式dutType");
        put("agreementCode", "协议模板code");
        put("currencyUnit", "货币单位");
        put("payAutoRenew", "包月支付时是否选中自动续费状态");
        put("source", "来源");
        put("onceAutoRenew", "是否自动续费过");
        put("renewCount", "自动续费次数");
        put("returnUrl", "返回URL");
        put("sourceVipType", "升级自动续费源会员类型");
        put("interruptFlag", "中断标志");
        put("vipCategory", "会员类型(1:大陆会员,2:台湾会员)");
        put("remainPeriods", "剩余期数");
        put("actCode", "活动代码");
        put("actTotalPeriods", "活动总期数");
        put("actType", "活动类型");
        put("timeZone", "时区");
    }};

    // 解释 setLog 对象属性的静态 Map
    private static final Map<String, String> SET_LOG_FIELD_DESC = new HashMap<String, String>() {{
        put("userId", "用户ID");
        put("signKey", "签约唯一标识");
        put("agreementNo", "协议编号");
        put("agreementType", "协议类型(1:自动续费,2:芝麻GO,3:微信先看后付,4:免密支付,5:双连包-QQ音乐,6:双连包-酷狗,7:亲情卡,8:双连包-支付宝立减金,9:双连包-微信立减金)");
        put("type", "代扣方式dutType");
        put("signKey", "签约标识");
        put("amount", "签约时长");
        put("operateTime", "操作时间");
        put("operator", "操作类型(0:取消,1:开通,2:自动续费活动更新,3:代扣,4:结算,5:履约完成,6:待解约,7:微信支付分结单)");
        put("platformCode", "平台代码");
        put("platform", "平台ID");
        put("description", "操作描述");
        put("vipType", "会员类型");
        put("id", "主键ID");
        put("agreementCode", "协议模板code");
        put("updateTime", "更新时间");
        put("pushChannel", "推广渠道");
        put("gateway", "推广入口");
        put("serialRenewCount", "连续续费次数");
        put("vipCategory", "会员类型(1:大陆会员,2:台湾会员)");
    }};

    // 解释 renewLog 对象属性的静态 Map
    private static final Map<String, String> RENEW_LOG_FIELD_DESC = new HashMap<String, String>() {{
        put("userId", "用户ID");
        put("signKey", "签约唯一标识");
        put("agreementNo", "协议编号");
        put("agreementType", "协议类型(1:自动续费,2:芝麻GO,3:微信先看后付,4:免密支付,5:双连包-QQ音乐,6:双连包-酷狗,7:亲情卡,8:双连包-支付宝立减金,9:双连包-微信立减金)");
        put("type", "代扣方式dutType");
        put("orderCode", "订单号");
        put("tradeCode", "交易号");
        put("operateType", "操作类型(0:取消,1:开通,2:自动续费活动更新,3:代扣,4:结算,5:履约完成,6:待解约,7:微信支付分结单)");
        put("status", "支付状态(0:支付失败,1:支付成功,2:需要异步确认)");
        put("errorCode", "错误码");
        put("fee", "费用");
        put("platform", "平台ID");
        put("platformCode", "平台代码");
        put("thirdErrorCode", "第三方错误码");
        put("thirdErrorMsg", "第三方错误信息");
        put("description", "描述信息");
        put("vipCategory", "会员类型(1:大陆会员,2:台湾会员)");
        put("amount", "续费时长");
        put("vipType", "会员类型");
        put("createTime", "创建时间");
        put("updateTime", "更新时间");
        put("id", "主键ID");
        put("reqErrorType", "请求错误类型");
        put("agreementCode", "协议模板code");
    }};

    public static UidAllRenewInfo buildWithFieldDesc(List<DutUserNew> renewStatus, List<DutRenewSetLog> setLog, List<DutRenewLog> renewLog) {
        return UidAllRenewInfo.builder()
                .renewStatus(renewStatus)
                .setLog(setLog)
                .renewLog(renewLog)
                .renewStatusFieldDesc(RENEW_STATUS_FIELD_DESC)
                .setLogFieldDesc(SET_LOG_FIELD_DESC)
                .renewLogFieldDesc(RENEW_LOG_FIELD_DESC)
                .build();
    }
}
