package com.qiyi.boss.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @auther: guojing
 * @date: 2023/8/26 14:22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserVipInfo {

    /**
     * 用户id
     */
    private Long uid;
    /** 会员类型 */
    private Long vipType;
    /** 会员类型名称 */
    private String vipTypeName;
    /** 会员权益是否过期，1: 未过期，0: 已过期 */
    private String status;
    /** 当前会员权益的到期时间，格式：yyyy-MM-dd HH:mm:ss */
    private String deadline;

}
