package com.qiyi.boss.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @auther: guojing
 * @date: 2023/8/26 14:44
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryUserSetLogResult {

    /**
     * 用户续费体系所有操作日志
     */
    private List<UserRenewSetLog> vipGroupSetLogs;

    /**
     * 用户最近的操作日志
     */
    private List<UserRenewSetLog> vipRecentlySetLogs;

}
