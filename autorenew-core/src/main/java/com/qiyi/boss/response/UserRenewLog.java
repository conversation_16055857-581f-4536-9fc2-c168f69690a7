package com.qiyi.boss.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @auther: guojing
 * @date: 2023/8/26 14:56
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRenewLog {

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 会员类型
     */
    private Long vipType;

    /**
     * 会员类型名称
     */
    private String vipTypeName;

    /**
     * 协议类型
     */
    private Integer agreementType;

    /**
     * 协议类型描述
     */
    private String agreementTypeDesc;

    /**
     * 协议编号
     */
    private Integer agreementNo;

    /**
     * 协议名称
     */
    private String agreementName;

    /**
     * 一次签约的唯一标识
     */
    private String signKey;

    /**
     * 签约时长
     */
    private Integer amount;

    /**
     * 代扣订单号
     */
    private String orderCode;

    /**
     * 续费金额，单位:元
     */
    private Double renewPrice;

    /**
     * 代扣时间，格式: yyyy-MM-dd HH:mm:ss
     */
    private String dutTime;

    /**
     * 代扣状态, 1:成功; 2: 异步待确认
     */
    private Integer status;

    /**
     * 代扣状态描述
     */
    private String statusDesc;

    /**
     * 代扣失败原因
     */
    private String failureReason;

    /**
     * 代扣操作类型描述，3:代扣; 4:结算
     */
    private String operateTypeDesc;
    /**
     * 支付渠道
     */
    private Integer payChannel;
    /**
     * 支付渠道
     */
    private String payChannelName;

    /**
     * 平台
     */
    private String platformName;

}
