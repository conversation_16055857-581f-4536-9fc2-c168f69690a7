package com.qiyi.boss.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 * 用户续费信息
 *
 * @auther: guojing
 * @date: 2023/8/26 14:31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRenewInfo {

    /**
     * 查询的用户ID
     */
    private Long uid;
    /**
     * 升级自动续费源会员类型
     */
    private Long sourceVipType;
    /**
     * 升级自动续费源会员类型名称
     */
    private String sourceVipTypeName;
    /**
     * 会员类型
     */
    private Long vipType;
    /**
     * 会员类型名称
     */
    private String vipTypeName;
    /**
     * 协议类型
     */
    private Integer agreementType;
    /**
     * 协议类型描述
     */
    private String agreementTypeDesc;
    /**
     * 自动续费状态，0: 未开通; 1: 生效中; 4: 结算中(芝麻GO); 5: 履约完成; 6: 待解约(微信支付分)
     */
    private Integer status;
    /**
     * 自动续费状态描述
     */
    private String statusDesc;
    /**
     * 下次续费渠道
     */
    private Integer nextDutChannel;
    /**
     * 预计下次代扣时间，格式: yyyy-MM-dd HH:mm:ss
     */
    private String nextDutTime;
    /**
     * 签约渠道列表
     */
    private List<ChannelSignInfo> channelSignInfos;

}
