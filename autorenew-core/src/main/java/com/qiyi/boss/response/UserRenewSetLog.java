package com.qiyi.boss.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @auther: guojing
 * @date: 2023/8/26 14:43
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRenewSetLog {

    /**
     * 查询的用户ID
     */
    private Long uid;

    /**
     * 会员类型
     */
    private Long vipType;

    /**
     * 会员类型名称
     */
    private String vipTypeName;

    /**
     * 协议类型
     */
    private Integer agreementType;

    /**
     * 协议类型描述
     */
    private String agreementTypeDesc;

    /**
     * 协议编号
     */
    private Integer agreementNo;
    /**
     * 代扣方式
     */
    private Integer dutType;

    /**
     * 协议名称
     */
    private String agreementName;

    /**
     * 一次签约的唯一标识
     */
    private String signKey;

    /**
     * 签约时长
     */
    private Integer amount;

    /**
     * 操作时间，格式: yyyy-MM-dd HH:mm:ss
     */
    private String operateTime;

    /**
     * 操作类型，0:取消; 1:开通; 6:结算
     */
    private Integer operator;

    /**
     * 操作类型描述
     */
    private String operatorDesc;

    /**
     * 平台
     */
    private String platformName;

    /**
     * 操作场景，可为"开通"或"关闭"
     */
    private String operateScene;
    /**
     * fc名称
     */
    private String fcDesc;
    /**
     * 支付渠道
     */
    private Integer payChannel;
    /**
     * 操作渠道名称
     */
    private String payChannelName;

    /**
     * 签约价格，单位: 元
     */
    private Double renewPrice;

    /**
     * 签约订单号
     */
    private String signOrderCode;

}
