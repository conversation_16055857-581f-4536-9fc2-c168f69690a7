package com.qiyi.boss.config;

import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;

/**
 * Created at: 2022-05-13
 *
 * <AUTHOR>
 */
@Component("customKeyGenerator")
public class CustomKeyGenerator implements KeyGenerator {

    @Override
    public Object generate(Object target, Method method, Object... params) {
        return method.getName() + "#" + StringUtils.arrayToDelimitedString(params, "#");
    }

}
