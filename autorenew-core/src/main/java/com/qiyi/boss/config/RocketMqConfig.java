package com.qiyi.boss.config;

import org.apache.rocketmq.spring.support.CloudRocketMQMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @className RocketMqConfig
 * @description
 * @date 2025/7/14
 **/
@Configuration
public class RocketMqConfig {

    @Bean
    public CloudRocketMQMessageConverter createRocketMQMessageConverter() {
        return new CloudRocketMQMessageConverter(true);
    }
}
