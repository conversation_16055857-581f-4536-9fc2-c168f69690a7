package com.qiyi.boss.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

import com.qiyi.vip.service.PaymentDutTypeClient;
import com.qiyi.vip.service.PaymentTypeClient;
import com.iqiyi.solar.config.client.spring.annotation.EnableCloudConfig;

/**
 * <AUTHOR>
 */
@EnableCloudConfig
@Configuration
public class ClientConfig {
    @Value("${pay.info.sign.key}")
    String payInfoSignKey;

    @Resource
    private RestTemplate payInfoQueryClient;

    @Value("${pay.info.url}")
    private String payInfoUrl;

    @Value("${pay.info.channel}")
    private String payInfoChannel;

    @Bean(name = "paymentTypeClient")
    public PaymentTypeClient paymentTypeClient() {
        PaymentTypeClient paymentTypeClient = new PaymentTypeClient();
        paymentTypeClient.setChannel(payInfoChannel);
        paymentTypeClient.setEnableSign(true);
        paymentTypeClient.setRestTemplate(payInfoQueryClient);
        paymentTypeClient.setRootUrl(payInfoUrl);
        paymentTypeClient.setSignKey(payInfoSignKey);
        return paymentTypeClient;
    }

    @Bean(name = "paymentDutTypeClient")
    public PaymentDutTypeClient paymentDutTypeClient() {
        PaymentDutTypeClient paymentDutTypeClient = new PaymentDutTypeClient();
        paymentDutTypeClient.setChannel(payInfoChannel);
        paymentDutTypeClient.setEnableSign(true);
        paymentDutTypeClient.setRootUrl(payInfoUrl);
        paymentDutTypeClient.setSignKey(payInfoSignKey);
        paymentDutTypeClient.setRestTemplate(payInfoQueryClient);
        return paymentDutTypeClient;
    }

}