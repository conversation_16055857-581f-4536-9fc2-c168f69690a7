package com.qiyi.boss.config.i18n;

import com.iqiyi.vip.language.distribute.CorpusTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> l<PERSON><PERSON><PERSON>ang
 * Date: 2019-5-6
 * Time: 20:43
 */
@Configuration
public class CorpusConfig {
	@Resource
	private DataSource masterDataSource;

	@Bean(initMethod = "init")
	public CorpusTemplate corpusTemplate() {
		return new CorpusTemplate(masterDataSource);
	}
}
