package com.qiyi.boss.config;

import com.iqiyi.smartjedis.SmartJedis;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

/**
 * Created at: 2020-12-02
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class RedisConfig {

    @Lazy(value = false)
    @Bean(name = "smartJedis", destroyMethod = "close")
    public SmartJedis smartJedis(){
        log.info("smartJedis：autorenew_redis init start");
        return SmartJedis.Builder.fromCloudConfig("autorenew_redis");
    }

}