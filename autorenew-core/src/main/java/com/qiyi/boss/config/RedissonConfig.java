package com.qiyi.boss.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import com.iqiyi.redisson.Redisson;
import com.iqiyi.redisson.RedissonClient;

@Slf4j
@Configuration
public class RedissonConfig {

    @Lazy(value = false)
    @Bean(name = "redissonClient")
    RedissonClient redissonClient() {
        log.info("redissonClient init success");
        return Redisson.create("autorenew_redis");
    }
}
