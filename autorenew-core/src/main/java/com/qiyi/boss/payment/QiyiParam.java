package com.qiyi.boss.payment;

/**
 * 支付中心参数
 * <AUTHOR> fangying
 * Date: 12-6-25
 * Time: 下午6:36
 * To change this template use File | Settings | File Templates.
 */
public class QiyiParam {

    /**
     * 支付服务的接口类型
     */
    public static final String SERVICE = "pay_by_api";
    /**
     * 系统ID，由支付中心分配
     */
    public static final String ACCOUNT = "qiyue";

    /**
    支付中心的银河支付业务方
     */
    public static final String YINHE = "gitv";
    /** 台湾业务方 */
    public static final String PARTNER_TW = "qiyue_tw";
    /** fun业务方 */
    public static final String PARTNER_FUN = "FUN_QIYUE";
    /** 国际站业务方 */
    public static final String PARTNER_GLOBEL = "qiyue_global";
    /** 奇巴布业务方 */
    public static final String PARTNER_QIBUBBLE = "qibubble_audio";

    /**
     * 版本号
     */
    public static final String VERSION = "1.0";

    /**
     * 接口编码
     */
    public static final String CHARSET_UTF8 = "UTF-8";
}
