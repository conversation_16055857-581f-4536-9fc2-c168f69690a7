package com.qiyi.boss.processor;

import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.qiyi.boss.Constants;
import com.qiyi.boss.dto.FreePayDto;
import com.qiyi.boss.service.impl.AutoRenewGracePeriodRecordManager;
import com.qiyi.boss.utils.ApplicationContextUtil;
import com.qiyi.boss.utils.AutoRenewConfig;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.EncodeUtils;
import com.qiyi.boss.utils.FreePayApi;
import com.qiyi.boss.utils.IPUtil;
import com.qiyi.boss.utils.SignatureUtil;
import com.qiyi.vip.commons.constant.QueryConstants;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewGracePeriodRecord;
import com.qiyi.vip.trade.autorenew.domain.FreePayConfig;
import com.qiyi.vip.trade.autorenew.service.BossFreePayConfigService;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2020/5/27
 * Time: 11:37
 */
@Component
public class FreePayProcessor {

	private static final Logger LOGGER = LoggerFactory.getLogger(FreePayProcessor.class);

	private static final String PARAM_AMOUNT = "amount";
	private static final String PARAM_UID = "uid";
	private static final String PARAM_ACT_CODE = "actCode";
	private static final String PARAM_SIGN = "sign";
	private static final String PARAM_CODE = "code";
	private static final String CODE_A00000 = "A00000";
	private static final String PARAM_TRADE_CODE = "tradeCode";
	private static final String PARAM_ORDER_CODE = "orderCode";
	private static final String PARAM_DATA = "data";

	private static final String PARAM_BATCH_NO = "batchNo";
	private static final String PARAM_PARTNER = "partner";
	private static final String PARAM_IDEMPOTENT_PARAM = "idempotentParam";
	private static final String PARAM_APP_IP = "appIp";
	private static String APP_IP;

	static {
		APP_IP = IPUtil.getLocalIp();
		if (StringUtils.isEmpty(APP_IP)) {
			LOGGER.error("getLocalIp error, ip is empty");
		}
	}

	/**
	 * 调用免费会员发放接口进行宽限期权限发放
	 *
	 * @see "免费会员发放接入步骤 http://wiki.qiyi.domain/pages/viewpage.action?pageId=205564843"
	 */
	public boolean dealFreePay(FreePayDto freePayDto) {

		FreePayConfig freePayConfig = ifNeedToCompensate(freePayDto);
		if (freePayConfig == null) {
			return false;
		}
		Map<String, String> params = buildParams(freePayDto, freePayConfig);

		// 调用免费会员发放接口
		Map<String, Object> resultMap = ApplicationContextUtil.getBean(FreePayApi.class).freePay(params);
		if (resultMap == null) {
			LOGGER.error("[FreePay interface return null!] [message:{}]", freePayDto.toString());
			return false;
		}

		String code = String.valueOf(resultMap.get(PARAM_CODE));
		if (StringUtils.isBlank(code) || !CODE_A00000.equals(code)) {
			LOGGER.error("[Failed] [FreePay interface resultMap:{}] [compensateDays:{}] [params:{}]",
					resultMap, freePayConfig.getCompensateDay(), freePayDto.toString());
			return false;
		}

		// 宽限期赠送保存赠送成功记录
		if (isGracePeriodCompensate(freePayDto)) {
			saveRecord(freePayDto, freePayConfig.getCompensateDay(), resultMap);
		}
		LOGGER.info("[Success] [compensateDays:{}] [params:{}]", freePayConfig.getCompensateDay(), freePayDto.toString());
        return true;
	}

	private boolean isGracePeriodCompensate(FreePayDto freePayDto) {
		return Constants.FREE_PAY_FOR_GRACE_PERIOD.equals(freePayDto.getPurpose());
	}

	private Map<String, String> buildParams(FreePayDto freePayDto, FreePayConfig freePayConfig) {
		Map<String, String> params = Maps.newHashMap();
		params.put(PARAM_BATCH_NO, freePayConfig.getBatchNo());
		params.put(PARAM_ACT_CODE, freePayConfig.getActCode());
		params.put(PARAM_PARTNER, freePayDto.getFreePayPartner());
		params.put(PARAM_IDEMPOTENT_PARAM, UUID.randomUUID().toString().replaceAll(QueryConstants.MINUS, ""));
		Integer compensateDay = isGracePeriodCompensate(freePayDto) ? freePayConfig.getCompensateDay() : freePayDto.getCompensateDays();
		params.put(PARAM_AMOUNT, String.valueOf(compensateDay));
		params.put(PARAM_UID, String.valueOf(freePayDto.getUserId()));
		params.put(PARAM_APP_IP, APP_IP);
		String sign = EncodeUtils.MD5(SignatureUtil.createLinkString(params) +
				StringUtils.defaultIfEmpty(freePayDto.getSignKey(), ""), "UTF-8");
		params.put(PARAM_SIGN, sign);
		return params;
	}

	private FreePayConfig ifNeedToCompensate(FreePayDto freePayDto) {
		if (isGracePeriodCompensate(freePayDto) && !validGracePeriodSwitch(freePayDto)) {
			LOGGER.info("Grace period cloud config switch is closed. switchName:{}.", freePayDto.getSwitchName());
			return null;
		}

		BossFreePayConfigService freePayConfigService = ApplicationContextUtil.getBean(BossFreePayConfigService.class);
		FreePayConfig freePayConfig = freePayConfigService.getConfigSelective(
				freePayDto.getPurpose(), freePayDto.getVipType(), freePayDto.getAutoRenewProductId());
		if (freePayConfig == null) {
			LOGGER.info("FreePayConfig is null. params:{}.", freePayDto.toString());
			return null;
		}

		if (validateDeadline(freePayDto, freePayConfig)) {
			LOGGER.info("Not need to compensate. user's vip right is equals or after compensateDate. " +
					"params:{}.", freePayDto.toString());
			return null;
		}

		if (isGracePeriodCompensate(freePayDto)) {
			AutoRenewGracePeriodRecordManager autoRenewGracePeriodRecordManager =
					(AutoRenewGracePeriodRecordManager) ApplicationContextUtil.getBean("autoRenewGracePeriodRecordService");
			List<AutoRenewGracePeriodRecord> autoRenewGracePeriodRecordList = autoRenewGracePeriodRecordManager
					.find(freePayDto.getUserId(), freePayDto.getAutoRenewProductId(), freePayConfig.getCompensateDay(), freePayDto.getInterval());
			if (CollectionUtils.isNotEmpty(autoRenewGracePeriodRecordList)) {
				LOGGER.info("Already compensated in last {} days. params:{}.", freePayDto.getInterval(), freePayDto.toString());
				return null;
			}
		}

		return freePayConfig;
	}

	private boolean validateDeadline(FreePayDto freePayDto, FreePayConfig freePayConfig) {
		if (!freePayDto.isValidateDeadline() || !isGracePeriodCompensate(freePayDto)) {
			return false;
		}
		Integer compensateDays = isGracePeriodCompensate(freePayDto) ? freePayConfig.getCompensateDay() : freePayDto.getCompensateDays();
		LocalDate compensateDate = LocalDate.now().plusDays(compensateDays);
		return freePayDto.getDeadline() != null && notNeedToCompensate(freePayDto, compensateDate);
	}

	private boolean validGracePeriodSwitch(FreePayDto freePayDto) {
		return ApplicationContextUtil.getBean(AutoRenewConfig.class).isNeedDealGracePeriod(freePayDto.getSwitchName());
	}

	private boolean notNeedToCompensate(FreePayDto freePayDto, LocalDate compensateDate) {
		return freePayDto.getDeadline() != null && compensateDate != null
				&& freePayDto.getDeadline().toLocalDateTime().toLocalDate().compareTo(compensateDate) >= 0;
	}

	private void saveRecord(FreePayDto freePayDto, Integer compensateDays, Map<String, Object> resultMap) {
		AutoRenewGracePeriodRecordManager autoRenewGracePeriodRecordManager =
				(AutoRenewGracePeriodRecordManager) ApplicationContextUtil.getBean("autoRenewGracePeriodRecordService");
		AutoRenewGracePeriodRecord autoRenewGracePeriodRecord = AutoRenewGracePeriodRecord.builder()
				.userId(freePayDto.getUserId())
				.vipType(Long.valueOf(freePayDto.getVipType()))
				.dutType(freePayDto.getDutType())
				.appId(freePayDto.getAutoRenewProductId())
				.originalTradeNo(freePayDto.getOriginalTransactionId())
				.amount(compensateDays)
				.createTime(DateHelper.getCurrentTime())
				.build();

		Map<String, String> data = (Map<String, String>) resultMap.get(PARAM_DATA);
		if (data != null && data.containsKey(PARAM_ORDER_CODE)) {
			autoRenewGracePeriodRecord.setOrderCode(data.get(PARAM_ORDER_CODE));
		}
		//适配免费会员发放接口Response
		if (data != null && data.containsKey(PARAM_TRADE_CODE)) {
			autoRenewGracePeriodRecord.setOrderCode(data.get(PARAM_TRADE_CODE));
		}
		autoRenewGracePeriodRecordManager.save(autoRenewGracePeriodRecord);
	}
}
