package com.qiyi.boss.processor;

import cn.hutool.core.util.StrUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import com.qiyi.boss.Constants;
import com.qiyi.boss.autorenew.enumerate.ActTypeEnum;
import com.qiyi.boss.component.NewMobileRenewComponent;
import com.qiyi.boss.dto.ActPeriodTip;
import com.qiyi.boss.dto.BindInfo;
import com.qiyi.boss.dto.BindUserInfo;
import com.qiyi.boss.dto.CancelTipInfo;
import com.qiyi.boss.dto.DiscountAgreementTip;
import com.qiyi.boss.dto.DutPriceTipInfo;
import com.qiyi.boss.dto.FamilyBindRelation;
import com.qiyi.boss.dto.FirstDutInfo;
import com.qiyi.boss.dto.FirstXDiscountInfo;
import com.qiyi.boss.dto.GenerateAutoRenewInfoDto;
import com.qiyi.boss.dto.GenerateOpenUrlDto;
import com.qiyi.boss.dto.KeyValuePair;
import com.qiyi.boss.dto.ManagementDto;
import com.qiyi.boss.dto.ManagementDutTypeInfo;
import com.qiyi.boss.dto.ManagementRenewGiftArea;
import com.qiyi.boss.dto.ManagementRenewInfoVO;
import com.qiyi.boss.dto.OtherRenewServiceInfoVO;
import com.qiyi.boss.dto.PayTypeInfoVO;
import com.qiyi.boss.dto.PriceInsuredPeriodInfo;
import com.qiyi.boss.dto.RenewGiftVO;
import com.qiyi.boss.dto.RenewInfoVO;
import com.qiyi.boss.dto.RenewVipInfoVO;
import com.qiyi.boss.dto.RestrictionStrategyInfo;
import com.qiyi.boss.dto.SupportToAddPayTypeInfo;
import com.qiyi.boss.dto.UnusedGiftInfo;
import com.qiyi.boss.dto.UserAutoRenewInfo;
import com.qiyi.boss.dto.UserDutTypeInfo;
import com.qiyi.boss.dto.UserRenewDiscountInfo;
import com.qiyi.boss.dto.UserUpgradeInfo;
import com.qiyi.boss.enums.AgreementStatusEnum;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.BizTypeEnum;
import com.qiyi.boss.enums.DetailDisplayMode;
import com.qiyi.boss.enums.FamilyBindStatusEnum;
import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.boss.enums.OtherRenewServiceInfoEnum;
import com.qiyi.boss.enums.PureSignPullUpModeEnum;
import com.qiyi.boss.enums.UpgradeStageEnum;
import com.qiyi.boss.model.UserInfo;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.outerinvoke.AutoRenewMarketingProxy;
import com.qiyi.boss.outerinvoke.CustomVipInfoClient;
import com.qiyi.boss.outerinvoke.VipChargeApi;
import com.qiyi.boss.outerinvoke.VipPartnerRenewServerProxy;
import com.qiyi.boss.outerinvoke.result.PartnerUserSignRecordQueryResult;
import com.qiyi.boss.service.AutoRenewInfoService;
import com.qiyi.boss.service.AutoRenewService;
import com.qiyi.boss.service.AutorenewDutConfigService;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.boss.service.DutService;
import com.qiyi.boss.service.DutUserService;
import com.qiyi.boss.service.PureSignManager;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.service.impl.AgreementManager;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.AgreementTemplateManager;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.service.impl.AutoRenewUpgradeConfigManager;
import com.qiyi.boss.service.impl.DutManager;
import com.qiyi.boss.service.impl.DutPriceActServiceImpl;
import com.qiyi.boss.service.impl.ManagementConfigService;
import com.qiyi.boss.service.impl.PaymentDutTypeManager;
import com.qiyi.boss.service.impl.UserManager;
import com.qiyi.boss.service.impl.VipTypeManager;
import com.qiyi.boss.utils.AppConfig;
import com.qiyi.boss.utils.AutoRenewConfig;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.EncodeUtils;
import com.qiyi.boss.utils.HystrixFutureHelper;
import com.qiyi.boss.utils.NumberFormatUtils;
import com.qiyi.boss.utils.PayUtils;
import com.qiyi.boss.utils.PureSignUtils;
import com.qiyi.boss.utils.TempIpUtil;
import com.qiyi.boss.utils.TipTypeUtils;
import com.qiyi.boss.utils.TipsUtils;
import com.qiyi.boss.utils.UpgradeAutoRenewUtils;
import com.qiyi.vip.commons.enums.PayChannelEnum;
import com.qiyi.vip.commons.enums.VipTypesEnum;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.constants.ParamConstants;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewUpgradeConfig;
import com.qiyi.vip.trade.autorenew.domain.AutorenewDutConfig;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew.ExtInfo;
import com.qiyi.vip.trade.autorenew.domain.ManagementConfig;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import com.qiyi.vip.trade.autorenew.service.DutRenewSetLogService;
import com.qiyi.vip.trade.qiyue.domain.VipType;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;

import static com.qiyi.boss.Constants.ALI_PURE_PAY_TYPE;
import static com.qiyi.boss.Constants.AMOUNT_OF_COMMON_AUTORENEW;
import static com.qiyi.boss.Constants.DUT_PRICE_TIP_TEXT;
import static com.qiyi.boss.Constants.VALID_END_TIME_TIP_TEXT;
import static com.qiyi.boss.constants.AgreementConstants.EXCLUDE_AGREEMENT_TYPES;
import static com.qiyi.vip.trade.autorenew.domain.PaymentDutType.PAY_CHANNEL_TYPE_THIRD_DUT_PAY;
import static com.qiyi.vip.trade.autorenew.domain.PaymentDutType.PAY_CHANNEL_TYPE_VIP_DUT_PAY;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2020-03-03
 * Time: 16:17
 */
@Component
public class AutoRenewManagementProcessor {

	private static final Logger LOGGER = LoggerFactory.getLogger(Constants.LOG_MODULE_COMMON);
	private static final String QIYI_DUT_BIND_NOTIFY_URL = AppConfig.getProperty("pay.dut.bind.notify.url");
	private static final String VERSION = "1.0";
	private static final String CHARSET_UTF8 = "UTF-8";

	public static final String PAGE_TYPE_MIX = "MIX";
	public static final String PAGE_TYPE_NO_MIX = "NO_MIX";

    public static final String OTHER_RENEW_INFO_TIP_TEXT = "已开通{{%s}}";
    public static final String OTHER_TV_RENEW_INFO_TIP_TEXT = "已开通{{%s}}，请在电视端查看";
    public static final String OTHER_MAIN_SITE_RENEW_INFO_TIP_TEXT = "已开通{{%s}}，请在爱奇艺APP查看";
    public static final String RENEW_GIFT_AREA_TIP_TEXT = "已开通自动续费%s天";
    public static final String OTHER_BASIC_RENEW_INFO_TIP_TEXT = "已开通{{%s}}，请在爱奇艺极速版APP查看";
    public static final String OTHER_FAMILY_RENEW_INFO_TIP_TEXT = "你的家人%s为你开通{{%s}}，如需取消续费请联系TA";


	@Resource
	private DutService dutService;
	@Resource
	private UserManager userService;
	@Resource
	private PaymentDutTypeManager paymentDutTypeManager;
	@Resource
	private DutManager dutManager;
	@Resource
	private AutoRenewDutTypeManager autoRenewDutTypeManager;
	@Resource
	private AutoRenewUpgradeConfigManager autoRenewUpgradeConfigManager;
	@Resource
	private DutUserService dutUserService;
	@Resource
	private DutRenewSetLogService dutRenewSetLogService;
	@Resource
	private ManagementConfigService managementConfigService;
	@Resource
	private VipTypeManager vipTypeManager;
	@Resource
	private AutoRenewMarketingProxy autoRenewMarketingProxy;
	@Resource
	private AutoRenewConfig autoRenewConfig;
	@Resource
	AutoRenewService autoRenewService;
	@Resource
	private VipPartnerRenewServerProxy partnerRenewServerProxy;
	@Resource
	private AutorenewDutConfigService autorenewDutConfigService;
	@Resource
    DutPriceActServiceImpl dutPriceActService;
    @Resource
    private UserAgreementService userAgreementService;
    @Resource
    private AutoRenewInfoService autoRenewInfoService;
    @Resource
    private AgreementNoInfoManager agreementNoInfoManager;
    @Resource
    private AgreementTemplateManager agreementTemplateManager;
    @Resource
    private PureSignManager pureSignManager;
    @Resource
    private AgreementManager agreementManager;
    @Resource
    private VipChargeApi vipChargeApi;
    @Resource
    private CustomVipInfoClient customVipInfoClient;

    @ConfigJsonValue("${tip.type.desc:{\"1\":\"手机话费\",\"2\":\"微信支付\",\"3\":\"支付宝\",\"4\":\"百度钱包\",\"5\":\"苹果支付\",\"6\":\"银行卡\",\"7\":\"Google Play\",\"8\":\"PayPal\",\"47\":\"抖音支付\",\"50\":\"快手(支付宝)\",\"51\":\"快手(微信)\",\"52\":\"支付宝支付(抖音)\"}}")
    private Map<Integer, String> tipTypeDescMap;
    @ConfigJsonValue("${discount.agreement.management.tips:{\"bc9162bdf1ef5be4\":{\"renewGiftType\":5,\"renewGiftTitle\":\"学生\",\"renewGiftDesc\":\"的学生折扣\"},\"a1d8b01cffdd0c3f\":{\"renewGiftType\":5,\"renewGiftTitle\":\"学生\",\"renewGiftDesc\":\"的学生折扣\"},\"adbb72c86c798da0\":{\"renewGiftType\":5,\"renewGiftTitle\":\"学生\",\"renewGiftDesc\":\"的学生折扣\"}}}")
    private Map<String, DiscountAgreementTip> discountAgreementManagementTips;
    @ConfigJsonValue("${autorenew.marketing.coupon.types:[4,6]}")
    private List<Integer> autoRenewMarketingCouponTypes;

    @ConfigJsonValue("${actionType.by.agreementTypeAndBizType}")
    private Map<String, LinkedHashMap<Integer, Integer>> actionTypeByAgreementTypeAndBizType;

    @ConfigJsonValue("${payChannel.tipType.mapping:{}}")
    private Map<Integer, Integer> payChannelTipTypeMapping;

	public Map<String, Object> getBindInfo(Long userId) {
		List<Integer> bindTypes = dutManager.getAutoRenewDutTypeBindList(userId);
		Map<String, Object> bindInfo = Maps.newHashMap();
		List<Map<String, Object>> bindTypeList = Lists.newArrayList();
		for (Integer bindType : bindTypes) {
			Map<String, Object> bindTypeMap = Maps.newHashMap();
			AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(bindType);
			bindTypeMap.put("type", bindType);
			Long vipType = autoRenewDutTypeManager.getVipTypeByDutType(bindType);
			if (vipType != null) {
				bindTypeMap.put("vipType", vipType.intValue());
			}
			bindTypeMap.put("name", autoRenewDutType.getName());

			bindTypeList.add(bindTypeMap);
		}
		bindInfo.put("list", bindTypeList);
		bindInfo.put("size", bindTypeList.size());

		return bindInfo;
	}


	public UserAutoRenewInfo getAutoRenewInfo(Long uid, Long vipType) {
		Future<List<PartnerUserSignRecordQueryResult>> partnerUserSignRecordsFuture = null;
		if (CloudConfigUtil.enableNewMobileFunc()) {
			partnerUserSignRecordsFuture = partnerRenewServerProxy.queryUserSignRecordAsync(uid, vipType);
		}
//		List<DutUserNew> dutUserNews = dutService.findByUidAndVipTypeAndAutoRenew(uid, vipType, DutUserNew.RENEW_AUTO);
		List<DutUserNew> dutUserNews = userAgreementService.getByExcludeAgreementTypesAndVipTypes(uid, EXCLUDE_AGREEMENT_TYPES, Collections.singletonList(vipType), AgreementStatusEnum.VALID);
		List<PartnerUserSignRecordQueryResult> partnerUserSignRecords = HystrixFutureHelper.getFutureResult(partnerUserSignRecordsFuture,
				Collections.emptyList(), HystrixCommandPropsEnum.QUERY_USER_SIGN_RECORD_URL);

		UserAutoRenewInfo userAutoRenewInfo = UserAutoRenewInfo.builder()
				.uid(uid)
				.vipType(vipType)
				.build();
		if (dutUserNews.isEmpty() && CollectionUtils.isEmpty(partnerUserSignRecords)) {
			userAutoRenewInfo.setStatus(DutUserNew.RENEW_NOT_AUTO);
			return userAutoRenewInfo;
		}
		//仅开通新话费
		if (CollectionUtils.isEmpty(dutUserNews)) {
			PartnerUserSignRecordQueryResult partnerUserSignRecord = partnerUserSignRecords.get(0);
			userAutoRenewInfo.setStatus(DutUserNew.RENEW_AUTO);
			userAutoRenewInfo.setAmount(partnerUserSignRecord.getAmount());
			userAutoRenewInfo.setNextDutTime(null);
            userAutoRenewInfo.setPayChannel(partnerUserSignRecord.getPayChannel());
			return userAutoRenewInfo;
		}
		UserAutoRenewInfo firstDutRenewInfo = autoRenewService.getFirstDutRenewInfo(dutUserNews, vipType);
		if (firstDutRenewInfo == null) {
            userAutoRenewInfo.setStatus(DutUserNew.RENEW_NOT_AUTO);
			return userAutoRenewInfo;
		}
		if (firstDutRenewInfo.getSourceVipType() != null) {
			userAutoRenewInfo.setSourceVipType(firstDutRenewInfo.getSourceVipType());
		}
		userAutoRenewInfo.setStatus(firstDutRenewInfo.getStatus());
		userAutoRenewInfo.setAmount(firstDutRenewInfo.getAmount());
		userAutoRenewInfo.setNextDutTime(firstDutRenewInfo.getNextDutTime());
        userAutoRenewInfo.setPayChannel(firstDutRenewInfo.getPayChannel());
		return userAutoRenewInfo;
	}

	public RenewInfoVO generateAutoRenewInfo(GenerateAutoRenewInfoDto generateAutoRenewInfoDto) {
		HttpServletRequest servletRequest = generateAutoRenewInfoDto.getServletRequest();
		UserInfo user = generateAutoRenewInfoDto.getUser();
		Timestamp deadline = generateAutoRenewInfoDto.getDeadline();
		Long vipType = generateAutoRenewInfoDto.getVipType();
		boolean isDutUser = generateAutoRenewInfoDto.isDutUser();
		String lang = generateAutoRenewInfoDto.getLang();
		String platform = generateAutoRenewInfoDto.getPlatform();
		String fc = generateAutoRenewInfoDto.getFc();
		String fv = generateAutoRenewInfoDto.getFv();
		String pageType = generateAutoRenewInfoDto.getPageType();
        RenewVipInfoVO vipInfo = generateAutoRenewInfoDto.getVipInfo();
        boolean justSignNewMobile = generateAutoRenewInfoDto.isJustSignNewMobile();
        boolean newVersion = generateAutoRenewInfoDto.isNewVersion();
        boolean needGenPureSignUrl = generateAutoRenewInfoDto.isNeedGenPureSignUrl();
        PartnerUserSignRecordQueryResult partnerUserSignRecord = generateAutoRenewInfoDto.getPartnerUserSignRecord();
        List<DutUserNew> dutUserNews = generateAutoRenewInfoDto.getDutUserNews();
        Integer agreementType = generateAutoRenewInfoDto.getAgreementType();
        Integer bizType = generateAutoRenewInfoDto.getBizType();

        VipType vipTypeObj = vipTypeManager.getVipTypeById(vipType);
		String vipTypeName = vipTypeObj != null ? vipTypeObj.getName() : "";
		if (justSignNewMobile) {
			return NewMobileRenewComponent.buildAutoRenewInfo(partnerUserSignRecord, vipTypeName, vipInfo, deadline, newVersion);
		}
        Long userId = user.getId();
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return null;
        }
        List<DutUserNew> upgradeDutUserNews = dutUserNews.stream()
            .filter(dutUserNew -> dutUserNew.getSourceVipType() != null)
            .collect(Collectors.toList());
        boolean isUpgrade = false;
        if (CollectionUtils.isNotEmpty(upgradeDutUserNews)) {
            isUpgrade = true;
            dutUserNews = upgradeDutUserNews;
        }
        List<DutRenewSetLog> dutRenewSetLogs = getAllSortedSetLog(dutUserNews, agreementType);
        RenewInfoVO renewInfoVO = new RenewInfoVO();
        if (CollectionUtils.isNotEmpty(dutRenewSetLogs)) {
            renewInfoVO.setLastOperateTime(dutRenewSetLogs.get(0).getOperateTime().getTime());
        }

        List<Integer> tipTypeList = Lists.newArrayList();
        List<ManagementDutTypeInfo> mobileDutTypeInfoList = Lists.newLinkedList();
        List<ManagementDutTypeInfo> weixinDutTypeInfoList = Lists.newLinkedList();
        List<ManagementDutTypeInfo> baiduDutTypeInfoList = Lists.newLinkedList();
        List<ManagementDutTypeInfo> aliDutTypeInfoList = Lists.newLinkedList();
        List<ManagementDutTypeInfo> douyinDutTypeInfoList = Lists.newLinkedList();
        List<ManagementDutTypeInfo> appleDutTypeInfoList = Lists.newLinkedList();
        List<ManagementDutTypeInfo> bankCardDutTypeInfoList = Lists.newLinkedList();
        List<ManagementDutTypeInfo> googleDutTypeInfoList = Lists.newLinkedList();
        List<ManagementDutTypeInfo> payPalDutTypeInfoList = Lists.newLinkedList();
        List<ManagementDutTypeInfo> ksGuaranteeAlipayDutTypeInfoList = Lists.newLinkedList();
        List<ManagementDutTypeInfo> ksGuaranteeWechatDutTypeInfoList = Lists.newLinkedList();
        List<ManagementDutTypeInfo> douYinGuaranteeAlipayDutTypeInfoList = Lists.newLinkedList();
        List<Integer> mobileDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_MOBILE);
        List<Integer> wechatDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_WECHAT);
        List<Integer> alipayDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_ALIPAY);
        List<Integer> baiduPayDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_BAIDUPAY);
        List<Integer> douyinPayDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_DOU_YIN);
        List<Integer> iapDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_IAP);
        List<Integer> bankCardDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_BANKCARD);
        List<Integer> googleDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_GOOGLE_BILLING);
        List<Integer> payPalDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_PAYPAL);
        List<Integer> overseaAlipayDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_OVERSEA_ALIPAY);
        List<Integer> overseaWechatDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_OVERSEA_WECHAT);
        List<Integer> ksGuaranteeAliPayDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_KS_GUARANTEE_ALIPAY);
        List<Integer> ksGuaranteeWeChatDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_KS_GUARANTEE_WECHAT);
        List<Integer> douyinGuaranteeAliPayDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_DOU_YIN_GUARANTEE_ALIPAY);
        List<DutRenewSetLog> thirdDutSetLogList = Lists.newArrayList();
        List<DutRenewSetLog> commonDutSetLogList = Lists.newArrayList();

        Integer firstCommonDutType = null;
        Integer firstCommonAgreementNo = null;
        for (DutRenewSetLog dutRenewSetLog : dutRenewSetLogs) {
            Integer dutType = dutRenewSetLog.getType();
            Integer agreementNo = dutRenewSetLog.getAgreementNo();
            Integer amount = dutRenewSetLog.getAmount();
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
            if (agreementNoInfo != null) {
                dutType = agreementNoInfo.getDutType();
            }
            AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutType);
            if (firstCommonDutType == null && !mobileDutTypeList.contains(dutType) && !autoRenewDutType.thirdDut()) {
                firstCommonDutType = dutType;
                firstCommonAgreementNo = agreementNo;
            }

            if (autoRenewDutType != null && autoRenewDutType.isMobileDutType()) {
                mobileDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                if (tipTypeList.contains(Constants.TIPTYPE_MOBILE)) {
                    continue;
                }
                tipTypeList.add(Constants.TIPTYPE_MOBILE);
            }
            if (autoRenewDutType != null && autoRenewDutType.isWechatDutType()) {
                weixinDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                commonDutSetLogList.add(dutRenewSetLog);
                if (tipTypeList.contains(Constants.TIPTYPE_WEIXIN)) {
                    continue;
                }
                tipTypeList.add(Constants.TIPTYPE_WEIXIN);
            }
            if (autoRenewDutType != null && autoRenewDutType.isAlipayDutType()) {
                aliDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                commonDutSetLogList.add(dutRenewSetLog);
                if (tipTypeList.contains(Constants.TIPTYPE_ALIPAY)) {
                    continue;
                }
                tipTypeList.add(Constants.TIPTYPE_ALIPAY);
            }
            if (autoRenewDutType != null && autoRenewDutType.isBaiduDutType()) {
                baiduDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                commonDutSetLogList.add(dutRenewSetLog);
                if (tipTypeList.contains(Constants.TIPTYPE_BAIDU)) {
                    continue;
                }
                tipTypeList.add(Constants.TIPTYPE_BAIDU);
            }
            if (autoRenewDutType != null && autoRenewDutType.isDouYinDutType()) {
                douyinDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                commonDutSetLogList.add(dutRenewSetLog);
                if (tipTypeList.contains(PaymentDutType.PAY_CHANNEL_DOU_YIN)) {
                    continue;
                }
                tipTypeList.add(PaymentDutType.PAY_CHANNEL_DOU_YIN);
            }
            if (autoRenewDutType != null && autoRenewDutType.isAppleDutType()) {
                appleDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                thirdDutSetLogList.add(dutRenewSetLog);
                if (tipTypeList.contains(Constants.TIPTYPE_APPLE)) {
                    continue;
                }
                tipTypeList.add(Constants.TIPTYPE_APPLE);
            }
            if (autoRenewDutType != null && autoRenewDutType.isBankCardDutType()) {
                bankCardDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                commonDutSetLogList.add(dutRenewSetLog);
                if (tipTypeList.contains(Constants.TIPTYPE_CARD)) {
                    continue;
                }
                tipTypeList.add(Constants.TIPTYPE_CARD);
            }
            if (autoRenewDutType != null && autoRenewDutType.isGoogleDutType()) {
                googleDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                thirdDutSetLogList.add(dutRenewSetLog);
                if (tipTypeList.contains(Constants.TIPTYPE_GOOGLE)) {
                    continue;
                }
                tipTypeList.add(Constants.TIPTYPE_GOOGLE);
            }
            if (autoRenewDutType != null && autoRenewDutType.isPayPalDutType()) {
                payPalDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                thirdDutSetLogList.add(dutRenewSetLog);
                if (tipTypeList.contains(Constants.TIPTYPE_PAYPAL)) {
                    continue;
                }
                tipTypeList.add(Constants.TIPTYPE_PAYPAL);
            }
            if (autoRenewDutType != null && autoRenewDutType.isKsGuaranteeAlipayDutType()) {
                ksGuaranteeAlipayDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                thirdDutSetLogList.add(dutRenewSetLog);
                if (tipTypeList.contains(PaymentDutType.PAY_CHANNEL_KS_GUARANTEE_ALIPAY)) {
                    continue;
                }
                tipTypeList.add(PaymentDutType.PAY_CHANNEL_KS_GUARANTEE_ALIPAY);
            }
            if (autoRenewDutType != null && autoRenewDutType.isKsGuaranteeWechatDutType()) {
                ksGuaranteeWechatDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                thirdDutSetLogList.add(dutRenewSetLog);
                if (tipTypeList.contains(PaymentDutType.PAY_CHANNEL_KS_GUARANTEE_WECHAT)) {
                    continue;
                }
                tipTypeList.add(PaymentDutType.PAY_CHANNEL_KS_GUARANTEE_WECHAT);
            }
            if (autoRenewDutType != null && autoRenewDutType.isDouYinGuaranteeAlipayDutType()) {
                douYinGuaranteeAlipayDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                commonDutSetLogList.add(dutRenewSetLog);
                if (tipTypeList.contains(PaymentDutType.PAY_CHANNEL_DOU_YIN_GUARANTEE_ALIPAY)) {
                    continue;
                }
                tipTypeList.add(PaymentDutType.PAY_CHANNEL_DOU_YIN_GUARANTEE_ALIPAY);
            }
        }

        PayTypeInfoVO mobileDutTypeInfoVO = new PayTypeInfoVO();
        PayTypeInfoVO weixinDutTypeInfoVO = new PayTypeInfoVO();
        PayTypeInfoVO aliDutTypeInfoVO = new PayTypeInfoVO();
        PayTypeInfoVO baiduDutTypeInfoVO = new PayTypeInfoVO();
        PayTypeInfoVO douyinDutTypeInfoVO = new PayTypeInfoVO();
        PayTypeInfoVO appleDutTypeInfoVO = new PayTypeInfoVO();
        PayTypeInfoVO cardDutTypeInfoVO = new PayTypeInfoVO();
        PayTypeInfoVO googleDutTypeInfoVO = new PayTypeInfoVO();
        PayTypeInfoVO payPalDutTypeInfoVO = new PayTypeInfoVO();
        PayTypeInfoVO ksGuaranteeAlipayDutTypeInfoVO = new PayTypeInfoVO();
        PayTypeInfoVO ksGuaranteeWechatDutTypeInfoVO = new PayTypeInfoVO();
        PayTypeInfoVO douYinGuaranteeAlipayDutTypeInfoVO = new PayTypeInfoVO();

        // 首先代扣的方式
        Integer firstDutType = null;
        Integer firstAgreementNo = null;
        // 首先代扣的方式对应的代扣价格
        Integer firstRenewPrice = null;
        DutUserNew firstDutUserNew = null;
        RestrictionStrategyInfo restrictionStrategyInfo = dutUserService.restrictDateToCancel(dutUserNews, agreementType);

        renewInfoVO.setAgreementType(agreementType);
        // 已开通第三方扣费支付方式
        if (CollectionUtils.isNotEmpty(thirdDutSetLogList)) {
            Collections.sort(thirdDutSetLogList, (o1, o2) -> o2.getOperateTime().compareTo(o1.getOperateTime()));
            firstDutType = thirdDutSetLogList.get(0).getType();
            firstAgreementNo = thirdDutSetLogList.get(0).getAgreementNo();
            DutUserNew dutUserNew = getDutUserNewByAgreementNoAndDutType(dutUserNews, firstAgreementNo, firstDutType);
            if (dutUserNew != null) {
                if (iapDutTypeList.contains(firstDutType) && dutUserNew.getOperateTime() != null) {
                    renewInfoVO.setDataUpdateTime(DateHelper.getDateStringByPattern(dutUserNew.getOperateTime(), "yyyy-MM-dd HH:mm:ss"));
                }
                firstRenewPrice = autoRenewService.getDisplayRenewPrice(dutUserNew);
                firstDutUserNew = dutUserNew;
            }
        }

        //开通新话费自动续费
        if (AgreementTypeEnum.AUTO_RENEW.getValue() == agreementType && partnerUserSignRecord != null && firstDutType == null && firstRenewPrice == null) {
            firstDutType = 0;
            firstAgreementNo = 0;
            firstRenewPrice = partnerUserSignRecord.getRenewPrice();
        }
        // 手机话费优先排到前面
        LocalDate restrictDateToCancel = restrictionStrategyInfo == null ? null : restrictionStrategyInfo.getRestrictEndDate();
        if (CollectionUtils.isNotEmpty(mobileDutTypeInfoList)) {
            mobileDutTypeInfoVO.setTipType(KeyValuePair.from(Constants.TIPTYPE_MOBILE, tipTypeDescMap));
            mobileDutTypeInfoVO.setDutType(getNameFromType(mobileDutTypeInfoList));
            mobileDutTypeInfoVO.setDutTypeInfo(mobileDutTypeInfoList);
            mobileDutTypeInfoVO.setDutTimeTips(buildDutTimeTips(restrictDateToCancel));
            mobileDutTypeInfoVO.setPayChannel(PayChannelEnum.PAY_CHANNEL_MOBILE.getValue());
            if (firstDutType == null && firstRenewPrice == null) {
                firstDutType = mobileDutTypeInfoList.get(0).getDutType();
                firstAgreementNo = mobileDutTypeInfoList.get(0).getAgreementNo();
                //重复查询
                DutUserNew dutUserMobile = userAgreementService.getByAgreementNoOrDutTypeForManagement(userId, firstAgreementNo, firstDutType, vipType);
                firstDutUserNew = dutUserMobile;
                if (dutUserMobile.getRenewPrice() != null && dutUserMobile.getRenewPrice() > 0) {
                    firstRenewPrice = dutUserMobile.getRenewPrice();
                } else {
                    firstRenewPrice = autoRenewService.getDisplayRenewPrice(dutUserMobile);
                    LOGGER.info("[get mobile defaultPrice progress][vipType:{}, dutType:{}, agreementNo:{}, firstRenewPrice:{}]",
                        vipType, dutUserMobile.getType(), dutUserMobile.getAgreementNo(), firstRenewPrice);
                }
            }
        }

        //升级阶段
        UpgradeStageEnum upgradeStage = UpgradeStageEnum.NON_UPGRADE;
        Integer upgradeRenewPrice = null;
        AutoRenewUpgradeConfig autoRenewUpgradeConfig = null;
        DutUserNew dutUserCommon = null;
        String firstSkuId = null;
        if (firstDutType == null && firstRenewPrice == null && firstCommonDutType != null) {
            firstDutType = firstCommonDutType;
            firstAgreementNo = firstCommonAgreementNo;
            if (isUpgrade) {
                if (firstAgreementNo != null) {
                    AgreementNoInfo firstAgreementNoInfo = agreementNoInfoManager.getById(firstAgreementNo);
                    autoRenewUpgradeConfig = autoRenewUpgradeConfigManager.getByVipType(firstAgreementNoInfo.getSourceVipType(),
                        firstAgreementNoInfo.getVipType(), firstAgreementNoInfo.getPriority());
                } else {
                    AutoRenewDutType firstAutoRenewDutType = autoRenewDutTypeManager.getByDutType(firstDutType);
                    autoRenewUpgradeConfig = autoRenewUpgradeConfigManager.getByVipType(firstAutoRenewDutType.getSourceVipType(),
                        firstAutoRenewDutType.getVipType(), firstAutoRenewDutType.getPriority());
                }
                dutUserCommon = getDutUserNewByDutTypeAndSourceVipType(dutUserNews,  firstDutType, autoRenewUpgradeConfig.getSourceVipType());
            } else {
                dutUserCommon = userAgreementService.getByAgreementNoOrDutTypeForManagement(userId, firstCommonAgreementNo, firstCommonDutType, vipType);
            }
            firstDutUserNew = dutUserCommon;
            firstAgreementNo = dutUserCommon.getAgreementNo();

            if (firstAgreementNo != null) {
                firstSkuId = agreementNoInfoManager.getSkuIdByAgreementNo(firstCommonAgreementNo);
            } else {
                firstSkuId = agreementNoInfoManager.getSkuIdByDutTypeAndAmount(firstDutType, firstDutUserNew.getAmount());
            }
            if (isUpgrade) {
                VipType sourceVipTypeObj = vipTypeManager.getVipTypeById(autoRenewUpgradeConfig.getSourceVipType());
                String sourceVipTypeName = sourceVipTypeObj.getName();
                Integer monthRenewPrice = autoRenewUpgradeConfig.getMonthRenewPrice();
                Integer dayRenewPrice = autoRenewUpgradeConfig.getDayRenewPrice();
                Integer finalRenewPrice = autoRenewUpgradeConfig.getFinalRenewPrice();

                Integer upgradeDays = customVipInfoClient.getUpgradeDays(userId, autoRenewUpgradeConfig.getSourceVipType(), autoRenewUpgradeConfig.getTargetVipType());
                UserUpgradeInfo userUpgradeInfo = null;
                if (upgradeDays == null) {
                    upgradeDays = DateHelper.daysInCurrentMonth();
                    userUpgradeInfo = UserUpgradeInfo.buildFinalStage(autoRenewUpgradeConfig.getFinalProductCode(), autoRenewUpgradeConfig.getFinalSkuId(), autoRenewUpgradeConfig.getFinalRenewPrice());
                } else {
                    userUpgradeInfo = UpgradeAutoRenewUtils.calcUpgradeInfo(dutUserCommon, autoRenewUpgradeConfig, upgradeDays);
                }
                upgradeStage = userUpgradeInfo.getUpgradeStage();
                firstRenewPrice = userUpgradeInfo.getRenewPrice();
                firstSkuId = userUpgradeInfo.getSkuId();
                if (upgradeStage == UpgradeStageEnum.FIRST_STAGE) {
                    upgradeRenewPrice = monthRenewPrice;
                    renewInfoVO.setProductName(getUpgradeProductName(vipTypeName, sourceVipTypeName, newVersion));
                } else if (upgradeStage == UpgradeStageEnum.SECOND_STAGE) {
                    upgradeRenewPrice = dayRenewPrice;
                    renewInfoVO.setProductName(newVersion ? getUpgradeProductName(vipTypeName, sourceVipTypeName, newVersion)
                        : String.format("%s升级%s自动续费", sourceVipTypeName, vipTypeName));
                } else {
                    upgradeRenewPrice = finalRenewPrice;
                    renewInfoVO.setProductName(
                        newVersion ? getUpgradeProductName(vipTypeName, sourceVipTypeName, newVersion) : vipTypeName + "1个月");
                }
                if (newVersion) {
                    double monthPrice = monthRenewPrice * 1.0 / 100;
                    double dayPrice = dayRenewPrice * 1.0 / 100;
                    double finalPrice = finalRenewPrice * 1.0 / 100;
                    String tipText = String.format(DUT_PRICE_TIP_TEXT, sourceVipTypeName, vipTypeName, monthPrice, sourceVipTypeName, dayPrice, sourceVipTypeName, finalPrice, vipTypeName);
                    DutPriceTipInfo dutPriceTipInfo = DutPriceTipInfo.builder()
                        .nextDutPrice(NumberFormatUtils.formatToStr(firstRenewPrice, "#.##"))
                        .upgradeableRightDay(upgradeDays < 0 ? Constants.DEFAULT_UPGRADEABLE_RIGHT_DAY : upgradeDays)
                        .tipText(tipText)
                        .build();
                    renewInfoVO.setDutPriceTipInfo(dutPriceTipInfo);
                }
            } else {
                firstRenewPrice = autoRenewService.getDisplayRenewPrice(dutUserCommon);
            }
        }

        // 返回首次代扣方式及代扣价格信息
        FirstDutInfo firstDutInfo = new FirstDutInfo();
        boolean fistDutIsPriceInsuredPeriod = false;
        if (firstDutType != null && firstRenewPrice != null) {
            firstDutInfo.setFirstDutType(firstDutType);
            firstDutInfo.setFirstAgreementNo(firstAgreementNo);
            firstDutInfo.setFirstRenewPrice(firstRenewPrice);
            firstDutInfo.setSkuId(firstSkuId);
            fistDutIsPriceInsuredPeriod = isPriceInsuredPeriod(firstDutType);
        }
        //续费折扣信息
        UserRenewDiscountInfo userRenewDiscountInfo = null;
        if (dutUserCommon != null && dutUserCommon.getAgreementNo() != null) {
            userRenewDiscountInfo = userAgreementService.canEnjoyDiscountPrice(dutUserCommon);
        }
        for (Integer tipType : tipTypeList) {
            if (tipType == Constants.TIPTYPE_WEIXIN) {
                weixinDutTypeInfoVO.setTipType(KeyValuePair.from(Constants.TIPTYPE_WEIXIN, tipTypeDescMap));
                weixinDutTypeInfoVO.setDutType(getNameFromType(weixinDutTypeInfoList));
                weixinDutTypeInfoVO.setDutTypeInfo(weixinDutTypeInfoList);
                weixinDutTypeInfoVO.setDutTimeTips(buildDutTimeTips(restrictDateToCancel));
                weixinDutTypeInfoVO.setPayChannel(PayChannelEnum.PAY_CHANNEL_WECHAT.getValue());
                continue;
            }
            if (tipType == Constants.TIPTYPE_ALIPAY) {
                aliDutTypeInfoVO.setTipType(KeyValuePair.from(Constants.TIPTYPE_ALIPAY, tipTypeDescMap));
                aliDutTypeInfoVO.setDutType(getNameFromType(aliDutTypeInfoList));
                aliDutTypeInfoVO.setDutTypeInfo(aliDutTypeInfoList);
                aliDutTypeInfoVO.setDutTimeTips(buildDutTimeTips(restrictDateToCancel));
                aliDutTypeInfoVO.setPayChannel(PayChannelEnum.PAY_CHANNEL_ALIPAY.getValue());
                continue;
            }

            if (tipType == Constants.TIPTYPE_BAIDU) {
                baiduDutTypeInfoVO.setTipType(KeyValuePair.from(Constants.TIPTYPE_BAIDU, tipTypeDescMap));
                baiduDutTypeInfoVO.setDutType(getNameFromType(baiduDutTypeInfoList));
                baiduDutTypeInfoVO.setDutTypeInfo(baiduDutTypeInfoList);
                baiduDutTypeInfoVO.setDutTimeTips(buildDutTimeTips(restrictDateToCancel));
                baiduDutTypeInfoVO.setPayChannel(PayChannelEnum.PAY_CHANNEL_BAIDUPAY.getValue());
                continue;
            }

            if (tipType == PaymentDutType.PAY_CHANNEL_DOU_YIN) {
                douyinDutTypeInfoVO.setTipType(KeyValuePair.from(PaymentDutType.PAY_CHANNEL_DOU_YIN, tipTypeDescMap));
                douyinDutTypeInfoVO.setDutType(getNameFromType(douyinDutTypeInfoList));
                douyinDutTypeInfoVO.setDutTypeInfo(douyinDutTypeInfoList);
                douyinDutTypeInfoVO.setDutTimeTips(buildDutTimeTips(restrictDateToCancel));
                douyinDutTypeInfoVO.setPayChannel(PaymentDutType.PAY_CHANNEL_DOU_YIN);
                continue;
            }

            if (tipType == Constants.TIPTYPE_APPLE) {
                appleDutTypeInfoVO.setTipType(KeyValuePair.from(Constants.TIPTYPE_APPLE, tipTypeDescMap));
                appleDutTypeInfoVO.setDutType(getNameFromType(appleDutTypeInfoList));
                appleDutTypeInfoVO.setDutTypeInfo(appleDutTypeInfoList);
                appleDutTypeInfoVO.setDutTimeTips(buildDutTimeTips(restrictDateToCancel));
                appleDutTypeInfoVO.setPayChannel(PayChannelEnum.PAY_CHANNEL_IAP.getValue());

                String cancelTip = "根据苹果公司规定,您需要到苹果设备上，登录您开通自动续费服务的Apple ID来取消续费。";
                String methodTip = "取消方法:\r\n通过手机【设置】进入【iTunesStore与App Store】>>选择【Apple ID】" +
                    ">>点击【查看Apple ID】>>在账户设置页面点击【订阅管理】>>取消爱奇艺会员的订阅即可。";
                CancelTipInfo cancelTipInfo = CancelTipInfo.builder()
                    .cancelTips(cancelTip)
                    .methodTips(methodTip).build();
                appleDutTypeInfoVO.setCancelTips(cancelTipInfo);
                continue;
            }

            if (tipType == Constants.TIPTYPE_GOOGLE) {
                googleDutTypeInfoVO.setTipType(KeyValuePair.from(Constants.TIPTYPE_GOOGLE, tipTypeDescMap));
                googleDutTypeInfoVO.setDutType(getNameFromType(googleDutTypeInfoList));
                googleDutTypeInfoVO.setDutTypeInfo(googleDutTypeInfoList);
                googleDutTypeInfoVO.setDutTimeTips(buildDutTimeTips(restrictDateToCancel));
                googleDutTypeInfoVO.setPayChannel(PayChannelEnum.PAY_CHANNEL_GOOGLE_BILLING.getValue());
                String cancelTip;
                if (Constants.ZH_TW_LANG.equals(lang)) {
                    cancelTip = "尊敬的用戶，若您要取消自動續費，請前往Google Play應用商店，登錄您開通自動續費的Google Play帳號，在My Apps頁面查看並取消訂閱。";
                } else {
                    cancelTip = "尊敬的用户，若您要取消自动续费，请前往Google Play应用商店，登录您开通自动续费的Google Play账号，在My Apps页面查看并取消订阅。";
                }
                CancelTipInfo cancelTipInfo = CancelTipInfo.builder()
                    .cancelTips(cancelTip)
                    .build();
                googleDutTypeInfoVO.setCancelTips(cancelTipInfo);
                continue;
            }

            if (tipType == Constants.TIPTYPE_PAYPAL) {
                payPalDutTypeInfoVO.setTipType(KeyValuePair.from(Constants.TIPTYPE_PAYPAL, tipTypeDescMap));
                payPalDutTypeInfoVO.setDutType(getNameFromType(payPalDutTypeInfoList));
                payPalDutTypeInfoVO.setDutTypeInfo(payPalDutTypeInfoList);
                payPalDutTypeInfoVO.setDutTimeTips(buildDutTimeTips(restrictDateToCancel));
                payPalDutTypeInfoVO.setPayChannel(PayChannelEnum.PAY_CHANNEL_PAY_PAL.getValue());
                continue;
            }

            if (tipType == Constants.TIPTYPE_CARD) {
                cardDutTypeInfoVO.setTipType(KeyValuePair.from(Constants.TIPTYPE_CARD, tipTypeDescMap));
                cardDutTypeInfoVO.setDutTimeTips(buildDutTimeTips(restrictDateToCancel));
                cardDutTypeInfoVO.setDutType(getNameFromType(bankCardDutTypeInfoList));
                cardDutTypeInfoVO.setDutTypeInfo(bankCardDutTypeInfoList);
                cardDutTypeInfoVO.setPayChannel(PayChannelEnum.PAY_CHANNEL_BANKCARD.getValue());
            }
            if (tipType == PaymentDutType.PAY_CHANNEL_KS_GUARANTEE_ALIPAY) {
                ksGuaranteeAlipayDutTypeInfoVO.setTipType(KeyValuePair.from(PaymentDutType.PAY_CHANNEL_KS_GUARANTEE_ALIPAY, tipTypeDescMap));
                ksGuaranteeAlipayDutTypeInfoVO.setDutTimeTips(buildDutTimeTips(restrictDateToCancel));
                ksGuaranteeAlipayDutTypeInfoVO.setDutType(getNameFromType(ksGuaranteeAlipayDutTypeInfoList));
                ksGuaranteeAlipayDutTypeInfoVO.setDutTypeInfo(ksGuaranteeAlipayDutTypeInfoList);
                ksGuaranteeAlipayDutTypeInfoVO.setPayChannel(PaymentDutType.PAY_CHANNEL_KS_GUARANTEE_ALIPAY);
            }
            if (tipType == PaymentDutType.PAY_CHANNEL_KS_GUARANTEE_WECHAT) {
                ksGuaranteeWechatDutTypeInfoVO.setTipType(KeyValuePair.from(PaymentDutType.PAY_CHANNEL_KS_GUARANTEE_WECHAT, tipTypeDescMap));
                ksGuaranteeWechatDutTypeInfoVO.setDutTimeTips(buildDutTimeTips(restrictDateToCancel));
                ksGuaranteeWechatDutTypeInfoVO.setDutType(getNameFromType(ksGuaranteeWechatDutTypeInfoList));
                ksGuaranteeWechatDutTypeInfoVO.setDutTypeInfo(ksGuaranteeWechatDutTypeInfoList);
                ksGuaranteeWechatDutTypeInfoVO.setPayChannel(PaymentDutType.PAY_CHANNEL_KS_GUARANTEE_WECHAT);
            }
            if (tipType == PaymentDutType.PAY_CHANNEL_DOU_YIN_GUARANTEE_ALIPAY) {
                douYinGuaranteeAlipayDutTypeInfoVO.setTipType(KeyValuePair.from(PaymentDutType.PAY_CHANNEL_DOU_YIN_GUARANTEE_ALIPAY, tipTypeDescMap));
                douYinGuaranteeAlipayDutTypeInfoVO.setDutTimeTips(buildDutTimeTips(restrictDateToCancel));
                douYinGuaranteeAlipayDutTypeInfoVO.setDutType(getNameFromType(douYinGuaranteeAlipayDutTypeInfoList));
                douYinGuaranteeAlipayDutTypeInfoVO.setDutTypeInfo(douYinGuaranteeAlipayDutTypeInfoList);
                douYinGuaranteeAlipayDutTypeInfoVO.setPayChannel(PaymentDutType.PAY_CHANNEL_DOU_YIN_GUARANTEE_ALIPAY);
            }
        }

        //老数据处理，log中无记录，续费表中有记录，根据扣费规则，直接返回第一个扣费类型
        PayTypeInfoVO dutMixPayTypeInfo = new PayTypeInfoVO();
        if (dutRenewSetLogs.isEmpty() && CollectionUtils.isNotEmpty(dutUserNews)) {
            DutUserNew dutUserNew = dutUserNews.iterator().next();
            Integer type = dutUserNew.getType();
            Integer agreementNo = dutUserNew.getAgreementNo();
            Integer amount = dutUserNew.getAmount();
            AgreementNoInfo agreementNoInfo = null;
            if (agreementNo != null) {
                agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
                type = agreementNoInfo != null ? agreementNoInfo.getDutType() : type;
            }
            AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(type);
            if (autoRenewDutType != null) {
                dutMixPayTypeInfo.setDutType(Lists.newArrayList(KeyValuePair.from(type, autoRenewDutType.getName())));
                dutMixPayTypeInfo.setDutTypeInfo(Lists.newArrayList(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount)));
            }
            if (mobileDutTypeList.contains(type)) {
                dutMixPayTypeInfo.setTipType(KeyValuePair.from(Constants.TIPTYPE_MOBILE, tipTypeDescMap));
                dutMixPayTypeInfo.setPayChannel(PayChannelEnum.PAY_CHANNEL_MOBILE.getValue());
                mobileDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                tipTypeList.add(Constants.TIPTYPE_MOBILE);
            } else if (alipayDutTypeList.contains(type) || overseaAlipayDutTypeList.contains(type)) {
                dutMixPayTypeInfo.setTipType(KeyValuePair.from(Constants.TIPTYPE_ALIPAY, tipTypeDescMap));
                dutMixPayTypeInfo.setPayChannel(PayChannelEnum.PAY_CHANNEL_ALIPAY.getValue());
                aliDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                tipTypeList.add(Constants.TIPTYPE_ALIPAY);
            } else if (baiduPayDutTypeList.contains(type)) {
                dutMixPayTypeInfo.setTipType(KeyValuePair.from(Constants.TIPTYPE_BAIDU, tipTypeDescMap));
                dutMixPayTypeInfo.setPayChannel(PayChannelEnum.PAY_CHANNEL_BAIDUPAY.getValue());
                baiduDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                tipTypeList.add(Constants.TIPTYPE_BAIDU);
            } else if (wechatDutTypeList.contains(type) || overseaWechatDutTypeList.contains(type)) {
                dutMixPayTypeInfo.setTipType(KeyValuePair.from(Constants.TIPTYPE_WEIXIN, tipTypeDescMap));
                dutMixPayTypeInfo.setPayChannel(PayChannelEnum.PAY_CHANNEL_WECHAT.getValue());
                weixinDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                tipTypeList.add(Constants.TIPTYPE_WEIXIN);
            } else if (bankCardDutTypeList.contains(type)) {
                dutMixPayTypeInfo.setTipType(KeyValuePair.from(Constants.TIPTYPE_CARD, tipTypeDescMap));
                dutMixPayTypeInfo.setPayChannel(PayChannelEnum.PAY_CHANNEL_BANKCARD.getValue());
                bankCardDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                tipTypeList.add(Constants.TIPTYPE_CARD);
            } else if (googleDutTypeList.contains(type)) {
                dutMixPayTypeInfo.setTipType(KeyValuePair.from(Constants.TIPTYPE_GOOGLE, tipTypeDescMap));
                dutMixPayTypeInfo.setPayChannel(PayChannelEnum.PAY_CHANNEL_GOOGLE_BILLING.getValue());
                googleDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                tipTypeList.add(Constants.TIPTYPE_GOOGLE);
            } else if (payPalDutTypeList.contains(type)) {
                dutMixPayTypeInfo.setTipType(KeyValuePair.from(Constants.TIPTYPE_PAYPAL, tipTypeDescMap));
                dutMixPayTypeInfo.setPayChannel(PayChannelEnum.PAY_CHANNEL_PAY_PAL.getValue());
                payPalDutTypeInfoList.add(ManagementDutTypeInfo.buildFrom(autoRenewDutType, agreementNoInfo, amount));
                tipTypeList.add(Constants.TIPTYPE_PAYPAL);
            }
            dutMixPayTypeInfo.setDutTimeTips(buildDutTimeTips(restrictDateToCancel));
        }

        List<PayTypeInfoVO> dutTypesByDutSequence = Lists.newArrayList();
        if (appleDutTypeInfoVO.notNull()) {
            dutTypesByDutSequence.add(appleDutTypeInfoVO);
        }
        if (ksGuaranteeAlipayDutTypeInfoVO.notNull()) {
            dutTypesByDutSequence.add(ksGuaranteeAlipayDutTypeInfoVO);
        }
        if (ksGuaranteeWechatDutTypeInfoVO.notNull()) {
            dutTypesByDutSequence.add(ksGuaranteeWechatDutTypeInfoVO);
        }
        if (partnerUserSignRecord != null) {
            dutTypesByDutSequence.add(NewMobileRenewComponent.buildNewMobilePayInfo(partnerUserSignRecord));
        }
        if (mobileDutTypeInfoVO.notNull()) {
            dutTypesByDutSequence.add(mobileDutTypeInfoVO);
        }
        List<Integer> commonPayChannelList = tipTypeList.stream().filter((this::isCommonChannel)).collect(Collectors.toList());
        for (Integer tipType : commonPayChannelList) {
            if (tipType == Constants.TIPTYPE_WEIXIN && weixinDutTypeInfoVO.notNull()) {
                dutTypesByDutSequence.add(weixinDutTypeInfoVO);
                continue;
            }
            if (tipType == Constants.TIPTYPE_ALIPAY && aliDutTypeInfoVO.notNull()) {
                dutTypesByDutSequence.add(aliDutTypeInfoVO);
                continue;
            }
            if (tipType == Constants.TIPTYPE_BAIDU && baiduDutTypeInfoVO.notNull()) {
                dutTypesByDutSequence.add(baiduDutTypeInfoVO);
                continue;
            }
            if (tipType == PaymentDutType.PAY_CHANNEL_DOU_YIN && douyinDutTypeInfoVO.notNull()) {
                dutTypesByDutSequence.add(douyinDutTypeInfoVO);
                continue;
            }
            if (tipType == PaymentDutType.PAY_CHANNEL_DOU_YIN_GUARANTEE_ALIPAY && douYinGuaranteeAlipayDutTypeInfoVO.notNull()) {
                dutTypesByDutSequence.add(douYinGuaranteeAlipayDutTypeInfoVO);
                continue;
            }
            if (tipType == Constants.TIPTYPE_CARD && cardDutTypeInfoVO.notNull()) {
                dutTypesByDutSequence.add(cardDutTypeInfoVO);
                continue;
            }
            if (tipType == Constants.TIPTYPE_GOOGLE && googleDutTypeInfoVO.notNull()) {
                dutTypesByDutSequence.add(googleDutTypeInfoVO);
                continue;
            }
            if (tipType == Constants.TIPTYPE_PAYPAL && payPalDutTypeInfoVO.notNull()) {
                dutTypesByDutSequence.add(payPalDutTypeInfoVO);
            }
        }
        if (dutTypesByDutSequence.isEmpty() && dutMixPayTypeInfo.notNull()) {
            dutTypesByDutSequence.add(dutMixPayTypeInfo);
        }
        resetPayTypeInfoDutTimeTips(dutTypesByDutSequence, vipInfo, userId, fistDutIsPriceInsuredPeriod, userRenewDiscountInfo, agreementType);
        renewInfoVO.setPayTypeInfo(dutTypesByDutSequence);
        //此处为到期时间
        if (deadline != null) {
            renewInfoVO.setDeadline(DateHelper.getDateStringByPattern(deadline, "yyyy-MM-dd"));
        }
        String allDutTypeDutTimeTips = CloudConfigUtil.managementPageAllDutTimeTips(lang, agreementType);
        renewInfoVO.setAllDutTypeDutTimeTips(allDutTypeDutTimeTips);
        setDoPayTime(agreementType, firstDutUserNew, deadline, mobileDutTypeInfoList, firstAgreementNo, firstDutType, thirdDutSetLogList, dutUserNews, renewInfoVO);
        setBindInfo(renewInfoVO, firstDutUserNew);

        Integer renewPrice = 0;
        Integer priceDutType = firstDutType;
        Integer priceAgreementNo = firstAgreementNo;
        Integer amount;
        if (CollectionUtils.isNotEmpty(tipTypeList)) {
            ManagementDutTypeInfo managementDutTypeInfo = null;
            if (CollectionUtils.isNotEmpty(thirdDutSetLogList)) {
                if (iapDutTypeList.contains(thirdDutSetLogList.get(0).getType())) {
                    managementDutTypeInfo = appleDutTypeInfoList.get(0);
                } else if (googleDutTypeList.contains(thirdDutSetLogList.get(0).getType())) {
                    managementDutTypeInfo = googleDutTypeInfoList.get(0);
                } else if (payPalDutTypeList.contains(thirdDutSetLogList.get(0).getType())) {
                    managementDutTypeInfo = payPalDutTypeInfoList.get(0);
                } else if (ksGuaranteeAliPayDutTypeList.contains(thirdDutSetLogList.get(0).getType())) {
                    managementDutTypeInfo = ksGuaranteeAlipayDutTypeInfoList.get(0);
                } else if (ksGuaranteeWeChatDutTypeList.contains(thirdDutSetLogList.get(0).getType())) {
                    managementDutTypeInfo = ksGuaranteeWechatDutTypeInfoList.get(0);
                }
            } else if (partnerUserSignRecord != null) {
                renewPrice = partnerUserSignRecord.getRenewPrice();
            } else if (CollectionUtils.isNotEmpty(mobileDutTypeInfoList)) {
                managementDutTypeInfo = mobileDutTypeInfoList.get(0);
            } else if (CollectionUtils.isNotEmpty(commonDutSetLogList)) {
                if (wechatDutTypeList.contains(commonDutSetLogList.get(0).getType())
                    || overseaWechatDutTypeList.contains(commonDutSetLogList.get(0).getType())) {
                    managementDutTypeInfo = weixinDutTypeInfoList.get(0);
                } else if (alipayDutTypeList.contains(commonDutSetLogList.get(0).getType())
                    || overseaAlipayDutTypeList.contains(commonDutSetLogList.get(0).getType())) {
                    managementDutTypeInfo = aliDutTypeInfoList.get(0);
                } else if (baiduPayDutTypeList.contains(commonDutSetLogList.get(0).getType())) {
                    managementDutTypeInfo = baiduDutTypeInfoList.get(0);
                } else if (douyinPayDutTypeList.contains(commonDutSetLogList.get(0).getType())) {
                    managementDutTypeInfo = douyinDutTypeInfoList.get(0);
                } else if (bankCardDutTypeList.contains(commonDutSetLogList.get(0).getType())) {
                    managementDutTypeInfo = bankCardDutTypeInfoList.get(0);
                } else if (douyinGuaranteeAliPayDutTypeList.contains(commonDutSetLogList.get(0).getType())) {
                    managementDutTypeInfo = douYinGuaranteeAlipayDutTypeInfoList.get(0);
                }
            }
            DutUserNew dutUserNew = null;
            if (managementDutTypeInfo != null) {
                dutUserNew = getDutUserNewByAgreementNoAndDutType(dutUserNews, managementDutTypeInfo.getAgreementNo(), managementDutTypeInfo.getDutType());
            }

            if (dutUserNew != null) {
                if (isUpgrade) {
                    renewPrice = firstRenewPrice;
                    priceDutType = dutUserNew.getType();
                    priceAgreementNo = dutUserNew.getAgreementNo();
                } else {
                    priceDutType = dutUserNew.getType();
                    priceAgreementNo = dutUserNew.getAgreementNo();
                    if (dutUserNew.getRenewPrice() != null && dutUserNew.getVipType() != null
                        && vipType != null && vipType.equals(dutUserNew.getVipType())) {
                        renewPrice = userAgreementService.getAutoRenewDisplayRenewPrice(dutUserNew);
                        dutUserNew.setRenewPrice(renewPrice);
                    }
                }
            }

            if (renewPrice > 0) {
                DecimalFormat df = new DecimalFormat("#.##");
                df.setRoundingMode(RoundingMode.HALF_UP);
                renewInfoVO.setPrice(df.format(renewPrice * 1.0 / 100));
            } else {
                Integer price = null;
                try {
                    if (isIosCNDutType(dutUserNew, iapDutTypeList)) {
                        amount = (null != dutUserNew && null != dutUserNew.getAmount())
                            ? dutUserNew.getAmount() : AMOUNT_OF_COMMON_AUTORENEW;
                        price = dutService.calculateRenewPrice(dutUserNew.getType(),
                            new AutorenewRequest(amount, vipType, Constants.QIYUE_ORDER_AUTORENEW_FIRST_LARGE, agreementType));
                    } else {
                        if (dutUserNew != null && dutUserNew.getAmount() != null) {
                            amount = dutUserNew.getAmount();
                        } else {
                            DutUserNew priceDutUserNew = userAgreementService.getByAgreementNoOrDutType(userId, priceAgreementNo, priceDutType, vipType);
                            amount = priceDutUserNew != null ? priceDutUserNew.getAmount() : AMOUNT_OF_COMMON_AUTORENEW;
                        }
                        price = dutService.calculateRenewPrice(priceDutType,
                            new AutorenewRequest(amount, vipType, Constants.QIYUE_ORDER_AUTORENEW_FIRST_LARGE, agreementType));
                    }
                } catch (Exception e) {
                    LOGGER.error("[autoRenewStatusCk] [getPriceError]", e);
                }
                if (price == null) {
                    price = paymentDutTypeManager.getCommonAutorenewDutPrice(vipType.intValue(), AMOUNT_OF_COMMON_AUTORENEW);
                }
                DecimalFormat df = new DecimalFormat("#.##");
                df.setRoundingMode(RoundingMode.HALF_UP);
                String priceTmp = df.format(price * 1.0 / 100);
                renewInfoVO.setPrice(priceTmp);
            }
            //IOS大陆自动续费展示代扣价格
            putMainlandPrice(dutUserNew, renewInfoVO);
            if (AutoRenewManagementProcessor.PAGE_TYPE_NO_MIX.equals(pageType)) {
                renewInfoVO.setActPeriodTips(null);
            } else if (AutoRenewManagementProcessor.PAGE_TYPE_MIX.equals(pageType)) {
                UserDutTypeInfo priceInsuredPeriodInfo = buildPriceInsuredPeriodInfo(firstDutType, fistDutIsPriceInsuredPeriod);
                renewInfoVO.setUserDutTypeInfo(priceInsuredPeriodInfo);
                renewInfoVO.setActPeriodTips(makeActPeriodTipsOfMix(dutUserNew, firstDutType, isUpgrade, fistDutIsPriceInsuredPeriod,
                    upgradeRenewPrice, upgradeStage, priceInsuredPeriodInfo, userRenewDiscountInfo));
            }
        }

        if (isDutUser) {
            if (firstPayTypeIsNewMobile(renewInfoVO)) {
                vipInfo.setAmount(partnerUserSignRecord.getAmount());
                vipInfo.setPid(partnerUserSignRecord.getProductCode());
            } else {
                DutUserNew dutUserNew = userAgreementService.getByAgreementNoOrDutType(userId, priceAgreementNo, priceDutType, vipType);
                Integer dutAmount = dutUserNew != null && dutUserNew.getAmount() != null ? dutUserNew.getAmount() : AMOUNT_OF_COMMON_AUTORENEW;
                vipInfo.setAmount(dutAmount);
                String pid = null;
                if (dutUserNew != null && dutUserNew.getAgreementNo() != null) {
                    AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(dutUserNew.getAgreementNo());
                    AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
                    pid = agreementTemplate != null ? agreementTemplate.getPid() : null;
                } else {
                    AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(firstDutType);
                    pid = autoRenewDutType != null ? autoRenewDutType.getProductCode() : null;
                }
                vipInfo.setPid(pid);
            }
        }
        boolean isOverseaAutorenew = dutUserNews.stream().anyMatch(
            dutUserNew -> overseaAlipayDutTypeList.contains(dutUserNew.getType())
                || overseaWechatDutTypeList.contains(dutUserNew.getType())
        );
        boolean normalAgreement = true;
        if (firstAgreementNo != null) {
            AgreementNoInfo firstAgreementNoInfo = agreementNoInfoManager.getById(firstAgreementNo);
            normalAgreement = firstAgreementNoInfo != null && firstAgreementNoInfo.defaultNo();
        }
        if (!isOverseaAutorenew && needGenPureSignUrl && normalAgreement) {
            geneSupportDutType(servletRequest, renewInfoVO, tipTypeList, vipType, platform, user, fc, fv, generateAutoRenewInfoDto.getAmount());
        }
        renewInfoVO.setRuleTips("1.以下是您开通自动续费的全部支付方式，会员到期前我们将按照从上到下的顺序依次尝试为您扣除下一周期" +
            "的会员费用，并为您自动续费VIP；\n\n2.您可以在这里添加（苹果设备暂不支持）或删除支付方式，删除后不再通过该支付方式扣款。");
        calOriginalPrice(renewInfoVO, vipType, priceDutType, user, isUpgrade, upgradeStage, vipTypeName, partnerUserSignRecord, newVersion, agreementType);

        renewInfoVO.setFirstDutInfo(firstDutInfo);
        if (!newVersion) {
            setDutFailReason(renewInfoVO, deadline, vipType, userId, firstDutType,
                appleDutTypeInfoList.size() > 0, googleDutTypeInfoList.size() > 0);
        }
        constructAvailablePayChannels(renewInfoVO, userId, bizType);
		return renewInfoVO;
	}

    /**
     * 构建可用的支付渠道信息
     *
     * @param renewInfoVO 续费信息VO
     * @param uid 用户ID
     * @param bizType 业务类型
     */
    private void constructAvailablePayChannels(RenewInfoVO renewInfoVO, Long uid, Integer bizType) {
        // 基础验证
        if (!isValidForPayChannelConstruction(renewInfoVO, uid, bizType)) {
            return;
        }

        // 获取配置的动作类型映射
        LinkedHashMap<Integer, Integer> actionTypeByPayChannel = getActionTypeByPayChannel(renewInfoVO.getAgreementType(), bizType, uid);
        if (MapUtils.isEmpty(actionTypeByPayChannel)) {
            return;
        }

        // 计算未开通的支付渠道（保持配置顺序）
        List<Integer> notOpenedPayChannels = calculateNotOpenedPayChannels(renewInfoVO, actionTypeByPayChannel, uid);
        if (notOpenedPayChannels.isEmpty()) {
            return;
        }

        // 验证协议是否支持展示支付渠道
        if (!isEligibleForPayChannelDisplay(renewInfoVO.getFirstDutInfo(), uid)) {
            return;
        }

        // 构建并设置可用支付渠道（保持顺序）
        List<SupportToAddPayTypeInfo> availablePayChannels = buildAvailablePayChannels(actionTypeByPayChannel, notOpenedPayChannels);
        renewInfoVO.setAvailablePayChannels(availablePayChannels);
    }

    /**
     * 验证是否满足构建支付渠道的基础条件
     */
    private boolean isValidForPayChannelConstruction(RenewInfoVO renewInfoVO, Long uid, Integer bizType) {
        Integer agreementType = renewInfoVO.getAgreementType();
        if (!AgreementTypeEnum.commonAutoRenew(agreementType)) {
            LOGGER.info("非常规自动续费协议不展示支付渠道， uid:{}, agreementType:{}", uid, agreementType);
            return false;
        }

        if (bizType == null) {
            LOGGER.info("业务类型为空不展示支付渠道， uid:{}, agreementType:{}", uid, agreementType);
            return false;
        }

        if (renewInfoVO.getFirstDutInfo() == null) {
            LOGGER.info("首次代扣信息为空不展示支付渠道, uid:{}, agreementType:{}", uid, agreementType);
            return false;
        }
        return true;
    }

    /**
     * 获取动作类型映射配置
     */
    private LinkedHashMap<Integer, Integer> getActionTypeByPayChannel(Integer agreementType, Integer bizType, Long uid) {
        String configKey = agreementType + "_" + bizType;
        LinkedHashMap<Integer, Integer> actionTypeByPayChannel = actionTypeByAgreementTypeAndBizType.get(configKey);

        if (MapUtils.isEmpty(actionTypeByPayChannel)) {
            LOGGER.info("actionTypeByPayChannel配置为空， uid:{} configKey:{}", uid, configKey);
            return null;
        }
        return actionTypeByPayChannel;
    }

    /**
     * 计算用户未开通的支付渠道（保持配置中的顺序）
     *
     * @param renewInfoVO 续费信息VO
     * @param actionTypeByPayChannel 支付渠道到动作类型的映射（有序）
     * @param uid 用户ID
     * @return 未开通的支付渠道列表，按照配置中的顺序排列
     */
    private List<Integer> calculateNotOpenedPayChannels(RenewInfoVO renewInfoVO, LinkedHashMap<Integer, Integer> actionTypeByPayChannel, Long uid) {
        // 获取用户已开通的支付渠道
        Set<Integer> openedPayChannels = renewInfoVO.getPayTypeInfo().stream()
            .map(PayTypeInfoVO::getPayChannel)
            .collect(Collectors.toSet());

        // 获取配置中的所有支付渠道（保持原有顺序）
        List<Integer> configuredPayChannels = new ArrayList<>(actionTypeByPayChannel.keySet());

        // 检查是否所有渠道都已开通
        if (openedPayChannels.containsAll(configuredPayChannels)) {
            LOGGER.info("用户已开通所有配置的支付渠道， uid:{} openedPayChannels:{} configuredPayChannels:{}", uid, openedPayChannels, configuredPayChannels);
            return Collections.emptyList();
        }

        // 按照配置顺序过滤出未开通的支付渠道
        List<Integer> notOpenedPayChannels = configuredPayChannels
            .stream()
            .filter(payChannel -> !openedPayChannels.contains(payChannel))
            .collect(Collectors.toList());

        LOGGER.info("计算未开通支付渠道完成， uid:{} notOpenedPayChannels:{} (按配置顺序)", uid, notOpenedPayChannels);
        return notOpenedPayChannels;
    }

    /**
     * 验证协议是否符合展示支付渠道的条件
     */
    private boolean isEligibleForPayChannelDisplay(FirstDutInfo firstDutInfo, Long uid) {
        // 检查协议编号
        Integer agreementNo = firstDutInfo.getFirstAgreementNo();
        if (agreementNo != null) {
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
            if (agreementNoInfo != null) {
                if (!agreementNoInfo.defaultNo() || agreementNoInfo.getSourceVipType() != null) {
                    LOGGER.info("升级或非正价协议不展示支付渠道， uid:{} agreementNo:{}", uid, agreementNo);
                    return false;
                }
                return true;
            }
        }

        // 检查代扣类型
        Integer dutType = firstDutInfo.getFirstDutType();
        if (dutType != null) {
            AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutType);
            if (autoRenewDutType != null) {
                if (autoRenewDutType.getSourceVipType() != null) {
                    LOGGER.info("升级或非正价代扣类型不展示支付渠道， uid:{} dutType:{}", uid, dutType);
                    return false;
                }
                return true;
            }
        }
        LOGGER.info("未找到有效的协议或代扣类型， uid:{} agreementNo:{} dutType:{}", uid, agreementNo, dutType);
        return false;
    }

    /**
     * 构建可用支付渠道信息列表（保持传入顺序）
     *
     * @param actionTypeByPayChannel 支付渠道到动作类型的映射
     * @param notOpenedPayChannels 未开通的支付渠道列表（已按配置顺序排列）
     * @return 可用支付渠道信息列表，保持与输入相同的顺序
     */
    private List<SupportToAddPayTypeInfo> buildAvailablePayChannels(Map<Integer, Integer> actionTypeByPayChannel, List<Integer> notOpenedPayChannels) {
        return notOpenedPayChannels.stream()
            .map(payChannel -> buildSinglePayChannelInfo(payChannel, actionTypeByPayChannel))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 构建单个支付渠道信息
     *
     * @param payChannel 支付渠道
     * @param actionTypeByPayChannel 动作类型映射
     * @return 支付渠道信息，如果构建失败返回null
     */
    private SupportToAddPayTypeInfo buildSinglePayChannelInfo(Integer payChannel, Map<Integer, Integer> actionTypeByPayChannel) {
        Integer tipType = payChannelTipTypeMapping.get(payChannel);
        if (tipType == null) {
            LOGGER.warn("支付渠道{}未配置对应的tipType", payChannel);
            return null;
        }

        Integer actionType = MapUtils.getInteger(actionTypeByPayChannel, payChannel, 0);
        KeyValuePair tipTypeInfo = KeyValuePair.from(tipType, tipTypeDescMap);
        return SupportToAddPayTypeInfo.builder()
            .actionType(actionType)
            .payChannel(payChannel)
            .tipType(tipTypeInfo)
            .build();
    }


    private void setBindInfo(RenewInfoVO renewInfoVO, DutUserNew firstDutUserNew) {
        Integer agreementType = renewInfoVO.getAgreementType();
        if (firstDutUserNew == null || firstDutUserNew.getDeadline() == null) {
            return;
        }
        if (!AgreementTypeEnum.familyType(agreementType) || !AgreementTypeEnum.familyType(firstDutUserNew.getAgreementType())) {
            return;
        }
        Long userId = firstDutUserNew.getUserId();
        FamilyBindRelation bindRelation = null;
        try {
            bindRelation = vipChargeApi.queryBindRelationBySourceUid(userId);
        } catch (Exception e) {
            LOGGER.info("queryBindRelationBySourceUid error，userId：{}", userId);
        }
        BindInfo bindInfo = new BindInfo();
        ArrayList<BindUserInfo> bindUserInfo = new ArrayList<>();
        List<PayTypeInfoVO> payTypeInfo = renewInfoVO.getPayTypeInfo();
        PayTypeInfoVO firstPayTypeInfoVO = payTypeInfo.get(0);
        boolean isApple = firstPayTypeInfoVO != null && ObjectUtils.equals(firstPayTypeInfoVO.getPayChannel(), PaymentDutType.PAY_CHANNEL_IAP);
        int payChannelType = isApple ? PAY_CHANNEL_TYPE_THIRD_DUT_PAY : PAY_CHANNEL_TYPE_VIP_DUT_PAY;

        if (bindRelation == null) {
            BindUserInfo userInfo = BindUserInfo.builder()
                .userId(userId)
                .deadline(firstDutUserNew.getDeadline().getTime())
                .status(FamilyBindStatusEnum.BIND.getValue())
                .statusTip(null)
                .build();
            bindUserInfo.add(userInfo);
            String statusTip = "未关联账号，请联系客服处理";
            BindUserInfo unbindUserInfo = BindUserInfo.builder()
                .status(FamilyBindStatusEnum.UNLINKED.getValue())
                .statusTip(statusTip)
                .build();
            bindUserInfo.add(unbindUserInfo);
            bindInfo.setBindUsers(bindUserInfo);
            bindInfo.setBindTip(CloudConfigUtil.getBindTipByPayChannelType(payChannelType));
            renewInfoVO.setBindInfo(bindInfo);
            return;
        }

        Long targetUid = bindRelation.getTargetUid();
        if (targetUid == null && StringUtils.isNotBlank(bindRelation.getTargetPhone())) {
            BindUserInfo userInfo = BindUserInfo.builder()
                .userId(userId)
                .deadline(firstDutUserNew.getDeadline().getTime())
                .status(FamilyBindStatusEnum.BIND.getValue())
                .build();
            bindUserInfo.add(userInfo);
            String statusTip = "账号未注册";
            BindUserInfo unregisteredUserInfo = BindUserInfo.builder()
                .userId(userId)
                .status(FamilyBindStatusEnum.UNREGISTERED.getValue())
                .statusTip(statusTip)
                .build();
            bindUserInfo.add(unregisteredUserInfo);
            bindInfo.setBindUsers(bindUserInfo);
            bindInfo.setBindTip(CloudConfigUtil.getBindTipByPayChannelType(payChannelType));
            renewInfoVO.setBindInfo(bindInfo);
            return;
        }

        if (targetUid != null && StringUtils.isNotBlank(bindRelation.getTargetPhone())) {
            String statusTip = "绑定中";
            BindUserInfo userInfo = BindUserInfo.builder()
                .userId(userId)
                .deadline(firstDutUserNew.getDeadline().getTime())
                .status(FamilyBindStatusEnum.BIND.getValue())
                .statusTip(statusTip)
                .build();
            bindUserInfo.add(userInfo);
            BindUserInfo bindedUser = BindUserInfo.builder()
                .userId(targetUid)
                .status(FamilyBindStatusEnum.BIND.getValue())
                .statusTip(statusTip)
                .build();

            VipUser vipInfo = customVipInfoClient.getVipInfoNew(targetUid, firstDutUserNew.getVipType());
            if (vipInfo != null && vipInfo.getDeadline() != null) {
                bindedUser.setDeadline(vipInfo.getDeadline().getTime());
            }
            if (vipInfo == null) {
                ExtInfo extInfo = firstDutUserNew.parseExt();
                if (extInfo != null && StringUtils.isNotBlank(extInfo.getBindUidDeadline())) {
                    Timestamp timestamp = DateHelper.getTimestamp(extInfo.getBindUidDeadline());
                    bindedUser.setDeadline(timestamp.getTime());
                }
            }
            bindUserInfo.add(bindedUser);
            bindInfo.setBindUsers(bindUserInfo);
            bindInfo.setBindTip(CloudConfigUtil.getBindTipByPayChannelType(payChannelType));
            renewInfoVO.setBindInfo(bindInfo);
        }
    }

    private void setDoPayTime(Integer agreementType, DutUserNew firstDutUserNew, Timestamp deadline, List<ManagementDutTypeInfo> mobileDutTypeInfoList, Integer firstAgreementNo, Integer firstDutType, List<DutRenewSetLog> thirdDutSetLogList, List<DutUserNew> dutUserNews, RenewInfoVO renewInfoVO) {
        //预计发起扣费时间
        if (!AgreementTypeEnum.commonAutoRenew(agreementType)) {
            if (firstDutUserNew.getNextDutTime() == null) {
                return;
            }
            renewInfoVO.setDoPayTime(DateHelper.getDateStringByPattern(firstDutUserNew.getNextDutTime(), "yyyy-MM-dd"));
            return;
        }
        Timestamp doPayTime = null;
        //手机话费提前4天，其他提前1天
        if (deadline != null) {
            if (CollectionUtils.isNotEmpty(mobileDutTypeInfoList)) {
                Integer advanceDay = autorenewDutConfigService.getAdvanceDay(AutorenewDutConfig.JOB_TYPE_MOBILE, AutorenewDutConfig.STATUS_VALID, agreementType);
                doPayTime = DateHelper.caculateTime(deadline, -24L * 3600 * 1000 * advanceDay);
            } else {
                doPayTime = deadline;
            }
        }
        // 第三方扣费代扣方式
        DutUserNew dutUser = getDutUserNewByAgreementNoAndDutType(dutUserNews, firstAgreementNo, firstDutType);
        if (CollectionUtils.isNotEmpty(thirdDutSetLogList)) {
            if (null != dutUser && null != dutUser.getNextDutTime()) {
                doPayTime = dutUser.getNextDutTime();
            }
        }

        if (CollectionUtils.isNotEmpty(dutUserNews) && doPayTime != null) {
            String doPayTimeShort = DateHelper.getDateStringByPattern(doPayTime, "yyyy-MM-dd");
            //此处deadline为发起扣费时间
            renewInfoVO.setDoPayTime(doPayTimeShort);
        }
        //不展示下次续费时间
        if (clearNextDutTime(renewInfoVO)) {
            renewInfoVO.setDoPayTime(null);
        }
    }

    private static DutUserNew getDutUserNewByAgreementNoAndDutType(List<DutUserNew> dutUserNews, Integer firstAgreementNo, Integer firstDutType) {
        DutUserNew dutUserNew = dutUserNews
            .stream()
            .filter(d -> (
                firstAgreementNo != null && Objects.equals(d.getAgreementNo(), firstAgreementNo)) ||
                (firstDutType != null && Objects.equals(d.getType(), firstDutType))
            )
            .findFirst()
            .orElse(null);
        return dutUserNew;
    }

    private static DutUserNew getDutUserNewByDutTypeAndSourceVipType(List<DutUserNew> dutUserNews, Integer firstDutType, Long sourceVipType) {
        DutUserNew dutUserNew = dutUserNews.stream().filter(d -> (
            sourceVipType != null && Objects.equals(d.getSourceVipType(), sourceVipType)) ||
            (firstDutType != null && Objects.equals(d.getType(), firstDutType))
        ).findFirst().orElse(null);
        return dutUserNew;
    }

    private String buildDutTimeTips(LocalDate restrictDateToCancel) {
        return TipsUtils.getDutTimeTips(restrictDateToCancel);
    }

    private void resetPayTypeInfoDutTimeTips(List<PayTypeInfoVO> dutTypesByDutSequence, RenewVipInfoVO vipInfo, Long userId, boolean fistDutIsPriceInsuredPeriod, UserRenewDiscountInfo userRenewDiscountInfo, Integer agreementType) {
        if (CollectionUtils.isEmpty(dutTypesByDutSequence)) {
            return;
        }
        PayTypeInfoVO payTypeInfoVO = dutTypesByDutSequence.get(0);
        if (payTypeInfoVO == null) {
            return;
        }
        String dutTimeTips = payTypeInfoVO.getDutTimeTips();
        if (StringUtils.isNotBlank(dutTimeTips)) {
            return;
        }
        boolean needShowDefaultDutTimeTip = CloudConfigUtil.needShowDefaultDutTimeTip();
        if (needShowDefaultDutTimeTip) {
            dutTimeTips = null;
            if (agreementType == null || AgreementTypeEnum.commonAutoRenew(agreementType)) {
                dutTimeTips = CloudConfigUtil.getDefaultDutTimeTip();
            } else {
                dutTimeTips = CloudConfigUtil.getDutTimeTipByAgreementType(agreementType);
            }
            payTypeInfoVO.setDutTimeTips(dutTimeTips);
        }
        if (!fistDutIsPriceInsuredPeriod && userRenewDiscountInfo == null) {
            return;
        }
        if (userRenewDiscountInfo != null) {
            String periodStr = MapUtils.getString(Constants.amountPeriodMap(), userRenewDiscountInfo.getAmount());
            dutTimeTips = MessageFormat.format("正在享受续费折扣，取消后无法再享{0}元/{1}续费", userRenewDiscountInfo.getRenewPrice() * 1.0 / 100, periodStr);
            payTypeInfoVO.setDutTimeTips(dutTimeTips);
            return;
        }

        List<ManagementDutTypeInfo> dutTypeInfo = payTypeInfoVO.getDutTypeInfo();
        if (CollectionUtils.isEmpty(dutTypeInfo)) {
            return;
        }
        ManagementDutTypeInfo typeInfo = dutTypeInfo.get(0);
        Integer agreementNo = typeInfo.getAgreementNo();
        Integer amount = typeInfo.getAmount();
        Integer dutType = typeInfo.getDutType();
        Integer vipType = vipInfo.getVipType();
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, dutType, vipType.longValue());
        Integer renewPrice = userAgreementService.getAutoRenewDisplayRenewPrice(dutUserNew);
        if (renewPrice == null) {
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
            if (agreementNoInfo != null) {
                amount = agreementNoInfo.getAmount();
            }
            renewPrice = paymentDutTypeManager.getRenewPrice(vipType.longValue(), amount, null, dutType, userId);
        }
        String periodStr = MapUtils.getString(Constants.amountPeriodMap(), amount);
        if (renewPrice != null && StringUtils.isNotBlank(periodStr)) {
            dutTimeTips = String.format("正在享受价格保护，取消后无法再享%s元/%s续费", renewPrice * 1.0 / 100, periodStr);
        } else if (renewPrice != null) {
            dutTimeTips = String.format("正在享受价格保护，取消后无法再享%s元续费", renewPrice * 1.0 / 100);
        }
        payTypeInfoVO.setDutTimeTips(dutTimeTips);
    }

    private boolean clearNextDutTime(RenewInfoVO renewInfoVO) {
        List<PayTypeInfoVO> payTypeInfo = renewInfoVO.getPayTypeInfo();
        if (CollectionUtils.isNotEmpty(payTypeInfo)) {
            PayTypeInfoVO firstPayTypeInfo = payTypeInfo.get(0);
            if (firstPayTypeInfo != null) {
                KeyValuePair tipType = firstPayTypeInfo.getTipType();
                if (tipType == null) {
                    return false;
                }
                return CloudConfigUtil.needClearNextDutTime(tipType.getKey());
            }
        }
        return false;
    }

    private boolean firstPayTypeIsNewMobile(RenewInfoVO renewInfoVO) {
        List<PayTypeInfoVO> payTypeInfo = renewInfoVO.getPayTypeInfo();
        if (CollectionUtils.isNotEmpty(payTypeInfo)) {
            PayTypeInfoVO firstPayTypeInfo = payTypeInfo.get(0);
            if (firstPayTypeInfo != null) {
                KeyValuePair tipType = firstPayTypeInfo.getTipType();
                if (tipType == null) {
                    return false;
                }
                return TipTypeUtils.isNewMobilePayChannel(tipType.getKey());
            }
        }
        return false;
    }

	private List<DutRenewSetLog> getAllSortedSetLog(List<DutUserNew> dutUserNews, Integer agreementType) {
		List<DutRenewSetLog> dutRenewSetLogs = Lists.newArrayList();

		for (DutUserNew dutUserNew : dutUserNews) {
			//获得用户的自动续费开通日志
			DutRenewSetLog dutRenewSetLog = dutRenewSetLogService.getRecentlyDutRenewSetLog(
					dutUserNew.getUserId(), agreementType, dutUserNew.getType(), DutRenewSetLog.RENEW_SET);
			if (null != dutRenewSetLog) {
				dutRenewSetLogs.add(dutRenewSetLog);
			}
		}

		//根据操作时间对日志排序，最晚开通的在前面
		dutRenewSetLogs.sort((o1, o2) -> o2.getOperateTime().compareTo(o1.getOperateTime()));

		return dutRenewSetLogs;
	}

    private static String getUpgradeProductName(String vipTypeName, String sourceVipTypeName, boolean newVersion) {
        if (!newVersion) {
            return String.format("%s升级%s1个月", sourceVipTypeName, vipTypeName);
        }
        return vipTypeName + Constants.amountNameMap().get(AMOUNT_OF_COMMON_AUTORENEW);
    }

	private Boolean isCommonChannel(Integer payChannel) {
		return payChannel != Constants.TIPTYPE_MOBILE
				&& payChannel != Constants.TIPTYPE_APPLE;
	}

    private List<KeyValuePair> getNameFromType(List<ManagementDutTypeInfo> dutTypeInfos) {
        return dutTypeInfos.stream()
            .map(item -> new KeyValuePair(item.getDutType(), item.getDutTypeName()))
            .collect(Collectors.toList());
    }

	private String buildFirstXActPeriodPriceInfo(DutUserNew dutUser) {
		DecimalFormat df = new DecimalFormat("#.#");
		df.setRoundingMode(RoundingMode.HALF_UP);
		String renewPrice = dutUser.getRenewPrice() == null ? "0" : df.format(dutUser.getRenewPrice() * 1.0 / 100);
		String contractPrice = dutUser.getContractPrice() == null ? renewPrice : df.format(dutUser.getContractPrice() * 1.0 / 100);
		return TipsUtils.getActPeriodsTip(dutUser.getAmount(), dutUser.getActTotalPeriods(), renewPrice, contractPrice);
	}

	private String buildFirstXSubActPeriodPriceInfo(DutUserNew dutUserNew) {
		return TipsUtils.getSubActPeriodsTip(dutUserNew.getAmount(), dutUserNew.getRemainPeriods(), dutUserNew.getActTotalPeriods());
	}

	private String buildFirstXActPeriodPriceInfoDetail(DutUserNew dutUserNew, AutoRenewDutType autoRenewDutType) {
		DecimalFormat df = new DecimalFormat("#.#");
		df.setRoundingMode(RoundingMode.HALF_UP);
		String renewPrice = dutUserNew.getRenewPrice() == null ? "0" : df.format(dutUserNew.getRenewPrice() * 1.0 / 100);
		String contractPrice = dutUserNew.getContractPrice() == null ? renewPrice : df.format(dutUserNew.getContractPrice() * 1.0 / 100);
		if (autoRenewDutType == null || autoRenewDutType.getValidStartTime() == null) {
			return "";
		}
		String startTime = DateHelper.getFormatDate(autoRenewDutType.getValidStartTime(), DateHelper.SIMPLE_PATTERN);
		return TipsUtils.getActPeriodsTipDetail(dutUserNew.getAmount(), renewPrice, contractPrice, startTime,
				dutUserNew.getActTotalPeriods(), dutUserNew.getRemainPeriods());
	}

	private String buildPriceInsuredActPeriodPriceInfoDetail(
			DutUserNew dutUserNew, AutoRenewDutType autoRenewDutType, boolean isUpgrade,
			UpgradeStageEnum upgradeStage, Integer upgradeRenewPrice
	) {
		if (autoRenewDutType == null) {
			return "";
		}

		DecimalFormat df = new DecimalFormat("#.#");
		df.setRoundingMode(RoundingMode.HALF_UP);
		String renewPrice;
		if (isUpgrade) {
			renewPrice = upgradeRenewPrice == null ? "0" : df.format(upgradeRenewPrice * 1.0 / 100);
		} else {
			renewPrice = dutUserNew.getRenewPrice() == null ? "0" : df.format(dutUserNew.getRenewPrice() * 1.0 / 100);
		}

		return TipsUtils.getPriceInsuredPeriodInfoDetail(autoRenewDutType, dutUserNew.getAmount(), renewPrice, isUpgrade, upgradeStage);
	}

	private String buildPriceInsuredActPeriodPriceInfo(
			DutUserNew dutUser, boolean isUpgrade,
			UpgradeStageEnum upgradeStage, Integer upgradeRenewPrice
	) {
		DecimalFormat df = new DecimalFormat("#.#");
		df.setRoundingMode(RoundingMode.HALF_UP);
		String renewPrice;
		if (isUpgrade) {
			renewPrice = upgradeRenewPrice == null ? "0" : df.format(upgradeRenewPrice * 1.0 / 100);
		} else {
			renewPrice = dutUser.getRenewPrice() == null ? "0" : df.format(dutUser.getRenewPrice() * 1.0 / 100);
		}
		return TipsUtils.getPriceInsuredPeriodInfo(dutUser.getAmount(), renewPrice, isUpgrade, upgradeStage);
	}

	private String buildSubPriceInsuredActPeriodPriceInfo(AutoRenewDutType autoRenewDutType) {
		if (autoRenewDutType == null) {
			return "";
		}

		if (autoRenewDutType.getValidEndTime() != null) {
			String deadline = DateHelper.getFormatDate(autoRenewDutType.getValidEndTime(), DateHelper.SIMPLE_PATTERN);
			return TipsUtils.getSubPriceInsuredPeriodInfo(deadline);
		}

		return TipsUtils.getSubPriceInsuredPeriodNoDeadlineInfo();
	}

	private ActPeriodTip makeActPeriodTipsOfMix(DutUserNew dutUserNew, Integer firstDutType,
        boolean isUpgrade, boolean firstDutIsPriceInsuredPeriod,
        Integer upgradeRenewPrice, UpgradeStageEnum upgradeStage,
        UserDutTypeInfo priceInsuredPeriodInfo, UserRenewDiscountInfo userRenewDiscountInfo
    ) {
		if (dutUserNew == null || firstDutType == null) {
			return null;
		}

        ActPeriodTip actPeriodTip = new ActPeriodTip();
        //保价期优惠信息
		if (firstDutIsPriceInsuredPeriod && priceInsuredPeriodInfo != null) {
			AutoRenewDutType autoRenewDutType = priceInsuredPeriodInfo.getAutoRenewDutType();
			Integer renewPrice = isUpgrade ? upgradeRenewPrice : dutUserNew.getRenewPrice();
			Integer amount = isUpgrade ? null : dutUserNew.getAmount();
			Integer contractPrice = isUpgrade
					? calcUpgradeContractPrice(dutUserNew, upgradeStage, priceInsuredPeriodInfo.getMaxPriority())
					: agreementManager.getDefaultRenewPrice(dutUserNew.getVipType(), dutUserNew.getAmount(), autoRenewDutType.getPayChannel(), priceInsuredPeriodInfo.getMaxPriority());
			if (!Objects.equals(renewPrice, contractPrice)) {
                actPeriodTip.setActPeriodPriceInfo(buildPriceInsuredActPeriodPriceInfo(dutUserNew, isUpgrade, upgradeStage, upgradeRenewPrice));
                actPeriodTip.setActPeriodDesc(buildSubPriceInsuredActPeriodPriceInfo(autoRenewDutType));
                actPeriodTip.setActPeriodDetail(buildPriceInsuredActPeriodPriceInfoDetail(dutUserNew, autoRenewDutType, isUpgrade, upgradeStage, upgradeRenewPrice));
                actPeriodTip.setPriceInsuredPeriodInfo(PriceInsuredPeriodInfo.buildFrom(renewPrice, amount, contractPrice, priceInsuredPeriodInfo.getInsuredPricePeriodEndTime(), true, isUpgrade, upgradeStage));
			}
		}

        //协议优惠信息
        actPeriodTip.setUserRenewDiscountInfo(userRenewDiscountInfo);

		//首X期优惠
		if (isFirstXDiscounts(firstDutType, dutUserNew)) {
			AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutUserNew.getType());
            actPeriodTip.setActPeriodPriceInfo(buildFirstXActPeriodPriceInfo(dutUserNew));
            actPeriodTip.setActPeriodDesc(buildFirstXSubActPeriodPriceInfo(dutUserNew));
            actPeriodTip.setActPeriodDetail(buildFirstXActPeriodPriceInfoDetail(dutUserNew, autoRenewDutType));
			if (dutUserNew.getActTotalPeriods() > 1 && dutUserNew.getRemainPeriods() > 0) {
                actPeriodTip.setFirstXDiscountInfo(FirstXDiscountInfo.buildFromDutUserNew(dutUserNew, autoRenewDutType));
			}
		}

		return actPeriodTip;
	}

	private boolean isFirstXDiscounts(Integer dutType, DutUserNew dutUser) {
		return dutType != null && dutType.equals(dutUser.getType())
				&& !dutPriceActService.isNotDutAct(dutUser.getRemainPeriods(), dutType)
				&& !ActTypeEnum.STOP_AFTER_X.getValue().equals(dutUser.getActType());
	}


	/**
	 * 计算升级产品的合同价
	 * @param dutUserNew
	 * @param upgradeStage
	 * @param maxPriority
	 */
	private Integer calcUpgradeContractPrice(DutUserNew dutUserNew, UpgradeStageEnum upgradeStage, Short maxPriority) {
		Integer contractPrice = null;
		AutoRenewUpgradeConfig upgradeConfig = autoRenewUpgradeConfigManager.getByVipType(dutUserNew.getSourceVipType(), dutUserNew.getVipType(), maxPriority);
		switch (upgradeStage) {
			case FIRST_STAGE:
				contractPrice = upgradeConfig.getMonthRenewPrice();
				break;
			case SECOND_STAGE:
				contractPrice = upgradeConfig.getDayRenewPrice();
				break;
			case FINAL_STAGE:
				contractPrice = upgradeConfig.getFinalRenewPrice();
				break;
			default:
				break;
		}
		return contractPrice;
	}

	private UserDutTypeInfo buildPriceInsuredPeriodInfo(Integer dutType, boolean isPriceInsuredPeriod) {
		if (!isPriceInsuredPeriod) {
			return null;
		}
		AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutType);
		if (autoRenewDutType == null) {
			return null;
		}
        Integer agreementType = autoRenewDutType.getAgreementType();
        List<AutoRenewDutType> autoRenewDutTypeList = autoRenewDutTypeManager
				.findByVipTypeAndPayChannel(autoRenewDutType.getSourceVipType(), autoRenewDutType.getVipType(), agreementType, autoRenewDutType.getPayChannel());
		if (CollectionUtils.isEmpty(autoRenewDutTypeList)) {
			return null;
		}

		//获取优先级最高的dutType列表
		List<AutoRenewDutType> maxPriorityAutoRenewDutTypes = autoRenewDutTypeList.stream()
				.collect(Collectors.groupingBy(AutoRenewDutType::getPriority)).entrySet().stream()
				.max(Map.Entry.comparingByKey())
				.map(Map.Entry::getValue).orElse(Collections.emptyList());
		Short maxPriority = maxPriorityAutoRenewDutTypes.get(0).getPriority();
		List<Integer> maxPriorityDutTypes = maxPriorityAutoRenewDutTypes.stream()
				.map(AutoRenewDutType::getDutType)
				.collect(Collectors.toList());

		return UserDutTypeInfo.builder()
				.isPriceInsuredPeriod(true)
                .isUpgrade(autoRenewDutType.isUpgrade())
				.insuredPricePeriodEndTime(autoRenewDutType.getValidEndTime())
				.autoRenewDutType(autoRenewDutType)
				.maxPriority(maxPriority)
				.maxPriorityDutTypes(maxPriorityDutTypes)
				.build();
	}

    private boolean isPriceInsuredPeriod(Integer dutType) {
        AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutType);
        if (autoRenewDutType == null) {
            return false;
        }
        Integer agreementType = autoRenewDutType.getAgreementType();
        List<AutoRenewDutType> autoRenewDutTypeList = autoRenewDutTypeManager.findByVipTypeAndPayChannel(autoRenewDutType.getSourceVipType(), autoRenewDutType.getVipType(), agreementType, autoRenewDutType.getPayChannel());
        if (CollectionUtils.isEmpty(autoRenewDutTypeList)) {
            return false;
        }

        //获取优先级最高的dutType列表
        List<AutoRenewDutType> maxPriorityAutoRenewDutTypes = autoRenewDutTypeList.stream()
            .collect(Collectors.groupingBy(AutoRenewDutType::getPriority)).entrySet().stream()
            .max(Map.Entry.comparingByKey())
            .map(Map.Entry::getValue).orElse(Collections.emptyList());
        Short maxPriority = maxPriorityAutoRenewDutTypes.get(0).getPriority();
        return maxPriority > autoRenewDutType.getPriority();
    }

	private boolean isIosCNDutType(DutUserNew dutUser, List<Integer> iapDutTypeList) {
		return null != dutUser && null != dutUser.getType()
				&& iapDutTypeList.contains(dutUser.getType());
	}

	private void putMainlandPrice(DutUserNew dutUserNew, RenewInfoVO renewInfoVO) {
		//IOS大陆自动续费展示代扣价格
		if (dutUserNew == null || !dutUserService.isIosMainLandAutoRenew(dutUserNew)) {
			return;
		}
		Integer price = dutUserNew.getRenewPrice();
		if (price != null) {
			DecimalFormat df = new DecimalFormat("#.##");
			df.setRoundingMode(RoundingMode.HALF_UP);
            renewInfoVO.setPrice(df.format(price * 1.0 / 100));
		}
	}

	private void geneSupportDutType(HttpServletRequest servletRequest, RenewInfoVO renewInfoVO,
									List<Integer> tiyTypeList, Long vipType, String platform, UserInfo user,
									String fc, String fv, Integer amount) {
		try {
			String dest = AppConfig.getProperty("pay.dut.bind.url");
			String uuid = UUID.randomUUID().toString();
			String subParamDisplayAccount = UserInfo.getSubParamDisplayAccount(user);
			List<SupportToAddPayTypeInfo> tipTypesCanOpen = Lists.newArrayList();
			for (Map.Entry<Integer, String> entry : tipTypesCanOpenMap(servletRequest).entrySet()) {
				Map<String, String> reqMap = Maps.newHashMap();
				reqMap.put("request_serial", uuid);
				reqMap.put("uid", String.valueOf(user.getId()));
				reqMap.put("partner", CloudConfigUtil.getPartnerByVipType(vipType));
				reqMap.put("platform_code", StringUtils.defaultIfEmpty(platform, ""));
				reqMap.put("notify_url", QIYI_DUT_BIND_NOTIFY_URL);
				reqMap.put("version", "1.0");
				reqMap.put("charset", "UTF-8");
				if (!tiyTypeList.contains(entry.getKey())) {
					String payType = entry.getValue();
                    SupportToAddPayTypeInfo supportToAddPayTypeInfo = new SupportToAddPayTypeInfo();
                    supportToAddPayTypeInfo.setPullUpMode(PureSignPullUpModeEnum.DEFAULT_MODE.getValue());
					if (isIqiyiBasicPlatform(platform) && Constants.WECHAT_PURE_PAY_TYPE.equals(payType)) {
						payType = Constants.WECHAT_APP_SIGN;
                        supportToAddPayTypeInfo.setPullUpMode(PureSignPullUpModeEnum.BUILD_UP_MODE.getValue());
					}
                    supportToAddPayTypeInfo.setTipType(KeyValuePair.from(entry.getKey(), tipTypeDescMap));
                    GenerateOpenUrlDto openUrlDto = GenerateOpenUrlDto.builder()
                        .dest(dest)
                        .uuid(uuid)
                        .notifyUrl(QIYI_DUT_BIND_NOTIFY_URL)
                        .reqMap(reqMap)
                        .payType(payType)
                        .user(user)
                        .platformCode(platform)
                        .vipType(vipType)
                        .fc(fc)
                        .fv(fv)
                        .agreementType(renewInfoVO.getAgreementType())
                        .subParamDisplayAccount(subParamDisplayAccount)
                        .amount(amount).build();
                    String openUrl = generateOpenUrl(servletRequest, openUrlDto);
                    if (StringUtils.isBlank(openUrl)) {
                        continue;
                    }
                    supportToAddPayTypeInfo.setOpenUrl(openUrl);
                    tipTypesCanOpen.add(supportToAddPayTypeInfo);
				}
			}
			if (CollectionUtils.isNotEmpty(tipTypesCanOpen)) {
                renewInfoVO.setSupportToAddPayTypes(tipTypesCanOpen);
			}
		} catch (Exception e) {
			LOGGER.error("generate supportToAddPayTypes failed", e);
		}
	}

	private void calOriginalPrice(RenewInfoVO renewInfoVO, Long vipType, Integer originalPriceDutType,
								  UserInfo user, boolean isUpgrade, UpgradeStageEnum upgradeStage, String vipTypeName,
								  PartnerUserSignRecordQueryResult partnerUserSignRecord, boolean newVersion, Integer agreementType) {
		if (firstPayTypeIsNewMobile(renewInfoVO)) {
            Integer amount = partnerUserSignRecord.getAmount();
            String productName = getCommonProductName(vipTypeName, agreementType, vipType, newVersion, amount);
            renewInfoVO.setProductName(productName);
            return;
		}
		Integer amount = AMOUNT_OF_COMMON_AUTORENEW;
		AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(originalPriceDutType);
		if (needOriginalDutPrice(autoRenewDutType)) {
            amount = dutUserService.getAmountFromDutUserNew(user.getId(), originalPriceDutType, vipType, agreementType);
			Integer originalDutPrice = getOriginalDutPrice(autoRenewDutType, vipType, agreementType, amount);
			if (originalDutPrice != null && originalDutPrice > 0 && !upgradeStage.isDutByDay()) {
				DecimalFormat df = new DecimalFormat("#.#");
				df.setRoundingMode(RoundingMode.HALF_UP);
				String originalDutPriceTmp = df.format(originalDutPrice * 1.0 / 100);
                renewInfoVO.setOriginalDutPrice(originalDutPriceTmp);
			}
		}
		if (!isUpgrade) {
            String productName = getCommonProductName(vipTypeName, agreementType, vipType, newVersion, amount);
            renewInfoVO.setProductName(productName);
		}
	}

    private static String getCommonProductName(String vipTypeName, Integer agreementType, Long vipType, boolean newVersion, Integer amount) {
        if (ObjectUtils.equals(AgreementTypeEnum.AUTO_RENEW.getValue(), agreementType)) {
            String productName = vipTypeName + amount + "个月";
            if (newVersion) {
                productName = vipTypeName + MapUtils.getString(Constants.amountNameMap(), amount, amount + "个月");
            }
            return productName;
        }
        String name = CloudConfigUtil.getProductNameByVipTypeAndAgreementType(vipType, agreementType);
        return name + MapUtils.getString(Constants.amountNameMap(), amount, amount + "个月");
    }

    private boolean needOriginalDutPrice(AutoRenewDutType originalPriceDutType) {
		return originalPriceDutType != null
				&& !originalPriceDutType.isGoogleDutType()
				&& !originalPriceDutType.isPayPalDutType();
	}

	private Integer getOriginalDutPrice(AutoRenewDutType autoRenewDutType, Long vipType, Integer agreementType, Integer amount) {
		Integer originalPrice = null;
		ManagementConfig managementConfig;
        if (autoRenewDutType.isMobileDutType()) {
			managementConfig = managementConfigService.searchBy(vipType, agreementType, PaymentDutType.PAY_CHANNEL_MOBILE, amount);
		} else if (autoRenewDutType.isAppleDutType()) {
			managementConfig = managementConfigService.searchBy(vipType, agreementType, PaymentDutType.PAY_CHANNEL_IAP, amount);
		} else if (autoRenewDutType.isOverseaAlipayDutType()) {
			managementConfig = managementConfigService.searchBy(vipType, agreementType, PaymentDutType.PAY_CHANNEL_OVERSEA_ALIPAY, amount);
		} else if (autoRenewDutType.isOverseaWechatDutType()) {
			managementConfig = managementConfigService.searchBy(vipType, agreementType, PaymentDutType.PAY_CHANNEL_OVERSEA_WECHAT, amount);
		} else {
			managementConfig = managementConfigService.searchBy(vipType, agreementType, null, amount);
		}

		if (managementConfig != null) {
			originalPrice = managementConfig.getOriginalPrice();
		}

		if (originalPrice == null) {
			managementConfig = managementConfigService.searchBy(vipType, agreementType, null, amount);
			if (managementConfig != null) {
				originalPrice = managementConfig.getOriginalPrice();
			}
		}

		return originalPrice;
	}

	private void setDutFailReason(RenewInfoVO renewInfoVO, Timestamp deadline, Long vipType, Long userId,
								  Integer firstDutType, boolean isAppleDut, boolean isGoogleDut) {
		if (firstDutType == null || deadline == null) {
			return;
		}
		if (isAppleDut) {
			return;
		}
		DutRenewLog dutRenewLog;
		if (deadline.after(DateHelper.getDateTime())) {
			if (isGoogleDut) {
				return;
			}
			dutRenewLog = dutService.getLastRecentlyNumDaysDutRenewLog(userId, firstDutType, vipType, 5);
		} else {
			dutRenewLog = dutService.getLastDutRenewLog(userId, firstDutType, vipType);
		}
		if (dutRenewLog != null && dutRenewLog.getStatus() != null && dutRenewLog.getStatus() == DutRenewLog.PAY_FAILURE) {
			if (Constants.getAutoRenewNotEnoughCodeList().contains(dutRenewLog.getThirdErrorCode())) {
                renewInfoVO.setDutFailMsg("账户余额不足，续费失败");
			} else {
                renewInfoVO.setDutFailMsg("自动续费失败");
			}
		}
	}

	private Map<Integer, String> tipTypesCanOpenMap(HttpServletRequest servletRequest) {
		Map<Integer, String> map = Maps.newHashMap();
		map.put(Constants.TIPTYPE_WEIXIN, PureSignUtils.getWechatPureSignPayType(servletRequest));
		map.put(Constants.TIPTYPE_ALIPAY, ALI_PURE_PAY_TYPE);
		return map;
	}

	public String generateOpenUrl(HttpServletRequest servletRequest, GenerateOpenUrlDto openUrlDto) throws UnsupportedEncodingException {
        String subParamDisplayAccount = openUrlDto.getSubParamDisplayAccount();
        UserInfo user = openUrlDto.getUser();
        Integer amount = openUrlDto.getAmount();
        Map<String, String> reqMap = openUrlDto.getReqMap();
        String payType = openUrlDto.getPayType();
        Long sourceVipType = openUrlDto.getSourceVipType();
        Long vipType = openUrlDto.getVipType();
        String platformCode = openUrlDto.getPlatformCode();
        String partner = openUrlDto.getPartner();
        String fc = openUrlDto.getFc();
        String fv = openUrlDto.getFv();
        String dest = openUrlDto.getDest();
        String uuid = openUrlDto.getUuid();
        String returnUrl = openUrlDto.getReturnUrl();
        String notifyUrl = openUrlDto.getNotifyUrl();
        Integer agreementType = openUrlDto.getAgreementType();

        if (StringUtils.isBlank(subParamDisplayAccount)) {
			subParamDisplayAccount = UserInfo.getSubParamDisplayAccount(user);
		}
		if (amount == null) {
			amount = AMOUNT_OF_COMMON_AUTORENEW;
		}
		reqMap.put("pay_type", payType);
		//支付方式跳转url
		StringBuilder url = new StringBuilder();
		String extendParamsValueEncode;
		String extendParamsValue;
        Integer agreementNo = null;
        Integer signDutType = null;
        if (Constants.getWechatPureSignPayTypeList().contains(payType)) {
            AgreementNoInfo agreementNoInfo = pureSignManager.findPureSignDutType(user.getId(), sourceVipType, vipType, agreementType, PaymentDutType.PAY_CHANNEL_WECHAT, amount, partner);
            if (agreementNoInfo != null) {
                signDutType = agreementNoInfo.getDutType();
                agreementNo = agreementNoInfo.getId();
            }
            if (signDutType == null) {
                LOGGER.warn("[无可签约代扣方式] [reqMap:{}]", reqMap);
                return null;
            }
			if (isTv(vipType)) {
				extendParamsValue = "subParam_returnWeb=1&subParam_dutType=" + signDutType;
			} else if (isIqiyiBasicPlatform(platformCode)) {
				extendParamsValue = "subParam_wechatAppAlias=app_lite&subParam_dutType=" + signDutType;
			} else {
				extendParamsValue = "subParam_dutType=" + signDutType;
			}
		} else {
            AgreementNoInfo agreementNoInfo = pureSignManager.findPureSignDutType(user.getId(), sourceVipType, vipType, agreementType, PaymentDutType.PAY_CHANNEL_ALIPAY, amount, partner);
            if (agreementNoInfo != null) {
                signDutType = agreementNoInfo.getDutType();
                agreementNo = agreementNoInfo.getId();
            }
            if (signDutType == null) {
                LOGGER.warn("[无可签约代扣方式] [reqMap:{}]", reqMap);
                return null;
            }
			extendParamsValue = "subParam_channel=ALIPAYAPP&subParam_schemaOfAddress=alipays&subParam_dutType=" + signDutType;
		}
		extendParamsValue += "&subParam_displayAccount=" + subParamDisplayAccount;
		extendParamsValueEncode = URLEncoder.encode(extendParamsValue, "UTF-8");

		reqMap.put("extend_params", extendParamsValue);
		Map<String, String> extraCommonParamsMap = Maps.newHashMap();
		extraCommonParamsMap.put("amount", String.valueOf(amount));
		if (StringUtils.isNotBlank(fc)) {
			extraCommonParamsMap.put("fc", fc);
		}
		if (StringUtils.isNotBlank(fv)) {
			extraCommonParamsMap.put("fv", fv);
		}
        if (agreementNo != null) {
            extraCommonParamsMap.put(ParamConstants.AGREEMENT_NO, agreementNo.toString());
        }
		if (MapUtils.isNotEmpty(extraCommonParamsMap)) {
			reqMap.put("extra_common_params", Joiner.on("&").withKeyValueSeparator("=").join(extraCommonParamsMap));
		}
		String extraCommonParamsStr = MapUtils.isNotEmpty(extraCommonParamsMap)
				? URLEncoder.encode(Joiner.on("&").withKeyValueSeparator("=").join(extraCommonParamsMap), "UTF-8") : "";
		//拼接支付中心调用url
		url.append(dest).
				append("?request_serial=").append(uuid).
				append("&uid=").append(user.getId()).
				append("&partner=").append(reqMap.get("partner")).
				append("&platform_code=").append(StringUtils.defaultIfEmpty(platformCode, "")).
				append("&pay_type=").append(payType).
				append("&notify_url=").append(notifyUrl).
				append("&version=").append(VERSION).
				append("&charset=").append(CHARSET_UTF8);
		if (StringUtils.isNotBlank(returnUrl)) {
			url.append("&return_url=").append(returnUrl);
			reqMap.put("return_url", EncodeUtils.urlDecode(returnUrl));
		}

		if (Constants.WECHAT_SIGN_H5.equals(payType)) {
			url.append("&cip=").append(getUserIp(servletRequest));
			reqMap.put("cip", getUserIp(servletRequest));
		}
		if (MapUtils.isNotEmpty(extraCommonParamsMap)) {
			url.append("&extra_common_params=").append(extraCommonParamsStr);
		}
        boolean supportServiceProvider = CloudConfigUtil.enableServiceProvider(reqMap.get("partner"));
        if (supportServiceProvider) {
            String payCenterServiceProviderCode = PayUtils.getPayCenterServiceProviderCode();
            url.append("&service_provider=").append(payCenterServiceProviderCode);
            reqMap.put("service_provider", payCenterServiceProviderCode);
        }
        String signKey = supportServiceProvider ? PayUtils.getPayCenterServiceProviderKey() : PayUtils.getPayCenterSignKey();
		url.append("&extend_params=").append(extendParamsValueEncode).
				append("&sign=").append(PayUtils.signMessageRequest(reqMap, signKey));

		LOGGER.info("[reqMap:{}]", reqMap);
		return url.toString();
	}

	public RenewVipInfoVO getVipInfo(Long vipType, RenewVipInfoVO vipInfo, Integer agreementType, List<DutUserNew> dutUserNews) {
		VipType vipTypeObj = vipTypeManager.getVipTypeById(vipType);
        vipInfo.setVipType(vipType.intValue());
        vipInfo.setVipTypeName(vipTypeObj.getName());
		putUpgradeSourceVip(vipType, vipInfo, agreementType, dutUserNews);
		return vipInfo;
	}

	private void putUpgradeSourceVip(Long vipType, RenewVipInfoVO vipInfo, Integer agreementType, List<DutUserNew> dutUserNews) {
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return;
        }
        DutUserNew dutUserNew = dutUserNews.stream()
            .filter(DutUserNew::upgradeDutUser)
            .filter(d -> ObjectUtils.equals(d.getAgreementType(), agreementType))
            .findFirst().orElse(null);
        vipInfo.setSourceVipType(dutUserNew != null ? dutUserNew.getSourceVipType().intValue() : null);
	}

	/**
	 * 获取客户端IP地址
	 *
	 * @return String IP address
	 */
	public String getUserIp(HttpServletRequest servletRequest) {
		return TempIpUtil.getAndDiffIp(servletRequest);
	}

	private boolean isTv(Long vipType) {
		return null != vipType && (Constants.VIP_USER_TV == vipType || Constants.VIP_USER_TV_DIAMOND == vipType);
	}

	// 爱奇艺极速版
	private boolean isIqiyiBasicPlatform(String platform) {
		return Constants.PLATFORM_IQIYI_BASIC_GPHONE.equals(platform);
	}

    public List<UnusedGiftInfo> getUnusedGiftList(String authCookie, Integer vipType) {
        if (StringUtils.isBlank(authCookie) || vipType == null) {
            return Collections.emptyList();
        }
        return autoRenewMarketingProxy.getUnusedGiftList(authCookie, vipType);
    }


    public List<OtherRenewServiceInfoVO> buildOtherRenewServiceInfoVO(UserInfo userInfo, ManagementDto managementDto) {
        ArrayList<OtherRenewServiceInfoVO> vos = new ArrayList<>();
        OtherRenewServiceInfoVO familyTypeRenewInfo = getFamilyTypeRenewInfo(userInfo.getId());
        if (familyTypeRenewInfo != null) {
            vos.add(familyTypeRenewInfo);
        }
        List<OtherRenewServiceInfoVO> otherRenewServiceInfoVOS = getUserOtherRenewServiceInfoVOS(userInfo, managementDto);
        if (CollectionUtils.isNotEmpty(otherRenewServiceInfoVOS)) {
            vos.addAll(otherRenewServiceInfoVOS);
        }
        return vos;
    }


    private OtherRenewServiceInfoVO getFamilyTypeRenewInfo(Long uid) {
        if (uid == null) {
            return null;
        }
        FamilyBindRelation bindRelation = vipChargeApi.queryBindRelationByTargetUid(uid);
        if (bindRelation == null) {
            return null;
        }
        String productName = "黄金VIP亲情卡套餐连续包月";
        String sourcePhone = bindRelation.getSourcePhone();
        if (StringUtils.isNotBlank(sourcePhone)) {
            sourcePhone = StrUtil.replace(sourcePhone, 3, 7, '*');
        }
        return OtherRenewServiceInfoVO.builder()
            .productName(productName)
            .tipText(String.format(OTHER_FAMILY_RENEW_INFO_TIP_TEXT, sourcePhone, productName))
            .agreementType(AgreementTypeEnum.FAMILY_CARD_PACKAGE.getValue())
            .build();
    }

    private List<OtherRenewServiceInfoVO> getUserOtherRenewServiceInfoVOS(UserInfo userInfo, ManagementDto managementDto) {
        Integer bizType = managementDto.getBizType();
        Long uid = userInfo.getId();
        BizTypeEnum bizTypeEnum = BizTypeEnum.of(bizType);
        if (bizTypeEnum == null) {
            return Collections.emptyList();
        }
        List<DutUserNew> dutUserNews = userAgreementService.getUserWithAllAgreements(uid, null)
            .stream()
            .filter(DutUserNew::notInvalidAndFinished)
            .sorted(Comparator.comparing(DutUserNew::getOperateTime).reversed())
            .collect(Collectors.toList());
        List<DutUserNew> filteredDutUserNews = filterSameVipType(dutUserNews);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return Collections.emptyList();
        }
        return buildOtherRenewServiceInfoByBizType(bizType, filteredDutUserNews);
    }

    public List<OtherRenewServiceInfoVO> buildOtherRenewServiceInfoByBizType(Integer bizType, List<DutUserNew> dutUserNews) {
        List<Integer> needShowOtherRenewInfoType = CloudConfigUtil.getOtherRenewInfoTypeVipType(bizType);
        if (CollectionUtils.isEmpty(needShowOtherRenewInfoType)) {
            return Collections.emptyList();
        }
        ArrayList<OtherRenewServiceInfoVO> otherRenewServiceInfoVOS = new ArrayList<>(dutUserNews.size());
        for (Integer type : needShowOtherRenewInfoType) {
            OtherRenewServiceInfoEnum infoEnum = OtherRenewServiceInfoEnum.valueOf(type);
            switch (infoEnum) {
                case ZHIMA_GO:
                    addZhiMaGoRenewInfo(otherRenewServiceInfoVOS, dutUserNews);
                    break;
                case WECHATS_CORE:
                    addWechatScoreRenewInfo(otherRenewServiceInfoVOS, dutUserNews);
                    break;
                case IQIYI_GROUP:
                    addMainSiteRenewInfo(otherRenewServiceInfoVOS, dutUserNews, bizType);
                    break;
                case QIYIGUO_GROUP:
                    addTvRenewInfo(otherRenewServiceInfoVOS, dutUserNews);
                    break;
                case BASIC:
                    addBasicVipRenewInfo(otherRenewServiceInfoVOS, dutUserNews);
                    break;
                case FUN:
                    addFunVipRenewInfo(otherRenewServiceInfoVOS, dutUserNews);
                    break;
                case VR:
                    addVrVipRenewInfo(otherRenewServiceInfoVOS, dutUserNews);
                    break;
                default:
                    break;
            }
        }
        return otherRenewServiceInfoVOS;
    }

    /**
     * 构造Fun会员在其他续费信息区块的展示
     * @param otherRenewServiceInfoVOS
     * @param dutUserNews
     */
    private void addFunVipRenewInfo(ArrayList<OtherRenewServiceInfoVO> otherRenewServiceInfoVOS, List<DutUserNew> dutUserNews) {
        List<OtherRenewServiceInfoVO> funVipRenewInfo = getFunVipRenewInfo(dutUserNews);
        if (CollectionUtils.isNotEmpty(funVipRenewInfo)) {
            otherRenewServiceInfoVOS.addAll(funVipRenewInfo);
        }
    }

    /**
     * 构造VR会员在其他续费信息区块的展示
     * @param otherRenewServiceInfoVOS
     * @param dutUserNews
     */
    private void addVrVipRenewInfo(ArrayList<OtherRenewServiceInfoVO> otherRenewServiceInfoVOS, List<DutUserNew> dutUserNews) {
        List<OtherRenewServiceInfoVO> vrVipRenewInfo = getVrVipRenewInfo(dutUserNews);
        if (CollectionUtils.isNotEmpty(vrVipRenewInfo)) {
            otherRenewServiceInfoVOS.addAll(vrVipRenewInfo);
        }
    }

    // 补全getVrVipRenewInfo
    private List<OtherRenewServiceInfoVO> getVrVipRenewInfo(List<DutUserNew> dutUserNews) {
        List<DutUserNew> vrDutUser = dutUserNews.stream()
            .filter(d -> needShowOtherVipRenewInfoBySingleVipType(d, VipTypesEnum.VIPTYPE_VR.getValue().longValue()))
            .filter(DutUserNew::isAutoRenewUser)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vrDutUser)) {
            return Collections.emptyList();
        }
        return vrDutUser.stream()
            .map(d -> constructOtherRenewInfo(d, OTHER_RENEW_INFO_TIP_TEXT))
            .collect(Collectors.toList());
    }



    private List<OtherRenewServiceInfoVO> getFunVipRenewInfo(List<DutUserNew> dutUserNews) {
        List<DutUserNew> tvDutUser = dutUserNews.stream()
            .filter(d -> needShowOtherVipRenewInfoBySingleVipType(d, VipTypesEnum.VIPTYPE_FUN.getValue().longValue()))
            .filter(DutUserNew::isAutoRenewUser)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tvDutUser)) {
            return Collections.emptyList();
        }
        return tvDutUser.stream()
            .map(d -> constructOtherRenewInfo(d, OTHER_TV_RENEW_INFO_TIP_TEXT))
            .collect(Collectors.toList());
    }

    /**
     * 构造爱奇异果员在其他续费信息区块的展示
     * @param otherRenewServiceInfoVOS
     * @param dutUserNews
     */
    private void addTvRenewInfo(ArrayList<OtherRenewServiceInfoVO> otherRenewServiceInfoVOS, List<DutUserNew> dutUserNews) {
        List<OtherRenewServiceInfoVO> tvRenewInfo = getTvRenewInfo(dutUserNews);
        if (CollectionUtils.isNotEmpty(tvRenewInfo)) {
            otherRenewServiceInfoVOS.addAll(tvRenewInfo);
        }
    }

    /**
     * 构造爱奇艺会员在其他续费信息区块的展示
     * @param otherRenewServiceInfoVOS
     * @param dutUserNews
     */
    private void addMainSiteRenewInfo(ArrayList<OtherRenewServiceInfoVO> otherRenewServiceInfoVOS, List<DutUserNew> dutUserNews, Integer bizType) {
        List<OtherRenewServiceInfoVO> mainSiteRenewInfo = getMainSiteRenewInfo(dutUserNews, bizType);
        if (CollectionUtils.isNotEmpty(mainSiteRenewInfo)) {
            otherRenewServiceInfoVOS.addAll(mainSiteRenewInfo);
        }
    }


    /**
     * 构造基础会员在其他续费信息处的展示
     * @param otherRenewServiceInfoVOS
     * @param dutUserNews
     */
    private void addBasicVipRenewInfo(ArrayList<OtherRenewServiceInfoVO> otherRenewServiceInfoVOS, List<DutUserNew> dutUserNews) {
        List<OtherRenewServiceInfoVO> basicVipRenewInfo = getBasicVipRenewInfo(dutUserNews);
        if (CollectionUtils.isNotEmpty(basicVipRenewInfo)) {
            otherRenewServiceInfoVOS.addAll(basicVipRenewInfo);
        }
    }

    /**
     * 构造微信支付分在其他续费信息处的展示
     * @param otherRenewServiceInfoVOS
     * @param dutUserNews
     */
    private void addWechatScoreRenewInfo(ArrayList<OtherRenewServiceInfoVO> otherRenewServiceInfoVOS, List<DutUserNew> dutUserNews) {
        List<OtherRenewServiceInfoVO> wechatScoreRenewInfo = getWechatScoreRenewInfo(dutUserNews);
        if (CollectionUtils.isNotEmpty(wechatScoreRenewInfo)) {
            otherRenewServiceInfoVOS.addAll(wechatScoreRenewInfo);
        }
    }

    /**
     * 构造芝麻go在其他续费信息处的展示
     * @param otherRenewServiceInfoVOS
     * @param dutUserNews
     */
    private void addZhiMaGoRenewInfo(ArrayList<OtherRenewServiceInfoVO> otherRenewServiceInfoVOS, List<DutUserNew> dutUserNews) {
        List<OtherRenewServiceInfoVO> zhimaGoRenewInfo = getZhimaGoRenewInfo(dutUserNews);
        if (CollectionUtils.isNotEmpty(zhimaGoRenewInfo)) {
            otherRenewServiceInfoVOS.addAll(zhimaGoRenewInfo);
        }
    }


    private List<OtherRenewServiceInfoVO> getBasicVipRenewInfo(List<DutUserNew> dutUserNews) {
        List<DutUserNew> basicVipDutUser = dutUserNews.stream()
            .filter(d -> needShowOtherVipRenewInfoBySingleVipType(d, VipTypesEnum.VIPTYPE_BASIC.getValue().longValue()))
            .filter(DutUserNew::isAutoRenewUser)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(basicVipDutUser)) {
            return Collections.emptyList();
        }
        return basicVipDutUser.stream()
            .map(d -> constructOtherRenewInfo(d, OTHER_BASIC_RENEW_INFO_TIP_TEXT))
            .collect(Collectors.toList());
    }

    private List<OtherRenewServiceInfoVO> getTvRenewInfo(List<DutUserNew> dutUserNews) {
        List<Long> vipTypeSort = CloudConfigUtil.getBizManagementVipTypeSort(BizTypeEnum.TV.getValue());
        List<DutUserNew> tvDutUser = dutUserNews.stream()
            .filter(d -> needShowTvRenewInfo(d, vipTypeSort))
            .filter(DutUserNew::isAutoRenewUser)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tvDutUser)) {
            return Collections.emptyList();
        }
        //此处需要根据vipTypeSort中的顺序对tvDutUser进行排序
        tvDutUser.sort(Comparator.comparing(d -> vipTypeSort.indexOf(d.getVipType())));
        return tvDutUser.stream()
            .map(d -> constructOtherRenewInfo(d, OTHER_TV_RENEW_INFO_TIP_TEXT))
            .collect(Collectors.toList());
    }

    private List<OtherRenewServiceInfoVO> getMainSiteRenewInfo(List<DutUserNew> dutUserNews, Integer bizType) {
        List<DutUserNew> mainSiteDutUser = dutUserNews.stream()
            .filter(this::needShowMainSiteRenewInfo)
            .filter(DutUserNew::isAutoRenewUser)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mainSiteDutUser)) {
            return Collections.emptyList();
        }
        // 此处是为了满足VR会员在其他续费信息区块不展示白金、星钻会员
        if (BizTypeEnum.VR.getValue().equals(bizType)) {
            List<Long> excludeOtherRenewVipTypes = CloudConfigUtil.getExcludeOtherRenewVipTypesByBizType(bizType);
                mainSiteDutUser = mainSiteDutUser.stream()
                .filter(d -> !excludeOtherRenewVipTypes.contains(d.getVipType()))
                .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(mainSiteDutUser)) {
            return Collections.emptyList();
        }
        List<Long> vipTypeSort = CloudConfigUtil.getBizManagementVipTypeSort(BizTypeEnum.MAIN_SITE.getValue());
        //此处需要根据vipTypeSort中的顺序对mainSiteDutUser进行排序
        mainSiteDutUser.sort(Comparator.comparing(d -> vipTypeSort.indexOf(d.getVipType())));
        return mainSiteDutUser.stream()
            .map(d -> constructOtherRenewInfo(d, OTHER_MAIN_SITE_RENEW_INFO_TIP_TEXT))
            .collect(Collectors.toList());
    }

    private List<DutUserNew> filterSameVipType(List<DutUserNew> dutUserNews) {
        ArrayList<DutUserNew> news = new ArrayList<>();
        for (DutUserNew userNew : dutUserNews) {
            if (notExistedSameType(news, userNew)) {
                news.add(userNew);
            }
        }
        return news;
    }

    private boolean notExistedSameType(List<DutUserNew> news, DutUserNew dutUserNew) {
        if (CollectionUtils.isEmpty(news)) {
            return true;
        }
        return news.stream()
            .noneMatch(dutUser -> dutUser.getAgreementType().equals(dutUserNew.getAgreementType())
                && dutUser.getVipType().equals(dutUserNew.getVipType())
                && dutUser.getAmount().equals(dutUserNew.getAmount())
                && dutUser.getAutoRenew() != null
                && dutUser.getAutoRenew().equals(dutUserNew.getAutoRenew()));
    }

    private boolean needShowMainSiteRenewInfo(DutUserNew dutUserNew) {
        Integer agreementType = dutUserNew.getAgreementType();
        return AgreementTypeEnum.AUTO_RENEW.getValue() == agreementType
            && VipType.mainSiteVipType(dutUserNew.getVipType().intValue())
            && DutUserNew.RENEW_AUTO == dutUserNew.getAutoRenew();
    }

    private boolean needShowTvRenewInfo(DutUserNew dutUserNew, List<Long> tvVipTypeList) {
        Integer agreementType = dutUserNew.getAgreementType();
        return tvVipTypeList.contains(dutUserNew.getVipType())
            && AgreementTypeEnum.AUTO_RENEW.getValue() == agreementType
            && DutUserNew.RENEW_AUTO == dutUserNew.getAutoRenew();
    }

    private boolean needShowOtherVipRenewInfoBySingleVipType(DutUserNew dutUserNew, Long vipType) {
        Integer agreementType = dutUserNew.getAgreementType();
        return AgreementTypeEnum.AUTO_RENEW.getValue() == agreementType
            && dutUserNew.getVipType().equals(vipType)
            && DutUserNew.RENEW_AUTO == dutUserNew.getAutoRenew();
    }


    private OtherRenewServiceInfoVO constructOtherRenewInfo(DutUserNew dutUserNew, String tipText) {
        VipType vipType = vipTypeManager.getVipTypeById(dutUserNew.getVipType());
        Integer amount = dutUserNew.getAmount();
        String productName = vipType.getName() + MapUtils.getString(Constants.amountNameMap(), amount, amount + "个月");
        return OtherRenewServiceInfoVO.builder()
            .productName(productName)
            .tipText(String.format(tipText, productName))
            .agreementType(AgreementTypeEnum.AUTO_RENEW.getValue())
            .url(null)
            .build();
    }


    private List<OtherRenewServiceInfoVO> getWechatScoreRenewInfo(List<DutUserNew> dutUserNews) {
        List<DutUserNew> zhimaGoDutUser = dutUserNews.stream()
            .filter(this::needShowWechatScoreRenewInfo)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(zhimaGoDutUser)) {
            return Collections.emptyList();
        }
        return zhimaGoDutUser.stream()
            .map(this::constructWechatScoreRenewInfo)
            .collect(Collectors.toList());
    }

    private OtherRenewServiceInfoVO constructWechatScoreRenewInfo(DutUserNew dutUserNew) {
        VipType vipTypeObject = vipTypeManager.getVipTypeById(dutUserNew.getVipType());
        String vipTypeName = vipTypeObject != null ? vipTypeObject.getName() : null;
        String productName = "微信支付-先看后付";
        if (StringUtils.isNotBlank(vipTypeName)) {
            productName += String.format("(%s)", vipTypeName);
        }
        return OtherRenewServiceInfoVO.builder()
            .productName(productName)
            .tipText(String.format(OTHER_RENEW_INFO_TIP_TEXT, productName))
            .url("https://vip.iqiyi.com/html5VIP/activity/wxAfterPay/index.html?rid=162823413881168")
            .agreementType(AgreementTypeEnum.WECHAT_PAY_SCORE.getValue())
            .build();
    }


    private boolean needShowWechatScoreRenewInfo(DutUserNew dutUserNew) {
        Integer agreementType = dutUserNew.getAgreementType();
        Integer autoRenew = dutUserNew.getAutoRenew();
        return AgreementTypeEnum.WECHAT_PAY_SCORE.getValue() == agreementType
            && (AgreementStatusEnum.VALID.getValue() == autoRenew || AgreementStatusEnum.CANCEL_PENDING.getValue() == autoRenew);
    }



    private List<OtherRenewServiceInfoVO> getZhimaGoRenewInfo(List<DutUserNew> dutUserNews) {
        List<DutUserNew> zhimaGoDutUser = dutUserNews.stream()
            .filter(this::needShowZhimaGoRenewInfo)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(zhimaGoDutUser)) {
            return Collections.emptyList();
        }
        return zhimaGoDutUser.stream()
            .map(this::constructZhimaGoRenewInfo)
            .collect(Collectors.toList());
    }

    private OtherRenewServiceInfoVO constructZhimaGoRenewInfo(DutUserNew dutUserNew) {
        VipType vipTypeObject = vipTypeManager.getVipTypeById(dutUserNew.getVipType());
        String vipTypeName = vipTypeObject != null ? vipTypeObject.getName() : null;
        String productName = "芝麻GO月月省";
        if (StringUtils.isNotBlank(vipTypeName)) {
            productName += String.format("(%s)", vipTypeName);
        }
        return OtherRenewServiceInfoVO.builder()
            .productName(productName)
            .tipText(String.format(OTHER_RENEW_INFO_TIP_TEXT, productName))
            .url("https://vip.iqiyi.com/html5VIP/activity/zhiMaGo2022/index.html?rid=165882488373194")
            .agreementType(AgreementTypeEnum.ZHIMA_GO.getValue())
            .build();
    }


    private boolean needShowZhimaGoRenewInfo(DutUserNew dutUserNew) {
        Integer agreementType = dutUserNew.getAgreementType();
        Integer autoRenew = dutUserNew.getAutoRenew();
        return AgreementTypeEnum.ZHIMA_GO.getValue() == agreementType
            && (AgreementStatusEnum.VALID.getValue() == autoRenew || AgreementStatusEnum.SETTLING.getValue() == autoRenew);
    }

    public List<RenewGiftVO> buildRenewGiftVOList(String authCookie, List<ManagementRenewInfoVO> autoRenewList) {
        ArrayList<RenewGiftVO> renewGiftVOList = new ArrayList<>();
        List<RenewGiftVO> priceInsuredPeriodRenewGift = autoRenewList.stream()
            .flatMap(managementRenewInfoVO -> buildDiscountPriceRenewGift(managementRenewInfoVO).stream())
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        renewGiftVOList.addAll(priceInsuredPeriodRenewGift);
        List<UnusedGiftInfo> unusedGiftList = new ArrayList<>();
        for (ManagementRenewInfoVO managementRenewInfoVO : autoRenewList) {
            RenewVipInfoVO vipInfo = managementRenewInfoVO.getVipInfo();
            List<UnusedGiftInfo> unusedGiftInfo = getUnusedGiftList(authCookie, vipInfo.getVipType());
            if (CollectionUtils.isEmpty(unusedGiftInfo)) {
                continue;
            }
            unusedGiftList.addAll(unusedGiftInfo);
        }

        List<UnusedGiftInfo> unusedGiftInfoList = unusedGiftList.stream()
            .distinct()
            .sorted(Comparator.comparing(UnusedGiftInfo::getRegisterTime).reversed())
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unusedGiftInfoList)) {
            return renewGiftVOList;
        }
        List<RenewGiftVO> renewGiftVOS = unusedGiftInfoList.stream()
            .filter(Objects::nonNull)
            .distinct()
            .map(this::buildRenewGiftVOByUnusedGiftList)
            .collect(Collectors.toList());
        renewGiftVOList.addAll(renewGiftVOS);
        return renewGiftVOList;
    }

    private RenewGiftVO buildRenewGiftVOByUnusedGiftList(UnusedGiftInfo unusedGiftInfo) {
        Integer displayMode = null;
        String detailUrl = unusedGiftInfo.getDetailUrl();
        String descriptionText = unusedGiftInfo.getDescriptionText();
        if (StringUtils.isNotBlank(detailUrl)) {
            displayMode = DetailDisplayMode.URL.getValue();
        } else if (StringUtils.isNotBlank(descriptionText)) {
            displayMode = DetailDisplayMode.COVER.getValue();
        }
        Long giftValidEndTime = unusedGiftInfo.getGiftValidEndTime();
        Integer giftType = unusedGiftInfo.getGiftType();
        if (giftValidEndTime != null && autoRenewMarketingCouponTypes.contains(giftType)) {
            String validEndTimeInfo = String.format(VALID_END_TIME_TIP_TEXT, DateHelper.getFormatDate(new Date(giftValidEndTime)));
            descriptionText = StringUtils.isBlank(descriptionText) ? validEndTimeInfo : descriptionText + validEndTimeInfo;
        }
        return RenewGiftVO.builder()
            .name(unusedGiftInfo.getGiftName())
            .giftType(giftType)
            .giftTypeDesc("renewBenefit-" + giftType)
            .vipType(unusedGiftInfo.getGiftVipType())
            .promotionText(unusedGiftInfo.getPromotionText())
            .detailTips(descriptionText)
            .giftCode(unusedGiftInfo.getGiftCode())
            .detailDisplayMode(displayMode)
            .detailUrl(unusedGiftInfo.getDetailUrl())
            .build();
    }

    private List<RenewGiftVO> buildDiscountPriceRenewGift(ManagementRenewInfoVO managementRenewInfoVO) {
        if (managementRenewInfoVO == null) {
            return Collections.emptyList();
        }
        RenewInfoVO autoRenewInfo = managementRenewInfoVO.getAutoRenewInfo();
        ActPeriodTip actPeriodTips = autoRenewInfo.getActPeriodTips();
        if (actPeriodTips == null) {
            return Collections.emptyList();
        }
        if (actPeriodTips.getPriceInsuredPeriodInfo() == null && actPeriodTips.getUserRenewDiscountInfo() == null) {
            return Collections.emptyList();
        }
        RenewVipInfoVO vipInfo = managementRenewInfoVO.getVipInfo();
        Integer vipType = vipInfo.getVipType();
        String productName = autoRenewInfo.getProductName();
        List<RenewGiftVO> result = new ArrayList<>();
        if (actPeriodTips.getUserRenewDiscountInfo() != null) {
            Integer firstAgreementNo = managementRenewInfoVO.getAutoRenewInfo().getFirstDutInfo().getFirstAgreementNo();
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(firstAgreementNo);
            DiscountAgreementTip discountAgreementTip = discountAgreementManagementTips.get(agreementNoInfo.getTemplateCode());
            //默认为首X期优惠
            if (discountAgreementTip == null) {
                discountAgreementTip = DiscountAgreementTip.newDefaultTip();
            }
            RenewGiftVO renewGiftVO = TipsUtils.buildRenewDiscountTips(discountAgreementTip, actPeriodTips.getUserRenewDiscountInfo(), productName);
            renewGiftVO.setVipType(vipType);
            result.add(renewGiftVO);
        }
        if (actPeriodTips.getPriceInsuredPeriodInfo() != null) {
            RenewGiftVO renewGiftVO = TipsUtils.buildNewPriceInsuredPeriodTips(actPeriodTips.getPriceInsuredPeriodInfo(), productName);
            renewGiftVO.setVipType(vipType);
            result.add(renewGiftVO);
        }
        return result;
    }

    public ManagementRenewGiftArea buildManagementRenewGiftArea(String authCookie, Long uid, List<ManagementRenewInfoVO> autoRenewList) {
        List<RenewGiftVO> renewGiftArea = buildRenewGiftVOList(authCookie, autoRenewList);
        return ManagementRenewGiftArea.builder()
            .renewGiftArea(renewGiftArea)
            .titleText(getRenewGiftAreaTipText(autoRenewList, uid))
            .build();
    }

    private String getRenewGiftAreaTipText(List<ManagementRenewInfoVO> autoRenewList, Long uid) {
        Integer maxOpenDuration = getMaxOpenDuration(autoRenewList, uid);
        return String.format(RENEW_GIFT_AREA_TIP_TEXT, maxOpenDuration);
    }

    private Integer getMaxOpenDuration(List<ManagementRenewInfoVO> autoRenewList, Long uid) {
        List<Integer> durationList = new ArrayList<>();
        for (ManagementRenewInfoVO managementRenewInfoVO : autoRenewList){
            RenewInfoVO autoRenewInfo = managementRenewInfoVO.getAutoRenewInfo();
            int newMobileRenewDuration = 0;
            //ToB新话费使用signTime字段值
            Optional<PayTypeInfoVO> newMobilePayTypeInfo = autoRenewInfo.getPayTypeInfo().stream()
                .filter(payTypeInfoVO -> NewMobileRenewComponent.NEW_MOBILE_PAY_CHANNEL_LIST.contains(payTypeInfoVO.getPayChannel()))
                .findFirst();
            if (newMobilePayTypeInfo.isPresent()) {
                Timestamp signTime = newMobilePayTypeInfo.get().getSignTime();
                if (signTime != null) {
                    newMobileRenewDuration = DateHelper.getDayInterval(new Date(signTime.getTime()), new Date());
                }
            }
            RenewVipInfoVO vipInfo = managementRenewInfoVO.getVipInfo();
            Integer vipType = vipInfo.getVipType();
            Integer renewDuration = autoRenewInfoService.getRenewDuration(uid, vipType.longValue());
            if (newMobileRenewDuration > renewDuration) {
                durationList.add(newMobileRenewDuration);
            } else {
                durationList.add(renewDuration);
            }
        }
        return durationList.stream()
            .filter(Objects::nonNull)
            .max(Comparator.comparing(Integer::intValue))
            .orElse(0);
    }
}
