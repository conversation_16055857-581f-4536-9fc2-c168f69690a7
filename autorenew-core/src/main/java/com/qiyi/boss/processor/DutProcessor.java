package com.qiyi.boss.processor;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.qiyi.boss.Constants;
import com.qiyi.boss.component.SmartJedisClient;
import com.qiyi.boss.constants.CacheConstants;
import com.qiyi.boss.dto.AccountBindInfo;
import com.qiyi.boss.dto.AccountResponse;
import com.qiyi.boss.dto.CloseOrderRequest;
import com.qiyi.boss.utils.DutAccountApi;
import com.qiyi.boss.outerinvoke.PayCenterApi;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;

import static com.qiyi.boss.Constants.DEFAULT_PARTNER;
import static com.qiyi.boss.Constants.PASSPORT_USER_KEY;

/**
 * Created by IntelliJ IDEA.
 * <AUTHOR>
 * <AUTHOR>
 * Date: 13-12-6
 * Time: 下午6:24
 * To change this template use File | Settings | File Templates.
 */
@Component
@Slf4j
public class DutProcessor {

    public static final Integer RETRY_TIMES = 3;
    @Resource(name = "dutAccountApi")
    private DutAccountApi accountApi;

    @Resource(name ="payCenterApi")
    private PayCenterApi payCenterApi;

    @Resource
    private SmartJedisClient smartJedisClient;
    /**
     * 判断用户是否绑定自动续费
     *
     * @param userId 用户ID
     * @return return true if bind
     */
    public boolean isBind(long userId) {
        Optional<AccountResponse> accountResponse = queryAccountBindInfos(userId);
        return accountResponse.isPresent() && accountResponse.get().getData().stream().anyMatch(AccountBindInfo::isBind);
    }

    /**
     * 判断用户是否
     *
     * @param userId 用户ID
     * @param bindType 1:支付宝 3：百付宝 4:微信
     * @return return true if bind
     */
    public boolean isBind(long userId, int bindType) {
        Optional<AccountResponse> accountResponse = queryAccountBindInfos(userId, bindType, null, null);
        if (!accountResponse.isPresent()) {
            return false;
        }

        List<AccountBindInfo> accountBindInfos = accountResponse.get().getData();
        Optional<AccountBindInfo> bindedInfo = accountBindInfos.stream().filter(accountBindInfo ->
                accountBindInfo.isBind() && accountBindInfo.getType() == bindType).findFirst();
        return bindedInfo.isPresent();
    }

    public Optional<AccountResponse> queryAccountBindInfos(long userId) {
        return accountApi.queyrAccountBindInfo(userId);
    }

    public List<Integer> queryValidBindTypes(Long userId) {
        Optional<AccountResponse> accountResponse = accountApi.queyrAccountBindInfo(userId);
        return accountResponse.map(response -> response.getData().stream()
            .filter(AccountBindInfo::isBind)
            .map(AccountBindInfo::getType)
            .collect(Collectors.toList()))
            .orElseGet(Lists::newArrayList);
    }

    public Optional<AccountResponse> queryAccountBindInfos(long userId, Integer bindType, String partner, String partnerOrderNo) {
        return accountApi.queyrAccountBindInfo(userId, bindType, partner, partnerOrderNo);
    }

    public void closeAliPayAsyncDutOrder(Long uid, Long vipType, String orderCode) {
        if (StringUtils.isBlank(orderCode)) {
            return;
        }
        String partner = CloudConfigUtil.getPartnerByVipType(vipType);
        CloseOrderRequest closeOrderRequest = CloseOrderRequest.builder()
            .user_id(uid)
            .partner(partner != null ? partner : DEFAULT_PARTNER)
            .contains_coupon(0)
            .partner_order_no(orderCode)
            .build();
        log.info("[closeAliPayAsyncDutOrder][closeOrderRequest :{}]", closeOrderRequest);
        payCenterApi.closeOrder(closeOrderRequest);
        String orderCacheKey = CacheConstants.bulidAliPayAsyncDutTaskOrderCacheKey(uid, vipType);
        smartJedisClient.delete(orderCacheKey);
    }

    public String getInProgressAliPayAsyncDutOrder(Long uid, Long vipType) {
        String orderCacheKey = CacheConstants.bulidAliPayAsyncDutTaskOrderCacheKey(uid, vipType);
        return getAliPayAsyncDutCacheOrderCode(orderCacheKey);
    }


    public String getAliPayAsyncDutCacheOrderCode(String orderCacheKey) {
        String orderCode = null;
        try {
            orderCode = smartJedisClient.get(orderCacheKey, new TypeReference<String>() {
            }, true);
        } catch (Exception e) {
            log.error("getAliPayAsyncDutOrder failed from cache. param: {}", orderCacheKey, e);
            for (int i = 0; i < RETRY_TIMES; i++) {
                try {
                    orderCode = smartJedisClient.get(orderCacheKey, new TypeReference<String>() {
                    }, true);
                    break;
                } catch (Exception retryException) {
                    log.error("[Retry getAliPayAsyncDutOrder failed from cache. param: {}", orderCacheKey, retryException);
                }
            }
        }
        return orderCode;
    }

    /**
     * 查询用户绑定的渠道列表
     *
     * @param userId 用户ID
     * @return 渠道列表
     */
    public List<Integer> queryBindType(long userId) {
        Optional<AccountResponse> accountResponse = queryAccountBindInfos(userId);
        return accountResponse.map(response -> response.getData().stream()
                .filter(AccountBindInfo::isBind)
                .map(AccountBindInfo::getType)
                .collect(Collectors.toList())).orElseGet(Lists::newArrayList);

    }

    public boolean isBind(AccountResponse accountResponse, Integer dutType) {

        if (accountResponse == null) {
            return false;
        }

        List<AccountBindInfo> accountBindInfos = accountResponse.getData();
        if (CollectionUtils.isEmpty(accountBindInfos) ) {
            return false;
        }

        return accountBindInfos.stream()
                .anyMatch(accountBindInfo -> accountBindInfo.isBind() && accountBindInfo.getType().equals(dutType));
    }

    public String getWechatCompensateSign(AccountResponse accountResponse, Integer dutType) {

        if (accountResponse == null) {
            return null;
        }

        List<AccountBindInfo> accountBindInfos = accountResponse.getData();
        if (CollectionUtils.isEmpty(accountBindInfos) ) {
            return null;
        }

        return accountBindInfos.stream()
                .filter(accountBindInfo -> accountBindInfo.isWechatCompensateSign() && accountBindInfo.getType().equals(dutType))
                .findFirst()
                .map(AccountBindInfo::getSignFlag)
                .orElse(null);

    }

    public String getAccouctSignCode(AccountResponse accountResponse, Integer dutType) {
        if (accountResponse == null) {
            return null;
        }

        List<AccountBindInfo> accountBindInfos = accountResponse.getData();
        if (CollectionUtils.isEmpty(accountBindInfos) ) {
            return null;
        }

        Optional<AccountBindInfo> accountBindInfo = accountBindInfos.stream()
                .filter(bindInfo -> bindInfo.isBind() && bindInfo.getType().equals(dutType))
                .findFirst();

        return accountBindInfo.map(AccountBindInfo::getSignCode).orElse(null);
    }

    public AccountBindInfo getAccountSignCode(AccountResponse accountResponse, Integer dutType, List<Integer> coverageDutTypes) {
        if (accountResponse == null) {
            return null;
        }
        List<AccountBindInfo> accountBindInfos = accountResponse.getData();
        if (CollectionUtils.isEmpty(accountBindInfos) ) {
            return null;
        }

        Optional<AccountBindInfo> accountBindInfo = accountBindInfos.stream()
                .filter(bindInfo -> bindInfo.isBind() && bindInfo.getType().equals(dutType))
                .findFirst();
        return accountBindInfo.orElseGet(() -> accountBindInfos.stream()
                .filter(bindInfo -> bindInfo.isBind() && coverageDutTypes.contains(bindInfo.getType()))
                .findFirst().orElse(null));
    }

}
