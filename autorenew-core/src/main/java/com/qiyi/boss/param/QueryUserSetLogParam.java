package com.qiyi.boss.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

import com.qiyi.boss.Constants;
import com.qiyi.boss.enums.AgreementTypeEnum;

/**
 * @auther: guojing
 * @date: 2023/8/26 14:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryUserSetLogParam {

    @NotNull(message = "用户ID不能为空")
    private Long uid;

    private Long vipType = Constants.VIP_DEFAULT_TYPE;

    private Integer agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();

}
