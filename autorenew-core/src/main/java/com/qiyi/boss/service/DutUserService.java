package com.qiyi.boss.service;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.qiyi.boss.Constants;
import com.qiyi.boss.dto.AutoRenewStatusDto;
import com.qiyi.boss.dto.RestrictionStrategyInfo;
import com.qiyi.boss.enums.AgreementStatusEnum;
import com.qiyi.boss.service.impl.AgreementCancelRestrictionManager;
import com.qiyi.boss.service.impl.AgreementManager;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.AppProductManager;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.service.impl.AutorenewRestrictionStrategyManager;
import com.qiyi.boss.service.impl.PaymentDutTypeManager;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.dao.DutUserNewDao;
import com.qiyi.vip.trade.autorenew.domain.AgreementCancelRestriction;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import com.qiyi.vip.trade.autorenew.domain.vo.DutUserNewVO;
import com.qiyi.vip.trade.autorenew.repository.DutUserRepository;
import com.qiyi.vip.trade.autorenew.service.DutRenewSetLogService;
import com.qiyi.vip.trade.qiyue.domain.AppProduct;

import static com.qiyi.boss.constants.AgreementConstants.EXCLUDE_AGREEMENT_TYPES;
import static com.qiyi.vip.trade.autorenew.domain.DutUserNew.caculateNextDutTime;
import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * created 2018/11/28 - 11:43
 */
@Service
@Slf4j
public class DutUserService {

    @Resource
    private AutoRenewDutTypeManager autoRenewDutTypeManager;
    @Resource
    private DutUserRepository dutUserRepository;
    @Resource
    private PaymentDutTypeManager paymentDutTypeManager;
    @Resource
    private AppProductManager appProductManager;
    @Resource
    private DutRenewSetLogService dutRenewSetLogService;
    @Resource
    private DutUserNewDao dutUserNewDao;
    @Resource
    private AutorenewRestrictionStrategyManager autorenewRestrictionStrategyManager;
    @Resource
    private AgreementCancelRestrictionManager agreementCancelRestrictionManager;
    @Resource
    private AgreementNoInfoManager agreementNoInfoManager;
    @Resource
    private AgreementManager agreementManager;
    @Resource
    private UserAgreementService userAgreementService;


    public Integer getAmountFromDutUser(Long userId, Integer type, Long vipType) {
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, vipType);
        if (null != dutUserNew && null != dutUserNew.getAmount()) {
            return dutUserNew.getAmount();
        }
        return Constants.AMOUNT_OF_COMMON_AUTORENEW;
    }

    public Integer getAmountFromDutUserNew(Long userId, Integer type, Long vipType, Integer agreementType) {
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, vipType);
        if (null != dutUserNew && null != dutUserNew.getAmount()) {
            return dutUserNew.getAmount();
        }
        if (dutUserNew != null && dutUserNew.getAgreementNo() != null) {
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(dutUserNew.getAgreementNo());
            return agreementNoInfo.getAmount();
        }

        List<Integer> weChatDutTypeList = autoRenewDutTypeManager.getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_WECHAT);
        return weChatDutTypeList.contains(type)
            ? paymentDutTypeManager.getWechatAmount(type)
            : Constants.AMOUNT_OF_COMMON_AUTORENEW;
    }

    public List<AutoRenewStatusDto> /**/queryAutoRenewStatus(final Long userId, final List<Long> vipTypes, final List<Integer> payChannels) {
//        List<DutUserNew> dutUserNews = dutUserRepository.findDutUserNews(userId);
        List<DutUserNew> dutUserNews = userAgreementService.getByExcludeAgreementTypesAndVipTypes(userId, EXCLUDE_AGREEMENT_TYPES, vipTypes, null);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return Collections.emptyList();
        }

        final List<AutoRenewStatusDto> autoRenewStatusDtoList = Lists.newLinkedList();

        Map<Long, List<DutUserNew>> vipTypeDutUserMap = dutUserNews
                .stream()
                .filter(dutUserNew -> vipTypes.contains(dutUserNew.getVipType()))
                .collect(groupingBy(DutUserNew::getVipType));

        for (Map.Entry<Long, List<DutUserNew>> entry : vipTypeDutUserMap.entrySet()) {
            entry.getValue().forEach(dutUserNew -> {
                AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutUserNew.getType());
                if (autoRenewDutType != null && payChannels.contains(autoRenewDutType.getPayChannel())) {
                    AutoRenewStatusDto autoRenewStatusDto = AutoRenewStatusDto
                            .builder()
                            .userId(userId)
                            .vipType(entry.getKey())
                            .dutType(dutUserNew.getType())
                            .agreementNo(dutUserNew.getAgreementNo())
                            .agreementType(dutUserNew.getAgreementType())
                            .autoRenew(dutUserNew.getAutoRenew())
                            .payChannel(autoRenewDutType.getPayChannel())
                            .renewPrice(dutUserNew.getRenewPrice())
                            .actCode(dutUserNew.getActCode())
                            .nextDutTime(dutUserNew.getNextDutTime())
                            .deadline(dutUserNew.getDeadline())
                            .build();
                    autoRenewStatusDtoList.add(autoRenewStatusDto);
                }
            });
        }

        return autoRenewStatusDtoList;
    }

    /**
     * 判断用户是否开通自动续费
     * @param userId 用户ID
     */
    public boolean isAutoRenew(long userId) {
//        List<DutUserNew> dutUserNews = dutUserRepository.findAllByUserIdAndVipType(userId, Constants.VIP_USER_SUPER);
        List<DutUserNew> dutUserNews = userAgreementService.getByExcludeAgreementTypesAndVipTypes(userId, EXCLUDE_AGREEMENT_TYPES, Lists.newArrayList(Constants.VIP_USER_SUPER), AgreementStatusEnum.VALID);
        for (DutUserNew dutUserNew : dutUserNews) {
            if (dutUserNew.isAutoRenewUser()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据用户ID, 获取自动续费状态
     */
    public Boolean isAutoRenewUser(long uid, Integer type, Long vipType) {
        DutUserNew dutUserNew;
        if (null != vipType) {
            dutUserNew = userAgreementService.getByDutTypeAndVipType(uid, type, vipType);
        } else {
            dutUserNew = userAgreementService.getByDutTypeAndVipType(uid, type, Constants.VIP_USER_SUPER);
        }

        return dutUserNew != null && dutUserNew.isAutoRenewUser();
    }

    /**
     * 判断用户是否开通IOS大陆自动续费
     *
     * @param dutUserNew {@link DutUserNew}
     * @return true 表示开通了IOS大陆自动续费
     */
    public boolean isIosMainLandAutoRenew(DutUserNew dutUserNew) {
        AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutUserNew.getType());
        return autoRenewDutType != null
                && autoRenewDutType.isAppleDutType()
                && dutUserNew.isAutoRenewUser()
                && dutUserNew.getVipType() == Constants.VIP_USER_SUPER;
    }

    public List<DutUserNew> getDutUserNews(Long userId, Long vipType, Integer agreementType, Integer autoRenew) {
        return dutUserRepository.findByUidAndVipTypeAndAutoRenew(userId, agreementType, vipType, autoRenew);
    }

    public List<DutUserNew> shardGetDutUserNews(Long userId, Integer autoRenew) {
//        return dutUserRepository.shardGetDutUserNews(userId, autoRenew);
        return userAgreementService.getByExcludeAgreementTypesAndVipTypes(userId, EXCLUDE_AGREEMENT_TYPES,null, AgreementStatusEnum.VALID);
    }

    public List<DutUserNew> getDutUserNewList(Long userId, Long vipType) {
//        return dutUserRepository.findAllByUserIdAndVipType(userId, vipType);
        return userAgreementService.getByExcludeAgreementTypesAndVipTypes(userId, EXCLUDE_AGREEMENT_TYPES, Lists.newArrayList(vipType), null);
    }

    /**
     * 处理代扣失败
     * @param dutUserNew {@link DutUserNew}
     */
    public void processDutFail(DutUserNew dutUserNew) {
        DutUserNewVO dutUserNewUpdater = DutUserNewVO.builder()
                .id(dutUserNew.getId())
                .userId(dutUserNew.getUserId())
                .interruptFlag(DutUserNew.INTERRUPT_FLAG_YES)
                .build();
        if (getGashNextDutTime(dutUserNew) != null) {
            dutUserNewUpdater.setNextDutTime(getGashNextDutTime(dutUserNew));
        }
        dutUserRepository.updateSelective(dutUserNewUpdater);
    }

    /**
     * 处理代扣成功
     * @param dutUserNew {@link DutUserNew}
     */
    public void processDutSuccess(DutUserNew dutUserNew) {
        DutUserNewVO dutUserNewUpdater = DutUserNewVO.builder()
                .id(dutUserNew.getId())
                .userId(dutUserNew.getUserId())
                .renewCount(dutUserNew.getRenewCount() + 1)
                .serialRenewCount(dutUserNew.getSerialRenewCount() + 1)
                .interruptFlag(DutUserNew.INTERRUPT_FLAG_NO)
                .build();
        if (getGashNextDutTime(dutUserNew) != null) {
            dutUserNewUpdater.setNextDutTime(getGashNextDutTime(dutUserNew));
        }
        dutUserRepository.updateSelective(dutUserNewUpdater);
    }

    public boolean isUserModifySubscribeAutoRenewAppId(DutUserNew dutUserNew, String appId) {
        List<AppProduct> appProductList = appProductManager.getListOfAppProductByAppId(appId);
        return !dutUserNew.getAmount().equals(appProductList.get(0).getQuantity());
    }

    /**
     * 获取 用户代扣原价
     */
    public Integer getOriginalDutPrice(DutUserNew dutUserNew) {
        if (dutUserNew.getContractPrice() != null && dutUserNew.getContractPrice() != 0) {
            return dutUserNew.getContractPrice();
        }
        return agreementManager.getDefaultRenewPrice(dutUserNew.getType(), dutUserNew.getAmount());
    }

    public DutUserNew getFirstDutUserNew(Long userId, Long vipType, Integer agreementType) {
        List<DutRenewSetLog> dutRenewSetLogs = Lists.newArrayList();
        List<DutUserNew> dutUserNews = getDutUserNewByPriority(userId, vipType, agreementType);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return null;
        }
        boolean bind = false;
        for (DutUserNew dutUserNew : dutUserNews) {
            //获得用户的自动续费开通日志
            DutRenewSetLog dutRenewSetLog = dutRenewSetLogService.getRecentlyDutRenewSetLog(userId, agreementType, dutUserNew.getType(), DutRenewSetLog.RENEW_SET);
            if (dutRenewSetLog != null) {
                dutRenewSetLogs.add(dutRenewSetLog);
            }
            bind = true;
        }
        //根据操作时间对日志排序,最晚开通的在前面
        Collections.sort(dutRenewSetLogs, (o1, o2) -> o2.getOperateTime().compareTo(o1.getOperateTime()));
        //绑定但是没有开通日志
        if (bind && CollectionUtils.isEmpty(dutRenewSetLogs)) {
            return dutUserNews.iterator().next();
        }
        Integer firstDutType = dutRenewSetLogs.get(0).getType();
        return dutUserNews.stream()
            .filter(item -> firstDutType.equals(item.getType()))
            .findFirst()
            .orElse(dutUserNews.get(0));
    }

    private List<DutUserNew> getDutUserNewByPriority(Long userId, Long vipType, Integer agreementType) {
        List<DutUserNew> dutUserNews;
        List<Integer> thirdPartyDutTypeList = autoRenewDutTypeManager.getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_IAP);
        dutUserNews = dutUserRepository.getShardAutoRenewUsers(agreementType, userId, thirdPartyDutTypeList, vipType);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            return dutUserNews;
        }
        List<Integer> mobileDutTypeList = autoRenewDutTypeManager.getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_MOBILE);
        if (CollectionUtils.isNotEmpty(mobileDutTypeList)) {
            dutUserNews = dutUserRepository.getShardAutoRenewUsers(agreementType, userId, mobileDutTypeList, vipType);
            if (CollectionUtils.isNotEmpty(dutUserNews)) {
                return dutUserNews;
            }
        }

        List<Integer> excludeDutTypeList = new ArrayList<>();
        excludeDutTypeList.addAll(thirdPartyDutTypeList);
        excludeDutTypeList.addAll(mobileDutTypeList);
        return dutUserRepository.getShardExcludeTypesAutoRenewUsers(userId, agreementType, excludeDutTypeList, null, vipType);
    }

    /**
     * 更新用户签约关系信息
     * @param dutUserNewVO {@link DutUserNewVO}
     */
    public void updateDutUser(DutUserNewVO dutUserNewVO) {
        if (dutUserNewVO == null || dutUserNewVO.getId() == null
                || dutUserNewVO.getUserId() == null
                || dutUserNewVO.getVipType() == null
                || dutUserNewVO.getType() == null) {
            throw new IllegalArgumentException("id and userId can not be null.");
        }
        log.info("update dut_user info, param:{}", dutUserNewVO);
        dutUserNewDao.updateDutUserByPrimaryKeyAndUserIdSelective(dutUserNewVO);
    }

    private Timestamp getGashNextDutTime(DutUserNew dutUserNew) {
        // 台湾GASH代扣方式只要发起代扣就更新下次续费时间
        // 为解决如下问题:扣款成功但是未给用户加权益,用户通过客服生成免费订单,那么,无法更新下次续费时间,导致下次无法续费.
        List<Integer> gashDutTypeList = autoRenewDutTypeManager
                .getDutTypeListByVipTypeAndPayChannel(dutUserNew.getVipType(), dutUserNew.getAgreementType(), PaymentDutType.PAY_CHANNEL_GASH);
        if (gashDutTypeList.contains(dutUserNew.getType())) {
            return caculateNextDutTime(dutUserNew.getNextDutTime());
        }
        return null;
    }

    /**
     * 获取取消限制策略信息
     */
    public RestrictionStrategyInfo restrictDateToCancel(List<DutUserNew> dutUserNews, Integer agreementType) {
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return null;
        }
        RestrictionStrategyInfo restrictionStrategyInfo = null;
        for (DutUserNew dutUserNew : dutUserNews) {
            Integer agreementNo = dutUserNew.getAgreementNo();
            if (agreementNo != null) {
                AgreementCancelRestriction cancelRestriction = agreementCancelRestrictionManager.getByAgreementNo(agreementNo);
                if (cancelRestriction != null) {
                    Timestamp restrictEndTime = DateHelper.caculateTime(dutUserNew.getSignTime(), cancelRestriction.getDuration(), Constants.PRODUCT_PERIODUNIT_DAY);
                    restrictionStrategyInfo = RestrictionStrategyInfo.builder()
                        .vipType(dutUserNew.getVipType())
                        .duration(cancelRestriction.getDuration())
                        .restrictEndDate(restrictEndTime.toLocalDateTime().toLocalDate())
                        .restrictEndTime(restrictEndTime)
                        .build();
                    break;
                }
            } else {
                DutRenewSetLog dutRenewSetLog = dutRenewSetLogService.getRecentlyDutRenewSetLog(dutUserNew.getUserId(), agreementType, dutUserNew.getType(), DutRenewSetLog.RENEW_SET);
                Timestamp recentlySetLogOperateTime = dutRenewSetLog == null || dutRenewSetLog.getOperateTime() == null ? null : dutRenewSetLog.getOperateTime();
                restrictionStrategyInfo = autorenewRestrictionStrategyManager.restrictDateToCancel(dutUserNew, recentlySetLogOperateTime);
                if (restrictionStrategyInfo != null) {
                    break;
                }
            }
        }

        return restrictionStrategyInfo;
    }

    /**
     * 获取用户的自动续费开通日志
     * sourceVipType = null时，会过滤掉升级的
     * @param userId
     * @param sourceVipType
     * @param vipType
     * @return
     */
    public List<DutRenewSetLog> getDutRenewSetLogs(Long userId, Long sourceVipType, Long vipType, Integer agreementType) {
        List<Integer> mobileDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_MOBILE);
        List<DutRenewSetLog> dutRenewSetLogs = Lists.newArrayList();

        //获得boss_dut_user_new表中的自动续费用户的列表,排除话费
        List<DutUserNew> dutUserNews = dutUserRepository.getShardExcludeTypesAutoRenewUsers(userId, agreementType, mobileDutTypeList, sourceVipType, vipType);
        for (DutUserNew userNew : dutUserNews) {
            //如果sourceVipType为空，过滤掉升级的
            if (sourceVipType == null && userNew.getSourceVipType() != null) {
                continue;
            }
            //获得用户的自动续费开通日志
            DutRenewSetLog dutRenewSetLog = dutRenewSetLogService.getRecentlyDutRenewSetLog(userId, userNew.getAgreementType(), userNew.getType(), DutRenewSetLog.RENEW_SET);
            if (dutRenewSetLog != null) {
                dutRenewSetLogs.add(dutRenewSetLog);
            }
        }
        //根据操作时间对日志排序,最晚开通的在前面
        dutRenewSetLogs.sort((o1, o2) -> o2.getOperateTime().compareTo(o1.getOperateTime()));

        /*
         * 处理开通多种微信代扣的情况,只对最晚开通的微信代扣方式扣费
         * (原则上用户最多只能开通黄金微信自动续费、青春套餐微信自动续费、黄金微信大时长自动续费其中的一种,某些异常情况下没有限制住需要特殊处理)
         */
        return dutRenewSetLogService.dealWechatRenew(dutRenewSetLogs, vipType, agreementType);
    }

}
