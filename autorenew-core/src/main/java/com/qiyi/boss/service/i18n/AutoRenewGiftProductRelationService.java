package com.qiyi.boss.service.i18n;

import com.qiyi.boss.autorenew.dto.AddAutoRenewDto;
import com.qiyi.boss.autorenew.dto.SaveDutUserDto;
import com.qiyi.boss.dto.CancelAutoRenewDto;
import com.qiyi.boss.model.SpecialVipUser;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.vip.trade.autorenew.domain.*;

import java.sql.Timestamp;
import java.util.List;

/**
 * 代扣相关业务逻辑
 *
 * <AUTHOR>
 * @version 11-11-22 - 下午2:57
 */
public interface AutoRenewGiftProductRelationService {

    String getActiveRelation(String sourceProductCode);

}
