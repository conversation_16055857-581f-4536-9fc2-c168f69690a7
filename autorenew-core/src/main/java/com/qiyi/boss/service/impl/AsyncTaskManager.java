package com.qiyi.boss.service.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

import com.qiyi.boss.utils.CronExpressionUtil;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.commons.constant.DelimiterChars;
import com.qiyi.vip.trade.autorenew.domain.AsyncTask;
import com.qiyi.vip.trade.autorenew.mapper.AsyncTaskMapper;
import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.uitls.mail.MailHelper;
import com.iqiyi.vip.uitls.model.MailHeader;

import static com.qiyi.vip.trade.autorenew.constants.ConfigConstants.SEND_ALARM_EMAIL;

/**
 * 异步持久化队列管理类
 *
 * <AUTHOR>
 */
@Component
public class AsyncTaskManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncTaskManager.class);

    private static final String DEFAULT_TASK_ALERT_CONTACT = "<EMAIL>,<EMAIL>,<EMAIL>";
    @Resource
    private AsyncTaskMapper asyncTaskMapper;
    @Resource
    private MailHelper mailHelper;
    @Resource
    private CloudConfig cloudConfig;

    public void addAsyncTask(AsyncTask asyncTask) {
        asyncTask.setUpdateTime(DateHelper.getDateTime());
        try {
            asyncTaskMapper.insertSelective(asyncTask);
        } catch (DuplicateKeyException e) {
            AsyncTask query = AsyncTask.builder().taskId(asyncTask.getTaskId()).build();
            List<AsyncTask> asyncTasks = asyncTaskMapper.listPageSelective(query, 0, 1);
            if (CollectionUtils.isNotEmpty(asyncTasks)) {
                if (asyncTask.getClassName().equals(asyncTasks.get(0).getClassName())) {
                    LOGGER.warn("保存异步任务-重复保存相同classname异步任务! asyncTask={}", asyncTask);
                    sendAsyncTaskSaveAlertMail("重复保存相同classname异步任务! " + asyncTask);
                } else {
                    LOGGER.error("保存异步任务-taskId重复! asyncTask={}", asyncTask);
                    sendAsyncTaskSaveAlertMail("taskId重复! " + asyncTask);
                }
            } else {
                LOGGER.warn("保存异步任务-相同taskId异步任务已经被执行! taskId={}", asyncTask.getTaskId());
                sendAsyncTaskSaveAlertMail("相同taskId异步任务已经被执行! " + asyncTask);
            }
        }
    }

    public void removeAsyncTask(AsyncTask asyncTask) {
        asyncTaskMapper.deleteByPrimaryKey(asyncTask.getId());
    }

    public void editAsyncTask(AsyncTask asyncTask) {
        asyncTask.setUpdateTime(DateHelper.getDateTime());
        asyncTaskMapper.updateByPrimaryKeySelective(asyncTask);
    }

    public boolean makeTaskProcessing(AsyncTask asyncTask) {
        return asyncTaskMapper.makeTaskProcessing(asyncTask.getId()) > 0;
    }

    public boolean makeTaskUnProcess(AsyncTask asyncTask) {
        return asyncTaskMapper.makeTaskUnProcess(asyncTask.getId()) > 0;
    }

    public void updateAllAsyncTaskStatus(int poolType) {
        asyncTaskMapper.updateAllAsyncTaskStatus(poolType);
    }

    public AsyncTask getAsyncTask(long id) {
        return asyncTaskMapper.selectByPrimaryKey(id);
    }

    public AsyncTask getByTaskId(String taskId) {
        return asyncTaskMapper.selectByTaskId(taskId);
    }

    public void processAsyncTaskProcessed(AsyncTask asyncTask, Exception e) {
        // 1.判断AsyncTask是否需要重复，不重复则删除AsyncTask，重复则更改下次执行时间
        if (e != null) {
            LOGGER.error("异步任务执行失败", e);
        }
        try {
            if (StringUtils.isNotBlank(asyncTask.getCronExpression())) {
                asyncTask.setInQueue(AsyncTask.TASK_IN_QUEUE_NO);
                asyncTask.setTimerRunAt(DateHelper.getDateTime(
                        CronExpressionUtil.getOwnNextValidTime(asyncTask.getCronExpression(), asyncTask.getTimerRunAt())));
                editAsyncTask(asyncTask);
            } else {
                this.removeAsyncTask(asyncTask);
            }
        } catch (Exception e1) {
            LOGGER.error("[module:asyncTask] [action:AsyncTaskManager] [step:processAsyncTaskProcessed] ", e1);
        }
    }

    private void sendAsyncTaskSaveAlertMail(String content) {
        if (cloudConfig.getBooleanProperty(SEND_ALARM_EMAIL, false)) {
            MailHeader header = new MailHeader();
            header.setTos(cloudConfig.getProperty("mail.error.contact", DEFAULT_TASK_ALERT_CONTACT).split(DelimiterChars.COMMMA));
            header.setTitle("保存异步任务异常");
            try {
                mailHelper.sendMail(header, content);
            } catch (Exception e) {
                LOGGER.error("[module:asyncTask] [action:AsyncTaskManager] [step:sendAsyncTaskSaveAlertMail] ", e);
            }
        }
    }

    public List<AsyncTask> getZhiMaGoTask(Timestamp startTime, Timestamp endTime) {
        return asyncTaskMapper.getZhiMaGoTask(startTime, endTime);
    }

    public AsyncTask getZhiMaGoTaskByUid(Long userId) {
        if (userId == null) {
            return null;
        }
        return asyncTaskMapper.getZhiMaGoTaskByUid(userId);
    }
}
