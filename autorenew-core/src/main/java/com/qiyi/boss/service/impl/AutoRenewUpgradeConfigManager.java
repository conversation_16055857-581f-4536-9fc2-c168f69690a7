package com.qiyi.boss.service.impl;

import com.qiyi.boss.Constants;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewUpgradeConfig;
import com.qiyi.vip.trade.autorenew.mapper.AutoRenewUpgradeConfigMapper;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> liuwanqiang
 * Date: 2018-9-6
 * Time: 16:47
 */
@Component
public class AutoRenewUpgradeConfigManager {

	@Resource
	private AutoRenewUpgradeConfigMapper autoRenewUpgradeConfigMapper;

	/**
	 * 获取升级配置
	 * @param sourceVipType 升级源会员类型
	 * @param targetVipType 升级目标会员类型
	 * @return AutoRenewUpgradeConfig
	 */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewUpgradeConfig_getByVipType", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public AutoRenewUpgradeConfig getByVipType(Long sourceVipType, Long targetVipType, Short priority) {
        AutoRenewUpgradeConfig query = new AutoRenewUpgradeConfig();
        query.setSourceVipType(sourceVipType);
        query.setTargetVipType(targetVipType);
		query.setPriority(priority);
		query.setStatus(Constants.STATUS_VALID);
		List<AutoRenewUpgradeConfig> upgradeConfigs = autoRenewUpgradeConfigMapper.listSelective(query);
		if (CollectionUtils.isEmpty(upgradeConfigs)) {
			return null;
		}
		return upgradeConfigs.get(0);
	}

	/**
	 * 获取升级配置
	 * @param targetVipType 升级目标会员类型
	 * @return AutoRenewUpgradeConfig
	 */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewUpgradeConfig_getByTargetVipType", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public AutoRenewUpgradeConfig getByTargetVipType(Long targetVipType, Short priority) {
        AutoRenewUpgradeConfig query = new AutoRenewUpgradeConfig();
        query.setTargetVipType(targetVipType);
		query.setPriority(priority);
        query.setStatus(Constants.STATUS_VALID);
        List<AutoRenewUpgradeConfig> upgradeConfigs = autoRenewUpgradeConfigMapper.listSelective(query);
        if (CollectionUtils.isEmpty(upgradeConfigs)) {
            return null;
        }
        return upgradeConfigs.get(0);
	}

	/**
	 * 根据targetVipType、pid查找配置
	 * @param targetVipType 升级目标会员类型
	 * @param productCode 产品code
	 * @return AutoRenewUpgradeConfig
	 */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewUpgradeConfig_getByTargetVipTypeAndPid", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public AutoRenewUpgradeConfig getByTargetVipTypeAndPid(Long targetVipType, String productCode) {
        AutoRenewUpgradeConfig query = AutoRenewUpgradeConfig.builder().status(Constants.STATUS_VALID).targetVipType(targetVipType).build();
        List<AutoRenewUpgradeConfig> autoRenewUpgradeConfigs = autoRenewUpgradeConfigMapper.listSelective(query);
        if (CollectionUtils.isEmpty(autoRenewUpgradeConfigs)) {
            return null;
        }

		return autoRenewUpgradeConfigs.stream().filter(autoRenewUpgradeConfig -> {
		    return autoRenewUpgradeConfig.getDayProductCode().equals(productCode)
                    || autoRenewUpgradeConfig.getMonthProductCode().equals(productCode)
                    || autoRenewUpgradeConfig.getFinalProductCode().equals(productCode);
		}).findFirst().orElse(null);
	}
}
