package com.qiyi.boss.service.impl;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import com.qiyi.boss.Constants;
import com.qiyi.vip.trade.autorenew.domain.AgreementDutMkt;
import com.qiyi.vip.trade.autorenew.domain.AgreementMaterial;
import com.qiyi.vip.trade.autorenew.domain.AgreementSettlementRule;
import com.qiyi.vip.trade.autorenew.domain.AgreementTempPayType;
import com.qiyi.vip.trade.autorenew.domain.AgreementTempPrice;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.mapper.AgreementDutMktMapper;
import com.qiyi.vip.trade.autorenew.mapper.AgreementMaterialMapper;
import com.qiyi.vip.trade.autorenew.mapper.AgreementSettlementRuleMapper;
import com.qiyi.vip.trade.autorenew.mapper.AgreementTempPayTypeMapper;
import com.qiyi.vip.trade.autorenew.mapper.AgreementTempPriceMapper;
import com.qiyi.vip.trade.autorenew.mapper.AgreementTemplateMapper;

/**
 * Created at: 2021-06-24
 *
 * <AUTHOR>
 */
@Service
public class AgreementTemplateManager {

    @Resource
    AgreementTemplateMapper agreementTemplateMapper;
    @Resource
    AgreementTempPriceMapper priceMapper;
    @Resource
    AgreementTempPayTypeMapper payTypeMapper;
    @Resource
    AgreementSettlementRuleMapper agreementSettlementRuleMapper;
    @Resource
    AgreementDutMktMapper dutMktActMapper;
    @Resource
    AgreementMaterialMapper materialMapper;

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementTemplate_getByCode", cacheType= CacheType.LOCAL)
    public AgreementTemplate getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return agreementTemplateMapper.selectByCode(code);
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementTempPrice_getAgreementPrice", cacheType= CacheType.LOCAL)
    public List<AgreementTempPrice> getAgreementPrice(Integer agreementNo) {
        if (agreementNo == null) {
            return Collections.emptyList();
        }
        return priceMapper.selectByAgreementNo(agreementNo);
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementTempPrice_getPriceByCode", cacheType= CacheType.LOCAL)
    public List<AgreementTempPrice> getPriceByCode(String agreementCode) {
        if (StringUtils.isBlank(agreementCode)) {
            return Collections.emptyList();
        }
        return priceMapper.selectByAgreementCode(agreementCode);
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementTempPrice_getDefaultPrice", cacheType= CacheType.LOCAL)
    public AgreementTempPrice getDefaultPrice(Integer agreementNo) {
        if (agreementNo == null) {
            return null;
        }
        List<AgreementTempPrice> priceList = priceMapper.selectByAgreementNo(agreementNo);
        if (CollectionUtils.isEmpty(priceList)) {
            return null;
        }
        return priceList.stream().filter(price -> price.getPeriodNo() == Constants.ZERO_PERIOD_NO)
            .findFirst().orElse(priceList.get(0));
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementTempPrice_getDefaultPriceByCode", cacheType= CacheType.LOCAL)
    public AgreementTempPrice getDefaultPriceByCode(String agreementCode) {
        if (StringUtils.isBlank(agreementCode)) {
            return null;
        }
        List<AgreementTempPrice> priceList = priceMapper.selectByAgreementCode(agreementCode);
        if (CollectionUtils.isEmpty(priceList)) {
            return null;
        }
        return priceList.stream().filter(price -> price.getPeriodNo() == Constants.ZERO_PERIOD_NO)
            .findFirst().orElse(priceList.get(0));
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementTempPayType_getPayTypeInfo", cacheType= CacheType.LOCAL)
    public List<AgreementTempPayType> getPayTypeInfo(Integer agreementNo, Integer payChannel) {
        if (agreementNo == null) {
            return Collections.emptyList();
        }
        return payTypeMapper.selectByAgreementNo(agreementNo, payChannel);
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementSettlementRule_getSettlementRule", cacheType= CacheType.LOCAL)
    public AgreementSettlementRule getSettlementRule(Integer agreementNo) {
        if (agreementNo == null) {
            return null;
        }
        return agreementSettlementRuleMapper.selectByAgreementNo(agreementNo);
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementDutMkt_getDutMktAct", cacheType= CacheType.LOCAL)
    public AgreementDutMkt getDutMktAct(Integer agreementNo) {
        if (agreementNo == null) {
            return null;
        }
        return dutMktActMapper.selectByAgreementNo(agreementNo);
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementMaterial_getMaterial", cacheType= CacheType.LOCAL)
    public AgreementMaterial getMaterial(Integer agreementNo) {
        if (agreementNo == null) {
            return null;
        }
        return materialMapper.selectByAgreementNo(agreementNo);
    }

}
