package com.qiyi.boss.service;

import com.qiyi.vip.trade.autorenew.domain.AutorenewDutConfig;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 * @author: liuwanqiang
 * Date: 2017-12-24
 * Time: 23:41
 */
public interface AutorenewDutConfigService {

	/**
	 * 查询有效代扣配置信息.
	 *
	 * @param status
	 * @return
	 */
	List<AutorenewDutConfig> findAutorenewDutConfig(Integer jobType, Integer status, Integer agreementType);

	/**
	 * 根据id查询代扣配置信息.
	 * @param id
	 * @return
	 */
	AutorenewDutConfig findById(Long id);

	/**
	 * 查询有效代扣配置信息.
	 *
	 * @param sourceVipType 源会员类型
	 * @param vipType 会员类型
	 * @param status 状态
	 * @return listDutTypes
	 */
	List<AutorenewDutConfig> findByVipType(Long sourceVipType, Long vipType, Integer jobType, Integer agreementType, Integer status);

	/**
	 * 查询有效代扣配置信息AdvanceDay
	 * @param jobType
	 * @param status
	 * @return
	 */
	Integer getAdvanceDay(Integer jobType, Integer status, Integer agreementType);
}
