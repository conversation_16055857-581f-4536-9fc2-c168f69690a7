package com.qiyi.boss.service.impl;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.google.common.collect.Maps;
import com.qiyi.boss.Constants;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewNodeLocation;
import com.qiyi.vip.trade.autorenew.mapper.AutoRenewNodeLocationMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2020-03-03
 * Time: 16:17
 */
@Component
public class AutoRenewNodeLocationManager {

	@Resource
	private AutoRenewNodeLocationMapper autoRenewNodeLocationMapper;

	/**
	 * 获取节点配置
	 * @param vipType 会员类型
	 * @param platform 平台code
	 * @return list
	 */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewNodeLocation_getByVipTypeAndPlatform", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public List<AutoRenewNodeLocation> getByVipTypeAndPlatform(Long vipType, String platform) {
        AutoRenewNodeLocation query = new AutoRenewNodeLocation();
        query.setVipType(vipType);
        query.setPlatform(platform);
		query.setStatus(Constants.STATUS_VALID);
		return autoRenewNodeLocationMapper.listSelective(query);
	}

	public Map<String, Object> findNodeLocationMap(Long vipType, String platform) {
        AutoRenewNodeLocationManager thisObj = (AutoRenewNodeLocationManager) AopContext.currentProxy();
        List<AutoRenewNodeLocation> nodeLocations = thisObj.getByVipTypeAndPlatform(vipType, platform);
		Map<String, Object> nodeLocationMap = findAloneNodeLocations(nodeLocations);
		nodeLocationMap.putAll(findGroupNodeLocations(nodeLocations));
		return nodeLocationMap;
	}

	public Map<String, Object> findAloneNodeLocations(List<AutoRenewNodeLocation> nodeLocationList) {
		Map<String, Object> aloneNodeLocations = Maps.newHashMap();
		for (AutoRenewNodeLocation location : nodeLocationList) {
			if (location.getType() != null && location.getType() == AutoRenewNodeLocation.TYPE_ALONE_LOCATION) {
				aloneNodeLocations.put(location.getLocationKey(), location.buildShowLocationValue());
			}
		}
		return aloneNodeLocations;
	}

	public Map<String, Object> findGroupNodeLocations(List<AutoRenewNodeLocation> nodeLocationList) {
		Map<String, Object> groupNodeLocations = Maps.newHashMap();
		for (AutoRenewNodeLocation location : nodeLocationList) {
			if (location.getType() != null && location.getType() == AutoRenewNodeLocation.TYPE_GROUP_LOCATION) {
				String locationKey = location.getLocationKey();
				List<Map<String, String>> locationValueList = (List<Map<String, String>>) groupNodeLocations.get(locationKey);
				if (CollectionUtils.isEmpty(locationValueList)) {
					locationValueList = new ArrayList<>();
				}
				locationValueList.add(location.buildShowLocationValue());
				groupNodeLocations.put(locationKey, locationValueList);
			}
		}
		return groupNodeLocations;
	}

}
