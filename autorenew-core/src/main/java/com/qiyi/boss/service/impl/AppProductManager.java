package com.qiyi.boss.service.impl;

import com.qiyi.vip.trade.qiyue.domain.AppProduct;
import com.qiyi.vip.trade.qiyue.domain.QiYueProductNew;
import com.qiyi.vip.trade.qiyue.domain.VipType;
import com.qiyi.vip.trade.qiyue.mapper.AppProductMapper;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 产品价格类
 * <AUTHOR>
 * Date: 10-12-29
 * Time: 下午6:11
 */
@Component
public class AppProductManager {

    @Resource
    private AppProductMapper appProductMapper;

    @Resource
    private QiYueProductNewService qiYueProductNewManager;

    @Resource
    private VipTypeManager vipTypeManager;

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AppProduct_getAppProductByAppId", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public AppProduct getAppProductByAppId(String appId) {
        List<AppProduct> productList = appProductMapper.listByAppId(appId);
        if (CollectionUtils.isNotEmpty(productList)) {
            return productList.get(0);
        }
        return null;
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AppProduct_getListOfAppProductByAppId", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public List<AppProduct> getListOfAppProductByAppId(String appId) {
        return appProductMapper.listByAppId(appId);
    }

    public Long getVipTypeByAppId(String appId) {
        // 根据appId去苹果产品表查找对应的记录，目的：获取产品唯一标识 -> productCode
        AppProduct appProduct = this.getAppProductByAppId(appId);
        // 根据产品唯一标识：code -> 获取对应产品，目的：获取产品对应的vipTypeCode
        QiYueProductNew qiYueProductNew = qiYueProductNewManager.getProductByCode(appProduct.getProductCode());
        // 根据会员类型唯一标识：code -> 获取对应的产品会员类型， 目的：获取产品对应的id
        VipType vipType = vipTypeManager.getVipTypeByCode(qiYueProductNew.getVipTypeCode());
        return vipType.getId();
    }
}
