package com.qiyi.boss.service.impl;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.concurrent.Callable;

import com.qiyi.boss.outerinvoke.PayInfoApi;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.data.PaymentTypeDTO;
import com.qiyi.vip.service.PaymentTypeClient;

/**
 * Created at: 2021-07-05
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class QiyuePaymentTypeManager {

    @Resource
    private PaymentTypeClient paymentTypeClient;

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "PaymentType_getPayCenterCodeByIdFromPayInfo", cacheType= CacheType.LOCAL)
    public String getPayCenterCodeByIdFromPayInfo(Integer id) {
        Callable<MultiResponse<PaymentTypeDTO>> invokePayInfo = () -> paymentTypeClient.getPaymentTypes(Collections.singletonList(id.longValue()));
        try {
            MultiResponse<PaymentTypeDTO> response = PayInfoApi.invokePayInfoForMultiResponse(invokePayInfo);
            log.info("getPayTypeByIdFromPayInfo, payType:{},  response:{}", id, response);
            if (response != null && response.isSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
                return response.getData().get(0).getPayCenterCode();
            }
            return null;
        } catch (Exception e) {
            log.error("getPayTypeByIdFromPayInfo error:", e);
            return null;
        }
    }

}
