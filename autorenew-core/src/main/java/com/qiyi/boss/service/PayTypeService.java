package com.qiyi.boss.service;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.data.PaymentTypeDTO;
import com.qiyi.vip.service.PaymentTypeClient;
import lombok.extern.slf4j.Slf4j;

/**
 * PayType相关处理服务
 * 负责支付类型相关的业务逻辑处理
 * 
 * <AUTHOR>
 * @date 2024/12/27
 */
@Component
@Slf4j
public class PayTypeService {

    @Resource
    private PaymentTypeClient paymentTypeClient;

    /**
     * 根据payType获取PaymentTypeDTO
     * 
     * @param payType 支付类型
     * @return 支付类型详细信息，获取失败时返回null
     */
    public PaymentTypeDTO getPaymentTypeByPayType(Integer payType) {
        if (payType == null) {
            log.warn("payType为空，无法获取PaymentTypeDTO");
            return null;
        }

        try {
            MultiResponse<PaymentTypeDTO> paymentTypes = paymentTypeClient.getPaymentTypes(
                Lists.newArrayList(payType.longValue())
            );
            
            if (paymentTypes == null || CollectionUtils.isEmpty(paymentTypes.getData())) {
                log.warn("未查询到支付类型信息, payType:{}", payType);
                return null;
            }

            PaymentTypeDTO paymentTypeDTO = paymentTypes.getData().get(0);
            log.debug("根据支付类型获取PaymentTypeDTO成功, payType:{}, paymentTypeDTO:{}", payType, paymentTypeDTO);
            return paymentTypeDTO;
            
        } catch (Exception e) {
            log.error("查询支付类型异常, payType:{}", payType, e);
            return null;
        }
    }

    /**
     * 判断指定的支付类型是否是签约的支付方式
     * 
     * @param paymentTypeDTO 支付类型详细信息
     * @return true-是签约支付方式，false-不是签约支付方式
     */
    public boolean isSignPaymentType(PaymentTypeDTO paymentTypeDTO) {
        if (paymentTypeDTO == null) {
            log.warn("paymentTypeDTO为空，判断为不是签约支付方式");
            return false;
        }
        
        Boolean isSupportSign = paymentTypeDTO.getIsSupportSign();
        boolean result = isSupportSign != null && isSupportSign;
        log.debug("判断是否是签约支付方式, payType:{}, isSupportSign:{}, result:{}", paymentTypeDTO.getId(), isSupportSign, result);
        return result;
    }

    /**
     * 根据payType获取支付渠道
     * 
     * @param payType 支付类型
     * @return 支付渠道，获取失败时返回null
     */
    public Integer getPayChannelByPayType(Integer payType) {
        PaymentTypeDTO paymentTypeDTO = getPaymentTypeByPayType(payType);
        if (paymentTypeDTO == null) {
            return null;
        }
        Integer payChannel = paymentTypeDTO.getPayChannel();
        log.debug("获取支付渠道成功, payType:{}, payChannel:{}", payType, payChannel);
        return payChannel;
    }

    /**
     * 判断指定的支付类型是否支持签约
     * 
     * @param payType 支付类型
     * @return true-支持签约，false-不支持签约
     */
    public boolean isSupportSign(Integer payType) {
        PaymentTypeDTO paymentTypeDTO = getPaymentTypeByPayType(payType);
        if (paymentTypeDTO == null) {
            return false;
        }
        return isSignPaymentType(paymentTypeDTO);
    }
}
