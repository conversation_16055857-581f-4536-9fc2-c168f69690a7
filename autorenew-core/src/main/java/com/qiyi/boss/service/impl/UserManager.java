package com.qiyi.boss.service.impl;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.qiyi.boss.model.SpecialVipUser;
import com.qiyi.boss.model.UserInfo;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.utils.AppConfig;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.SimpleHystrixCommand;
import com.qiyi.usercloud.UidEnigma;
import com.qiyi.vip.commons.component.BossQueryApi;
import com.qiyi.vip.commons.component.UserInfoApi;
import com.qiyi.vip.commons.component.VipInfoBatchQueryCloudApi;
import com.qiyi.vip.commons.component.dto.BatchQueryVipInfoReq;
import com.qiyi.vip.commons.component.dto.ComposeVipInfo;
import com.qiyi.vip.commons.component.dto.VipInfo;
import com.qiyi.vip.commons.component.vo.EncryptUserId;
import com.iqiyi.kit.http.client.ResponseObject;
import com.iqiyi.kit.http.client.util.HttpClients;

/**
 * 用户信息管理类，订单创建相关的代码从此类移出
 *
 * <AUTHOR> fangying
 * <AUTHOR>
 * <AUTHOR> Date: 11-1-7 13-7-4 Time: 上午11:27
 */
@Component
public class UserManager {

    private static Logger logger = LoggerFactory.getLogger(UserManager.class);

    private static final String QUERY_BOSS_VIP_INFO = AppConfig.getProperty("query.boss.vip.info");

    @Resource(name = "queryHttpRestTemplate300")
    RestTemplate queryHttpRestTemplate300;
    @Resource
    private BossQueryApi bossQueryApi;

    @Resource
    private UserInfoApi userInfoApi;
    @Resource
    private VipInfoBatchQueryCloudApi vipInfoBatchQueryCloudApi;

    /**
     * 不用修改， 因为是非 vinfo.vip.qiyi.domain域名
     */
    public SpecialVipUser getSpecialVipFromBoss(Long uid, Long vipType) {
        try {
            ResponseEntity<ResponseObject<Map<String, String>>> responseEntity;
            try {
                responseEntity = new QueryFromBossCommand(uid, vipType).execute();
            } catch (Exception e) {
                logger.error("[getSpecialVipFromBoss] [uid:{}] [vipType:{}]", uid, vipType, e);
                return null;
            }

            if (isFailed(responseEntity)) {
                logger.error("[getSpecialVipFromBoss] [failed] [uid:{}] [type:{}]", uid, vipType, responseEntity);
                return null;
            }

            ResponseObject<Map<String, String>> respBody = responseEntity.getBody();
            if (respBody == null) {
                return null;
            }
            Map<String, String> data = respBody.getData();
            Integer vipTypeFromBoss = Integer.valueOf(data.get("type_id"));
            Timestamp deadLine = DateHelper.getTimestamp(data.get("deadline"));
            Integer status = Integer.valueOf(data.get("status"));
            Timestamp createTime = DateHelper.getTimestamp(data.get("create_time"));

            SpecialVipUser specialVipUser = new SpecialVipUser();
            specialVipUser.setType(vipTypeFromBoss);
            specialVipUser.setDeadline(deadLine);
            specialVipUser.setUserId(uid);
            specialVipUser.setStatus(status);
            specialVipUser.setCreateTime(createTime);
            return specialVipUser;
        } catch (Exception e) {
            logger.error("[getSpecialVipFromBoss] [major error] [uid:{}] [vipType:{}]", uid, vipType, e);
            return null;
        }
    }

    /**
     * wiki:http://wiki.qiyi.domain/pages/viewpage.aZction?pageId=144425963 内网 or 无法获取 authCookie 时使用 获取指定身份的会员信息
     *
     * @param userId 用户id
     * @param platform 平台
     * @return SpecialVipUser
     */
    @Deprecated
    public SpecialVipUser getSpecialVipUserFromVip(Long userId, Long vipType, String platform) {
        try {
            Optional<List<VipInfo>> responseEntity = new GetVipInfosCommand(new EncryptUserId(UidEnigma.encrypt(userId)), platform).execute();
            return getFromVipInfos(userId, responseEntity.orElse(Collections.emptyList()), vipType);
        } catch (Exception e) {
            logger.error("getSpecialVipUserFromVip-error,userId:{} vipType:{} platform:{}", userId, vipType, platform, e);
            return null;
        }
    }

    /**
     * 获取指定身份的会员信息
     */
    public SpecialVipUser getSpecialVipUserFromVip(Long userId, String authCookie, Long vipType, String platform) {
        try {
            Optional<List<VipInfo>> responseEntity = new GetVipInfosCommand(authCookie, platform).execute();
            return getFromVipInfos(userId, responseEntity.orElse(Collections.emptyList()), vipType);
        } catch (Exception e) {
            logger.error("getSpecialVipUserFromVip-error,userId:{} vipType:{} platform:{}", userId, vipType, platform, e);
            return null;
        }
    }

    /**
     * wiki:http://wiki.qiyi.domain/pages/viewpage.action?pageId=144425963boss
     *
     * @param userId 用户id
     * @param platform 平台
     * @return SpecialVipUser
     */
    @Deprecated
    public SpecialVipUser getSpecialVipUserFromVipNew(Long userId, Long vipType, String platform) {
        return doGetSpecialVipUserFromVipNew(userId, null, vipType, platform);
    }

    public SpecialVipUser getSpecialVipUserFromVipNew(String authCookie, Long vipType, String platform) {
        return doGetSpecialVipUserFromVipNew(null, authCookie, vipType, platform);
    }

    /**
     * wiki:http://wiki.qiyi.domain/pages/viewpage.action?pageId=144425963
     *
     * @param userId 用户id
     * @param platform 平台
     * @return VipUser
     */
    @Deprecated
    public VipUser getVipUserFromVip(Long userId, Long vipType, String platform) {
        try {
            Optional<List<VipInfo>> vipInfoList = new GetVipInfosCommand(new EncryptUserId(UidEnigma.encrypt(userId)), platform).execute();
            SpecialVipUser specialVipUser = getFromVipInfos(userId, vipInfoList.orElse(Collections.emptyList()), vipType);
            if (specialVipUser == null) {
                return null;
            }
            VipUser vipUser = new VipUser();
            vipUser.setId(specialVipUser.getUserId());
            vipUser.setTypeId(Long.valueOf(specialVipUser.getType()));
            vipUser.setStatus(specialVipUser.getStatus());
            vipUser.setDeadline(specialVipUser.getDeadline());
            vipUser.setCreateTime(specialVipUser.getCreateTime());
            return vipUser;
        } catch (Exception e) {
            logger.error("[getVipUserFromVip] [Exception] [userId:{}] [vipType:{}] [platform:{}]", userId, vipType, platform, e);
            return null;
        }
    }

    /**
     * http://wiki.qiyi.domain/pages/viewpage.action?pageId=197188114
     */
    public Map<Long, VipUser> getUserMultiVipTypeInfo(Long userId, List<Long> vipTypes, String platform) {
        BatchQueryVipInfoReq.BatchQueryVipInfoReqBuilder paramBuilder = BatchQueryVipInfoReq.builder();
        paramBuilder.uid(userId);
        paramBuilder.platform(platform);
        paramBuilder.vipTypeList(vipTypes);
        try {
            List<VipInfo> vipInfos = vipInfoBatchQueryCloudApi.queryMultiVipInfoFromBossWithCircuitBreaker(paramBuilder.build());
            return buildFromVipInfos(userId, vipInfos);
        } catch (Exception e) {
            logger.error("[getUserMultiVipTypeInfo] [Exception] [userId:{}] [vipTypes:{}] [platform:{}]", userId, vipTypes, platform, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 注意: 多个身份时, 获取最高身份
     * 接口文档：https://iq.feishu.cn/wiki/DaZ9wOR9zinzRvkVgyncZzlEnEc
     * status：会员状态， 0:临时封停 1:有效 2:永久封停 3:过期
     */
    @Deprecated
    public VipUser getVipInfo(Long uid, List<Long> vipTypes) {
        Optional<ComposeVipInfo> composeVipInfo = bossQueryApi.queryVipInfo(new EncryptUserId(UidEnigma.encrypt(uid)), vipTypes);
        if (!composeVipInfo.isPresent() || composeVipInfo.get().getVipInfo() == null) {
            return null;
        }
        VipInfo goldVipInfo = composeVipInfo.get().getVipInfo();
        return VipUser.ofVipInfo(uid, goldVipInfo);
    }

    public VipUser getVipInfo(Long uid, String authCookie, List<Long> vipTypes) {
        Optional<ComposeVipInfo> composeVipInfo = bossQueryApi.queryVipInfo(authCookie, vipTypes);
        if (!composeVipInfo.isPresent() || composeVipInfo.get().getVipInfo() == null) {
            return null;
        }
        VipInfo goldVipInfo = composeVipInfo.get().getVipInfo();
        return VipUser.ofVipInfo(uid, goldVipInfo);
    }

    private SpecialVipUser getFromVipInfos(Long uid, List<VipInfo> vipInfoList, Long vipType) {
        if (CollectionUtils.isEmpty(vipInfoList)) {
            return null;
        }
        SpecialVipUser specialVipUser = new SpecialVipUser();
        specialVipUser.setUserId(uid);
        specialVipUser.setType(vipType.intValue());
        for (VipInfo vipInfo : vipInfoList) {
            Long type = Long.valueOf(vipInfo.getVipType());
            if (!vipType.equals(type)) {
                continue;
            }
            Timestamp deadline = new Timestamp(vipInfo.getDeadline().getT());
            Integer expire = vipInfo.getExpire();
            Integer status = vipInfo.getStatus();

            specialVipUser.setStatus(getPassportStatus(expire, status));
            specialVipUser.setType(type.intValue());
            specialVipUser.setDeadline(deadline);
            break;
        }
        return specialVipUser;
    }

    private Map<Long, VipUser> buildFromVipInfos(Long userId, List<VipInfo> vipInfoList) {
        if (CollectionUtils.isEmpty(vipInfoList)) {
            return Collections.emptyMap();
        }
        Map<Long, VipUser> result = Maps.newHashMap();
        for (VipInfo vipInfo : vipInfoList) {
            if (vipInfo == null) {
                continue;
            }
            Long vipType = Long.valueOf(vipInfo.getVipType());
            Timestamp deadline = new Timestamp(vipInfo.getDeadline().getT());
            Integer expire = vipInfo.getExpire();
            Integer status = vipInfo.getStatus();
            VipUser vipUser = new VipUser();
            vipUser.setId(userId);
            vipUser.setTypeId(vipType);
            vipUser.setStatus(getPassportStatus(expire, status));
            vipUser.setDeadline(deadline);

            result.put(vipType, vipUser);
        }
        return result;
    }

    /**
     * 会员接口返回status与passport不一致,需要进行转换
     *
     * @return Integer
     */
    public static Integer getPassportStatus(Integer expire, Integer status) {
        if (UserInfo.EXPIRE_YES.equals(expire)) {
            return UserInfo.PASSPORT_STATUS_INVALID;
        } else if (UserInfo.VIP_STATUS_FREEZE_FOREVER == status) {
            return UserInfo.PASSPORT_STATUS_FREEZE_FOREVER;
        } else if (UserInfo.VIP_STATUS_FREEZE == status) {
            return UserInfo.PASSPORT_STATUS_FREEZE;
        } else {
            return status;
        }
    }

    private class QueryFromBossCommand extends SimpleHystrixCommand<ResponseEntity<ResponseObject<Map<String, String>>>> {

        private Long uid;
        private Long vipType;

        QueryFromBossCommand(Long uid, Long vipType) {
            super("UserManager", 20, 490);
            this.uid = uid;
            this.vipType = vipType;
        }

        @Override
        protected ResponseEntity<ResponseObject<Map<String, String>>> run() {
            return queryVipInfoByUserIdVipType(uid, vipType);
        }

        @Override
        protected ResponseEntity<ResponseObject<Map<String, String>>> getFallback() {
            return queryVipInfoByUserIdVipType(uid, vipType);
        }
    }

    private ResponseEntity<ResponseObject<Map<String, String>>> queryVipInfoByUserIdVipType(Long userId, Long vipType) {
        Map<String, Object> reqPara = ImmutableMap.of("uid", String.valueOf(userId), "type", String.valueOf(vipType));
        String url = HttpClients.buildQueryUrl(QUERY_BOSS_VIP_INFO, reqPara);

        ParameterizedTypeReference<ResponseObject<Map<String, String>>> paramTypeRef =
            new ParameterizedTypeReference<ResponseObject<Map<String, String>>>() {
            };
        try {
            return queryHttpRestTemplate300.exchange(url, HttpMethod.GET, null, paramTypeRef);
        } catch (RestClientException e) {
            logger.error("Query url:<{}> error, uid:{}, type:{}, error{}", url, userId, vipType, e.getMessage());
            return new ResponseEntity<>(HttpStatus.REQUEST_TIMEOUT);
        }
    }

    private class GetVipInfosCommand extends SimpleHystrixCommand<Optional<List<VipInfo>>> {

        private Long userId;
        private String authCookie;
        private EncryptUserId encryptUserId;
        private String platform;

        GetVipInfosCommand(String authCookie, String platform) {
            super("UserManager", 20, 490);
            this.authCookie = authCookie;
            this.platform = platform;
        }

        GetVipInfosCommand(EncryptUserId encryptUserId, String platform) {
            super("UserManager", 20, 490);
            this.encryptUserId = encryptUserId;
            this.platform = platform;
        }

        @Override
        protected Optional<List<VipInfo>> run() {
            return doQueryVipInfo(userId, authCookie, encryptUserId, platform);
        }

        @Override
        protected Optional<List<VipInfo>> getFallback() {
            return doQueryVipInfo(userId, authCookie, encryptUserId, platform);
        }

        private Optional<List<VipInfo>> doQueryVipInfo(Long uid, String authCookie, EncryptUserId encryptedUid, String platform) {
            if (StringUtils.isNotBlank(authCookie)) {
                return bossQueryApi.getUserInfoFromBoss(authCookie, platform, null);
            } else if (encryptedUid != null) {
                return bossQueryApi.getUserInfoFromBoss(encryptedUid, platform, null);
            } else {
                return bossQueryApi.getUserInfoFromBoss(uid, platform, null);
            }
        }
    }

    private boolean isFailed(ResponseEntity<ResponseObject<Map<String, String>>> responseEntity) {
        if (responseEntity == null) {
            return true;
        }
        ResponseObject<Map<String, String>> body = responseEntity.getBody();
        return responseEntity.getStatusCode() != HttpStatus.OK || (responseEntity.getStatusCode() == HttpStatus.OK && body != null && !"A00000".equals(body.getCode()));
    }

    private SpecialVipUser doGetSpecialVipUserFromVipNew(Long userId, String authCookie, Long vipType, String platform) {
        try {
            BatchQueryVipInfoReq batchQueryVipInfoReq = BatchQueryVipInfoReq.builder()
                .vipType(vipType)
                .platform(platform)
                .authCookie(authCookie)
                .build();
            if (userId != null) {
                batchQueryVipInfoReq.setEncryptUserId(new EncryptUserId(UidEnigma.encrypt(userId)));
            }
            VipInfo vipInfo = userInfoApi.querySingleVipInfoFromBossWithCircuitBreaker(batchQueryVipInfoReq);
            if (vipInfo != null) {
                return SpecialVipUser.ofVipInfo(userId, vipInfo);
            }
            return null;
        } catch (Exception e) {
            logger.error("[getSpecialVipUserFromVip] [Exception] [userId:{}]" +
                " [vipType:{}] [platform:{]]", userId, vipType, platform, e);
            return null;
        }
    }

}
