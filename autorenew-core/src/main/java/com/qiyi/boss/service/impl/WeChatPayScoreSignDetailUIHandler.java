package com.qiyi.boss.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.qiyi.boss.dto.AgreementDutInfo;
import com.qiyi.boss.dto.AgreementDutInfo.PayTypeInfo;
import com.qiyi.boss.dto.UserAgreementRespDto;
import com.qiyi.boss.enums.AgreementStatusEnum;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.OperateStatusEnum;
import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.outerinvoke.ContentQueryServerProxy;
import com.qiyi.boss.outerinvoke.result.VcqContentBaseInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.qiyue.domain.QiYueProductNew;
import com.iqiyi.vip.order.dal.model.Order;

/**
 * 微信信用分签约详情 Handler
 *
 * <AUTHOR>
 * @date 2021/7/26 11:37
 */
@Slf4j
@Component
public class WeChatPayScoreSignDetailUIHandler implements SignDetailUIHandler {

    @Resource
    UserAgreementLogManager userAgreementLogManager;
    @Resource
    OrderManager orderManager;
    @Resource
    ContentQueryServerProxy contentQueryServerProxy;

    @Override
    public AgreementTypeEnum getAgreementType() {
        return AgreementTypeEnum.WECHAT_PAY_SCORE;
    }

    @Override
    public UserAgreementRespDto constructRespDto(AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate, DutUserNew dutUserNew, QiYueProductNew qiYueProductNew) {
        UserAgreementRespDto signedAgreementRespDto = UserAgreementRespDto.createSignedAgreementBasicRespDto(agreementNoInfo, agreementTemplate, dutUserNew, qiYueProductNew.getName());
        // 返回连续代扣次数
        signedAgreementRespDto.setPerformedPeriods(dutUserNew.getSerialRenewCount());
        // 查询用户所有的代扣记录,包含创单&结单
        List<DutRenewLog> renewLogList = userAgreementLogManager.listRenewLogBySignKey(dutUserNew.getUserId(), dutUserNew.getSignKey());
        renewLogList = renewLogList.stream()
            .sorted(Comparator.comparing(DutRenewLog::getCreateTime).reversed())
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(renewLogList)) {
            log.warn("constructRespDto-用户扣费记录为空!userId:{},signKey:{}", dutUserNew.getUserId(), dutUserNew.getSignKey());
            return signedAgreementRespDto;
        }
        DutRenewLog latestRenewLog = renewLogList.get(0);
        // 保留成功的代扣记录
        List<DutRenewLog> successLogList = renewLogList.stream().filter(DutRenewLog::isPaySuccess).collect(Collectors.toList());
        // 处理开通日志和代扣日志
        DutRenewLog openRenewLog = null;
        DutRenewLog completeRenewLog = null;
        if (OperateTypeEnum.DUT.getValue() == latestRenewLog.getOperateType()) {
            if (latestRenewLog.isPaySuccess()) {
                openRenewLog = latestRenewLog;
            } else {
                openRenewLog = successLogList.stream().filter(log -> OperateTypeEnum.DUT.getValue() == log.getOperateType()).findFirst().orElse(null);
                completeRenewLog = successLogList.stream().filter(log -> OperateTypeEnum.COMPLETE_ORDER.getValue() == log.getOperateType()).findFirst().orElse(null);
            }
        } else if (OperateTypeEnum.COMPLETE_ORDER.getValue() == latestRenewLog.getOperateType()) {
            openRenewLog = successLogList.stream().filter(log -> OperateTypeEnum.DUT.getValue() == log.getOperateType()).findFirst().orElse(null);
            completeRenewLog = latestRenewLog;
        }
        if (Objects.isNull(openRenewLog)) {
            log.error("constructRespDto-用户创单代扣日志为空!userId:{},signKey:{}", dutUserNew.getUserId(), dutUserNew.getSignKey());
            return signedAgreementRespDto;
        }
        log.info("constructRespDto-用户创单 log 记录! renewLog:{}", openRenewLog);
        // 查询权益单
        Order order = orderManager.queryOrder(openRenewLog.getOrderCode(), dutUserNew.getUserId());
        // 构建用户履约记录
        AgreementDutInfo agreementDutInfo = constructAgreementDutInfo(order, agreementNoInfo, completeRenewLog, dutUserNew);
        signedAgreementRespDto.setAgreementDutInfo(agreementDutInfo);
        // 处理取消状态的结束时间和执行次数
        if (AgreementStatusEnum.INVALID.getValue() == dutUserNew.getStatus()) {
            DutRenewSetLog cancelSetLog = userAgreementLogManager.getSetLogBySignKeyFromMaster(dutUserNew.getUserId(), dutUserNew.getSignKey(), OperateTypeEnum.CANCEL);
            if (Objects.nonNull(cancelSetLog)) {
                signedAgreementRespDto.setEndTime(cancelSetLog.getOperateTime());
                signedAgreementRespDto.setPerformedPeriods(cancelSetLog.getSerialRenewCount());
            }
        }
        String businessValues = order.getBusinessValues();
        if (NumberUtils.isDigits(businessValues)) {
            VcqContentBaseInfo contentBaseInfo = contentQueryServerProxy.queryContentBaseInfo(Long.parseLong(businessValues));
            if (contentBaseInfo != null) {
                agreementDutInfo.setCreateOrderSource(contentBaseInfo.getName());
            }
        }
        return signedAgreementRespDto;
    }

    /**
     * 构建代扣记录信息
     */
    private AgreementDutInfo constructAgreementDutInfo(Order order, AgreementNoInfo agreementNoInfo, DutRenewLog completeLog, DutUserNew dutUserNew) {
        AgreementDutInfo dutInfo = new AgreementDutInfo();
        dutInfo.setDutPrice(order.getSettlementFee());
        if (Objects.nonNull(completeLog)) {
            dutInfo.setDutTime(completeLog.getCreateTime());
            if (completeLog.isPaySuccess()) {
                dutInfo.setDutStatus(OperateStatusEnum.DUT_SUCCESS.getValue());
            } else {
                dutInfo.setDutStatus(OperateStatusEnum.DUT_IN_PROGRESS.getValue());
            }
        } else {
            dutInfo.setDutStatus(OperateStatusEnum.DUT_PENDING.getValue());
            // 预期代扣时间为 dutUserNew 表中的下次代扣时间
            dutInfo.setDutTime(dutUserNew.getNextDutTime());
        }
        dutInfo.setRightStartTime(order.getStartTime());
        dutInfo.setRightEndTime(order.getDeadline());
        AgreementDutInfo.PayTypeInfo payTypeInfo = new PayTypeInfo();
        payTypeInfo.setPayChannel(agreementNoInfo.getPayChannel());
        payTypeInfo.setDutType(agreementNoInfo.getDutType());
        dutInfo.setPayTypeInfo(payTypeInfo);
        return dutInfo;
    }

}
