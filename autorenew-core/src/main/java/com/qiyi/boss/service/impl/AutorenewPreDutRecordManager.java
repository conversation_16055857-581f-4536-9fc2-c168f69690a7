package com.qiyi.boss.service.impl;

import com.qiyi.boss.Constants;
import com.qiyi.boss.service.AutorenewPreDutRecordService;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.domain.AutorenewPreDutRecord;
import com.qiyi.vip.trade.autorenew.repository.AutorenewPreDutRecordRepository;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2020-6-28
 * Time: 22:08
 */
@Component(value = "autorenewPreDutRecordService")
public class AutorenewPreDutRecordManager implements AutorenewPreDutRecordService {

	private static final int MIN_TIME_OFFSET = -10;

	@Resource
	private AutorenewPreDutRecordRepository autorenewPreDutRecordRepository;

	@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
	@Override
	public void save(AutorenewPreDutRecord autorenewPreDutRecord) {
		autorenewPreDutRecordRepository.save(autorenewPreDutRecord);
	}

	@Override
	public List<AutorenewPreDutRecord> list(AutorenewPreDutRecord autorenewPreDutRecord) {
		return autorenewPreDutRecordRepository.list(autorenewPreDutRecord);
	}

	public AutorenewPreDutRecord getAutorenewPreDutRecord(Long userId, Long vipType, Integer dutType, Integer amount) {
		AutorenewPreDutRecord query = AutorenewPreDutRecord.builder()
				.userId(userId)
				.vipType(vipType)
				.dutType(dutType)
				.amount(amount)
				.build();
		List<AutorenewPreDutRecord> autorenewPreDutRecordList = autorenewPreDutRecordRepository.list(query);

		Timestamp minTime = DateHelper.caculateTime(DateHelper.getDateTime(), MIN_TIME_OFFSET, Constants.PRODUCT_PERIODUNIT_DAY);
		autorenewPreDutRecordList = autorenewPreDutRecordList.stream()
				.filter(autorenewPreDutRecord -> minTime.before(autorenewPreDutRecord.getCreateTime())).collect(Collectors.toList());

		if (CollectionUtils.isEmpty(autorenewPreDutRecordList)) {
			return null;
		}

		return autorenewPreDutRecordList.get(0);
	}
}
