package com.qiyi.boss.service.i18n;

import com.qiyi.vip.trade.autorenew.domain.PayChannelTip;
import com.qiyi.vip.trade.autorenew.mapper.AsyncTaskMapper;
import com.qiyi.vip.trade.autorenew.mapper.PayChannelTipMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020-07-03
 * @doc 根据支付渠道获取该支付渠道下自动续费提示信息国际化文案i18nKey
 */
@Service
public class PayChannelTipServiceImpl implements PayChannelTipService {

    @Resource
    private PayChannelTipMapper payChannelTipMapper;

    @Override
    public PayChannelTip getPayChannelTip(Integer payChannel) {
        return payChannelTipMapper.selectByPayChannel(payChannel);
    }
}
