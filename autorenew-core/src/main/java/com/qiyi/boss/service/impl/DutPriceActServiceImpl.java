package com.qiyi.boss.service.impl;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.qiyi.boss.autorenew.dto.DutSuccessMsgReqDto;
import com.qiyi.boss.autorenew.dto.IntroductoryActDto;
import com.qiyi.boss.autorenew.dto.UpdateActSetLogDesp;
import com.qiyi.boss.autorenew.dto.VipRenewUnitDto;
import com.qiyi.boss.autorenew.enumerate.ActTypeEnum;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.boss.service.DutUserService;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.IntroductoryPriceAct;
import com.qiyi.vip.trade.autorenew.repository.DutUserRepository;
import com.qiyi.vip.trade.autorenew.service.DutRenewSetLogService;

/**
 * <AUTHOR> sip peng
 * @date : 2018/3/20
 */
@Component
public class DutPriceActServiceImpl {

    @Resource
    private DutUserService dutUserService;
    @Resource
    private DutRenewSetLogService dutRenewSetLogService;
    @Resource
    private DutUserRepository dutUserRepository;
    @Resource
    AutoRenewDutTypeManager autoRenewDutTypeManager;
    @Resource
    IntroductoryPriceActManager introductoryPriceActManager;
    @Resource
    private UserAgreementService userAgreementService;

    public boolean isNotDutAct(Integer remainPeriods, Integer dutType) {
        return remainPeriods == null || remainPeriods.equals(0) || dutType == null
            || autoRenewDutTypeManager.dutTypeUnSupportAct(dutType);
    }

    /**
     * 找到支持活动的会员时长用户（userId, vipType, amount）
     */
    public void changeVipSubscriptionActStatus(DutSuccessMsgReqDto reqDto) {
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(reqDto.getUserId(), reqDto.getDutType(), reqDto.getVipType());
        if (dutUserNew == null || isNotDutAct(dutUserNew.getRemainPeriods(), dutUserNew.getType())) {
            return;
        }
        if (isNotRightAmount(reqDto.getAmount(), dutUserNew.getAmount())) {
            return;
        }
        DutPriceActServiceImpl thisObj = (DutPriceActServiceImpl) AopContext.currentProxy();
        Integer prevPeriods = dutUserNew.getRemainPeriods();
        if (isFinalActDut(prevPeriods)) {
            if (ActTypeEnum.STOP_AFTER_X.getValue().equals(dutUserNew.getActType())) {
                thisObj.changeVipActPeriods(dutUserNew.buildVipRenewUnit(), prevPeriods - 1);
            } else {
                Integer originalDutPrice = dutUserService.getOriginalDutPrice(dutUserNew);
                thisObj.resetVipActPeriods(dutUserNew.buildVipRenewUnit(), originalDutPrice);
            }
        } else if (isNormalActDut(prevPeriods)) {
            thisObj.changeVipActPeriods(dutUserNew.buildVipRenewUnit(), prevPeriods - 1);
        }
    }

    private boolean isNotRightAmount(Integer dutAmount, Integer contractAmount) {
        if (dutAmount == null || contractAmount == null) {
            return true;
        }
        return !dutAmount.equals(contractAmount);
    }

    private boolean isFinalActDut(Integer remainPeriods) {
        return remainPeriods == 1;
    }

    private boolean isNormalActDut(Integer remainPeriods) {
        return remainPeriods > 1;
    }

    /**
     * 初始化活动信息
     */
    public void doAct(DutUserNew openingDutUser, AutorenewRequest autorenewRequest) {
        IntroductoryActDto actDto = IntroductoryActDto.builder()
            .fee(autorenewRequest.getOrderFee())
            .pid(autorenewRequest.getPid())
            .payTime(autorenewRequest.getPayTime())
            .payType(autorenewRequest.getPayType())
            .platform(autorenewRequest.getPlatformCode())
            .amount(autorenewRequest.getAmount())
            .build();
        Optional<IntroductoryPriceAct> act = introductoryPriceActManager.findValidAct(actDto, openingDutUser.getType());
        if (!act.isPresent()) {
            return;
        }

        DutPriceActServiceImpl thisObj = (DutPriceActServiceImpl) AopContext.currentProxy();
        List<DutUserNew> openedDutUsersOfSupportAct = thisObj.findVipSupportDutUsers(openingDutUser.buildVipRenewUnit());
        IntroductoryPriceAct introductoryPriceAct = act.get();
        if (ActTypeEnum.STOP_AFTER_X.getValue().equals(introductoryPriceAct.getActType())) {
            if (CollectionUtils.isNotEmpty(openedDutUsersOfSupportAct) && openedDutUsersOfSupportAct.get(0).getRemainPeriods() != null) {
                int remainPeriods = openedDutUsersOfSupportAct.get(0).getRemainPeriods() - 1;
                openingDutUser.initActInfo(introductoryPriceAct, remainPeriods);
                thisObj.changeVipActPeriods(openedDutUsersOfSupportAct, remainPeriods);
            } else {
                int remainPeriods = introductoryPriceAct.getActPeriods() - 1;
                openingDutUser.initActInfo(introductoryPriceAct, remainPeriods);
                thisObj.participateVipDutPriceAct(openingDutUser, openedDutUsersOfSupportAct, remainPeriods, introductoryPriceAct);
            }
        } else {
            int remainPeriods = introductoryPriceAct.getActPeriods() - 1;
            openingDutUser.initActInfo(introductoryPriceAct, remainPeriods);
            thisObj.participateVipDutPriceAct(openingDutUser, openedDutUsersOfSupportAct, remainPeriods, introductoryPriceAct);
        }
    }

    /**
     * 支持活动的且与开通时长相同的签约关系
     */
    public List<DutUserNew> findVipSupportDutUsers(VipRenewUnitDto unit) {
        List<DutUserNew> dutUserNews = dutUserRepository.findAllByUserIdAndVipType(unit.getUserId(), unit.getAgreementType(), unit.getVipType());
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return Collections.emptyList();
        }
        return dutUserNews.stream()
            .filter(item -> !autoRenewDutTypeManager.dutTypeUnSupportAct(item.getType())
                && Objects.equals(item.getAmount(), unit.getAmount()))
            .collect(Collectors.toList());
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void changeVipActPeriods(VipRenewUnitDto unit, Integer newPeriods) {
        DutPriceActServiceImpl thisObj = (DutPriceActServiceImpl) AopContext.currentProxy();
        List<DutUserNew> dutUserNewList = thisObj.findVipSupportDutUsers(unit);
        for (DutUserNew dutUser : dutUserNewList) {
            changeActPeriodsByDutUser(dutUser, newPeriods);
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void changeVipActPeriods(List<DutUserNew> dutUserNews, Integer newPeriods) {
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return;
        }
        for (DutUserNew dutUser : dutUserNews) {
            changeActPeriodsByDutUser(dutUser, newPeriods);
        }
    }

    private void changeActPeriodsByDutUser(DutUserNew dutUserNew, Integer newPeriods) {
        dutRenewSetLogService.addDutRenewSetLog(dutUserNew, UpdateActSetLogDesp.TYPE_NORMAL_ACT_UPDATE);
        dutUserRepository.changeActPeriodsByDutUser(dutUserNew, newPeriods);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean resetVipActPeriods(VipRenewUnitDto unit, Integer originalPrice) {
        DutPriceActServiceImpl thisObj = (DutPriceActServiceImpl) AopContext.currentProxy();
        List<DutUserNew> dutUserNewList = thisObj.findVipSupportDutUsers(unit);
        for (DutUserNew dutUser : dutUserNewList) {
            resetActPeriodsByDutUser(dutUser, originalPrice);
        }
        return true;
    }

    private void resetActPeriodsByDutUser(DutUserNew dutUserNew, Integer originalPrice) {
        dutRenewSetLogService.addDutRenewSetLog(dutUserNew, UpdateActSetLogDesp.TYPE_FINAL_ACT_UPDATE);
        dutUserRepository.resetActPeriodsByDutUser(dutUserNew, originalPrice);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean participateVipDutPriceAct(DutUserNew openingDutUser, List<DutUserNew> dutUserNews, Integer nowPeriods, IntroductoryPriceAct act) {
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return true;
        }
        if (nowPeriods == null || openingDutUser.getContractPrice() == null) {
            return false;
        }
        for (DutUserNew dutUser : dutUserNews) {
            if (dutUser.getType().equals(openingDutUser.getType())) {
                continue;
            }
            participateByUser(dutUser, nowPeriods, openingDutUser.getContractPrice(), openingDutUser.getOrderCode(), act);
        }
        return true;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void participateByUser(DutUserNew dutUserNew, Integer nowPeriods, Integer contractPrice, String orderCode, IntroductoryPriceAct act) {
        dutRenewSetLogService.addDutRenewSetLog(dutUserNew, UpdateActSetLogDesp.TYPE_OPEN_DUT_ACT_UPDATE);
        dutUserRepository.participateByUser(dutUserNew, nowPeriods, contractPrice, orderCode, act);
    }

    /**
     * 更新活动信息
     */
    public void updateUserActInfo(DutUserNew openingDutUser, Integer contractPrice) {
        List<DutUserNew> dutUserNewList = findVipSupportDutUsers(openingDutUser.buildVipRenewUnit());
        for (DutUserNew user : dutUserNewList) {
            if (user.getType().equals(openingDutUser.getType())) {
                continue;
            }
            if (user.isBeingInAct()) {
                openingDutUser.setContractPrice(contractPrice);
                openingDutUser.setRemainPeriods(user.getRemainPeriods());
                openingDutUser.setActTotalPeriods(user.getActTotalPeriods());
                openingDutUser.setActCode(user.getActCode());
                openingDutUser.setRenewPrice(user.getRenewPrice());
                openingDutUser.setActType(user.getActType() == null ? ActTypeEnum.FIRST_X_FAVOR.getValue() : user.getActType());
                return;
            }
        }
    }

}
