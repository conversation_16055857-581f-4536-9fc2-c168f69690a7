package com.qiyi.boss.service.impl;

import com.qiyi.boss.service.UserPasswordFreeLogService;
import com.qiyi.vip.trade.autorenew.domain.UserPasswordFreeLog;
import com.qiyi.vip.trade.autorenew.repository.UserPasswordFreeLogRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 用户免密签约操作日志
 *
 * @Author: <PERSON> P<PERSON>hui
 * @Date: 2020/9/28
 */
@Slf4j
@Service
public class UserPasswordFreeLogServiceImpl implements UserPasswordFreeLogService {
    @Resource
    private UserPasswordFreeLogRepository userPasswordFreeLogRepository;

    @Override
    public void save(UserPasswordFreeLog userPasswordFreeLog) {
        userPasswordFreeLogRepository.insert(userPasswordFreeLog);
    }
}
