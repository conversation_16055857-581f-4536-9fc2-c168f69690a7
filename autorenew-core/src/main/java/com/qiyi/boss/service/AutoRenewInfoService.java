package com.qiyi.boss.service;

import java.util.List;

import com.qiyi.boss.dto.AutoRenewDurationInfo;
import com.qiyi.boss.dto.UserAutoRenewInfo;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * Created at: 2020-10-28
 *
 * <AUTHOR>
 */
public interface AutoRenewInfoService {

    /**
     * 查询用户的多个会员类型自动续费信息
     * @param uid
     * @param vipTypes
     */
    List<UserAutoRenewInfo> multiVipTypeRenewInfo(Long uid, List<Long> vipTypes);

    AutoRenewDurationInfo getUserRenewDurationInfo(List<DutUserNew> dutUserNews, Integer vipGroup);

    /**
     * 获取用户取消自动续费前的自动续费时长
     * @param uid
     * @param vipGroup
     */
    AutoRenewDurationInfo getUserRenewDurationBeforeCancel(Long uid, Long vipType, Integer vipGroup);

    /**
     * 获取体系
     * @param uid
     * @param vipGroups
     * @return
     */
    List<AutoRenewDurationInfo> getUserRenewDurationInfoByVipGroups(Long uid, List<Integer> vipGroups);

    /**
     * 获取自动续费开通时长
     * @param uid
     * @param vipType
     * @return
     */
    Integer getRenewDuration(Long uid, Long vipType);

    /**
     * 用户之前是否开通过指定支付渠道的签约关系，若开通则返回指定文案，vipType所属体系维度
     * @param uid
     * @param vipType
     * @param payChannel
     */
    String unbindTipsAfterSignPay(Long uid, Long vipType, Integer payChannel);

}
