package com.qiyi.boss.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import com.qiyi.boss.Constants;
import com.qiyi.boss.component.SmartJedisClient;
import com.qiyi.boss.dto.AutoRenewDurationInfo;
import com.qiyi.boss.dto.UserAutoRenewInfo;
import com.qiyi.boss.enums.AgreementStatusEnum;
import com.qiyi.boss.enums.VipGroupEnum;
import com.qiyi.boss.outerinvoke.VipPartnerRenewServerProxy;
import com.qiyi.boss.outerinvoke.result.PartnerUserSignRecordQueryResult;
import com.qiyi.boss.service.AutoRenewInfoService;
import com.qiyi.boss.service.AutoRenewService;
import com.qiyi.boss.service.DutService;
import com.qiyi.boss.service.PayCenterAccountUnbindManager;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.repository.DutUserRepository;
import com.qiyi.vip.trade.autorenew.service.DutRenewSetLogService;

import static com.qiyi.boss.constants.AgreementConstants.EXCLUDE_AGREEMENT_TYPES;

/**
 * Created at: 2020-10-28
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AutoRenewInfoServiceImpl implements AutoRenewInfoService {

    private static final int FIRST_INDEX = 0;
    private static final int ONE = 1;
    private static final int ZERO = 0;

    public static final int MAXIMUM_INTERVAL = 2000;
    @Resource
    private DutUserRepository dutUserRepository;
    @Resource
    private AutoRenewService autoRenewService;
    @Resource
    private AutoRenewDutTypeManager autoRenewDutTypeManager;
    @Resource
    private DutRenewSetLogService dutRenewSetLogService;
    @Resource
    private VipPartnerRenewServerProxy partnerRenewServerProxy;
    @Resource
    private DutService dutService;
    @Resource
    private PayCenterAccountUnbindManager payCenterAccountUnbindManager;
    @Resource
    private SmartJedisClient smartJedisClient;

    @Resource
    private UserAgreementService agreementService;

    @Override
    public List<UserAutoRenewInfo> multiVipTypeRenewInfo(Long uid, List<Long> vipTypes) {
        List<PartnerUserSignRecordQueryResult> partnerUserSignRecords = Collections.emptyList();
        if (CloudConfigUtil.enableNewMobileFunc()) {
            partnerUserSignRecords = partnerRenewServerProxy.queryUserSignRecordWithMultiVipType(uid, vipTypes);
        }
        List<DutUserNew> dutUserNews = agreementService.getByExcludeAgreementTypesAndVipTypes(uid, EXCLUDE_AGREEMENT_TYPES, vipTypes, AgreementStatusEnum.VALID);
        if (CollectionUtils.isEmpty(dutUserNews) && CollectionUtils.isEmpty(partnerUserSignRecords)) {
            return Collections.emptyList();
        }

        Map<Long, List<DutUserNew>> vipTypeToDutUserNewsMap = dutUserNews.stream().collect(Collectors.groupingBy(DutUserNew::getVipType));
        Map<Long, List<PartnerUserSignRecordQueryResult>> vipTypeToSignRecordMapOfPartner = partnerUserSignRecords.stream()
            .collect(Collectors.groupingBy(PartnerUserSignRecordQueryResult::getVipType));
        List<UserAutoRenewInfo> renewInfos = new ArrayList<>();
        for (Map.Entry<Long, List<DutUserNew>> entry : vipTypeToDutUserNewsMap.entrySet()) {
            Long vipType = entry.getKey();
            List<DutUserNew> sameVipTypeDutUserNews = entry.getValue();
            UserAutoRenewInfo firstDutRenewInfo = autoRenewService.getFirstDutRenewInfo(sameVipTypeDutUserNews, vipType);
            if (firstDutRenewInfo != null) {
                renewInfos.add(firstDutRenewInfo);
            }
        }
        Collection<Long> otherVipTypes = CollectionUtils.removeAll(vipTypeToSignRecordMapOfPartner.keySet(), vipTypeToDutUserNewsMap.keySet());
        for (Long otherVipType : otherVipTypes) {
            renewInfos.add(UserAutoRenewInfo.buildFromPartnerUserSignRecord(uid, vipTypeToSignRecordMapOfPartner.get(otherVipType).get(0)));
        }

        return renewInfos;
    }

    private Integer calculateRenewDuration(Timestamp beginTimestamp) {
        if (beginTimestamp == null) {
            return 0;
        }
        Date beginTime = new Date(beginTimestamp.getTime());
        Date endTime = new Date();
        return DateHelper.getDayInterval(beginTime, endTime);
    }

    @Override
    public AutoRenewDurationInfo getUserRenewDurationInfo(List<DutUserNew> dutUserNews, Integer vipGroup) {
        Long uid = dutUserNews.get(0).getUserId();
        Long vipType = dutUserNews.get(0).getVipType();
        List<DutRenewSetLog> dutRenewSetLogs = new ArrayList<>();
        if (vipGroup != null) {
            dutRenewSetLogs = getAllDutRenewSetLogsByVipGroup(uid, vipGroup);
        } else {
            dutRenewSetLogs = getAllDutRenewSetLogs(uid, vipType);
        }
        List<Integer> openedAutoRenewDutTypes = dutUserNews.stream().map(DutUserNew::getType).collect(Collectors.toList());
        Timestamp beginTime = calculateRenewBeginTime(dutRenewSetLogs, openedAutoRenewDutTypes);
        Integer renewDuration = calculateRenewDuration(beginTime);
        DutRenewSetLog recentlyOpenSetLog = getRecentlyOpenAutoRenewTime(dutRenewSetLogs);
        Timestamp recentlyOpenAutoRenewTime = recentlyOpenSetLog != null ? recentlyOpenSetLog.getOperateTime() : null;
        Timestamp currentRenewPackageOpenTime = calcCurrentRenewPackageOpenTime(recentlyOpenSetLog, beginTime, dutRenewSetLogs);
        Integer serialRenewCount = calculateSerialRenewCountByVipType(dutUserNews);
        return AutoRenewDurationInfo.builder()
            .uid(uid)
            .vipType(vipType)
            .autoRenewStatusUnderVipGroup(DutUserNew.RENEW_AUTO)
            .duration(renewDuration)
            .durationStartTime(beginTime)
            .recentlyOpenAutoRenewTime(recentlyOpenAutoRenewTime)
            .durationStartTimeOfCurrentRenewPackage(currentRenewPackageOpenTime)
            .serialRenewCount(serialRenewCount)
            .build();
    }

    @Override
    public AutoRenewDurationInfo getUserRenewDurationBeforeCancel(Long uid, Long vipType, Integer vipGroup) {
        List<Long> vipTypesUnderVipGroup = CloudConfigUtil.getVipTypesByVipGroup(vipGroup);
//        List<DutUserNew> dutUserNews = dutUserRepository.listDutUserNew(uid, vipTypesUnderVipGroup, DutUserNew.RENEW_AUTO);
        List<DutUserNew> dutUserNews = agreementService.getByExcludeAgreementTypesAndVipTypes(uid, EXCLUDE_AGREEMENT_TYPES, vipTypesUnderVipGroup, AgreementStatusEnum.VALID);

        List<DutRenewSetLog> dutRenewSetLogs = getAllDutRenewSetLogsByVipGroup(uid, vipGroup);
        List<Integer> openedDutTypes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            openedDutTypes = dutUserNews.stream().map(DutUserNew::getType).collect(Collectors.toList());
        } else {
            Iterator<DutRenewSetLog> setLogIterator = dutRenewSetLogs.iterator();
            while (setLogIterator.hasNext()) {
                DutRenewSetLog setLog = setLogIterator.next();
                if (vipTypesUnderVipGroup.contains(setLog.getVipType()) && setLog.getOperator().equals(DutRenewSetLog.RENEW_CANCEL)) {
                    openedDutTypes.add(setLog.getType());
                    setLogIterator.remove();
                } else {
                    break;
                }
            }
        }

        Timestamp beginTime = calculateRenewBeginTime(dutRenewSetLogs, openedDutTypes);
        Integer renewDuration = calculateRenewDuration(beginTime);
        DutRenewSetLog recentlyOpenSetLog = getRecentlyOpenAutoRenewTime(dutRenewSetLogs);
        Timestamp recentlyOpenAutoRenewTime = recentlyOpenSetLog != null ? recentlyOpenSetLog.getOperateTime() : null;
        Timestamp currentRenewPackageOpenTime = calcCurrentRenewPackageOpenTime(recentlyOpenSetLog, beginTime, dutRenewSetLogs);
        Integer serialRenewCount = calculateSerialRenewCountByVipType(dutUserNews);
        int autoRenewStatusUnderVipGroup = CollectionUtils.isNotEmpty(dutUserNews) ? DutUserNew.RENEW_AUTO : DutUserNew.RENEW_NOT_AUTO;
        return AutoRenewDurationInfo.builder()
            .uid(uid)
            .vipType(vipType)
            .vipGroup(vipGroup)
            .autoRenewStatusUnderVipGroup(autoRenewStatusUnderVipGroup)
            .duration(renewDuration)
            .durationStartTime(beginTime)
            .recentlyOpenAutoRenewTime(recentlyOpenAutoRenewTime)
            .durationStartTimeOfCurrentRenewPackage(currentRenewPackageOpenTime)
            .serialRenewCount(serialRenewCount)
            .build();
    }

    /**
     * 获取体系
     */
    @Override
    public List<AutoRenewDurationInfo> getUserRenewDurationInfoByVipGroups(Long uid, List<Integer> vipGroups) {
        List<Integer> distinctVipGroups = vipGroups.stream().distinct().collect(Collectors.toList());
        ArrayList<AutoRenewDurationInfo> result = new ArrayList<>(vipGroups.size());
        for (Integer vipGroup : distinctVipGroups) {
            VipGroupEnum vipGroupEnum = VipGroupEnum.valueOf(vipGroup);
            if (vipGroupEnum == null) {
                continue;
            }
            List<Long> calculateRenewDurationVipTypes = CloudConfigUtil.getVipTypesByVipGroup(vipGroup);

            //处理toB自动续费
            Future<List<PartnerUserSignRecordQueryResult>> partnerFuture = null;
            List<PartnerUserSignRecordQueryResult> partnerUserSignRecords = Collections.emptyList();
            boolean hasPartnerSign = false;

            List<DutUserNew> dutUserNews = agreementService.getByExcludeAgreementTypesAndVipTypes(uid, EXCLUDE_AGREEMENT_TYPES, calculateRenewDurationVipTypes, AgreementStatusEnum.VALID);
            if (needCheckToBAutoRenew(vipGroup, dutUserNews)) {
                partnerFuture = partnerRenewServerProxy.queryUserSignRecordWithMultiVipTypeAsync(uid, calculateRenewDurationVipTypes);
            }
            boolean isAutoRenewUser = CollectionUtils.isNotEmpty(dutUserNews);
            //判断partnerFuture来给hasPartnerSign赋值
            if (partnerFuture != null) {
                try {
                    partnerUserSignRecords = partnerFuture.get();
                    hasPartnerSign = CollectionUtils.isNotEmpty(partnerUserSignRecords);
                } catch (Exception e) {
                    log.error("getUserRenewDurationInfoByVipGroups partnerFuture.get error", e);
                }
            }
            if (!hasPartnerSign && !isAutoRenewUser) {
                continue;
            }
            Long needCheckVipType = null;
            if (hasPartnerSign) {
                needCheckVipType = partnerUserSignRecords.get(FIRST_INDEX).getVipType();
            } else {
                needCheckVipType = dutUserNews.get(FIRST_INDEX).getVipType();
            }
            AutoRenewDurationInfo partnerSignDurationInfo = getUserRenewDurationInfo(partnerUserSignRecords, dutUserNews, uid, needCheckVipType, vipGroup);
            if (partnerSignDurationInfo != null) {
                result.add(partnerSignDurationInfo);
            }
        }
        return result;
    }

    private AutoRenewDurationInfo getUserRenewDurationInfo(List<PartnerUserSignRecordQueryResult> partnerUserSignRecords, List<DutUserNew> dutUserNews, Long uid, Long vipType, Integer vipGroup) {
        boolean hasPartnerSign = CollectionUtils.isNotEmpty(partnerUserSignRecords);
        Timestamp partnerSignDurationStartTime = null;
        int partnerSignDuration = 0;
        AutoRenewDurationInfo partnerSignDurationInfo = null;
        if (hasPartnerSign) {
            PartnerUserSignRecordQueryResult partnerUserSignRecord = partnerUserSignRecords.stream()
                .min(Comparator.comparing(PartnerUserSignRecordQueryResult::getSignTime)).orElse(null);
            if (partnerUserSignRecord != null && partnerUserSignRecord.getSignTime() != null) {
                Date partnerRenewSignTime = new Date(partnerUserSignRecord.getSignTime().getTime());
                partnerSignDuration = DateHelper.getDayInterval(partnerRenewSignTime, new Date());
                partnerSignDurationStartTime = new Timestamp(partnerRenewSignTime.getTime());
            }
            partnerSignDurationInfo = AutoRenewDurationInfo.builder()
                .uid(uid)
                .vipType(vipType)
                .vipGroup(vipGroup)
                .autoRenewStatusUnderVipGroup(DutUserNew.RENEW_AUTO)
                .duration(partnerSignDuration)
                .durationStartTime(partnerSignDurationStartTime)
                .durationStartTimeOfCurrentRenewPackage(partnerSignDurationStartTime)
                .build();
        }

        int signDuration = 0;
        AutoRenewDurationInfo signDurationInfo = null;
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            signDurationInfo = getUserRenewDurationInfo(dutUserNews, vipGroup);
            signDurationInfo.setVipGroup(vipGroup);
            signDuration = signDurationInfo.getDuration();
        }
        return signDuration >= partnerSignDuration ? signDurationInfo : partnerSignDurationInfo;
    }

    private static boolean needCheckToBAutoRenew(Integer vipGroup, List<DutUserNew> dutUserNews) {
        return VipGroupEnum.MAIN_SITE.getValue().equals(vipGroup)
            && dutUserNews.stream().anyMatch(d -> d.getVipType().equals(Constants.VIP_USER_SUPER))
            && CloudConfigUtil.enableNewMobileFunc();
    }

    @Override
    public Integer getRenewDuration(Long uid, Long vipType) {
        List<DutRenewSetLog> dutRenewSetLogs = getAllDutRenewSetLogs(uid, vipType);
        Timestamp beginTime = calculateRenewBeginTime(dutRenewSetLogs, uid, vipType);
        return calculateRenewDuration(beginTime);
    }

    private Integer calculateSerialRenewCountByVipType(List<DutUserNew> dutUserNews) {
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return ZERO;
        }
        return dutUserNews.stream().map(DutUserNew::getSerialRenewCount).reduce(ZERO, Integer::sum);
    }

    private List<DutRenewSetLog> getAllDutRenewSetLogs(Long uid, Long vipType) {
        List<Long> vipTypesInGroup = CloudConfigUtil.getCalculateRenewDurationVipTypes(vipType);
        List<Integer> dutTypes = autoRenewDutTypeManager.listDutTypeInfoByVipTypesAndExcludeAgreementTypes(vipTypesInGroup, null)
                .stream()
                .map(AutoRenewDutType::getDutType)
                .collect(Collectors.toList());
        return dutRenewSetLogService.getAllDutRenewSetLogList(uid, dutTypes)
            .stream()
            .sorted(Comparator.comparing(DutRenewSetLog::getOperateTime, Comparator.reverseOrder())
                .thenComparing(DutRenewSetLog::getOperator, Comparator.reverseOrder()))
            .collect(Collectors.toList());
    }

    private List<DutRenewSetLog> getAllDutRenewSetLogsByVipGroup(Long uid, Integer vipGroup) {
        List<Long> vipTypesInGroup = CloudConfigUtil.getVipTypesByVipGroup(vipGroup);
        List<Integer> dutTypes = autoRenewDutTypeManager.listDutTypeInfoByVipTypesAndExcludeAgreementTypes(vipTypesInGroup, null).stream()
            .map(AutoRenewDutType::getDutType)
            .collect(Collectors.toList());
        return dutRenewSetLogService.getAllDutRenewSetLogList(uid, dutTypes).stream()
            .sorted(Comparator.comparing(DutRenewSetLog::getOperateTime, Comparator.reverseOrder())
                .thenComparing(DutRenewSetLog::getOperator, Comparator.reverseOrder()))
            .collect(Collectors.toList());
    }

    private DutRenewSetLog getRecentlyOpenAutoRenewTime(List<DutRenewSetLog> dutRenewSetLogs) {
        if (CollectionUtils.isEmpty(dutRenewSetLogs)) {
            return null;
        }
        return dutRenewSetLogs.stream()
            .filter(dutRenewSetLog -> DutRenewSetLog.RENEW_SET == dutRenewSetLog.getOperator())
            .findFirst()
            .orElse(null);
    }


    private Timestamp calcCurrentRenewPackageOpenTime(DutRenewSetLog recentlyOpenSetLog, Timestamp beginTime, List<DutRenewSetLog> dutRenewSetLogs) {
        if (recentlyOpenSetLog == null || CollectionUtils.isEmpty(dutRenewSetLogs)) {
            return null;
        }
        if (beginTime == null) {
            log.info("beginTime is null, uid:{}", recentlyOpenSetLog.getUserId());
            return null;
        }
        Long vipType = recentlyOpenSetLog.getVipType();
        Integer amount = recentlyOpenSetLog.getAmount();
        Timestamp currentRenewPackageOpenTime = recentlyOpenSetLog.getOperateTime();
        for (DutRenewSetLog dutRenewSetLog : dutRenewSetLogs) {
            Timestamp operateTime = dutRenewSetLog.getOperateTime();
            if (operateTime == null) {
                log.info("operateTime is null, dutRenewSetLog:{}", dutRenewSetLog);
                continue;
            }
            if (operateTime.before(beginTime)) {
                break;
            }
            if (!Objects.equals(vipType, dutRenewSetLog.getVipType()) || !Objects.equals(amount, dutRenewSetLog.getAmount())) {
                break;
            }
            if (dutRenewSetLog.getOperator() == DutRenewSetLog.RENEW_SET) {
                currentRenewPackageOpenTime = operateTime;
            }
        }
        return currentRenewPackageOpenTime;
    }

    private Timestamp calculateRenewBeginTime(List<DutRenewSetLog> dutRenewSetLogs, Long uid, Long vipType) {
        List<Long> vipTypesInGroup = CloudConfigUtil.getCalculateRenewDurationVipTypes(vipType);
        List<DutUserNew> dutUserNews = agreementService.getByExcludeAgreementTypesAndVipTypes(uid, EXCLUDE_AGREEMENT_TYPES, vipTypesInGroup, AgreementStatusEnum.VALID);
        List<Integer> openedAutoRenewDutTypes = dutUserNews.stream().map(DutUserNew::getType).collect(Collectors.toList());
        return calculateRenewBeginTime(dutRenewSetLogs, openedAutoRenewDutTypes);
    }

    private Timestamp calculateRenewBeginTime(List<DutRenewSetLog> dutRenewSetLogs, List<Integer> openedAutoRenewDutTypes) {
        if (CollectionUtils.isEmpty(openedAutoRenewDutTypes)) {
            return null;
        }
        return calculateBeginTimeBySetlog(openedAutoRenewDutTypes, dutRenewSetLogs);
    }

    private Timestamp calculateBeginTimeBySetlog(List<Integer> openedAutoRenewDutTypes, List<DutRenewSetLog> dutRenewSetLogs) {
        Timestamp beginTime = new Timestamp(System.currentTimeMillis());
        HashSet<Integer> openedDutTypeSet = new HashSet<>(openedAutoRenewDutTypes);
        int index = FIRST_INDEX;
        int size = dutRenewSetLogs.size();
        while (index < size) {
            DutRenewSetLog dutRenewSetLog = dutRenewSetLogs.get(index);
            Integer operator = dutRenewSetLog.getOperator();
            Timestamp operateTime = dutRenewSetLog.getOperateTime();
            Integer type = dutRenewSetLog.getType();
            if (DutRenewSetLog.RENEW_SET == operator) {
                List<DutRenewSetLog> restSetLogs = dutRenewSetLogs.subList(index + 1, size);
                if (reOpenSameDutType(type, restSetLogs)) {
                    beginTime = operateTime;
                    index++;
                    continue;
                }
                List<DutRenewSetLog> sameTimeCancelSetLogs = getSameTimeCancelSetLogs(restSetLogs, dutRenewSetLog);
                if (firstOpenTheOnlyDutType(dutRenewSetLog, restSetLogs, openedDutTypeSet, sameTimeCancelSetLogs)) {
                    beginTime = operateTime;
                    openedDutTypeSet.remove(type);
                    break;
                }
                if (CollectionUtils.isNotEmpty(sameTimeCancelSetLogs)) {
                    beginTime = operateTime;
                    openedDutTypeSet.remove(type);
                    List<Integer> canceledDutType = sameTimeCancelSetLogs.stream().map(DutRenewSetLog::getType).collect(Collectors.toList());
                    openedDutTypeSet.addAll(canceledDutType);
                    index = index + sameTimeCancelSetLogs.size();
                    index++;
                    continue;
                }
                if (openWithOtherDutType(openedDutTypeSet, type) && CollectionUtils.isEmpty(sameTimeCancelSetLogs)) {
                    beginTime = operateTime;
                    openedDutTypeSet.remove(type);
                    index++;
                    continue;
                }
            }
            if (DutRenewSetLog.RENEW_CANCEL == operator) {
                if (cancelWithOtherDutType(openedDutTypeSet, type)) {
                    openedDutTypeSet.add(type);
                    beginTime = operateTime;
                } else {
                    beginTime = operateTime;
                    break;
                }
            }
            index++;
        }
        return beginTime;
    }

    private boolean cancelWithOtherDutType(HashSet<Integer> openedDutTypeSet, Integer dutType) {
        return !openedDutTypeSet.isEmpty() && openedDutTypeSet.stream().anyMatch(type -> !Objects.equals(dutType, type));
    }


    private boolean openWithOtherDutType(HashSet<Integer> openedDutTypeSet, Integer dutType) {
        return openedDutTypeSet.contains(dutType) && openedDutTypeSet.size() > ONE;
    }

    private boolean reOpenSameDutType(Integer type, List<DutRenewSetLog> dutRenewSetLogs) {
        DutRenewSetLog renewSetLog = dutRenewSetLogs.stream()
            .filter(dutRenewSetLog -> dutRenewSetLog.getType().equals(type))
            .findFirst()
            .orElse(null);
        if (renewSetLog == null || DutRenewSetLog.RENEW_CANCEL == renewSetLog.getOperator()) {
            return false;
        }
        return true;
    }

    private boolean firstOpenTheOnlyDutType(DutRenewSetLog dutRenewSetLog, List<DutRenewSetLog> dutRenewSetLogs,
        HashSet<Integer> openedDutTypeSet, List<DutRenewSetLog> sameTimeCancelSetLogs) {
        Integer type = dutRenewSetLog.getType();
        DutRenewSetLog renewSetLog = dutRenewSetLogs.stream()
            .filter(setLog -> setLog.getType().equals(type))
            .findFirst()
            .orElse(null);
        boolean firstOpen = renewSetLog == null || DutRenewSetLog.RENEW_CANCEL == renewSetLog.getOperator();
        boolean onlyDutType = openedDutTypeSet.contains(type) && openedDutTypeSet.size() == ONE;
        return firstOpen && onlyDutType && org.apache.commons.collections.CollectionUtils.isEmpty(sameTimeCancelSetLogs);
    }

    private List<DutRenewSetLog> getSameTimeCancelSetLogs(List<DutRenewSetLog> dutRenewSetLogs, DutRenewSetLog dutRenewSetLog) {
        Timestamp operateTime = dutRenewSetLog.getOperateTime();
        return dutRenewSetLogs.stream().filter(setLog -> cancelWithOpenOperator(operateTime, setLog)).collect(Collectors.toList());
    }

    private boolean cancelWithOpenOperator(Timestamp operateTime, DutRenewSetLog dutRenewSetLog) {
        return DutRenewSetLog.RENEW_CANCEL == dutRenewSetLog.getOperator()
            && operateTimeClosely(operateTime, dutRenewSetLog.getOperateTime());
    }

    private boolean operateTimeClosely(Timestamp operateTime, Timestamp otherOperateTime) {
        if (operateTime == null || otherOperateTime == null) {
            return false;
        }
        return Math.abs(operateTime.getTime() - otherOperateTime.getTime()) <= MAXIMUM_INTERVAL;
    }

    @Override
    public String unbindTipsAfterSignPay(Long uid, Long vipType, Integer payChannel) {
        boolean exists = smartJedisClient.exists(PayCenterAccountUnbindManager.unbindRedisKeyWhenSignPay(uid, payChannel));
        if (exists) {
            return CloudConfigUtil.unbindTipsAfterSignPay(payChannel);
        }
        if (!CloudConfigUtil.needUnbindUidSuffixWhenSignPay(uid)) {
            return "";
        }
        List<Integer> needUnbindDutTypes = payCenterAccountUnbindManager.needUnbindDutTypesWhenBuy(uid, vipType, payChannel, null);
        return CollectionUtils.isNotEmpty(needUnbindDutTypes) ? CloudConfigUtil.unbindTipsAfterSignPay(payChannel) : "";
    }
}
