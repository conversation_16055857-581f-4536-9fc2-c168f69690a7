package com.qiyi.boss.service.impl;

import com.qiyi.boss.exception.ConfigException;
import com.qiyi.boss.model.Product;
import com.qiyi.vip.trade.qiyue.domain.QiYueProductNew;
import com.qiyi.vip.trade.qiyue.mapper.QiyueProductNewMapper;

import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import com.iqiyi.solar.config.client.CloudConfig;

/**
 * Created by IntelliJ IDEA.
 * <AUTHOR>
 * Date: 10-12-30
 * Time: 下午3:50
 * To change this template use File | Settings | File Templates.
 */
@Slf4j
@Component
public class QiYueProductNewService {

    @Resource
    private QiyueProductNewMapper qiyueProductNewMapper;
    @Resource
    private CloudConfig cloudConfig;

    /**
     * 根据产品的code取产品对象
     *
     * @param code 产品code
     * @return 产品对象
     */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "QiYueProductNew_getProductByCode", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public QiYueProductNew getProductByCode(String code) {
        return this.qiyueProductNewMapper.queryByCode(code);
    }

    /**
     * 查询指定产品对应的天卡产品code
     * @param productCode 原产品code
     * @return 对应的天卡产品code
     */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "QiYueProductNew_selectRelatedDayProductCode", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public String selectRelatedDayProductCode(String productCode) {
        String dayProductCodeMapStr = cloudConfig.getProperty("dayProductCodeMap", null);
        if (StringUtils.isNotBlank(dayProductCodeMapStr)) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(dayProductCodeMapStr);
                String dayProductCode = jsonObject.getString(productCode);
                if (dayProductCode != null) {
                    return dayProductCode;
                }
            } catch (Exception e) {
                log.error("解析JSON配置项dayProductCodeMap失败, json:{}", dayProductCodeMapStr);
                throw new ConfigException("解析JSON配置项dayProductCodeMap失败");
            }
        }
        return qiyueProductNewMapper.selectRelatedDayProductCode(productCode);
    }
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "Product_getProductFromQiYueProduct", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public Product getProductFromQiYueProduct(String productCode) {
        if (StringUtils.isBlank(productCode)) {
            return null;
        }
        QiYueProductNew qiYueProductNew = qiyueProductNewMapper.queryByCode(productCode);
        if (qiYueProductNew == null) {
            return null;
        }
        Product product = new com.qiyi.boss.model.Product();
        BeanUtils.copyProperties(qiYueProductNew, product);
        product.setSubtype(qiYueProductNew.getSubType());
        return product;
    }
}
