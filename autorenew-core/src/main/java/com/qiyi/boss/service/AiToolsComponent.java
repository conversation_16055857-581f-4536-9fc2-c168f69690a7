package com.qiyi.boss.service;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import com.qiyi.boss.Constants;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.dto.UserAutoRenewInfo;
import com.qiyi.boss.enums.AgreementStatusEnum;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.enums.PayStatusEnum;
import com.qiyi.boss.enums.VipUserStatusEnum;
import com.qiyi.boss.response.ChannelSignInfo;
import com.qiyi.boss.response.QueryUserSetLogResult;
import com.qiyi.boss.response.UidAllRenewInfo;
import com.qiyi.boss.response.UserRenewInfo;
import com.qiyi.boss.response.UserRenewLog;
import com.qiyi.boss.response.UserRenewSetLog;
import com.qiyi.boss.response.UserVipInfo;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.AutoRenewVipTypeUpgradeConfigManager;
import com.qiyi.boss.service.impl.GatewayManager;
import com.qiyi.boss.service.impl.QiYuePlatformManager;
import com.qiyi.boss.service.impl.UserAgreementLogManager;
import com.qiyi.boss.service.impl.UserManager;
import com.qiyi.boss.service.impl.VipTypeManager;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.NumberFormatUtils;
import com.qiyi.vip.commons.component.VipInfoBatchQueryCloudApi;
import com.qiyi.vip.commons.component.dto.BatchQueryVipInfoReq;
import com.qiyi.vip.commons.component.dto.VipInfo;
import com.qiyi.vip.trade.autorenew.dao.DutRenewLogDao;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.service.DutRenewSetLogService;
import com.qiyi.vip.trade.qiyue.domain.Gateway;
import com.qiyi.vip.trade.qiyue.domain.VipType;
import java.util.concurrent.CompletableFuture;

/**
 * @auther: guojing
 * @date: 2023/8/26 15:08
 */
@Slf4j
@Component
public class AiToolsComponent {

    @Resource
    private VipTypeManager vipTypeManager;
    @Resource
    private GatewayManager gatewayManager;
    @Resource
    private AgreementNoInfoManager agreementNoInfoManager;
    @Resource
    private QiYuePlatformManager qiYuePlatformManager;
    @Resource
    private AutoRenewVipTypeUpgradeConfigManager autoRenewVipTypeUpgradeConfigManager;
    @Resource
    private UserAgreementService userAgreementService;
    @Resource
    private UserAgreementLogManager userAgreementLogManager;
    @Resource
    private AutoRenewService autoRenewService;
    @Resource
    private VipInfoBatchQueryCloudApi vipInfoBatchQueryCloudApi;
    @Resource
    private DutRenewSetLogService dutRenewSetLogService;
    @Resource
    private DutRenewLogDao dutRenewLogDao;;

    /**
     * 查询用户会员信息
     */
    public List<UserVipInfo> getUserVipInfo(Long uid, List<Long> vipTypes) {
        BatchQueryVipInfoReq.BatchQueryVipInfoReqBuilder paramBuilder = BatchQueryVipInfoReq.builder()
            .uid(uid)
            .platform(Constants.PLATFORM_DEFAULT_CODE)
            .vipTypeList(vipTypes);
        List<VipInfo> vipInfos = null;
        try {
            vipInfos = vipInfoBatchQueryCloudApi.queryMultiVipInfoFromBossWithCircuitBreaker(paramBuilder.build());
        } catch (Exception e) {
            log.error("[getUserVipInfo] [Exception] [userId:{}] [vipTypes:{}]]", uid, vipTypes, e);
        }
        if (CollectionUtils.isEmpty(vipInfos)) {
            return Collections.emptyList();
        }

        List<UserVipInfo> result = new ArrayList<>();
        for (VipInfo vipInfo : vipInfos) {
            Long vipType = Long.valueOf(vipInfo.getVipType());
            Timestamp deadline = new Timestamp(vipInfo.getDeadline().getT());
            VipType vipTypeObj = vipTypeManager.getVipTypeById(vipType);
            Integer vipUserStatus = UserManager.getPassportStatus(vipInfo.getExpire(), vipInfo.getStatus());
            VipUserStatusEnum vipUserStatusEnum = VipUserStatusEnum.parseOf(vipUserStatus);
            UserVipInfo userVipInfo = UserVipInfo.builder()
                .uid(uid)
                .vipType(vipType)
                .vipTypeName(vipTypeObj != null ? vipTypeObj.getName() : null)
                .status(vipUserStatusEnum != null ? vipUserStatusEnum.getDesc() : null)
                .deadline(DateHelper.getDateString(deadline))
                .build();
            result.add(userVipInfo);
        }
        return result;
    }

    /**
     * 查询用户签约信息
     */
    public UserRenewInfo getUserRenewInfo(Long uid, AgreementTypeEnum agreementType, Long vipType) {
        List<DutUserNew> dutUserNews = userAgreementService.getEffectiveByAgreementTypeAndVipType(uid, agreementType, vipType);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return null;
        }

        Integer status = dutUserNews.get(0).getStatus();
        AgreementStatusEnum agreementStatusEnum = AgreementStatusEnum.valueOf(status);
        UserAutoRenewInfo firstDutRenewInfo = autoRenewService.getFirstDutRenewInfo(dutUserNews, vipType);
        String nextDutTime = DateHelper.getDateString(firstDutRenewInfo.getNextDutTime());

        dutUserNews.sort(Comparator.comparing(DutUserNew::getSignTime, Comparator.nullsFirst(Comparator.naturalOrder())));
        List<ChannelSignInfo> channelSignInfoList = new ArrayList<>();
        for (DutUserNew dutUserNew : dutUserNews) {
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getByAgreementNoOrDutType(dutUserNew.getAgreementNo(), dutUserNew.getType(), dutUserNew.getAmount());
            String platformName = qiYuePlatformManager.getPlatformName(dutUserNew.getPlatformCode(), dutUserNew.getPlatform());
            Integer renewPrice = userAgreementService.getAutoRenewDisplayRenewPrice(dutUserNew);
            String signTimeStr = dutUserNew.getSignTime() != null ? DateHelper.getDateString(dutUserNew.getSignTime()) : null;
            ChannelSignInfo channelSignInfo = ChannelSignInfo.builder()
                .agreementNo(dutUserNew.getAgreementNo())
                .dutType(dutUserNew.getType())
                .agreementName(agreementNoInfo.getName())
                .signKey(dutUserNew.getSignKey())
                .signTime(signTimeStr)
                .amount(dutUserNew.getAmount())
                .renewPrice(NumberFormatUtils.formatNumber(renewPrice))
                .payChannel(agreementNoInfo.getPayChannel())
                .payChannelName(agreementNoInfo.getPayChannelName())
                .platformName(platformName)
                .build();
            channelSignInfoList.add(channelSignInfo);
        }

        VipType vipTypeObj = vipTypeManager.getVipTypeById(vipType);
        VipType sourceVipTypeObj = null;
        if (firstDutRenewInfo.getSourceVipType() != null) {
            sourceVipTypeObj = vipTypeManager.getVipTypeById(firstDutRenewInfo.getSourceVipType());
        }
        return UserRenewInfo.builder()
            .uid(uid)
            .sourceVipType(firstDutRenewInfo.getSourceVipType())
            .sourceVipTypeName(sourceVipTypeObj != null ? sourceVipTypeObj.getName() : null)
            .vipType(vipType)
            .vipTypeName(vipTypeObj != null ? vipTypeObj.getName() : null)
            .agreementType(agreementType.getValue())
            .agreementTypeDesc(agreementType.getDesc())
            .status(status)
            .statusDesc(agreementStatusEnum != null ? agreementStatusEnum.getDesc() : null)
            .nextDutChannel(firstDutRenewInfo.getPayChannel())
            .nextDutTime(nextDutTime)
            .channelSignInfos(channelSignInfoList)
            .build();
    }

    /**
     * 查询用户自动续费操作记录查询
     */
    public QueryUserSetLogResult getUserRenewSetLogs(Long uid, AgreementTypeEnum agreementType, Long vipType) {
        List<Long> sameGroupVipTypes = autoRenewVipTypeUpgradeConfigManager.getSameGroupVipTypes(vipType, agreementType.getValue());
        List<DutRenewSetLog> setLogs = userAgreementLogManager.getSetLogByAgreementTypeAndVipTypes(uid, agreementType, sameGroupVipTypes);
        if (CollectionUtils.isEmpty(setLogs)) {
            return null;
        }

        int latestOpenSetLogIdx = 0;
        List<UserRenewSetLog> vipGroupSetLogs = new ArrayList<>();
        for (int i = 0; i < setLogs.size(); i++) {
            DutRenewSetLog setLog = setLogs.get(i);
            VipType vipTypeObj = vipTypeManager.getVipTypeById(setLog.getVipType());
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getByAgreementNoOrDutType(setLog.getAgreementNo(), setLog.getType(), setLog.getAmount());
            String platformName = qiYuePlatformManager.getPlatformName(setLog.getPlatformCode(), setLog.getPlatform());
            OperateTypeEnum operateTypeEnum = OperateTypeEnum.parseValue(setLog.getOperator());
            Map<String, Object> descriptionMap = setLog.parseDescription();
            String operateScene = MapUtils.getString(descriptionMap, "scene");
            String fc = MapUtils.getString(descriptionMap, "fc");
            Gateway gateway = gatewayManager.getGatewayByCode(fc);
            OperateSceneEnum operateSceneEnum = OperateSceneEnum.parseValue(operateScene);
            Integer renewPrice = MapUtils.getInteger(descriptionMap, "renewPrice");
            UserRenewSetLog userRenewSetLog = UserRenewSetLog.builder()
                .uid(setLog.getUserId())
                .vipType(setLog.getVipType())
                .vipTypeName(vipTypeObj != null ? vipTypeObj.getName() : null)
                .agreementType(setLog.getAgreementType())
                .agreementTypeDesc(agreementType.getDesc())
                .agreementNo(setLog.getAgreementNo())
                .dutType(setLog.getType())
                .agreementName(agreementNoInfo.getName())
                .signKey(setLog.getSignKey())
                .amount(setLog.getAmount())
                .operateTime(DateHelper.getDateString(setLog.getOperateTime()))
                .operator(setLog.getOperator())
                .operatorDesc(operateTypeEnum.getDesc())
                .platformName(platformName)
                .operateScene(operateSceneEnum != null ? operateSceneEnum.getDesc() : null)
                .fcDesc(gateway != null ? gateway.getName() : null)
                .payChannel(agreementNoInfo.getPayChannel())
                .payChannelName(agreementNoInfo.getPayChannelName())
                .renewPrice(renewPrice != null ? NumberFormatUtils.formatNumber(renewPrice) : null)
                .signOrderCode(MapUtils.getString(descriptionMap, "orderCode"))
                .build();
            vipGroupSetLogs.add(userRenewSetLog);
            if (setLog.getVipType().equals(vipType) && OperateTypeEnum.OPEN.getValue() == setLog.getOperator()) {
                latestOpenSetLogIdx = i;
            }
        }

        UserRenewSetLog latestOpenSetLog = vipGroupSetLogs.get(latestOpenSetLogIdx);
        List<UserRenewSetLog> vipRecentlySetLogs = Lists.newArrayList(latestOpenSetLog);
        for (int i = latestOpenSetLogIdx + 1; i < vipGroupSetLogs.size(); i++) {
            UserRenewSetLog currentItem = vipGroupSetLogs.get(i);
            if (Objects.equals(latestOpenSetLog.getAgreementNo(), currentItem.getAgreementNo())) {
                vipRecentlySetLogs.add(currentItem);
            } else if (Objects.equals(latestOpenSetLog.getDutType(), currentItem.getDutType())
                && Objects.equals(latestOpenSetLog.getAmount(), currentItem.getAmount())) {
                vipRecentlySetLogs.add(currentItem);
            }
        }
        return QueryUserSetLogResult.builder()
            .vipGroupSetLogs(vipGroupSetLogs)
            .vipRecentlySetLogs(vipRecentlySetLogs)
            .build();
    }

    /**
     * 查询用户自动续费代扣记录
     */
    public List<UserRenewLog> getUserRenewLogs(Long uid, AgreementTypeEnum agreementType, Long vipType, Timestamp startTime) {
        List<DutRenewLog> renewLogs = null;
        if (startTime == null) {
            DutRenewLog renewLog = userAgreementLogManager.getLatestRenewLogByAgreementTypeAndVipTypes(uid, agreementType, vipType);
            if (renewLog != null) {
                renewLogs = Collections.singletonList(renewLog);
            }
        } else {
            renewLogs = userAgreementLogManager.getRenewLogByAgreementTypeAndVipTypes(uid, agreementType, vipType, startTime);
        }
        if (CollectionUtils.isEmpty(renewLogs)) {
            return Collections.emptyList();
        }

        List<UserRenewLog> result = new ArrayList<>();
        for (DutRenewLog renewLog : renewLogs) {
            VipType vipTypeObj = vipTypeManager.getVipTypeById(renewLog.getVipType());
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getByAgreementNoOrDutType(renewLog.getAgreementNo(), renewLog.getType(), renewLog.getAmount());
            String platformName = qiYuePlatformManager.getPlatformName(renewLog.getPlatformCode(), renewLog.getPlatform());
            OperateTypeEnum operateTypeEnum = OperateTypeEnum.parseValue(renewLog.getOperateType());
            PayStatusEnum payStatusEnum = PayStatusEnum.valueOf(renewLog.getStatus());
            UserRenewLog userRenewLog = UserRenewLog.builder()
                .uid(uid)
                .vipType(vipType)
                .vipTypeName(vipTypeObj != null ? vipTypeObj.getName() : null)
                .agreementType(renewLog.getAgreementType())
                .agreementTypeDesc(agreementType.getDesc())
                .agreementNo(renewLog.getAgreementNo())
                .agreementName(agreementNoInfo.getName())
                .signKey(renewLog.getSignKey())
                .amount(renewLog.getAmount())
                .orderCode(renewLog.getOrderCode())
                .renewPrice(renewLog.getFee() != null ? NumberFormatUtils.formatNumber(renewLog.getFee()) : null)
                .dutTime(DateHelper.getDateString(renewLog.getCreateTime()))
                .status(renewLog.getStatus())
                .statusDesc(payStatusEnum != null ? payStatusEnum.getDesc() : null)
                .failureReason(renewLog.getThirdErrorMsg())
                .operateTypeDesc(operateTypeEnum != null ? operateTypeEnum.getDesc() : null)
                .payChannel(agreementNoInfo.getPayChannel())
                .payChannelName(agreementNoInfo.getPayChannelName())
                .platformName(platformName)
                .build();

            result.add(userRenewLog);
        }
        return result;
    }


    public UidAllRenewInfo getUserRenewInfoByUid(Long uid) {
        try {
            CompletableFuture<List<DutUserNew>> renewStatusFuture = CompletableFuture.supplyAsync(() -> queryRenewStatus(uid));
            CompletableFuture<List<DutRenewSetLog>> setLogFuture = CompletableFuture.supplyAsync(() -> querySetLog(uid));
            CompletableFuture<List<DutRenewLog>> renewLogFuture = CompletableFuture.supplyAsync(() -> queryRenewLog(uid));

            return UidAllRenewInfo.buildWithFieldDesc(
                    renewStatusFuture.get(),
                    setLogFuture.get(),
                    renewLogFuture.get()
            );
        } catch (Exception e) {
            log.error("[getUserRenewInfoByUid] build UidAllRenewInfo error, uid: {}", uid, e);
            return UidAllRenewInfo.buildWithFieldDesc(
                    Collections.emptyList(),
                    Collections.emptyList(),
                    Collections.emptyList()
            );
        }
    }

    private List<DutUserNew> queryRenewStatus(Long uid) {
        try {
            return userAgreementService.getUserWithAllAgreements(uid, null).stream()
                    .sorted(Comparator.comparing(DutUserNew::getOperateTime).reversed())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[getUserRenewInfoByUid] getUserWithAllAgreements error, uid: {}", uid, e);
            return Collections.emptyList();
        }
    }

    private List<DutRenewSetLog> querySetLog(Long uid) {
        try {
            return dutRenewSetLogService.queryDutSetLogsByUserId(uid, null, null, null, null).stream()
                    .sorted(Comparator.comparing(DutRenewSetLog::getOperateTime).reversed())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[getUserRenewInfoByUid] queryDutSetLogsByUserId error, uid: {}", uid, e);
            return Collections.emptyList();
        }
    }

    private List<DutRenewLog> queryRenewLog(Long uid) {
        try {
            return dutRenewLogDao.getDutRenewLogByOrderCode(uid, null).stream()
                    .sorted(Comparator.comparing(DutRenewLog::getCreateTime).reversed())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[getUserRenewInfoByUid] getDutRenewLogByOrderCode error, uid: {}", uid, e);
            return Collections.emptyList();
        }
    }
}
