package com.qiyi.boss.service;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

import com.qiyi.boss.enums.TypeOfOrderEnum;
import com.iqiyi.trade.order.service.model.OrderStatus;

/**
 * @Author: <PERSON>
 * @Date: 2021/7/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderPaidMsg {
    private Long id;

    private String orderCode;

    private String tradeCode;

    private Integer fee;

    private Integer status;

    private Timestamp createTime;

    private Timestamp modifyTime;

    private Timestamp payTime;

    private Long userId;

    private String accountId;

    private Integer payType;

    private Long platform;

    private Long channel;

    private Long pushChannel;

    private Long gateway;

    private String tradeNo;

    private Timestamp beginTime;

    private Timestamp tradeCreate;

    private Timestamp tradePayment;

    private String refer;

    private String cartCode;

    private String centerCode;

    private Integer renewType;

    private Timestamp updateTime;

    private Integer type;

    private Timestamp validTime;

    private Long serviceId;

    private String serviceOrderNo;

    private String returnUrl;

    private String notifyUrl;

    private String notifyResult;

    private String userIp;

    private Integer couponFee;

    private String fv;

    private Long productId;

    private String skuId;

    private Integer realFee;

    private Integer productFee;

    private Long contentId;

    private Integer productType;

    private String contentUrl;

    private String pictureUrl;

    private Timestamp deadline;

    private Integer renewalsFlag;

    private Integer amount;

    private Timestamp startTime;

    private String name;

    private Integer originalPrice;

    private String partner;

    private String businessValues;

    private Integer autoRenew;

    private String frVersion;

    private String sendMsgId;

    private Integer couponSettlementFee;

    private Integer settlementFee;

    private String centerPayType;

    private Integer centerPayService;

    private Integer chargeType;

    private String orders;

    private String orderType;

    private String currencyUnit;

    public boolean notPaidStatus() {
        return !OrderStatus.PAID.getValue().equals(status);
    }

    /**
     * 不是微信支付分完结订单
     * @return
     */
    public boolean notWeChatPayScoreCompleteOrder() {
        return !TypeOfOrderEnum.WECHAT_PAY_SCORE_COMPLETE.getCode().equals(type);
    }

}
