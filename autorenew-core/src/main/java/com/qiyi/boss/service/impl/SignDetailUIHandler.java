package com.qiyi.boss.service.impl;

import com.qiyi.boss.dto.UserAgreementRespDto;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.qiyue.domain.QiYueProductNew;

/**
 * 协议签约信息 Handler
 *
 * <AUTHOR>
 * @date 2021/7/26 11:32
 */
public interface SignDetailUIHandler {

    AgreementTypeEnum getAgreementType();

    /**
     * 构造签约信息详情
     */
    UserAgreementRespDto constructRespDto(AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate, DutUserNew dutUserNew, QiYueProductNew qiYueProductNew);
}
