package com.qiyi.boss.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.qiyi.boss.Constants;
import com.qiyi.boss.async.queue.AsyncTaskFactory;
import com.qiyi.boss.async.task.CommonManualDutTask;
import com.qiyi.boss.async.task.UpgradeManualDutTask;
import com.qiyi.boss.autorenew.dto.ManualDutRespDto;
import com.qiyi.boss.autorenew.dto.ManualDutResultQueryDto;
import com.qiyi.boss.enums.AgreementStatusEnum;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.outerinvoke.CustomVipInfoClient;
import com.qiyi.boss.service.AutorenewDutConfigService;
import com.qiyi.boss.service.DutUserService;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.JacksonUtils;
import com.qiyi.vip.trade.autorenew.domain.AsyncTask;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.AutorenewDutConfig;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

import static com.qiyi.boss.Constants.COMMON_POOL_TYPE_OFFSET;
import static com.qiyi.boss.Constants.RENEW_LOG_REAL_DUT_FEE;

/**
 * <AUTHOR>
 * @className ManualDutManager
 * @description
 * @date 2022/9/6
 **/
@Component
@Slf4j
public class ManualDutManager {
    @Resource
    private AutoRenewDutTypeManager autoRenewDutTypeManager;
    @Resource
    private DutManager dutManager;
    @Resource
    private AutorenewDutConfigService autorenewDutConfigService;
    @Resource
    private DutUserService dutUserService;
    @Resource
    private UserAgreementService agreementService;
    @Resource
    private CustomVipInfoClient customVipInfoClient;

    public static final int MANUAL_DUT_NOT_START = 3;

    public void manualDut(DutUserNew dutUserForManualDut, String fc) {
        Integer dutType = dutUserForManualDut.getType();
        AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutType);
        if (dutUserForManualDut.upgradeDutUser()) {
            upgradeManualDut(dutUserForManualDut, autoRenewDutType, fc);
            return;
        }
        commonManualDut(dutUserForManualDut, autoRenewDutType, fc);
    }

    public ManualDutRespDto queryManualDutResult(ManualDutResultQueryDto queryDto) {
        Timestamp currentTime = DateHelper.getCurrentTime();
        Long manualDutTime = queryDto.getManualDutTime();
        Long uid = queryDto.getUid();
        Long vipType = queryDto.getVipType();
        DutRenewLog dutRenewLog = dutManager.findDutRenewLogByTime(uid, new Timestamp(manualDutTime), currentTime, AgreementTypeEnum.AUTO_RENEW.getValue(), vipType)
            .stream()
            .filter(DutRenewLog::manualDutLog)
            .min(Comparator.comparing(DutRenewLog::getCreateTime)).orElse(null);
        int dutResultStatus = dutRenewLog == null ? MANUAL_DUT_NOT_START : dutRenewLog.getStatus();
        Integer fee = dutRenewLog == null ? null : dutRenewLog.getFee();
        if (dutRenewLog != null && StringUtils.isNotBlank(dutRenewLog.getDescription())) {
            Map<String, Object> descMap = JacksonUtils.parseMap(dutRenewLog.getDescription());
            Integer realDutFee = MapUtils.getInteger(descMap, RENEW_LOG_REAL_DUT_FEE, null);
            fee = realDutFee == null ? fee : realDutFee;
        }
//        List<DutUserNew> dutUserNews = dutUserService.getDutUserNews(uid, vipType, DutUserNew.RENEW_AUTO);
        List<DutUserNew> dutUserNews = agreementService.getByAgreementTypeAndVipType(uid, AgreementTypeEnum.AUTO_RENEW, vipType, AgreementStatusEnum.VALID);
        String platformCode = dutUserNews.stream().map(DutUserNew::getPlatformCode).filter(StringUtils::isNotBlank).findFirst().orElse(null);
        Long deadLine;
        if (StringUtils.isBlank(platformCode) && CollectionUtils.isNotEmpty(dutUserNews)) {
            deadLine = dutUserNews.get(0).getDeadline().getTime();
        } else {
            VipUser vipInfo = customVipInfoClient.getVipInfoNew(uid, vipType);
            deadLine = vipInfo == null ? null : vipInfo.getDeadline().getTime();
        }
        ManualDutRespDto respDto = ManualDutRespDto.builder()
            .uid(uid)
            .vipType(vipType.intValue())
            .realFee(fee)
            .dutResult(dutResultStatus)
            .deadLine(deadLine)
            .build();
        return respDto;
    }

    private void upgradeManualDut(DutUserNew dutUserNew, AutoRenewDutType autoRenewDutType, String fc) {
        Long userId = dutUserNew.getUserId();
        Long vipType = dutUserNew.getVipType();
        Long sourceVipType = dutUserNew.getSourceVipType();

        UpgradeManualDutTask upgradeManualDutTask = new UpgradeManualDutTask();
        upgradeManualDutTask.setTaskType(Constants.TASK_TYPE_MANUALDUT);
        upgradeManualDutTask.setFc(fc);
        upgradeManualDutTask.setUserId(userId);
        upgradeManualDutTask.setVipType(vipType);
        upgradeManualDutTask.setDutBindType(autoRenewDutType.getDutType());
        upgradeManualDutTask.setAgreementNo(dutUserNew.getAgreementNo());
        upgradeManualDutTask.genTaskId(userId, vipType, sourceVipType);

        Optional<AutorenewDutConfig> autorenewDutConfig = autorenewDutConfigService.findByVipType(dutUserNew.getSourceVipType(),
            dutUserNew.getVipType(), AutorenewDutConfig.JOB_TYPE_UPGRADE, autoRenewDutType.getAgreementType(), AutorenewDutConfig.STATUS_VALID).stream().findFirst();
        if (!autorenewDutConfig.isPresent()) {
            log.error("[upgradeManualDut][not find autorenewDutConfig][upgradeManualDutTask: {}]", upgradeManualDutTask);
            return;
        }
        upgradeManualDutTask.setAutorenewDutConfig(autorenewDutConfig.get());
        AsyncTaskFactory.getInstance().insertIntoDB(upgradeManualDutTask, dutManager.getPoolType(userId, COMMON_POOL_TYPE_OFFSET), AsyncTask.Task_PRIORITY_1, DateHelper.getCurrentTime());

    }

    private void commonManualDut(DutUserNew dutUserNew, AutoRenewDutType autoRenewDutType, String fc) {
        Long userId = dutUserNew.getUserId();
        Long vipType = dutUserNew.getVipType();
        Long sourceVipType = dutUserNew.getSourceVipType();
        Integer type = dutUserNew.getType();
        CommonManualDutTask commonManualDutTask = new CommonManualDutTask();
        commonManualDutTask.setTaskType(Constants.TASK_TYPE_MANUALDUT);
        commonManualDutTask.setFc(fc);
        commonManualDutTask.setUserId(userId);
        commonManualDutTask.setVipType(vipType);
        commonManualDutTask.setDutBindType(type);
        commonManualDutTask.setAgreementNo(dutUserNew.getAgreementNo());
        commonManualDutTask.genTaskId(userId, vipType, sourceVipType);
        commonManualDutTask.setProductCode(autoRenewDutType.getProductCode());

        Optional<AutorenewDutConfig> autorenewDutConfig = autorenewDutConfigService.findByVipType(dutUserNew.getSourceVipType(),
            dutUserNew.getVipType(), AutorenewDutConfig.JOB_TYPE_COMMON, autoRenewDutType.getAgreementType(), AutorenewDutConfig.STATUS_VALID).stream().findFirst();
        if (!autorenewDutConfig.isPresent()) {
            log.error("[commonManualDut][not find autorenewDutConfig][commonManualDut: {}]", commonManualDutTask);
            return;
        }
        commonManualDutTask.setAutorenewDutConfig(autorenewDutConfig.get());
        AsyncTaskFactory.getInstance().insertIntoDB(commonManualDutTask, dutManager.getPoolType(userId, COMMON_POOL_TYPE_OFFSET), AsyncTask.Task_PRIORITY_1, DateHelper.getCurrentTime());
    }

}
