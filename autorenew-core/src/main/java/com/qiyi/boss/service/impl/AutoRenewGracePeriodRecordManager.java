package com.qiyi.boss.service.impl;

import com.qiyi.boss.Constants;
import com.qiyi.boss.db.DataSource;
import com.qiyi.boss.db.DataSourceEnum;
import com.qiyi.boss.service.AutoRenewGracePeriodRecordService;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewGracePeriodRecord;
import com.qiyi.vip.trade.autorenew.mapper.AutoRenewGracePeriodRecordMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> liuwanqiang
 * Date: 2018-10-31
 * Time: 0:55
 */
@Component(value = "autoRenewGracePeriodRecordService")
public class AutoRenewGracePeriodRecordManager implements AutoRenewGracePeriodRecordService {

	@Resource
	private AutoRenewGracePeriodRecordMapper autoRenewGracePeriodRecordMapper;

	/**
	 * 查询宽限期记录
	 *
	 * @param userId 用户id
	 * @param appId  苹果商品id
	 * @param amount 赠送时长
	 * @param interval 间隔天数
	 * @return listDutTypes
	 */
	@Override
    //@DataSource(DataSourceEnum.SLAVE)
	public List<AutoRenewGracePeriodRecord> find(Long userId, String appId, Integer amount, Integer interval) {
        Timestamp intervalTime = DateHelper.caculateTime(DateHelper.getCurrentTime(), -1 * interval, Constants.PRODUCT_PERIODUNIT_DAY);
        return autoRenewGracePeriodRecordMapper.find(userId, appId, amount, intervalTime);
	}

	//@DataSource(DataSourceEnum.MASTER)
	public void save(AutoRenewGracePeriodRecord autoRenewGracePeriodRecord) {
	    if (autoRenewGracePeriodRecord.getId() != null) {
            autoRenewGracePeriodRecordMapper.updateByPrimaryKeySelective(autoRenewGracePeriodRecord);
        } else {
            autoRenewGracePeriodRecordMapper.insertSelective(autoRenewGracePeriodRecord);
        }
	}
}
