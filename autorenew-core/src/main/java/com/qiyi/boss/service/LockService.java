package com.qiyi.boss.service;

/**
 * 简单数据库命名锁管理器
 * <AUTHOR> lishunlong
 * Date: 11-2-8
 * Time: 下午3:43
 * To change this template use File | Settings | File Templates.
 */
public interface LockService {

    long LOCK_THREAD_SLEEP_SECOND = 5;

    long LOCK_TIMEOUT_SECOND = 60;

    /**
     * 锁定:
     *      锁存在且未过期，返回false
     *      锁存在且已过期，则删除过期锁，返回false
     *      锁不存在, 创建锁, 返回true
     *
     * @param lockName 锁
     * @return 锁定成功true  锁定失败false
     */
    boolean lock(String lockName);

    /**
     * 加锁-指定超时时间
     */
    boolean lock(String lockName, long timeout);

    /**
     * 解锁
     *      锁存在，删除锁，返回true
     *      锁不存在，返回true
     *      异常返回false
     * @param lockName 锁
     * @return 解锁成功true  解锁失败false
     */
    boolean unlock(String lockName);
}
