package com.qiyi.boss.service.impl;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ListMultimap;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.dao.UserAgreementRenewLogMapper;
import com.qiyi.vip.trade.autorenew.dao.UserAgreementSetLogMapper;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * Created at: 2021-06-29
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserAgreementLogManager {

    @Resource
    private AgreementNoInfoManager agreementNoInfoManager;
    @Resource
    UserAgreementSetLogMapper setLogMapper;
    @Resource
    UserAgreementRenewLogMapper renewLogMapper;

    @Transactional(rollbackFor = Exception.class)
    public boolean saveRenewLog(DutRenewLog dutRenewLog) {
        dutRenewLog.setUpdateTime(DateHelper.getDateTime());
        if (dutRenewLog.getId() == null) {
            dutRenewLog.setCreateTime(DateHelper.getDateTime());
            renewLogMapper.insert(dutRenewLog);
        } else {
            renewLogMapper.update(dutRenewLog);
        }
        return true;
    }

    public DutRenewLog getRenewLogByOrderCode(Long userId, String orderCode) {
        return renewLogMapper.selectByOrderCode(userId, orderCode);
    }

    /**
     * 查询协议模版维度下用户代扣成功的记录数
     * @param userId
     * @param templateCode
     */
    public int successDutLogCount(Long userId, String templateCode, Timestamp startTime) {
        List<Integer> agreementNosUnderTmp = agreementNoInfoManager.getAgreementNoByTemplateCode(templateCode);
        if (CollectionUtils.isEmpty(agreementNosUnderTmp)) {
            return 0;
        }
        return renewLogMapper.calcRenewLogCount(userId, agreementNosUnderTmp, OperateTypeEnum.DUT.getValue(), DutRenewLog.PAY_SUCCESS, startTime);
    }

    //@DataSource(DataSourceEnum.MASTER)
    public DutRenewSetLog getSetLogBySignKeyFromMaster(Long uid, String signKey, OperateTypeEnum operateType) {
        Integer operateTypeValue = operateType != null ? operateType.getValue() : null;
        return setLogMapper.selectBySignKeyAndOptType(uid, signKey, operateTypeValue);
    }

    public DutRenewSetLog getSetLogBySignKey(Long uid, String signKey, OperateTypeEnum operateType) {
        Integer operateTypeValue = operateType != null ? operateType.getValue() : null;
        return setLogMapper.selectBySignKeyAndOptType(uid, signKey, operateTypeValue);
    }

    //@DataSource(DataSourceEnum.MASTER)
    public boolean addSetLog(DutRenewSetLog dutRenewSetLog) {
        setLogMapper.insert(dutRenewSetLog);
        return true;
    }

    //@DataSource(DataSourceEnum.MASTER)
    public Map<Integer, DutRenewLog> listRenewLogBySignKeyFromMaster(Long uid, String signKey) {
        List<DutRenewLog> renewLogList = renewLogMapper.listRecordBySignKey(uid, signKey, OperateTypeEnum.DUT.getValue());
        ListMultimap<Integer, DutRenewLog> dutRenewLogListMultimap = ArrayListMultimap.create();
        Map<Integer, DutRenewLog> dutRenewLogMap = Maps.newHashMap();
        // 按照代扣期数进行归类
        for (DutRenewLog dutRenewLog : renewLogList) {
            Integer periodNo = dutRenewLog.getSerialRenewCount();
            dutRenewLogListMultimap.put(periodNo, dutRenewLog);
        }
        // 保留最新一期成功的
        for (Integer period : dutRenewLogListMultimap.keySet()) {
            List<DutRenewLog> periodRenewLogList = dutRenewLogListMultimap.get(period);
            periodRenewLogList = periodRenewLogList.stream()
                .sorted(Comparator.comparing(DutRenewLog::getCreateTime).reversed())
                .collect(Collectors.toList());
            Optional<DutRenewLog> dutRenewLogOptional = periodRenewLogList.stream().filter(DutRenewLog::isPaySuccess).findFirst();
            if (dutRenewLogOptional.isPresent()) {
                DutRenewLog successLog = dutRenewLogOptional.get();
                dutRenewLogMap.put(period, successLog);
            } else {
                dutRenewLogMap.put(period, periodRenewLogList.get(0));
            }
        }
        return dutRenewLogMap;
    }

    /**
     * 根据 signKey 查找结算 RenewLog
     */
    //@DataSource(DataSourceEnum.MASTER)
    public DutRenewLog getSettleRenewLogBySignKeyFromMaster(Long uid, String signKey) {
        List<DutRenewLog> renewLogList = renewLogMapper.listRecordBySignKey(uid, signKey, OperateTypeEnum.SETTLE.getValue());
        if (CollectionUtils.isEmpty(renewLogList)) {
            return null;
        }
        renewLogList = renewLogList.stream().sorted(Comparator.comparing(DutRenewLog::getCreateTime).reversed()).collect(Collectors.toList());
        Optional<DutRenewLog> successSettleLogOptional = renewLogList.stream().filter(DutRenewLog::isPaySuccess).findFirst();
        if (successSettleLogOptional.isPresent()) {
            return successSettleLogOptional.get();
        }
        return renewLogList.get(0);
    }

    /**
     * 针对微信支付分，是否有待完结订单
     */
    //@DataSource(DataSourceEnum.MASTER)
    public boolean hasUnCompleteOrder(Long userId, String signKey) {
        DutRenewLog latestRenewLog = renewLogMapper.getLatestRecordBySignKey(userId, signKey);
        if (latestRenewLog == null) {
            return false;
        }
        OperateTypeEnum operateType = OperateTypeEnum.parseValue(latestRenewLog.getOperateType());
        if (operateType == OperateTypeEnum.DUT) {
            return latestRenewLog.isPaySuccess() || latestRenewLog.payConfirm();
        }
        if (operateType == OperateTypeEnum.COMPLETE_ORDER) {
            return latestRenewLog.payFailure() || latestRenewLog.payConfirm();
        }
        return false;
    }

    /**
     * 微信结单使用，如果用户可以执行结单，返回最近一次的代扣记录；
     */
    //@DataSource(DataSourceEnum.MASTER)
    public Optional<DutRenewLog> getLatestDutRecordForComplete(Long userId, String signKey) {
        List<DutRenewLog> renewLogList = renewLogMapper.listRecordBySignKey(userId, signKey, null);
        if (CollectionUtils.isEmpty(renewLogList)) {
            log.info("getLatestDutRecordForComplete-user renew log is empty!userId:{},signKey:{}",userId,signKey);
            return Optional.empty();
        }
        renewLogList = renewLogList.stream()
            .sorted(Comparator.comparing(DutRenewLog::getCreateTime).reversed())
            .collect(Collectors.toList());
        DutRenewLog latestRenewLog = renewLogList.get(0);
        // 如果最近的一次renewLong 为结单，且状态为异步待确认；返回 Optional.empty()
        if ((OperateTypeEnum.COMPLETE_ORDER.getValue() == latestRenewLog.getOperateType()) && latestRenewLog.payConfirm()) {
            log.info("getLatestDutRecordForComplete-user complete renew log is confirm!userId:{},signKey:{}",userId,signKey);
            return Optional.empty();
        }
        List<DutRenewLog> successDutRenewLogList = renewLogList.stream()
            .filter(DutRenewLog::isPaySuccess)
            .sorted(Comparator.comparing(DutRenewLog::getCreateTime).reversed())
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(successDutRenewLogList)) {
            log.warn("getLatestDutRecordForComplete-user success renew log is empty!userId:{},signKey:{}", userId, signKey);
            return Optional.empty();
        }
        DutRenewLog latestDutRecord = successDutRenewLogList.get(0);
        OperateTypeEnum operateType = OperateTypeEnum.parseValue(latestDutRecord.getOperateType());
        if (!OperateTypeEnum.DUT.equals(operateType)) {
            log.warn("getLatestDutRecordForComplete-user latest success renew log is not dut!userId:{},signKey:{}", userId, signKey);
            return Optional.empty();
        }
        return Optional.ofNullable(latestDutRecord);
    }

    //@DataSource(DataSourceEnum.MASTER)
    public List<DutRenewLog> listRenewLogBySignKey(Long userId, String signKey) {
        return renewLogMapper.listRecordBySignKey(userId, signKey, null);
    }

    //@DataSource(DataSourceEnum.MASTER)
    public DutRenewLog getLatestRecordBySignKeyAndOperateType(Long userId, String signKey, OperateTypeEnum operateTypeEnum) {
        return renewLogMapper.getLatestRecordBySignKeyAndOperateType(userId, signKey, operateTypeEnum.getValue());
    }

    public DutRenewSetLog getRecentlySetLog(Long userId, Integer agreementNo, OperateTypeEnum operateType) {
        Integer operateTypeValue = operateType != null ? operateType.getValue() : null;
        List<DutRenewSetLog> setLogs = setLogMapper.getByAgreementNo(userId, agreementNo, operateTypeValue);
        if (CollectionUtils.isEmpty(setLogs)) {
            return null;
        }
        setLogs.sort(Comparator.comparing(DutRenewSetLog::getOperateTime).reversed());
        return setLogs.get(0);
    }

    public DutRenewSetLog getRecentlySetLog(Long userId, Integer dutType, Integer operator) {
        List<DutRenewSetLog> setLogs = setLogMapper.selectByDutType(userId, dutType, operator);
        if (CollectionUtils.isEmpty(setLogs)) {
            return null;
        }
        setLogs.sort(Comparator.comparing(DutRenewSetLog::getUpdateTime).reversed());
        return setLogs.get(0);
    }

    public List<DutRenewSetLog> getSetLogByAgreementTypeAndVipTypes(Long userId, AgreementTypeEnum agreementType, List<Long> vipTypes) {
        List<DutRenewSetLog> setLogs = setLogMapper.getByAgreementTypeAndVipTypes(userId, agreementType.getValue(), vipTypes);
        return setLogs.stream()
            .sorted(Comparator.comparing(DutRenewSetLog::getOperateTime)
            .thenComparing(DutRenewSetLog::getOperator))
            .collect(Collectors.toList());
    }

    public List<DutRenewLog> getRenewLogByAgreementTypeAndVipTypes(Long userId, AgreementTypeEnum agreementType, Long vipType, Timestamp startTime) {
        return renewLogMapper.getRecordByAgreementTypeAndVipTypes(userId, agreementType.getValue(), vipType, startTime);
    }

    public DutRenewLog getLatestRenewLogByAgreementTypeAndVipTypes(Long userId, AgreementTypeEnum agreementType, Long vipType) {
        return renewLogMapper.getLatestRecordByAgreementTypeAndVipTypes(userId, agreementType.getValue(), vipType);
    }

    public Integer getSuccessDutLogCount(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate) {
        if (agreementTemplate.needRestrictPeriod()) {
            return dutUserNew.getTemplateRenewCount();
        }
        Timestamp recentlyOperateTime = getRecentlyOperateTime(dutUserNew, agreementNoInfo);
        if (recentlyOperateTime == null) {
            return 0;
        }
        return successDutLogCount(dutUserNew.getUserId(), agreementNoInfo.getTemplateCode(), recentlyOperateTime);
    }

    public Timestamp getRecentlyOperateTime(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo) {
        Long userId = dutUserNew.getUserId();
        DutRenewSetLog recentlySetLog = getRecentlySetLog(userId, agreementNoInfo.getId(), OperateTypeEnum.OPEN);
        if (recentlySetLog != null) {
            return recentlySetLog.getOperateTime();
        }
        log.error("getRecentlyOperateTime not find setLog, userId:{}, agreementNo:{}", userId, agreementNoInfo.getId());
        return dutUserNew.getOperateTime();
    }
}
