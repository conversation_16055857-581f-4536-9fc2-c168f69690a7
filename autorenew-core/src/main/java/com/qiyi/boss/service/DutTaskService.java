package com.qiyi.boss.service;

import com.qiyi.boss.autorenew.dto.DutTaskDto;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

import java.sql.Timestamp;

public interface DutTaskService {
    /**
     *
     * @param dutUserNew
     * @param retryTime
     * @param taskType
     * @return
     */
    public DutTaskDto makeDutTaskWhenNoRenewLog(DutUserNew dutUserNew, Timestamp retryTime, String taskType);

    /**
     *
     * @param dutUserNew
     * @param renewLog
     * @param retryTime
     * @param taskType
     * @return
     */
    public DutTaskDto makeDutTaskWhenNoQiyueOrder(DutUserNew dutUserNew, DutRenewLog renewLog, Timestamp retryTime,
                                                  String taskType);
}
