package com.qiyi.boss.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.StringJoiner;
import com.qiyi.boss.Constants;
import com.qiyi.boss.constants.ParamKeyConstants;
import com.qiyi.boss.dto.AvailableDutCouponInfo;
import com.qiyi.boss.dto.ConponGoodsInfo;
import com.qiyi.boss.dto.CreateOrderDto;
import com.qiyi.boss.dto.DutParamsDto;
import com.qiyi.boss.dto.FeeDTO;
import com.qiyi.boss.dto.GoodsDTO;
import com.qiyi.boss.dto.OrderCreationRequest;
import com.qiyi.boss.dto.OrderPrepareDto;
import com.qiyi.boss.dto.PayCenterDutReq;
import com.qiyi.boss.dto.PayCenterDutResult;
import com.qiyi.boss.dto.PaymentDTO;
import com.qiyi.boss.dto.StatisticsDTO;
import com.qiyi.boss.dto.UserDTO;
import com.qiyi.boss.dto.WeChatPayScoreExtendParam;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.IsSuccessEnum;
import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.enums.ResultCodeEnum;
import com.qiyi.boss.enums.TypeOfOrderEnum;
import com.qiyi.boss.exception.OrderSystemException;
import com.qiyi.boss.model.DivideGoodsPrice;
import com.qiyi.boss.model.Product;
import com.qiyi.boss.outerinvoke.CommodityProxy;
import com.qiyi.boss.outerinvoke.OrderCoreProxy;
import com.qiyi.boss.outerinvoke.PayCenterApi;
import com.qiyi.boss.outerinvoke.param.OrderFulfillRequest;
import com.qiyi.boss.outerinvoke.result.CommodityInfo;
import com.qiyi.boss.param.QiyiParam;
import com.qiyi.boss.service.OrderService;
import com.qiyi.boss.utils.AppConfig;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.EagleMeterReporter;
import com.qiyi.boss.utils.IPUtil;
import com.qiyi.boss.utils.JacksonUtils;
import com.qiyi.boss.utils.PayUtils;
import com.qiyi.boss.utils.dut.OrderUtil;
import com.qiyi.vip.commons.constant.OrderStatusConstants;
import com.qiyi.vip.commons.enums.ChargeTypeEnum;
import com.qiyi.vip.commons.enums.OrderStatusEnum;
import com.qiyi.vip.commons.enums.ProductPeriodUnitEnum;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.constants.FeeUnitConstants;
import com.qiyi.vip.trade.autorenew.constants.ParamConstants;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import com.qiyi.vip.trade.qiyue.domain.Business;
import com.qiyi.vip.trade.qiyue.domain.Gateway;
import com.qiyi.vip.trade.qiyue.domain.QiYuePlatform;
import com.qiyi.vip.trade.qiyue.domain.VipType;
import com.iqiyi.vip.language.distribute.CorpusTemplate;
import com.iqiyi.vip.order.dal.OrderRepository;
import com.iqiyi.vip.order.dal.model.DiscountDetail;
import com.iqiyi.vip.order.dal.model.Order;
import com.iqiyi.vip.order.dal.model.OrderReferDto;
import com.iqiyi.vip.order.dal.model.VipGiftInfo;
import com.iqiyi.vip.uuid.center.starter.service.GenerateNoUtils;
import static com.qiyi.boss.Constants.AGREEMENT_TYPE;
import static com.qiyi.boss.Constants.DUT_TASK_TYPE;
import static com.qiyi.boss.Constants.MERCHANT_NO;
import static com.qiyi.boss.Constants.SIGN_ORDER_CODE;
import static com.qiyi.boss.Constants.TIME_ZONE;
import static com.qiyi.boss.Constants.TRADE_TYPE_KEY;
import static com.qiyi.boss.Constants.TRADE_TYPE_VALUE;
import static com.qiyi.boss.Constants.USER_AUTO_RENEW;
import static com.qiyi.boss.constants.AgreementConstants.EXCLUDE_AGREEMENT_TYPES;
import static com.qiyi.boss.utils.I18nConstants.I18N_KEY_DAY;
import static com.qiyi.vip.trade.autorenew.constants.ConfigConstants.BATCH_NO;
import static com.qiyi.vip.trade.autorenew.constants.ConfigConstants.COUPON_BATCH;
import static com.qiyi.vip.trade.autorenew.constants.ConfigConstants.COUPON_CODE;
import static com.qiyi.vip.trade.autorenew.constants.ConfigConstants.DUT_DISCOUNT_TYPE;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang Date: 2020-10-20 Time: 22:27
 */
@Component(value = "orderService")
@Slf4j
public class OrderManager implements OrderService {

	private static final Logger LOGGER = LoggerFactory.getLogger(OrderManager.class);
	private static final String AUTO_RENEW_DESCRIPTION = "自动续费";
    private static final String DUT_ORDER_NAME_SUFFIX_ZHIMA_GO = "-芝麻信用任务";
    private static final String DUT_ORDER_NAME_SUFFIX_WECHAT_PAY_SCORE_CREATE = "-微信支付分创单";
    public static final String BUSINESS_PROPERTY = "businessProperty";
	/**
	 * 续费时长单位多语言key.
	 */
	private static final String I18N_KEY_MONTH = "VIP_1557974606839_685";

	//沙盒订单
	private static final String SAND_BOX = "1";

	@Resource
	private BusinessManager businessManager;
	@Resource
	private GatewayManager gatewayManager;
	@Resource
	private VipTypeManager vipTypeManager;
	@Resource
	private OrderRepository orderRepository;
	@Resource
	private CorpusTemplate corpusTemplate;
	@Resource
	private PayCenterApi payCenterApi;
	@Resource
	private QiYuePlatformManager qiYuePlatformManager;

    @Resource
    private QiyuePaymentTypeManager qiyuePaymentTypeManager;
    @Resource
    private OrderCoreProxy orderCoreProxy;
    @Resource
    private PaymentDutTypeManager paymentDutTypeManager;
    @Resource
    private CommodityProxy commodityProxy;



	@Override
	public Order createOrder(CreateOrderDto createOrderDto) {
		Long userId = createOrderDto.getUserId();
		Integer amount = createOrderDto.getAmount();
        CommodityInfo commodityInfo = createOrderDto.getCommodityInfo();
        Product qiyueProduct = createOrderDto.getQiyueProduct();
        DutParamsDto dutParamsDto = createOrderDto.getDutParamsDto();
        AgreementTemplate agreementTemplate = createOrderDto.getAgreementTemplate();
        OperateTypeEnum operateType = createOrderDto.getOperateType();
        Map<String, String> params = createOrderDto.getParams();
		DutUserNew dutUserNew = createOrderDto.getDutUserNew();
        String skuId = commodityInfo != null ? commodityInfo.getSkuId() : null;
        Order order = new Order();
		String orderCode = GenerateNoUtils.getUUno();
		log.info("generate autorenew dut uid:{}, skuId:{}, orderCode:{}", userId, skuId, orderCode);
		order.setOrderCode(orderCode);
		order.setUserId(userId);
		order.setRenewType(dutUserNew.getAgreementNo() != null ? dutUserNew.getAgreementNo() : dutUserNew.getType());
		Business business = businessManager.getBusinessByCode(dutParamsDto.getServiceCode());
		if (business != null) {
			order.setServiceId(business.getId());
		}
		order.setCreateTime(new Timestamp(System.currentTimeMillis()));
		order.setTradeNo(orderCode);
		order.setStatus(OrderStatusEnum.ORDER_STATUS_PROCESSING.getStatus());
        Integer fee = dutParamsDto.getFee();
        AvailableDutCouponInfo availableDutCouponInfo = dutParamsDto.getAvailableDutCouponInfo();
        if (fee != null) {
			order.setFee(fee);
            int realFee = dutParamsDto.getRealFee() == null ? fee : dutParamsDto.getRealFee();
            if (availableDutCouponInfo != null) {
                int couponInfoValue = availableDutCouponInfo.getValue();
                order.setCouponFee(couponInfoValue);
                order.setCouponSettlementFee(couponInfoValue);
                // 区分站内站外券的扣减逻辑
                if (Boolean.TRUE.equals(availableDutCouponInfo.getOffSite())) {
                    // 站外券：扣减逻辑在渠道侧，券的价格只记录，不扣减
                    LOGGER.info("站外券不扣减realFee. couponInfoValue:{}, realFee:{}, code:{}", couponInfoValue, realFee, availableDutCouponInfo.getCouponCode());
                } else {
                    // 站内券：原逻辑，订单上的价格直接-券的价格
                    //如果代金券大于等于实际代扣金额，也至少要扣1分钱
                    realFee = realFee > couponInfoValue ? realFee - couponInfoValue : 1;
                    LOGGER.info("站内券扣减realFee. orginal realFee:{}, now realFee:{}, code:{}", dutParamsDto.getRealFee(), realFee, availableDutCouponInfo.getCouponCode());
                }
            }
            order.setRealFee(realFee);
			order.setSettlementFee(dutParamsDto.getSettlementFee() == null ? fee : dutParamsDto.getSettlementFee());

		} else {
            LOGGER.error("Renew price is null, query from price engine. Params: {}.", params);
            Integer dutPrice = null;
            if (commodityInfo != null) {
                dutPrice = commodityInfo.getPrice();
            } else {
                dutPrice = paymentDutTypeManager.getRenewPrice(dutUserNew.getVipType(), dutUserNew.getAmount(), dutUserNew.getActCode(), dutUserNew.getType(), userId);
            }
			order.setFee(dutPrice);
			order.setSettlementFee(dutPrice);
			Integer realFee = dutPrice;
            if (availableDutCouponInfo != null) {
                int couponInfoValue = availableDutCouponInfo.getValue();
                order.setCouponFee(couponInfoValue);
                order.setCouponSettlementFee(couponInfoValue);
                // 区分站内站外券的扣减逻辑
                if (Boolean.TRUE.equals(availableDutCouponInfo.getOffSite())) {
                    // 站外券：扣减逻辑在渠道侧，券的价格只记录，不扣减
                    LOGGER.info("站外券不扣减realFee. couponInfoValue:{}, realFee:{}, code:{}", couponInfoValue, realFee, availableDutCouponInfo.getCouponCode());
                } else {
                    // 站内券：原逻辑，订单上的价格直接-券的价格
                    realFee = realFee > couponInfoValue ? realFee - couponInfoValue : 1;
                    LOGGER.info("站内券扣减realFee. orginal realFee:{}, now realFee:{}, code:{}", dutParamsDto.getRealFee(), realFee, availableDutCouponInfo.getCouponCode());
                }
            }
			order.setRealFee(realFee);
		}
		order.setPayType(dutParamsDto.getPayType());
		Gateway gateway = gatewayManager.getGatewayByCode(dutParamsDto.getFc());
		if (gateway == null) {
			gateway = gatewayManager.getGatewayByCode(Constants.GATEWAY_DEFAULT_CODE);
		}
		order.setGateway(gateway != null ? gateway.getId() : 1L);
		order.setPushChannel(gateway != null ? Long.valueOf(gateway.getChannelId()) : 0L);
		order.setChannel(0L);
		order.setBusinessValues(dutParamsDto.getAid() != null ? dutParamsDto.getAid().toString() : "0");
		order.setFv(dutParamsDto.getFv());
		if (StringUtils.isNotBlank(dutParamsDto.getPlatform())) {
			QiYuePlatform qiYuePlatform = qiYuePlatformManager.getPlatformByCode(dutParamsDto.getPlatform());
			order.setPlatform(qiYuePlatform != null ? qiYuePlatform.getId() : Constants.PLATFORM_PC_ID);
		} else {
			order.setPlatform(Constants.PLATFORM_PC_ID);
		}
		order.setUserIp(IPUtil.getLocalIp());
		order.setAutoRenew(Integer.valueOf(Constants.QIYUE_ORDER_AUTORENEW_DUT));
		order.setFrVersion(null);
		// 对外合作代扣保存合作方信息
		if (StringUtils.isNotBlank(dutParamsDto.getPartnerNo())) {
			order.setPartner(dutParamsDto.getPartnerNo());
		}
        AgreementTypeEnum agreementType = AgreementTypeEnum.valueOf(agreementTemplate != null ? agreementTemplate.getType() : null);
		String orderName = createOrderName(params, commodityInfo, qiyueProduct, amount, dutParamsDto.getVipType(), agreementType, operateType);
        order.setName(orderName);
		order.setAmount(amount);
        order.setCurrencyUnit(commodityInfo != null ? commodityInfo.getCurrencyUnit() : qiyueProduct.getCurrencyUnit());
        order.setProductType(commodityInfo != null ? commodityInfo.getType() : qiyueProduct.getType());
        order.setProductId(commodityInfo != null ? commodityInfo.getProductId() : qiyueProduct.getId());
        order.setSkuId(skuId);
        order.setSkuAmount(commodityInfo != null ? createOrderDto.getSkuAmount(): null);
        order.setProductFee(commodityInfo != null ? commodityInfo.getPrice() : qiyueProduct.getPrice());
        order.setType(getType(agreementType, operateType));
        setOrderExtraParams(order, dutParamsDto, agreementType);
		//自动续费时，如果自动续费系统需要添加赠送信息，则在order的refer信息中添加此部分内容，但是注意，此部分信息不需要生产gift订单
		addDutGiftInfo(order, params);
        boolean needCreateOrder = createOrderDto.isNeedCreateOrder();
        if (!needCreateOrder) {
            log.info("no needCreateOrder, uid:{}, skuId:{}，orderCode:{}", userId, order.getSkuId(), orderCode);
            return order;
        }
        if (CloudConfigUtil.routeToOrderCode(userId)) {
            boolean saved = orderCoreProxy.saveOrder(order);
            if (!saved) {
                throw OrderSystemException.newSaveOrderException();
            }
        } else {
            orderRepository.save(order);
        }
		return order;
	}

    @Override
    public OrderCreationRequest createUnpaidOrder(CreateOrderDto createOrderDto) {
        DutParamsDto dutParamsDto = createOrderDto.getDutParamsDto();
        DutUserNew dutUserNew = createOrderDto.getDutUserNew();
        OrderCreationRequest request = new OrderCreationRequest();
        String orderCode = GenerateNoUtils.getUUno();
        Long userId = dutUserNew.getUserId();
        String skuId = createOrderDto.getCommodityInfo().getSkuId();
        log.info("generate unpaid dut, uid:{}, skuId:{}, orderCode:{}", userId, skuId, orderCode);
        request.setOrderCode(orderCode);
        request.setGoodsDTO(buildGoodsDTO(createOrderDto));
        request.setPaymentDTO(buildPaymentDTO(dutParamsDto, dutUserNew));
        request.setRefer(buildDutOrderRefer(dutParamsDto, dutUserNew.getAgreementType()));
        request.setReturnUrl(null);
        request.setStatisticsDTO(buildStatisticsDTO(dutParamsDto));
        request.setTradeNo(orderCode);
        request.setUserDTO(buildUserDTO(dutUserNew));
        request.setOrderName(generateOrderName(createOrderDto));
        boolean needCreateOrder = createOrderDto.isNeedCreateOrder();
        if (!needCreateOrder) {
            log.info("no needCreateOrder by V2, uid:{}, skuId:{}, orderCode:{}", userId, skuId, orderCode);
            return request;
        }
        boolean saved = orderCoreProxy.createUnpaidOrder(request);
        if (!saved) {
            throw OrderSystemException.newSaveOrderException();
        }
        return request;
    }

    private UserDTO buildUserDTO(DutUserNew dutUserNew) {
        UserDTO userDTO = new UserDTO();
        userDTO.setUserId(dutUserNew.getUserId());
        userDTO.setUserIp(IPUtil.getLocalIp());
        return userDTO;
    }


    private PaymentDTO buildPaymentDTO(DutParamsDto dutParamsDto, DutUserNew dutUserNew) {
        PaymentDTO dto = new PaymentDTO();
        dto.setPayType(dutParamsDto.getPayType());
        dto.setRenewType(dutUserNew.getAgreementNo() != null ? dutUserNew.getAgreementNo() : dutUserNew.getType());
        return dto;
    }

    private StatisticsDTO buildStatisticsDTO(DutParamsDto dutParamsDto) {
        StatisticsDTO dto = new StatisticsDTO();
        dto.setChannel(0L);
        dto.setFrVersion(null);
        dto.setFv(dutParamsDto.getFv());
        Gateway gateway = gatewayManager.getGatewayByCode(dutParamsDto.getFc());
        if (gateway == null) {
            gateway = gatewayManager.getGatewayByCode(Constants.GATEWAY_DEFAULT_CODE);
        }
        dto.setGateway(gateway != null ? gateway.getId() : 1L);
        dto.setPushChannel(gateway != null ? Long.valueOf(gateway.getChannelId()) : 0L);
        // 对外合作代扣保存合作方信息
        if (StringUtils.isNotBlank(dutParamsDto.getPartnerNo())) {
            dto.setPartner(dutParamsDto.getPartnerNo());
        }

        if (StringUtils.isNotBlank(dutParamsDto.getPlatform())) {
            QiYuePlatform qiYuePlatform = qiYuePlatformManager.getPlatformByCode(dutParamsDto.getPlatform());
            dto.setPlatform(qiYuePlatform != null ? qiYuePlatform.getId() : Constants.PLATFORM_PC_ID);
        } else {
            dto.setPlatform(Constants.PLATFORM_PC_ID);
        }
        return dto;
    }

    private GoodsDTO buildGoodsDTO(CreateOrderDto createOrderDto) {
        CommodityInfo commodityInfo = createOrderDto.getCommodityInfo();
        Integer skuAmount = createOrderDto.getSkuAmount();
        DutParamsDto dutParamsDto = createOrderDto.getDutParamsDto();
        DutUserNew dutUserNew = createOrderDto.getDutUserNew();

        GoodsDTO goodsDTO = new GoodsDTO();
        goodsDTO.setSkuId(commodityInfo != null ? commodityInfo.getSkuId() : null);
        goodsDTO.setSkuAmount(commodityInfo != null ? skuAmount : null);
        FeeDTO feeDTO = buildFeeDTO(dutParamsDto, commodityInfo, dutUserNew);
        goodsDTO.setFeeDTO(feeDTO);
        goodsDTO.setBusinessValues(dutParamsDto.getAid() != null ? dutParamsDto.getAid().toString() : "0");
        goodsDTO.setOrderName(generateOrderName(createOrderDto));
        OperateTypeEnum operateType = createOrderDto.getOperateType();
        AgreementTemplate agreementTemplate = createOrderDto.getAgreementTemplate();
        AgreementTypeEnum agreementType = AgreementTypeEnum.valueOf(agreementTemplate != null ? agreementTemplate.getType() : null);
        goodsDTO.setType(getType(agreementType, operateType));
        return goodsDTO;
    }

    private String generateOrderName(CreateOrderDto createOrderDto) {
        AgreementTemplate agreementTemplate = createOrderDto.getAgreementTemplate();
        Map<String, String> params = createOrderDto.getParams();
        CommodityInfo commodityInfo = createOrderDto.getCommodityInfo();
        Product qiyueProduct = createOrderDto.getQiyueProduct();
        Integer amount = createOrderDto.getAmount();
        DutParamsDto dutParamsDto = createOrderDto.getDutParamsDto();
        OperateTypeEnum operateType = createOrderDto.getOperateType();

        AgreementTypeEnum agreementType = AgreementTypeEnum.valueOf(createOrderDto.getAgreementType());
        return createOrderName(params, commodityInfo, qiyueProduct, amount, dutParamsDto.getVipType(), agreementType, operateType);
    }


    private Map<String, String> buildDutOrderRefer(DutParamsDto dutParamsDto, Integer agreementType) {
        HashMap<String, String> map = new HashMap<>();
        // 判断用户是否是自动续费状态
        if (agreementType == null || !EXCLUDE_AGREEMENT_TYPES.contains(agreementType)) {
            map.put(USER_AUTO_RENEW, Boolean.TRUE.toString());
        }
        putBusinessProperty(map, TRADE_TYPE_KEY, TRADE_TYPE_VALUE);
        if (agreementType != null) {
            putBusinessProperty(map, AGREEMENT_TYPE, agreementType);
        }

        //记录自动续费开通订单号，用于线下联盟识别代扣订单对应的产品并匹配返佣政策
        if (StringUtils.isNotBlank(dutParamsDto.getSignOrderCode())) {
            map.put(SIGN_ORDER_CODE, dutParamsDto.getSignOrderCode());
        }
        if (StringUtils.isNotBlank(dutParamsDto.getTaskType())) {
            map.put(DUT_TASK_TYPE, dutParamsDto.getTaskType());
        }

        if (StringUtils.isNotBlank(dutParamsDto.getTimeZone())) {
            map.put(TIME_ZONE, dutParamsDto.getTimeZone());
        }

        // 保存通用业务属性
        Map<String, Object> paramsDtoBusinessProperty = dutParamsDto.getBusinessProperty();
        if (MapUtils.isNotEmpty(paramsDtoBusinessProperty)) {
            paramsDtoBusinessProperty.forEach((k, v) -> putBusinessProperty(map, k, v));
        }

        AvailableDutCouponInfo dutCouponInfo = dutParamsDto.getAvailableDutCouponInfo();
        if (dutCouponInfo != null) {
            String batchNo = dutCouponInfo.getBatchNo();
            String couponCode = dutCouponInfo.getCouponCode();
            Integer dutCouponType = dutCouponInfo.getType();
            LOGGER.info("add coupon Info related params on order, dutCouponInfo:{}", dutCouponInfo);
            putBusinessProperty(map, BATCH_NO, batchNo);
            putBusinessProperty(map, COUPON_CODE, couponCode);
            putBusinessProperty(map, COUPON_BATCH, batchNo);
            putBusinessProperty(map, DUT_DISCOUNT_TYPE, dutCouponType);
            // 如果是站外券，记录站外标识和映射后的渠道
            if (Boolean.TRUE.equals(dutCouponInfo.getOffSite())) {
                putBusinessProperty(map, "couponOffSite", true);
            }
        }
        return map;
    }

    private void putBusinessProperty(Map<String, String> map, String key, Object value) {
        // Null-safe check to ensure the value is not null before calling toString()
        if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value.toString())) {
            map.put("businessProperty." + key, value.toString());
        }
    }


    private FeeDTO buildFeeDTO(DutParamsDto dutParamsDto, CommodityInfo commodityInfo, DutUserNew dutUserNew) {
        FeeDTO feeDTO = new FeeDTO();
        Integer fee = dutParamsDto.getFee();
        Long userId = dutUserNew.getUserId();
        AvailableDutCouponInfo availableDutCouponInfo = dutParamsDto.getAvailableDutCouponInfo();
        if (fee != null) {
            feeDTO.setFee(fee);
            int realFee = dutParamsDto.getRealFee() == null ? fee : dutParamsDto.getRealFee();
            if (availableDutCouponInfo != null) {
                int couponInfoValue = availableDutCouponInfo.getValue();
                feeDTO.setCouponFee(couponInfoValue);
                feeDTO.setCouponSettlementFee(couponInfoValue);
                
                // 区分站内和站外券的处理逻辑
                if (Boolean.TRUE.equals(availableDutCouponInfo.getOffSite())) {
                    // 站外券：扣减逻辑在渠道侧，券的价格只能记录，不能扣减
                    LOGGER.info("站外券处理订单价格不扣减. userId:{}, couponCode:{}, couponValue:{}, realFee:{}", userId, availableDutCouponInfo.getCouponCode(), couponInfoValue, realFee);
                    // realFee保持不变，不进行扣减
                } else {
                    // 站内券：原逻辑，订单上的价格直接-券的价格
                    //如果代金券大于等于实际代扣金额，也至少要扣1分钱
                    realFee = realFee > couponInfoValue ? realFee - couponInfoValue : 1;
                    LOGGER.info("站内券订单扣减券价格. userId:{}, couponCode:{}, couponValue:{}, original realFee:{}, new realFee:{}", userId, availableDutCouponInfo.getCouponCode(), couponInfoValue, dutParamsDto.getRealFee(), realFee);
                }
            }
            feeDTO.setRealFee(realFee);
            feeDTO.setSettlementFee(dutParamsDto.getSettlementFee() == null ? fee : dutParamsDto.getSettlementFee());
        } else {
            LOGGER.error("Renew price is null, query from price engine. Params: {}.", dutParamsDto);
            Integer dutPrice = null;
            if (commodityInfo != null) {
                dutPrice = commodityInfo.getPrice();
            } else {
                dutPrice = paymentDutTypeManager.getRenewPrice(dutUserNew.getVipType(), dutUserNew.getAmount(), dutUserNew.getActCode(), dutUserNew.getType(), dutUserNew.getUserId());
            }
            feeDTO.setFee(dutPrice);
            Integer realFee = dutPrice;
            if (availableDutCouponInfo != null) {
                int couponInfoValue = availableDutCouponInfo.getValue();
                feeDTO.setCouponFee(couponInfoValue);
                feeDTO.setCouponSettlementFee(couponInfoValue);
                
                // 区分站内和站外券的处理逻辑
                if (Boolean.TRUE.equals(availableDutCouponInfo.getOffSite())) {
                    // 站外券：扣减逻辑在渠道侧，券的价格只能记录，不能扣减
                    LOGGER.info("站外券处理订单价格不扣减. userId:{}, couponCode:{}, couponValue:{}, realFee:{}", userId, availableDutCouponInfo.getCouponCode(), couponInfoValue, realFee);
                    // realFee保持不变，不进行扣减
                } else {
                    // 站内券：原逻辑，订单上的价格直接-券的价格
                    realFee = realFee > couponInfoValue ? realFee - couponInfoValue : 1;
                    LOGGER.info("站内券订单扣减券价格. userId:{}, couponCode:{}, couponValue:{}, original realFee:{}, new realFee:{}", userId, availableDutCouponInfo.getCouponCode(), couponInfoValue, dutParamsDto.getRealFee(), realFee);
                }
            }
            feeDTO.setRealFee(realFee);
            feeDTO.setSettlementFee(dutParamsDto.getSettlementFee() == null ? fee : dutParamsDto.getSettlementFee());
        }
        return feeDTO;
    }


    private int getType(AgreementTypeEnum agreementType, OperateTypeEnum operateType) {
        if (agreementType == AgreementTypeEnum.ZHIMA_GO) {
            return TypeOfOrderEnum.ZHIMA_GO_DUT.getCode();
        }
        if (agreementType == AgreementTypeEnum.WECHAT_PAY_SCORE) {
            if (operateType == OperateTypeEnum.COMPLETE_ORDER) {
                return TypeOfOrderEnum.WECHAT_PAY_SCORE_COMPLETE.getCode();
            } else {
                return TypeOfOrderEnum.WECHAT_PAY_SCORE_CREATE.getCode();
            }
        }
        return TypeOfOrderEnum.NORMAL.getCode();
    }

	@Override
	public PayCenterDutReq prepare(OrderPrepareDto orderPrepareDto) {
        CommodityInfo commodityInfo = orderPrepareDto.getCommodityInfo();
        Product qiyueProduct = orderPrepareDto.getQiyueProduct();
        Long productVipType = commodityInfo != null ? commodityInfo.getSubType() : qiyueProduct.getSubtype();
        String pid = commodityInfo != null ? commodityInfo.getSkuId() : qiyueProduct.getCode();
		Map<String, String> configs = Maps.newHashMap();
		configs.put("return_request", "json");
		configs.put("service", QiyiParam.SERVICE);
		Business business = businessManager.getBusinessByCode(orderPrepareDto.getDutParamsDto().getServiceCode());
        String businessPartner = OrderUtil.getPartner(business, productVipType);
        String partnerId = orderPrepareDto.getDutParamsDto().getPartnerId();
        String partner = StringUtils.isNotBlank(partnerId) ? partnerId : businessPartner;
        if (CloudConfigUtil.enableServiceProvider(partner)) {
            configs.put("service_provider", PayUtils.getPayCenterServiceProviderCode());
        }
        Order order = orderPrepareDto.getOrder();
        OrderCreationRequest creationRequest = orderPrepareDto.getOrderCreationRequest();
        boolean commonDut = order != null;
        String orderCode = commonDut ? order.getOrderCode() : creationRequest.getOrderCode();
        Integer realFee = commonDut ? order.getRealFee() : creationRequest.getGoodsDTO().getFeeDTO().getRealFee();
        String userIp = commonDut ? order.getUserIp() : creationRequest.getUserDTO().getUserIp();
        String orderCurrencyUnit = commonDut ? order.getCurrencyUnit() : null;
        String orderName = commonDut ? order.getName() : creationRequest.getOrderName();

        configs.put("partner", partner);
		configs.put("version", QiyiParam.VERSION);
		configs.put("notify_url", getNotifyUrl(businessPartner));
		configs.put("charset", QiyiParam.CHARSET_UTF8);
		configs.put("uid", String.valueOf(orderPrepareDto.getUserId()));
		configs.put("user_account", String.valueOf(orderPrepareDto.getUserId()));

        configs.put("partner_order_no", orderCode);

        configs.put("fee", String.valueOf(realFee));
		// 设置币种相关信息

        if (commonDut && StringUtils.isNotBlank(orderCurrencyUnit)) {
			configs.put("currency", orderCurrencyUnit);
		}
		if (commonDut && FeeUnitConstants.getFeeUnit(orderCurrencyUnit) != null) {
			configs.put("fee_unit", FeeUnitConstants.getFeeUnit(orderCurrencyUnit));
		}

        configs.put("ip", userIp);
		configs.put("subject", orderName);
		configs.put("description", getDescription(businessPartner,orderName));
		configs.put("show_url", QiyiParam.iqiyiCom);
		configs.put("pid", pid);
		configs.put("expire_time", "48h");
		configs.put("order_time", String.valueOf(System.currentTimeMillis()));
        configs.put("pay_type", getPayCenterCodeByPayType(orderPrepareDto.getDutParamsDto().getPayType()));
		configs.put("extra_common_param", getExtraCommonParamStr(orderPrepareDto));
		setExtendsParams(orderPrepareDto, order, configs);

		// 自动续费时手机号由支付中心从账户中心获取,并且网关地址改变，gateway 不在签名计算之内
		//兼容现有的逻辑，后面可以去掉 PAY_TYPE_SMS_YDYD 的判断
		String gateway = null;
		String payType = String.valueOf(orderPrepareDto.getDutParamsDto().getPayType());
		if (payType.equals(Constants.PAY_TYPE_TELEPHONE_FEE + "")
				|| payType.equals(Constants.PAY_TYPE_SMS_LDYS_DUT + "")
				|| String.valueOf(Constants.PAY_TYPE_TELECOM_TELEPHONE_FEE).equals(payType)
				|| String.valueOf(Constants.PAY_TYPE_SMS_YDYD).equals(payType)
				|| String.valueOf(Constants.PAY_TYPE_CHINAUNICOMDUT).equals(payType)
				|| String.valueOf(Constants.PAY_TYPE_ARCSOFTDUT).equals(payType)
				|| String.valueOf(Constants.PAY_TYPE_TIANYIDUT).equals(payType)) {
			if (!configs.containsKey("payParamMobile")) {
				gateway = AppConfig.getProperty("pay.center.dut.mobile");
			}
		}

		return new PayCenterDutReq(configs, gateway, orderPrepareDto.getDutParamsDto().getPayChannel());
	}

    public String getPayCenterCodeByPayType(Integer payType) {
        if (payType == null) {
            return null;
        }
        String payCenterCode = qiyuePaymentTypeManager.getPayCenterCodeByIdFromPayInfo(payType);
        return StringUtils.isBlank(payCenterCode) ? null : CloudConfigUtil.transferPayCenterCode(payCenterCode);
    }

    /**
     * ExtraCommonParam中各属性值需要进行UrlEncode，值为普通字符可不用encode
     */
    private String getExtraCommonParamStr(OrderPrepareDto orderPrepareDto) {
        DutParamsDto dutParamsDto = orderPrepareDto.getDutParamsDto();
        Map<String, String> extraCommonParamMap = Maps.newHashMap();
        extraCommonParamMap.put("payChannel", dutParamsDto.getPayChannel() != null ? String.valueOf(dutParamsDto.getPayChannel()) : "");
        extraCommonParamMap.put("dutType", dutParamsDto.getDutType() != null ? String.valueOf(dutParamsDto.getDutType()) : "");
        extraCommonParamMap.put("vipType", dutParamsDto.getVipType() != null ? String.valueOf(dutParamsDto.getVipType()) : "");
        if (dutParamsDto.getAgreementNo() != null) {
            extraCommonParamMap.put(ParamConstants.AGREEMENT_NO, String.valueOf(dutParamsDto.getAgreementNo()));
        }
        // append coupon info
        AvailableDutCouponInfo couponInfo = dutParamsDto.getAvailableDutCouponInfo();
        if (couponInfo != null) {
            extraCommonParamMap.put(ParamKeyConstants.DUT_DISCOUNT_ID, couponInfo.getCouponCode());
            extraCommonParamMap.put(ParamKeyConstants.DUT_DISCOUNT_VALUE, String.valueOf(couponInfo.getValue()));
        }
        return Joiner.on("&").withKeyValueSeparator("=").join(extraCommonParamMap);
	}



	String getNotifyUrl(String partner) {
		if (QiyiParam.TV_PARTNER_LIST.contains(partner)) {
			return QiyiParam.YINHE_DUT_NOTIFY_URL;
		}

		return QiyiParam.NOTIFY_URL;
	}

	String getDescription(String partner, String name) {
		if (QiyiParam.TV_PARTNER_LIST.contains(partner)) {
			return name;
		}

		return QiyiParam.body;
	}

	@Override
	public Optional<PayCenterDutResult> processPayments(PayCenterDutReq dutReq) {
		return payCenterApi.processPayments(dutReq);
	}

    @Override
    public PayCenterDutResult processPaymentsWithRetry(PayCenterDutReq dutReq) {
        return payCenterApi.processPaymentsWithRetry(dutReq).orElse(null);
    }


	public String setExtendsParams(OrderPrepareDto orderPrepareDto, Order order, Map<String, String> configs) {
		DutParamsDto dutParamsDto = orderPrepareDto.getDutParamsDto();
		StringBuilder extendParams = new StringBuilder();
		String payType = String.valueOf(dutParamsDto.getPayType());
        OrderCreationRequest creationRequest = orderPrepareDto.getOrderCreationRequest();
        boolean commonDut = order != null;
        String orderCode = commonDut ? order.getOrderCode() : creationRequest.getOrderCode();
        Long orderPlatform = commonDut ? order.getPlatform() : creationRequest.getStatisticsDTO().getPlatform();

		String subParamExternalSignNo = dutParamsDto.getAccountSignCode();
		if (payType.equals(String.valueOf(Constants.PAY_TYPE_ALIPAY_DUT))
				|| payType.equals(String.valueOf(Constants.PAY_TYPE_BAIDUPAY_DUT))
				|| payType.equals(String.valueOf(Constants.PAY_TYPE_WEIXIN_DUT))
				|| payType.equals(String.valueOf(Constants.PAY_TYPE_WX_APP_DUT))
				|| payType.equals(String.valueOf(Constants.PAY_TYPE_WECHAT_V3))
				|| payType.equals(String.valueOf(Constants.PAY_TYPE_BAIDUPAY_PCWEB_QUICK))
				|| payType.equals(String.valueOf(Constants.PAY_TYPE_BAIDUPAY_PCWEB_ONEKEY))
				|| payType.equals(String.valueOf(Constants.PAY_TYPE_MASTER_CARD_DUT))
				|| payType.equals(String.valueOf(Constants.PAY_TYPE_MPGSTOKEN_DUT))
				|| payType.equals(String.valueOf(Constants.PAY_TYPE_GCASH_DUT))
				|| payType.equals(String.valueOf(Constants.PAY_TYPE_DANA_DUT))
				|| payType.equals(String.valueOf(Constants.PAY_TYPE_TRUEMONEY_DUT))
				|| payType.equals(String.valueOf(Constants.PAY_TYPE_CODAPAY_DUT))
				|| payType.equals(String.valueOf(Constants.PAY_TYPE_GO_PAY_DUT))
				|| payType.equals(String.valueOf(Constants.PAY_TYPE_KAKAO_DUT))
				|| payType.equals(String.valueOf(Constants.PAY_TYPE_TOUCH_GO_DUT))
                || payType.equals(String.valueOf(Constants.PAY_TYPE_JKO_DUT))
                || payType.equals(String.valueOf(Constants.PAY_TYPE_ALIPAYCN_DUT))
                || payType.equals(String.valueOf(Constants.PAY_TYPE_ALIPAY_BOOST_DUT))
                || payType.equals(String.valueOf(Constants.PAY_TYPE_ALIPAY_HK_DUT))) {
			String subParamDutConfirm = "0";
			extendParams.append("subParam_dut_version=1&");

            try {
				if (String.valueOf(Constants.PAY_TYPE_ALIPAY_DUT).equals(payType)) {
					if (subParamExternalSignNo != null) {
						extendParams.append("subParam_external_sign_no=").append(URLEncoder.encode(subParamExternalSignNo, "UTF-8")).append("&");
					} else {
						extendParams.append("subParam_external_sign_no=").append(orderCode).append("&");
					}
					extendParams.append("subParam_dut_confirm=").append(subParamDutConfirm).append("&");
					extendParams.append("subParam_detail_name=").append(URLEncoder.encode(QiyiParam.subject, "UTF-8")).append("&");
				}

				if (("" + Constants.PAY_TYPE_BAIDUPAY_DUT).equals(payType)
						|| payType.equals(String.valueOf(Constants.PAY_TYPE_BAIDUPAY_PCWEB_QUICK))
						|| payType.equals(String.valueOf(Constants.PAY_TYPE_BAIDUPAY_PCWEB_ONEKEY))) {
					if (subParamExternalSignNo != null) {
						extendParams.append("subParam_contractNo=").append(URLEncoder.encode(subParamExternalSignNo, "UTF-8"));
					}
				}

                //微信快捷
				if (("" + Constants.PAY_TYPE_WX_APP_DUT).equals(payType)) {
					if (subParamExternalSignNo != null) {
						extendParams.append("subParam_contractNo=").append(URLEncoder.encode(subParamExternalSignNo, "UTF-8"));
					}

					if (ObjectUtils.equals(orderPlatform, 14L)) {
						extendParams.append("&subParam_wechatAppAlias=app_pps_mobile_client");
					}
					if (ObjectUtils.equals(orderPlatform, 1301L)) {
						extendParams.append("&subParam_wechatAppAlias=app_lite");
					}
				}

				// 万事达代扣
				// TODO 后续对代扣支付的改造以此为模板
				String masterPayType = String.valueOf(Constants.PAY_TYPE_MASTER_CARD_DUT);
				String masterM4mPayType = String.valueOf(Constants.PAY_TYPE_MPGSTOKEN_DUT);
				if (masterPayType.equals(payType) || masterM4mPayType.equals(payType)) {

					// tokenRequestorId 为万事达签约号参数
					extendParams
							.append("subParam_tokenRequestorId=")
							.append(URLEncoder.encode(subParamExternalSignNo, "UTF-8"));

					if (dutParamsDto.getDutType() != null) {
						extendParams.append("&subParam_dutType=").append(dutParamsDto.getDutType());
					}
				}
				// GCASH 代扣 和 DANA代扣
				if (String.valueOf(Constants.PAY_TYPE_GCASH_DUT).equals(payType)
						|| String.valueOf(Constants.PAY_TYPE_KAKAO_DUT).equals(payType)
						|| String.valueOf(Constants.PAY_TYPE_TOUCH_GO_DUT).equals(payType)
						|| String.valueOf(Constants.PAY_TYPE_DANA_DUT).equals(payType)
						|| String.valueOf(Constants.PAY_TYPE_TRUEMONEY_DUT).equals(payType)
						|| String.valueOf(Constants.PAY_TYPE_CODAPAY_DUT).equals(payType)
						|| String.valueOf(Constants.PAY_TYPE_GO_PAY_DUT).equals(payType)
                        || String.valueOf(Constants.PAY_TYPE_JKO_DUT).equals(payType)
                        || String.valueOf(Constants.PAY_TYPE_ALIPAY_BOOST_DUT).equals(payType)
                        || String.valueOf(Constants.PAY_TYPE_ALIPAYCN_DUT).equals(payType)
                        || String.valueOf(Constants.PAY_TYPE_ALIPAY_HK_DUT).equals(payType)) {

					// 签约号参数
					extendParams.append("subParam_contractNo=").append(URLEncoder.encode(subParamExternalSignNo, "UTF-8"));

					if (dutParamsDto.getDutType() != null) {
						extendParams.append("&subParam_dutType=").append(dutParamsDto.getDutType());
					}
				}
			} catch (UnsupportedEncodingException e) {
				LOGGER.error("", e);
			}
			configs.put("extend_params", extendParams.toString());
		} else if (("" + Constants.PAY_TYPE_WECHATAPPV3_DUT).equals(payType)) {
			//支付要求微信次月代扣增加此参数
			extendParams.append("subParam_type=dut&");
			if (StringUtils.isNotBlank(subParamExternalSignNo)) {
				try {
					extendParams.append("subParam_contractNo=").append(URLEncoder.encode(subParamExternalSignNo, "UTF-8"));
				} catch (UnsupportedEncodingException ignored) {
					LOGGER.error("", ignored);
				}
			} else {
				extendParams.append("subParam_isFirstSigning=yes");
				if (Constants.QIYUE_BUSINESS_CODE_YINHEVIP.equals(dutParamsDto.getServiceCode())) {
					extendParams.append("&subParam_returnWeb=1");
				}
			}

			if (dutParamsDto.getDutType() != null) {
				extendParams.append("&subParam_dutType=").append(dutParamsDto.getDutType());
			}

			if (orderPlatform != null) {
				if (orderPlatform.equals(14L)) {
					if (extendParams.length() > 0) {
						extendParams.append("&subParam_wechatAppAlias=app_pps_mobile_client");
					} else {
						extendParams.append("subParam_wechatAppAlias=app_pps_mobile_client");
					}
				} else if (orderPlatform.equals(1026L)) {
					if (extendParams.length() > 0) {
						extendParams.append("&subParam_wechatAppAlias=app_qiyiguo_children");
					} else {
						extendParams.append("subParam_wechatAppAlias=app_qiyiguo_children");
					}
				} else if (orderPlatform.equals(1080L)) {
					//百度视频APK
					if (extendParams.length() > 0) {
						extendParams.append("&subParam_wechatAppAlias=app_baidu_video");
					} else {
						extendParams.append("subParam_wechatAppAlias=app_baidu_video");
					}
				} else if (orderPlatform.equals(1301L)) {
					//爱奇艺极速版
					if (extendParams.length() > 0) {
						extendParams.append("&subParam_wechatAppAlias=app_lite");
					} else {
						extendParams.append("subParam_wechatAppAlias=app_lite");
					}
				}
				if (863L == orderPlatform) {
					//动画屋app需要自己的微信appid
					if (extendParams.length() > 0) {
						extendParams.append("&subParam_wechatAppAlias=app_donghuawu");
					} else {
						extendParams.append("subParam_wechatAppAlias=app_donghuawu");
					}
				}
			}
			configs.put("extend_params", extendParams.toString());
		} else if (payType.equals(Constants.PAY_TYPE_SMS_YDYD + "")
				|| payType.equals(Constants.PAY_TYPE_SMS_LDYS + "")
				|| String.valueOf(Constants.PAY_TYPE_TELECOM_TELEPHONE_FEE).equals(payType)
				|| String.valueOf(Constants.PAY_TYPE_CHINAUNICOMPAY).equals(payType)
				|| String.valueOf(Constants.PAY_TYPE_ARCSOFTPAY).equals(payType)
				|| String.valueOf(Constants.PAY_TYPE_TIANYIPAY).equals(payType)) {
			// 通用话费支付第一次请求支付网关时，设置扩展参数标识，支付中心依此重新选择pay_type
			// 自动续费时也会设置此参数，但是不影响整体的逻辑
			configs.put("extend_params", "subParam_mobilePay=yes");

			// 订单过期时间设置为60小时，以便让支付中心能够重试一次（共两次）
			configs.put("expire_time", "60h");
		} else if (payType.equals("" + Constants.PAY_TYPE_ALIPAYDUTV3)
				|| payType.equals("" + Constants.PAY_TYPE_ALIPAY_DUT_V20_1)
				|| payType.equals("" + Constants.PAY_TYPE_ALIPAY_DUT_V20_2)
				|| payType.equals("" + Constants.PAY_TYPE_ALIPAY_HUAZHI)
				|| payType.equals("" + Constants.PAY_TYPE_ALIPAY_ZHIMA_GO)) {
			//代扣需要使用的参数
			if (StringUtils.isNotBlank(subParamExternalSignNo)) {
				try {
					extendParams.append("subParam_contractNo=").append(URLEncoder.encode(subParamExternalSignNo, "UTF-8"));
				} catch (UnsupportedEncodingException ignored) {

				}
				if (dutParamsDto.getDutType() != null) {
					extendParams.append("&subParam_dutType=").append(dutParamsDto.getDutType());
				}
				if (allowAliPayAsyncDut(orderPrepareDto)) {
					extendParams.append("&subParam_is_async_pay=").append(Constants.ENABLE_ALIPAY_DUT_ASYNC_PAY);
					configs.put("expire_time", getAliPayAsyncDutExpireTime());
				}
				configs.put("extend_params", extendParams.toString());
			}
		} else if (payType.equals("" + Constants.PAY_TYPE_BANK_CARD)) {
			//银联银行卡代扣，需要给支付中心传对应的pay_type,及一些额外的参数
			//fee_unit 支付中心货币类型->  1:人民币 2:奇豆  3:积分 4:代金卷  5:美元 6:台币 7:港币 8:马来西亚币 9:菲律宾币 10:韩币  11:泰国币 12:印尼币 13:越南币 14:奇点
			configs.put("extend_params", "cardId=" + subParamExternalSignNo);
			configs.put("sign_type", QiyiParam.sign_type);
			configs.put("fee_unit", "1");
			configs.put("currency", "CNY");

		} else if (OrderUtil.isGashDutPayment(payType)) {
			setExtendParamsForGashDut(configs, subParamExternalSignNo);
		} else if (OrderUtil.isSpGatewayDutPayment(payType)) {
			setExtendParamsForSpGatewayDut(configs, subParamExternalSignNo);
		} else if (OrderUtil.isGoogleBillingDutPayment(payType)) {
			setExtendParamsForGoogleBillingDut(configs, subParamExternalSignNo, dutParamsDto);
        } else if (OrderUtil.isWeChatPayScoreCreate(payType)) {
            setExtendParamsForWeChatPayScoreCreate(configs, dutParamsDto);
        } else if (OrderUtil.isWeChatPayScoreComplete(payType)){
            setExtendParamsForWeChatPayScoreComplete(configs, dutParamsDto);
        } else {
            if (StringUtils.isNotBlank(subParamExternalSignNo)) {
                try {
                    extendParams.append("subParam_contractNo=").append(URLEncoder.encode(subParamExternalSignNo, StandardCharsets.UTF_8.name()));
                } catch (UnsupportedEncodingException e) {
                    log.error("subParamExternalSignNo encode error, signNo:{}", subParamExternalSignNo, e);
                }
            }
            if (dutParamsDto.getDutType() != null) {
                extendParams.append("&subParam_dutType=").append(dutParamsDto.getDutType());
            }
            configs.put("extend_params", extendParams.toString());
        }
		// 添加扩展字段标识,供支付中心区分自动续费重试订单
		setSubParamOrderType(configs, dutParamsDto);

		//优惠券核销信息
		setSubParamGoodsDetail(configs, dutParamsDto);

        //增加主动代扣订单标识，支付中心可快速返回支付结果
        setManualDutOrderFlag(order, configs);

        addChannelDiscountTokenIfNeeded(orderPrepareDto, configs);
        LOGGER.info("uid :{} final extendParams:{}", orderPrepareDto.getUserId(), configs.get("extend_params"));
		return extendParams.toString();
	}

    private void setManualDutOrderFlag(Order order, Map<String, String> configs) {
        if (!manualDutOrder(order)) {
            return;
        }
        StringBuilder extendParams = new StringBuilder();
        if (StringUtils.isNotEmpty(configs.get("extend_params"))) {
            extendParams.append(configs.get("extend_params"));
        }

        try {
            if (extendParams.length() > 0) {
                extendParams.append("&dut_mode=").append("SYNC");
            } else {
                extendParams.append("dut_mode=").append("SYNC");
            }

            configs.put("extend_params", extendParams.toString());
        } catch (Exception e) {
            LOGGER.error("setManualDutOrderFlag error. Params:{} ", configs, e);
        }
    }

	private boolean allowAliPayAsyncDut(OrderPrepareDto orderPrepareDto) {
        int hourInterval = DateHelper.getHourInterval(DateHelper.getCurrentDate(), DateHelper.getTomorrowDate());
        log.info("[vertify hourInterval][hourInterval :{}]", hourInterval);
        return CloudConfigUtil.allowAliPayAsyncDutPay()
            && orderPrepareDto.isEnableAliPayAsyncDut()
            && hourInterval >= CloudConfigUtil.getAliPayAsyncDutMinExpireTime();
    }

    private String getAliPayAsyncDutExpireTime() {
        int hourInterval = DateHelper.getHourInterval(DateHelper.getCurrentDate(), DateHelper.getTomorrowDate());
        return hourInterval + "h";
    }


	public boolean dealOrder(PayCenterDutResult payCenterDutResult, Order order) {
		try {
            OrderFulfillRequest fulfillRequest = new OrderFulfillRequest();
			fulfillRequest.setOrderCode(order.getOrderCode());
			if (order.getStatus() == OrderStatusConstants.ORDER_STATUS_PROCESSING) {
                fulfillRequest.setStatus(OrderStatusConstants.ORDER_STATUS_DELIVERED);
				modifyOrderAfterPaid(payCenterDutResult, order, fulfillRequest);
                synchronizeOrderInfo(order, fulfillRequest);
                updateRealFee(order, fulfillRequest, payCenterDutResult);
				order.setStatus(OrderStatusConstants.ORDER_STATUS_DELIVERED);
			}

            // 根据notify场景的状态机开关和UID取模来决定是否使用新的处理逻辑
            Long userId = order.getUserId();
            boolean useStateMachine = CloudConfigUtil.enableOrderStateMachineNotify() &&
                                      CloudConfigUtil.enableOrderStateMachineNotifyByTaskType(getDutTaskType(order)) &&
                                      (CloudConfigUtil.isUidInAllowedRangeNotify(userId) || CloudConfigUtil.isUidInWhiteListNotify(userId));
            
            LOGGER.info("[dealOrder] [useStateMachine:{}] [uid:{}] [orderCode:{}]", useStateMachine, userId, order.getOrderCode());
            
            if (useStateMachine) {
                // 使用状态机处理逻辑
                try {
                    boolean payAndFulfillOrder = orderCoreProxy.payAndFulfillOrder(order.getOrderCode(), fulfillRequest);
					log.info("[dealOrder] [payAndFulfillOrder:{}] [orderCode:{}]", payAndFulfillOrder, order.getOrderCode());
                    return payAndFulfillOrder;
                } catch (Exception e) {
                    LOGGER.error("[dealOrder] [state machine error] [order:{}] [payCenterDutResult:{}]", order, payCenterDutResult, e);
                    return false;
                }
            } else {
                // 使用原有处理逻辑
                orderRepository.save(order);
                return true;
            }
		} catch (Exception e) {
			LOGGER.error("[dealOrder error] [order:{}] [payCenterDutResult:{}]", order, payCenterDutResult, e);
			return false;
		}
	}

    private void updateRealFee(Order order, OrderFulfillRequest fulfillRequest, PayCenterDutResult payCenterDutResult) {
        Integer payCenterRealFee = NumberUtils.toInt(payCenterDutResult.getRealFee(), -1);
        if (Objects.equals(order.getRealFee(), payCenterRealFee)) {
            return;
        }
        order.setRealFee(payCenterRealFee);
        order.setSettlementFee(payCenterRealFee);
        fulfillRequest.setRealFee(payCenterRealFee);
        fulfillRequest.setSettlementFee(payCenterRealFee);
        if (StringUtils.isBlank(order.getSkuId())) {
            return;
        }
        CommodityInfo commodityInfo = commodityProxy.queryCommodity(order.getSkuId());
        List<CommodityInfo> subSkuList = commodityInfo.getSubSkuList();
        if (CollectionUtils.isEmpty(subSkuList)) {
            return;
        }

        List<DivideGoodsPrice> ratioGoods = new ArrayList<>();
        List<DivideGoodsPrice> absoluteGoods = new ArrayList<>();
        for (CommodityInfo subSku : subSkuList) {
            if (Objects.equals(subSku.getSettleType(), CommodityInfo.SETTLE_TYPE_RATIO)) {
                ratioGoods.add(new DivideGoodsPrice(subSku.getSkuId(), subSku.getSettleType(), subSku.getSettleValue()));
            }
            if (Objects.equals(subSku.getSettleType(), CommodityInfo.SETTLE_TYPE_ABSOLUTE)) {
                absoluteGoods.add(new DivideGoodsPrice(subSku.getSkuId(), subSku.getSettleType(), subSku.getSettleValue()));
            }
        }

        Integer totalFeeOfAbsoluteGoods = calcAbsoluteGoodsPrice(absoluteGoods);
        if (payCenterRealFee - totalFeeOfAbsoluteGoods < 0) {
            log.error("代扣成功后，重新分摊价格异常, orderCode: {}, payCenterRealFee: {}, totalFeeOfAbsoluteGoods:{}", order.getOrderCode(), payCenterRealFee, totalFeeOfAbsoluteGoods);
            return;
        }

        calcRatioGoodsPrice(ratioGoods, payCenterRealFee - totalFeeOfAbsoluteGoods);
        // 将按比例分摊和固定价值分摊的商品合并为map
        Map<String, DivideGoodsPrice> allGoodsMap = new HashMap<>();
        for (DivideGoodsPrice goods : absoluteGoods) {
            allGoodsMap.put(goods.getSkuId(), goods);
        }
        for (DivideGoodsPrice goods : ratioGoods) {
            allGoodsMap.put(goods.getSkuId(), goods);
        }
        for (OrderFulfillRequest payAndFulfillRequest : fulfillRequest.getPayAndFulfillRequests()) {
            DivideGoodsPrice divideGoodsPrice = allGoodsMap.get(payAndFulfillRequest.getSkuId());
            if (divideGoodsPrice == null) {
                continue;
            }
            payAndFulfillRequest.setRealFee(divideGoodsPrice.getRealFee());
            payAndFulfillRequest.setSettlementFee(divideGoodsPrice.getRealFee());
        }
    }

    /**
     * @param absoluteGoods 按固定价值分摊的商品
     */
    private int calcAbsoluteGoodsPrice(List<DivideGoodsPrice> absoluteGoods) {
        if (CollectionUtils.isEmpty(absoluteGoods)) {
            return 0;
        }
        int totalFeeOfAbsoluteGoods = 0;
        for (DivideGoodsPrice currentGoods : absoluteGoods) {
            int subRealFee = Objects.nonNull(currentGoods.getSettleValue()) ? currentGoods.getSettleValue() : 0;
            currentGoods.setRealFee(subRealFee);
            totalFeeOfAbsoluteGoods += subRealFee;
        }

        return totalFeeOfAbsoluteGoods;
    }

    /**
     * @param ratioGoods  按比例分摊的商品
     * @param subTotalFee 子商品累计的总价
     */
    private void calcRatioGoodsPrice(List<DivideGoodsPrice> ratioGoods, Integer subTotalFee) {
        if (CollectionUtils.isEmpty(ratioGoods)) {
            return;
        }
        Integer subFee = 0;
        for (int index = 0; index < ratioGoods.size() - 1; index++) {
            DivideGoodsPrice currentGoods = ratioGoods.get(index);
            BigDecimal rate = new BigDecimal(currentGoods.getSettleValue())
                .divide(BigDecimal.valueOf(100), 5, RoundingMode.HALF_EVEN);
            int subRealFee = BigDecimal.valueOf(subTotalFee).multiply(rate).setScale(1, RoundingMode.HALF_UP).intValue();
            currentGoods.setRealFee(subRealFee);
            subFee += subRealFee;
        }

        DivideGoodsPrice currentGoods = ratioGoods.get(ratioGoods.size() - 1);
        currentGoods.setRealFee(subTotalFee - subFee);
    }

    private String getDutTaskType(Order order) {
        if (StringUtils.isBlank(order.getRefer())) {
            return null;
        }
        Optional<OrderReferDto> referDto = order.extractReferDto();
        return referDto.map(OrderReferDto::getDutTaskType).orElse(null);
    }

    private void synchronizeOrderInfo(Order order, OrderFulfillRequest request) {
        if (CollectionUtils.isEmpty(order.goodsOrder())) {
            return;
        }
        ArrayList<OrderFulfillRequest> fulfillRequests = new ArrayList<>();
        for (Order o : order.goodsOrder()) {
            o.setStatus(OrderStatusConstants.ORDER_STATUS_DELIVERED);
            if (ObjectUtils.equals(order.getType(), -1)) {
                o.setType(-1);
            }
            o.setPayType(order.getPayType());
            o.setRefer(order.getRefer());
            o.setTradeCreate(order.getTradeCreate());
            o.setTradePayment(order.getTradePayment());
            o.setPayTime(order.getPayTime());
            o.setCenterCode(order.getCenterCode());
            o.setCenterPayType(order.getCenterPayType());
            String accountId = order.getAccountId();
            if (StringUtils.isNotBlank(accountId) && accountId.length() < 255) {
                o.setAccountId(accountId);
            }
            o.setCenterPayService(order.getCenterPayService());


            OrderFulfillRequest subOrderRequest = new OrderFulfillRequest();
            subOrderRequest.setOrderCode(o.getOrderCode());
            subOrderRequest.setStatus(OrderStatusConstants.ORDER_STATUS_DELIVERED);
            if (ObjectUtils.equals(request.getType(), -1)) {
                subOrderRequest.setType(-1);
            }
            subOrderRequest.setPayType(request.getPayType());
            subOrderRequest.setRefer(request.getRefer());
            subOrderRequest.setTradeCreate(request.getTradeCreate());
            subOrderRequest.setTradePayment(request.getTradePayment());
            subOrderRequest.setPayTime(request.getPayTime());
            subOrderRequest.setCenterCode(request.getCenterCode());
            subOrderRequest.setCenterPayType(request.getCenterPayType());
            String requestAccountId = request.getAccountId();
            if (StringUtils.isNotBlank(requestAccountId) && requestAccountId.length() < 255) {
                subOrderRequest.setAccountId(requestAccountId);
            }
            subOrderRequest.setCenterPayService(request.getCenterPayService());
            subOrderRequest.setSkuId(o.getSkuId());
            fulfillRequests.add(subOrderRequest);
        }
        request.setPayAndFulfillRequests(fulfillRequests);
    }

    private static boolean manualDutOrder(Order order) {
        if (order == null || StringUtils.isBlank(order.getRefer())) {
            return false;
        }
        Map<String, Object> stringObjectMap = JacksonUtils.parseMap(order.getRefer());
        return MapUtils.isNotEmpty(stringObjectMap)
            && Constants.TASK_TYPE_MANUALDUT.equals(MapUtils.getString(stringObjectMap, "dutTaskType", ""));
    }

	private static void setExtendParamsForGashDut(Map<String, String> configs, String subParamExternalSignNo) {
		StringBuilder extendParams = new StringBuilder();
		if (StringUtils.isBlank(subParamExternalSignNo)) {
			return;
		}
		try {
			extendParams.append("subParam_contractNo=").append(URLEncoder.encode(subParamExternalSignNo, "UTF-8"));
		} catch (UnsupportedEncodingException ignored) {
			LOGGER.error("Meets an error when encoding with params:{}", configs.toString());
		}
		configs.put("extend_params", extendParams.toString());
	}

	private static void setExtendParamsForSpGatewayDut(Map<String, String> configs, String subParamExternalSignNo) {
		configs.put("fee_unit", "6");
		configs.put("currency", "TWD");
		configs.put("sign_type", QiyiParam.sign_type);
		if (StringUtils.isBlank(subParamExternalSignNo)) {
			return;
		}
		StringBuilder extendParams = new StringBuilder();
		try {
			extendParams.append("subParam_contractNo=").append(URLEncoder.encode(subParamExternalSignNo, "UTF-8"));
		} catch (UnsupportedEncodingException ignored) {
			LOGGER.error("Meets an error when encoding with params:{}", configs.toString());
		}
		configs.put("extend_params", extendParams.toString());
	}

	private static void setExtendParamsForGoogleBillingDut(Map<String, String> configs, String subParamExternalSignNo, DutParamsDto dutParamsDto) {
		Map<String, Object> extendParamsMap = Maps.newHashMap();
		try {
			if (StringUtils.isNotBlank(subParamExternalSignNo)) {
				extendParamsMap.put("subParam_contractNo", URLEncoder.encode(subParamExternalSignNo, "UTF-8"));
			}
			if (dutParamsDto.getDutType() != null) {
				extendParamsMap.put("subParam_dutType", dutParamsDto.getDutType());
			}
		} catch (UnsupportedEncodingException ignored) {
			LOGGER.error("Meets an error when encoding with params:{}", configs.toString());
		}
		configs.put("extend_params", JacksonUtils.toJsonString(extendParamsMap));
	}

    /**
     * 微信支付分0元创单 extend_param参数
     */
    private static void setExtendParamsForWeChatPayScoreCreate(Map<String, String> configs, DutParamsDto dutParamsDto) {
        WeChatPayScoreExtendParam weChatPayScoreExtendParam = dutParamsDto.getWeChatPayScoreExtendParam();
        StringBuilder extendParams = new StringBuilder();
        extendParams.append("subParam_dutType=").append(dutParamsDto.getDutType());
        extendParams.append("&subParam_contractNo=").append(dutParamsDto.getAccountSignCode());
        if (StringUtils.isNotBlank(weChatPayScoreExtendParam.getPostPaymentsName())) {
            extendParams.append("&subParam_post_payments_name=").append(weChatPayScoreExtendParam.getPostPaymentsName());
        }
        if (weChatPayScoreExtendParam.getPostPaymentsAmount() != null) {
            extendParams.append("&subParam_post_payments_amount=").append(weChatPayScoreExtendParam.getPostPaymentsAmount());
        }
        extendParams.append("&subParam_time_range_start_time=").append(weChatPayScoreExtendParam.getTimeRangeStartTime());
        if (StringUtils.isNotBlank(weChatPayScoreExtendParam.getTimeRangeEndTime())) {
            extendParams.append("&subParam_time_range_end_time=").append(weChatPayScoreExtendParam.getTimeRangeEndTime());
        }
        extendParams.append("&subParam_service_introduction=").append(weChatPayScoreExtendParam.getServiceIntroduction());
        extendParams.append("&subParam_risk_fund_name=").append(weChatPayScoreExtendParam.getRiskFundName());
        extendParams.append("&subParam_risk_fund_amount=").append(weChatPayScoreExtendParam.getRiskFundAmount());
        if (StringUtils.isNotBlank(weChatPayScoreExtendParam.getRiskFundDescription())) {
            extendParams.append("&subParam_risk_fund_description=").append(weChatPayScoreExtendParam.getRiskFundDescription());
        }
        configs.put("extend_params", extendParams.toString());
    }

    /**
     * 微信支付分0元创单 extend_param参数
     */
    private static void setExtendParamsForWeChatPayScoreComplete(Map<String, String> configs, DutParamsDto dutParamsDto) {
        StringBuilder extendParams = new StringBuilder();
        WeChatPayScoreExtendParam weChatPayScoreExtendParam = dutParamsDto.getWeChatPayScoreExtendParam();
        extendParams.append("subParam_dutType=").append(dutParamsDto.getDutType());
        extendParams.append("&subParam_create_order_no=").append(weChatPayScoreExtendParam.getCreateOrderNo());
        if (StringUtils.isNotBlank(weChatPayScoreExtendParam.getPostPaymentsName())) {
            extendParams.append("&subParam_post_payments_name=").append(weChatPayScoreExtendParam.getPostPaymentsName());
        }
        if (weChatPayScoreExtendParam.getPostPaymentsAmount() != null) {
            extendParams.append("&subParam_post_payments_amount=").append(weChatPayScoreExtendParam.getPostPaymentsAmount());
        }
        extendParams.append("&subParam_time_range_start_time=").append(weChatPayScoreExtendParam.getTimeRangeStartTime());
        if (StringUtils.isNotBlank(weChatPayScoreExtendParam.getTimeRangeEndTime())) {
            extendParams.append("&subParam_time_range_end_time=").append(weChatPayScoreExtendParam.getTimeRangeEndTime());
        }
        if (StringUtils.isNotBlank(weChatPayScoreExtendParam.getPostPaymentsDescription())) {
            extendParams.append("&subParam_post_payments_description=").append(weChatPayScoreExtendParam.getPostPaymentsDescription());
        }
        configs.put("extend_params", extendParams.toString());
    }

	private static void setSubParamOrderType(Map<String, String> configs, DutParamsDto dutParamsDto) {
		String orderType = dutParamsDto.getOrderType();
		if (StringUtils.isEmpty(orderType)) {
			return;
		}
		StringBuilder extendParams = new StringBuilder();
		if (StringUtils.isNotEmpty(configs.get("extend_params"))) {
			extendParams.append(configs.get("extend_params"));
		}

		try {
			if (extendParams.length() > 0) {
				extendParams.append("&subParam_orderType=").append(orderType);
			} else {
				extendParams.append("subParam_orderType=").append(orderType);
			}

			configs.put("extend_params", extendParams.toString());
		} catch (Exception e) {
			LOGGER.error("setSubParamOrderType error. Params:{} ", configs, e);
		}
	}

	/**
	 * 优惠券核销信息
	 */
	private static void setSubParamGoodsDetail(Map<String, String> configs, DutParamsDto dutParamsDto) {

		if (dutParamsDto.getVipType() == null) {
			return;
		}

		if (dutParamsDto.getPayChannel() == null || PaymentDutType.PAY_CHANNEL_ALIPAY != dutParamsDto.getPayChannel()) {
			return;
		}

		StringBuilder extendParams = new StringBuilder();
		if (StringUtils.isNotEmpty(configs.get("extend_params"))) {
			extendParams.append(configs.get("extend_params"));
		}

		String amount = String.valueOf(dutParamsDto.getAmount());
		StringJoiner goodsIdJoiner = new StringJoiner("-");
		if (dutParamsDto.getSourceVipType() != null) {
			goodsIdJoiner.add(dutParamsDto.getSourceVipType().toString());
			goodsIdJoiner.add(dutParamsDto.getVipType().toString());
			//amount固定为1
			goodsIdJoiner.add("1");
		} else {
			goodsIdJoiner.add(dutParamsDto.getVipType().toString());
			goodsIdJoiner.add(amount);
		}
		//表示代扣支付
		goodsIdJoiner.add("dutpay");
		goodsIdJoiner.add("autorenew");

		double feeOfYuan = BigDecimal.valueOf(dutParamsDto.getFee()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue();
		ConponGoodsInfo conponGoodsInfo = ConponGoodsInfo.builder()
				.goods_id(goodsIdJoiner.toString())
				.goods_name(dutParamsDto.getProductName())
				.quantity(Integer.valueOf(amount))
				.price(feeOfYuan)
				.build();

		String goodsDetailJson = JSONArray.toJSONString(Collections.singleton(conponGoodsInfo));
		if (extendParams.length() > 0) {
			extendParams.append("&");
		}
		extendParams.append("subParam_goods_detail=").append(goodsDetailJson);
		extendParams.append("&subParam_query_options=[\"discount_goods_detail\"]");
		configs.put("extend_params", extendParams.toString());
	}

	public String createOrderName(Map<String, String> params, CommodityInfo commodityInfo, Product product, Integer amount, Long vipType, AgreementTypeEnum agreementTypeEnum, OperateTypeEnum operateType) {
        String businessCode = commodityInfo != null ? commodityInfo.getBusinessCode() : product.getBusinessCode();
        if (intlProduct(businessCode)) {
			return namingOrderOfIntl(params, commodityInfo, product, amount);
		}
        if (useSkuDisplayName(agreementTypeEnum, commodityInfo)) {
            return commodityInfo.getDisplayName();
        }
        String productName = commodityInfo != null ? commodityInfo.getDisplayName() : product.getName();
        Integer periodUnit = commodityInfo != null ? commodityInfo.getPeriodUnit() : product.getPeriodUnit();
        Long sourceVipType = commodityInfo != null ? commodityInfo.getSourceSubType() : product.getSourceSubType();
        String vipTypeName = vipType == null ? productName : vipTypeManager.getVipTypeById(vipType).getName();
        if (sourceVipType != null) {
            VipType sourceVipTypeObj = vipTypeManager.getVipTypeById(sourceVipType);
            String sourceVipTypeName = sourceVipTypeObj != null ? sourceVipTypeObj.getName() : "";
            String unit = Constants.unitNameMap().get(periodUnit);
            return sourceVipTypeName + "升级" + vipTypeName + amount + unit + AUTO_RENEW_DESCRIPTION;
        }
        if (agreementTypeEnum == null || agreementTypeEnum == AgreementTypeEnum.AUTO_RENEW) {
            String amountAndUnit = periodUnit == Constants.PRODUCT_PERIODUNIT_MONTH
                ? MapUtils.getString(Constants.amountNameMap(), amount, amount + "个月")
                : amount + Constants.unitNameMap().get(periodUnit);
            return vipTypeName + amountAndUnit;
        }
        String unit = Constants.unitNameMap().get(periodUnit);
        String nameSuffix = "";
        if (agreementTypeEnum == AgreementTypeEnum.ZHIMA_GO) {
            nameSuffix = DUT_ORDER_NAME_SUFFIX_ZHIMA_GO;
        }
        if (agreementTypeEnum == AgreementTypeEnum.WECHAT_PAY_SCORE) {
            if (OperateTypeEnum.COMPLETE_ORDER.equals(operateType)) {
                return productName;
            } else {
                nameSuffix = DUT_ORDER_NAME_SUFFIX_WECHAT_PAY_SCORE_CREATE;
            }
        }
		return vipTypeName + amount + unit + nameSuffix;
	}

    private boolean useSkuDisplayName(AgreementTypeEnum agreementTypeEnum, CommodityInfo commodityInfo) {
        return agreementTypeEnum != null
            && commodityInfo != null
            && !EXCLUDE_AGREEMENT_TYPES.contains(agreementTypeEnum.getValue())
            && !AgreementTypeEnum.commonAutoRenew(agreementTypeEnum.getValue());
    }



	/**
	 * 判断是否为国际站或台湾站会员
	 */
	private static boolean intlProduct(String businessCode) {
		return Constants.BUSINESS_CODE_GLOBAL.equals(businessCode)
				|| Constants.BUSINESS_CODE_TW.equals(businessCode);
	}

	/**
	 * 获取国际站和台湾站订单名称
	 *
	 * @param params  params
	 * @param product Product
	 * @param amount  amount
	 * @return String
	 */
	private String namingOrderOfIntl(Map<String, String> params, CommodityInfo commodityInfo, Product product, Integer amount) {
        Long productVipType = commodityInfo != null ? commodityInfo.getSubType() : product.getSubtype();
        StringBuilder stringBuilder = new StringBuilder();
		//台湾黄金套餐订单的商品名称
		if (Constants.VIP_USER_TW == productVipType) {
			stringBuilder.append("愛奇藝台灣VIP");
			if (String.valueOf(Constants.PAY_TYPE_SPGATEWAYCREDITDUT).equals(params.get("payType"))
					|| String.valueOf(Constants.PAY_TYPE_GPB_TWDUT).equals(params.get("payType"))) {
				stringBuilder.append("續租");
			} else {
				stringBuilder.append("會員");
			}
			stringBuilder.append(amount);
			stringBuilder.append("個月");
			return stringBuilder.toString();
		}

		// 国际站获取订单名称
		return getIntlOrderName(params, amount, commodityInfo, product);
	}

	/**
	 * 获取国际站订单名称
	 *
	 * @param params
	 * @param amount
	 * @param product
	 * @return
	 */
	private String getIntlOrderName(Map<String, String> params, Integer amount, CommodityInfo commodityInfo, Product product) {
        Long vipType = commodityInfo != null ? commodityInfo.getSubType() : product.getSubtype();
        String productCode = commodityInfo != null ? commodityInfo.getSkuId() : product.getCode();
        String productName = commodityInfo != null ? commodityInfo.getDisplayName() : product.getName();
        Integer periodUnit = commodityInfo != null ? commodityInfo.getPeriodUnit() : product.getPeriodUnit();
        String language = params.get("lang");
		if (StringUtils.isEmpty(language)) {
			language = Constants.ZH_TW_LANG.toLowerCase();
		} else if (Constants.ZH_TW_LANG.equals(language)) {
			language = language.toLowerCase();
		}
		VipType vipTypeObj = vipTypeManager.getVipTypeById(vipType);
		// 参数错误
		if (Objects.isNull(vipTypeObj)
				|| StringUtils.isEmpty(vipTypeObj.getI18n())) {
			LOGGER.error("vipType not valid, i18n is not valid, params:{}, amount:{}, product:{}", params, amount, productCode);
			return productName;
		}

		JSONObject i18nJson = JSON.parseObject(vipTypeObj.getI18n());
		// vipType对应i18n异常
		if (Objects.isNull(i18nJson) || StringUtils.isEmpty(i18nJson.getString("name"))) {
			LOGGER.error("error occurs in intl getOrderName, i18n is invalid, params:{}, amount:{}, product:{}",
					params, amount, productCode);
			return productName;
		}
		String translationKey = getTranslationKey(periodUnit);
		return corpusTemplate.getTranslation(i18nJson.getString("name"), language) +
				amount +
				corpusTemplate.getTranslation(translationKey, language);
	}

	/**
	 * 获取国际站待翻译的时长单位翻译key
	 *
	 * @param periodUnit
	 * @return
	 */
	private String getTranslationKey(Integer periodUnit) {
		if (Objects.equals(ProductPeriodUnitEnum.PRODUCT_PERIOD_UNIT_DAY.getCode(), periodUnit)) {
			return I18N_KEY_DAY;
		}
		return I18N_KEY_MONTH;
	}

	/**
	 * 设置订单拓展字段.
	 * 目前拓展字段的存储格式为json，如 {"userAutoRenew":"true", "suitABTestId":"A"}
	 *
	 * @param order        订单对象
	 * @param dutParamsDto 请求参数
	 */
	public static void setOrderExtraParams(Order order, DutParamsDto dutParamsDto, AgreementTypeEnum agreementType) {
		try {
			Optional<OrderReferDto> referDto = order.extractReferDto();
			OrderReferDto temp;
			temp = referDto.orElseGet(OrderReferDto::new);

			// 判断用户是否是自动续费状态
            if (agreementType == null || agreementType == AgreementTypeEnum.AUTO_RENEW) {
                temp.setUserAutoRenew(Boolean.TRUE);
            }

			//记录自动续费开通订单号，用于线下联盟识别代扣订单对应的产品并匹配返佣政策
			if (StringUtils.isNotBlank(dutParamsDto.getSignOrderCode())) {
				temp.setSignOrderCode(dutParamsDto.getSignOrderCode());
			}
			if (StringUtils.isNotBlank(dutParamsDto.getTaskType())) {
				temp.setDutTaskType(dutParamsDto.getTaskType());
			}

			if (StringUtils.isNotBlank(dutParamsDto.getTimeZone())) {
				temp.setTimeZone(dutParamsDto.getTimeZone());
			}

			// 保存通用业务属性
			if (MapUtils.isNotEmpty(dutParamsDto.getBusinessProperty())) {
                Map<String, Object> businessProperty = dutParamsDto.getBusinessProperty();
                if (MapUtils.isEmpty(temp.getBusinessProperty())) {
					temp.setBusinessProperty(businessProperty);
				} else {
					Map<String, Object> tempMap = temp.getBusinessProperty();
					tempMap.putAll(businessProperty);
					temp.setBusinessProperty(tempMap);
				}
			}

            AvailableDutCouponInfo dutCouponInfo = dutParamsDto.getAvailableDutCouponInfo();
            if (dutCouponInfo != null) {
                String batchNo = dutCouponInfo.getBatchNo();
                String couponCode = dutCouponInfo.getCouponCode();
                Integer dutCouponType = dutCouponInfo.getType();
                LOGGER.info("add coupon Info related params on order, dutCouponInfo:{}", dutCouponInfo);
                if (MapUtils.isNotEmpty(temp.getBusinessProperty())) {
                    temp.getBusinessProperty().put(BATCH_NO, batchNo);
                    temp.getBusinessProperty().put(COUPON_CODE, couponCode);
                    temp.getBusinessProperty().put(COUPON_BATCH, batchNo);
                    temp.getBusinessProperty().put(DUT_DISCOUNT_TYPE, dutCouponType);
                } else {
                    Map<String, Object> tempMap = new HashMap<>();
                    tempMap.put(BATCH_NO, batchNo);
                    tempMap.put(COUPON_CODE, couponCode);
                    tempMap.put(COUPON_BATCH, batchNo);
                    tempMap.put(DUT_DISCOUNT_TYPE, dutCouponType);
                    temp.setBusinessProperty(tempMap);
                }
            }
			order.setReferByReferDto(temp);
		} catch (Exception e) {
			LOGGER.error("setOrderExtraParams:{}", order, e);
		}
	}

    /**
     * 根据协议类型和支付渠道添加渠道优惠核销权限token
     *
     * @param orderPrepareDto 订单准备参数
     * @param configs 支付中心配置参数
     */
    private void addChannelDiscountTokenIfNeeded(OrderPrepareDto orderPrepareDto, Map<String, String> configs) {
        DutParamsDto dutParamsDto = orderPrepareDto.getDutParamsDto();
        Long userId = orderPrepareDto.getUserId();
        Integer agreementType = dutParamsDto.getAgreementType();
        Integer payChannel = dutParamsDto.getPayChannel();
        try {
            // 检查功能开关
            if (!CloudConfigUtil.isChannelDiscountTokenEnabled()) {
                LOGGER.warn("渠道优惠核销功能未开启");
                return;
            }

            if (payChannel == null || agreementType == null) {
                LOGGER.warn("协议类型或支付渠道为空, userId:{}", userId);
                return;
            }
            // 1. 检查是否已使用站内代金券
            AvailableDutCouponInfo availableDutCouponInfo = dutParamsDto.getAvailableDutCouponInfo();
            if (availableDutCouponInfo != null && !Boolean.TRUE.equals(availableDutCouponInfo.getOffSite())) {
                LOGGER.info("已使用站内代金券，不设置渠道优惠token, userId:{}, couponCode:{}, offSite:{}", userId, availableDutCouponInfo.getCouponCode(), availableDutCouponInfo.getOffSite());
                return; // 提前返回，不设置渠道优惠token
            }

            // 获取渠道优惠核销token
            String discountToken = CloudConfigUtil.getChannelDiscountToken(agreementType, payChannel);
            if (StringUtils.isNotBlank(discountToken)) {
                // 获取当前的extend_params
                StringBuilder extendParams = new StringBuilder();
                String currentExtendParams = configs.get("extend_params");
                if (StringUtils.isNotEmpty(currentExtendParams)) {
                    extendParams.append(currentExtendParams);
                }

                // 添加subParam_activity_code参数
                if (extendParams.length() > 0) {
                    extendParams.append("&");
                }
                extendParams.append("subParam_activity_code=").append(discountToken);
                // 更新配置
                configs.put("extend_params", extendParams.toString());
                LOGGER.info("添加渠道优惠核销token成功，uid:{}, agreementType:{}, payChannel:{}, discountToken:{}", userId, agreementType, payChannel, discountToken);
            }
        } catch (Exception e) {
            LOGGER.error("添加渠道优惠核销token失败，uid:{}, agreementType:{}, payChannel:{}", userId, agreementType, payChannel, e);
        }
    }
	/**
	 * 更新订单的赠送属性，此方法仅在代扣需要弥补不足天数时使用
	 *
	 * @param order  Order
	 * @param params params
	 */
	public void addDutGiftInfo(Order order, Map<String, String> params) {
		try {
			String giftInfos = params.get("giftInfos");
			if (StringUtils.isEmpty(giftInfos)) {
				return;
			}
			String decodeStr = new String(Base64.getDecoder().decode(giftInfos), "UTF-8");
			Map<String, Object> giftInfosMap = JSON.parseObject(decodeStr);
			String giftProductCode = (String) giftInfosMap.get("productCode");
			String actId = (String) giftInfosMap.get("actId");
			int amount = (int) giftInfosMap.get("amount");

			Optional<OrderReferDto> orderReferDto = order.extractReferDto();
			if (!orderReferDto.isPresent()) {
				orderReferDto = Optional.of(OrderReferDto.builder().build());
			}
			if (CollectionUtils.isEmpty(orderReferDto.get().getGiftInfos())) {
				orderReferDto.get().setGiftInfos(Lists.newArrayList());
			}

			VipGiftInfo vipFavorAct = new VipGiftInfo();
			//和权益系统确认，actid保证唯一即可
			vipFavorAct.setActId(actId);
			vipFavorAct.setProductCode(giftProductCode);
			vipFavorAct.setAmount(amount);
			orderReferDto.get().getGiftInfos().add(vipFavorAct);

			order.setReferByReferDto(orderReferDto.get());
		} catch (Exception e) {
			LOGGER.error("[OrderUtils] [excute addDutGiftInfo] [params:{}]", params, e);
		}
	}

	/**
	 * 支付完成后的订单处理
	 *
	 * @param payCenterDutResult PayCenterDutResult
	 * @param order     订单对象
	 */
	public void modifyOrderAfterPaid(PayCenterDutResult payCenterDutResult, Order order, OrderFulfillRequest request) {

		String tradeCreate = String.valueOf(payCenterDutResult.getCreateTime());
		String tradePayment = String.valueOf(payCenterDutResult.getPayTime());
		String centerCode = payCenterDutResult.getOrderCode();
		String centerPayType = payCenterDutResult.getPayType();

		//标识是否沙盒订单 1沙盒订单
		if (SAND_BOX.equals(payCenterDutResult.getPayMode())) {
			order.setType(-1);
            request.setType(-1);
		}

		if (centerCode != null) {
			order.setCenterCode(centerCode);
            request.setCenterCode(centerCode);
		}

		if (tradeCreate != null) {
            Timestamp timestamp = DateHelper.getTimestamp(tradeCreate);
            order.setTradeCreate(timestamp);
            request.setTradeCreate(timestamp);
		}
		if (tradePayment != null) {
            Timestamp tradePaymentTimestamp = DateHelper.getTimestamp(tradePayment);
            order.setTradePayment(tradePaymentTimestamp);
            request.setTradePayment(tradePaymentTimestamp);
		}
		if (StringUtils.isNotBlank(centerPayType)) {
			order.setCenterPayType(centerPayType);
            request.setCenterPayType(centerPayType);
		}

		// 支付渠道编码
		setCenterPayService(payCenterDutResult, order, request);

        String tradeCode = payCenterDutResult.getTradeCode();
        order.setTradeCode(tradeCode);
        request.setTradeCode(tradeCode);

		//若第三方uid不为空且长度小于255（数据库字段长度），则赋值给 accountId 字段，在校验首次优惠的时候，会判断爱奇艺uid、第三方uid是否参与过活动
		String thirdUid = payCenterDutResult.getThirdUid();
		if (org.apache.commons.lang3.StringUtils.isNotBlank(thirdUid) && thirdUid.length() < 255) {
			order.setAccountId(thirdUid);
            request.setAccountId(thirdUid);
		}

		if (order.getTradeNo() == null) {
            String tradeNo = order.getOrderCode();
            order.setTradeNo(tradeNo);
            request.setTradeNo(tradeNo);
		}

        Timestamp payTimestamp = new Timestamp(System.currentTimeMillis());
        order.setPayTime(payTimestamp);
        request.setPayTime(payTimestamp);

		// 处理Google Billing自动续费首次免费订单
		filterGoogleBillingDutPayNotify(order, payCenterDutResult);

		setReferForPaid(order, payCenterDutResult, request);

		modifyItem(order, payCenterDutResult, request);

		// 获取赠送产品订单
//		List<Order> giftOrderList = getGiftOrderList(order);
//
//		// 更新赠送订单
//		modifyGiftOrderAfterPaid(order, giftOrderList);

	}

	/**
	 * 支付成功时更新赠送订单信息
	 */
	private void modifyGiftOrderAfterPaid(Order order, List<Order> giftOrderList) {
		if (giftOrderList != null && !giftOrderList.isEmpty()) {
			for (Order giftOrder : giftOrderList) {
				giftOrder.setPayTime(order.getPayTime());
				giftOrder.setCenterCode(order.getCenterCode());
				giftOrder.setTradeCreate(order.getTradeCreate());
				giftOrder.setTradePayment(order.getTradePayment());
				giftOrder.setAccountId(order.getAccountId());
			}
		}
	}

	private List<Order> getGiftOrderList(Order mainOrder) {
		List<Order> giftOrderList = new ArrayList<>();
		java.util.Optional<OrderReferDto> orderReferDto = mainOrder.extractReferDto();
		if (!orderReferDto.isPresent() || CollectionUtils.isEmpty(orderReferDto.get().getGiftInfos())) {
			return giftOrderList;
		}

		List<VipGiftInfo> vipGiftInfos = orderReferDto.get().getGiftInfos();
		if (CollectionUtils.isNotEmpty(vipGiftInfos)) {
			for (VipGiftInfo vipGiftInfo : vipGiftInfos) {
				if (null != vipGiftInfo.getOrderCode()) {
					Order giftOrder = queryOrder(vipGiftInfo.getOrderCode(), mainOrder.getUserId());
					giftOrderList.add(giftOrder);
				}
			}
		}
		return giftOrderList;
	}

	/**
	 * 设置通过体验码、激活码支付订单项的状态
	 *
	 * @param order 订单项
	 */
	private void modifyItem(Order order, PayCenterDutResult payCenterDutResult, OrderFulfillRequest request) {
		//订单支付成功后，获取支付中心回调实际财务收入，存入订单项中
		String realFeeStr = payCenterDutResult.getRealFee();
		Integer realFee = NumberUtils.toInt(realFeeStr, -1);
		if (realFee >= 0) {
			if (realFee > 0 || TypeOfOrderEnum.WECHAT_PAY_SCORE_CREATE.getCode().equals(order.getType())) {
				order.setChargeType(ChargeTypeEnum.PAID_CHARGE.getValue());
                request.setChargeType(ChargeTypeEnum.PAID_CHARGE.getValue());
			} else {
				order.setChargeType(ChargeTypeEnum.FREE_CHARGE.getValue());
                request.setChargeType(ChargeTypeEnum.FREE_CHARGE.getValue());
			}
		}
	}

	private void setReferForPaid(Order order, PayCenterDutResult payCenterDutResult, OrderFulfillRequest request) {
		Optional<OrderReferDto> referDtoOptional = order.extractReferDto();
		OrderReferDto referDto = referDtoOptional.orElse(new OrderReferDto());

		boolean updated = false;
		// 记录商户号
		if (org.apache.commons.lang3.StringUtils.isNotEmpty(payCenterDutResult.getChannelAccountNo())) {
			updated = true;
			referDto.setMerchantNo(payCenterDutResult.getChannelAccountNo());
		}

		//保存优惠金额信息到订单Refer
		DiscountDetail discountDetail = OrderUtil.buildDiscountDetail(payCenterDutResult);
		if (discountDetail != null) {
			updated = true;
			referDto.setDiscountDetail(discountDetail);
		}
		JSONObject dutDiscountDetail = getDutDiscountDetail(payCenterDutResult);
		if (dutDiscountDetail != null) {
			updated = true;
			Map<String, Object> businessProperty = referDto.getBusinessProperty();
			if (businessProperty == null) {
				businessProperty = new HashMap<>();
			}
			businessProperty.put(ParamKeyConstants.DUT_DISCOUNT_DETAIL, dutDiscountDetail);
			referDto.setBusinessProperty(businessProperty);
		}

		if (updated) {
			order.setReferByReferDto(referDto);
            request.setRefer(constructRefer(referDto));
            Map<String, String> differences = new HashMap<>();
            Long userId = order.getUserId();
            String skuId = order.getSkuId();
            diffRefer(order, request.getRefer(), differences, userId, skuId);
            boolean diffSuccess = MapUtils.isEmpty(differences);
            if (diffSuccess) {
                log.info("diff refer success, uid:{}, skuId:{}", userId, skuId);
            } else {
                log.error("diff refer fail, uid:{}, skuId:{}, differences:{}", userId, skuId, JacksonUtils.toJsonString(differences));
            }
        }
	}

    private Map<String, Object> constructRefer(OrderReferDto referDto) {
        Map<String, Object> map = JacksonUtils.beanToMap(referDto);
        Map<String, Object> businessProperty = referDto.getBusinessProperty();
        map.remove(BUSINESS_PROPERTY);
        if (MapUtils.isEmpty(businessProperty)) {
            return map;
        } else {
            for (Entry<String, Object> entry : businessProperty.entrySet()) {
                map.put("businessProperty." + entry.getKey(), entry.getValue());
            }
        }
        return map;
    }

	private JSONObject getDutDiscountDetail(PayCenterDutResult payCenterDutResult) {
		Map<String, String> extraCommonParamMap = payCenterDutResult.parseExtraCommonParam();
		if (MapUtils.isEmpty(extraCommonParamMap)) {
			return null;
		}
		String dutDiscountId = extraCommonParamMap.get(ParamKeyConstants.DUT_DISCOUNT_ID);
		String dutDiscountValue = extraCommonParamMap.get(ParamKeyConstants.DUT_DISCOUNT_VALUE);
		if (StringUtils.isBlank(dutDiscountId) || StringUtils.isBlank(dutDiscountValue)) {
			return null;
		}
		JSONObject dutDiscountDetail = new JSONObject();
		dutDiscountDetail.put(ParamKeyConstants.DUT_DISCOUNT_DETAIL_DISCOUNT_ID, dutDiscountId);
		dutDiscountDetail.put(ParamKeyConstants.DUT_DISCOUNT_DETAIL_DISCOUNT_AMOUNT, dutDiscountValue);
		return dutDiscountDetail;
	}

	private void setCenterPayService(PayCenterDutResult payCenterDutResult, Order order, OrderFulfillRequest request) {

		String centerPayService = payCenterDutResult.getServiceId();
		try {
			if (org.apache.commons.lang3.StringUtils.isNumeric(centerPayService)) {
                Integer payServiceId = Integer.valueOf(centerPayService);
                order.setCenterPayService(payServiceId);
                request.setCenterPayService(payServiceId);
				return;
			}
		} catch (Exception e) {
			LOGGER.error("centerPayService " + centerPayService + " is invalid", e);
		}

		LOGGER.warn("Order {} cannot set center_pay_service", order.getOrderCode());
	}

	private void filterGoogleBillingDutPayNotify(Order order, PayCenterDutResult payCenterDutResult) {

		String payType = String.valueOf(order.getPayType());
		if (!String.valueOf(Constants.PAY_TYPE_GPB_TWFIRSTDUT).equals(payType)
				&& !String.valueOf(Constants.PAY_TYPE_GPB_TWDUT).equals(payType)) {
			return;
		}

		String extraCommonParams = payCenterDutResult.getExtraCommonParam();

		if (org.apache.commons.lang3.StringUtils.isEmpty(extraCommonParams)) {
			return;
		}

		//支付成功后支付中心返回的额外参数
		Map<String, String> paramsMap = Maps.newHashMap();
		String[] commonParams = extraCommonParams.split("&");
		for (String str : commonParams) {
			if (org.apache.commons.lang3.StringUtils.isNotBlank(str)) {
				String[] commonParam = str.split("=");
				paramsMap.put(commonParam[0], commonParam[1]);
			}
		}

		if (MapUtils.isEmpty(paramsMap)) {
			return;
		}

        Long expiryTimeMills = MapUtils.getLong(paramsMap, "expiry_time");
        Timestamp expire = DateHelper.getTimestamp(MapUtils.getString(paramsMap, "expire"));
        if (expire != null) {
            expiryTimeMills = expire.getTime();
        }
		if (null == order.getExpireTime()) {
			Timestamp expireTime = null;
			if (null != expiryTimeMills) {
				Date date = new Date(expiryTimeMills);
				expireTime = DateHelper.getDateTime(date);
			}
			Optional<OrderReferDto> referDto = order.extractReferDto();
			if (referDto.isPresent()) {
				referDto.get().setExpireTime(expireTime);
				order.setReferByReferDto(referDto.get());
			} else {
				OrderReferDto temp = new OrderReferDto();
				temp.setExpireTime(expireTime);
				order.setReferByReferDto(temp);
			}
		}
	}

	public Map<String, Object> transfer(PayCenterDutResult payCenterDutResult, Order order, OrderCreationRequest orderCreationRequest, String signOrderCode) {
		Map<String, Object> data = Maps.newHashMap();
		String code = null;
		String message;

		if (StringUtils.isNotEmpty(payCenterDutResult.getCode())) {
            code = QiyiParam.getErrorCodeMap().get(payCenterDutResult.getCode());
		} else {
			if (IsSuccessEnum.IS_SUCCESS_T.getSuc().equals(payCenterDutResult.getIsSuccess())) {
				if (payCenterDutResult.isOrderStatusDelivered()) {
					// 同步返回成功时也交由异步通知处理，避免订单覆盖更新问题
					code = "A00003";
				} else if (payCenterDutResult.isOrderStatusCancel()) {
					if ("S".equals(payCenterDutResult.getConfirmType())) {
						code = "A00002";
					} else {
						code = "A00001";
					}
				} else if ("2".equals(payCenterDutResult.getOrderStatus())) {
					code = "A00003";
				}
			} else {
				if ("NOT_SUPPORT_BALANCE".equals(payCenterDutResult.getError())) {
					code = "Q00344";
				} else if ("NOT_HAVE_ENOUGH_BALANCE".equals(payCenterDutResult.getError())) {
					code = "Q00343";
				} else {
					code = "Q00332";
				}
			}
		}
		if (StringUtils.isNotBlank(payCenterDutResult.getReqErrorType())) {
			data.put("req_error_type", payCenterDutResult.getReqErrorType());
		}
		if (StringUtils.isNotBlank(payCenterDutResult.getThirdErrorCode())) {
			data.put("third_error_code", payCenterDutResult.getThirdErrorCode());
		}
		if (StringUtils.isNotBlank(payCenterDutResult.getThirdErrorMsg())) {
			data.put("third_error_msg", payCenterDutResult.getThirdErrorMsg());
		}
		message = QiyiParam.getCodeMessageMap().get(code);
        boolean commonDut = order != null;
        Integer orderPayType = commonDut ? order.getPayType() : orderCreationRequest.getPaymentDTO().getPayType();
        String orderCode = commonDut ? order.getOrderCode() : orderCreationRequest.getOrderCode();
        Integer orderFee = commonDut ? order.getFee() : orderCreationRequest.getGoodsDTO().getFeeDTO().getFee();
        Long orderUid = commonDut ? order.getUserId() : orderCreationRequest.getUserDTO().getUserId();
        // 银行卡代扣返回结果不一致,需要单独处理
		if (StringUtils.isNotBlank(payCenterDutResult.getCode()) && Constants.PAY_TYPE_BANK_CARD == orderPayType) {
			if (payCenterDutResult.cardPayDealSuccess()) {
				code = ResultCodeEnum.SUCCESS.value();
			} else if (payCenterDutResult.isReqSuccess()) {
				code = ResultCodeEnum.NEED_ASYNC_CONFIRM.value();
				message = ResultCodeEnum.NEED_ASYNC_CONFIRM.desc();
			} else {
				code = "Q00332";
				message = "支付失败";
				data.put("third_error_code", payCenterDutResult.getCode());
				if (StringUtils.isNotBlank(payCenterDutResult.getMsg())) {
					data.put("third_error_msg", payCenterDutResult.getMsg());
				}
			}
		}
		Map<String, Object> resultMap = Maps.newHashMap();
		resultMap.put("code", code);
		resultMap.put("message", message);
		data.put("orderCode", orderCode);
		data.put("fee", orderFee);

		if (orderPayType == Constants.PAY_TYPE_GPB_TWDUT && StringUtils.isNotBlank(signOrderCode)) {
			// 谷歌宽限期处理用到app_id和extra_common_param
			Order signOrder = queryOrder(signOrderCode, orderUid);
			if (signOrder != null && StringUtils.isNotBlank(signOrder.getRefer())) {
				OrderReferDto orderReferDto = JSON.parseObject(signOrder.getRefer(), OrderReferDto.class);
				data.put("app_id", orderReferDto.getActCode());
			}
			if (resultMap.get("extra_common_param") != null) {
				data.put("extra_common_param", resultMap.get("extra_common_param"));
			}
		}

		resultMap.put("data", data);
		return resultMap;
	}

    @Override
    public Order queryOrder(String orderCode, Long userId) {
        Order order = null;
        if (CloudConfigUtil.routeToOrderCode(userId)) {
            order = orderCoreProxy.getByOrderCode(orderCode);
        } else {
            order = orderRepository.findByOrderCode(orderCode);
        }
        return order;
    }

    /**
     * 验证订单数据一致性
     * 比较通过createOrder和createUnpaidOrder创建的订单数据
     * @param createOrderDto 创建订单的DTO
     * @return 比对结果，包含不一致的字段信息
     */
    @Override
    public boolean validateOrderConsistency(CreateOrderDto createOrderDto) {
        LOGGER.info("[validateOrderConsistency] start validating order consistency for dto: {}", createOrderDto);
        Map<String, Object> result = new HashMap<>();
        boolean needCheck = CloudConfigUtil.needValidateOrderConsistency();
        if (!needCheck) {
            log.info("no need validateOrderConsistency, needCheck:{}", needCheck);
            return true;
        }
        
        try {
            // 1. 使用createOrder创建订单属性，但是不创建订单
            createOrderDto.setNeedCreateOrder(false);
            Order order = createOrder(createOrderDto);
            
            // 2. 使用createUnpaidOrder创建订单请求，但是不创建订单
            OrderCreationRequest request = createUnpaidOrder(createOrderDto);
            
            // 3. 比较两者的差异
            Map<String, String> differences = compareOrderWithRequest(order, request);
            
            // 4. 记录比对结果
            result.put("success", differences.isEmpty());
            result.put("differences", differences);
            result.put("orderCode", order.getOrderCode());
            
            // 5. 记录日志
            createOrderDto.setNeedCreateOrder(true);
            if (differences.isEmpty()) {
                LOGGER.info("[validateOrderConsistency] validation successful for orderCode: {}", order.getOrderCode());
                return true;
            } else {
                LOGGER.warn("[validateOrderConsistency] validation failed for orderCode: {}, differences: {}", order.getOrderCode(), JSONObject.toJSON(differences));
                return false;
            }

        } catch (Exception e) {
            LOGGER.error("[validateOrderConsistency] error during validation", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            return false;
        } finally {
            createOrderDto.setNeedCreateOrder(true);
        }
    }
    
    /**
     * 比较Order对象和OrderCreationRequest对象的差异
     * @param order Order对象
     * @param request OrderCreationRequest对象
     * @return 不一致的字段及其值
     */
    private Map<String, String> compareOrderWithRequest(Order order, OrderCreationRequest request) {
        Map<String, String> differences = new HashMap<>();
        
        // 基础信息比对
		Long uid = order.getUserId();
		String skuId = order.getSkuId();
        compareField(differences, "name", order.getName(), request.getOrderName(), uid, skuId);
        
        // 用户信息比对
        if (request.getUserDTO() != null) {
            compareField(differences, "userId", order.getUserId(), request.getUserDTO().getUserId(), uid, skuId);
            compareField(differences, "userIp", order.getUserIp(), request.getUserDTO().getUserIp(), uid, skuId);
        } else {
            differences.put("userDTO", "Missing in OrderCreationRequest");
        }
        
        // 费用信息比对
        if (request.getGoodsDTO() != null && request.getGoodsDTO().getFeeDTO() != null) {
            FeeDTO feeDTO = request.getGoodsDTO().getFeeDTO();
            compareField(differences, "fee", order.getFee(), feeDTO.getFee(), uid, skuId);
            compareField(differences, "realFee", order.getRealFee(), feeDTO.getRealFee(), uid, skuId);
            compareField(differences, "couponFee", order.getCouponFee(), feeDTO.getCouponFee(), uid, skuId);
            compareField(differences, "couponSettlementFee", order.getCouponSettlementFee(), feeDTO.getCouponSettlementFee(), uid, skuId);
            compareField(differences, "originalPrice", order.getOriginalPrice(), feeDTO.getOriginalPrice(), uid, skuId);
            compareField(differences, "settlementFee", order.getSettlementFee(), feeDTO.getSettlementFee(), uid, skuId);
        } else {
            differences.put("feeDTO", "Missing in OrderCreationRequest");
        }
        
        // 商品信息比对
        if (request.getGoodsDTO() != null) {
            compareField(differences, "skuId", order.getSkuId(), request.getGoodsDTO().getSkuId(), uid, skuId);
            compareField(differences, "skuAmount", order.getSkuAmount(), request.getGoodsDTO().getSkuAmount(), uid, skuId);
            compareField(differences, "type", order.getType(), request.getGoodsDTO().getType(), uid, skuId);
            compareField(differences, "orderName", order.getName(), request.getGoodsDTO().getOrderName(), uid, skuId);
        } else {
            differences.put("goodsDTO", "Missing in OrderCreationRequest");
        }
        
        // 支付信息比对
        if (request.getPaymentDTO() != null) {
            compareField(differences, "payType", order.getPayType(), request.getPaymentDTO().getPayType(), uid, skuId);
            compareField(differences, "renewType", order.getRenewType(), request.getPaymentDTO().getRenewType(), uid, skuId);
        } else {
            differences.put("paymentDTO", "Missing in OrderCreationRequest");
        }
        
        // 统计信息比对
        if (request.getStatisticsDTO() != null) {
            compareField(differences, "channel", order.getChannel(), request.getStatisticsDTO().getChannel(), uid, skuId);
            compareField(differences, "fv", order.getFv(), request.getStatisticsDTO().getFv(), uid, skuId);
            compareField(differences, "platform", order.getPlatform(), request.getStatisticsDTO().getPlatform(), uid, skuId);
            compareField(differences, "frVersion", order.getFrVersion(), request.getStatisticsDTO().getFrVersion(), uid, skuId);
            compareField(differences, "partner", order.getPartner(), request.getStatisticsDTO().getPartner(), uid, skuId);
            compareField(differences, "gateway", order.getGateway(), request.getStatisticsDTO().getGateway(), uid, skuId);
        } else {
            differences.put("statisticsDTO", "Missing in OrderCreationRequest");
        }
        diffRefer(order, request.getRefer(), differences, uid, skuId);
        return differences;
    }

    private void diffRefer(Order order, Map requestRefer, Map<String, String> differences, Long uid, String skuId) {
        // Refer属性比对
        Optional<OrderReferDto> orderReferDtoOptional = order.extractReferDto();
        if (orderReferDtoOptional.isPresent() && MapUtils.isNotEmpty(requestRefer)) {
            OrderReferDto orderReferDto = orderReferDtoOptional.get();

            // 1. 比较userAutoRenew - 在refer外部直接比较
            Boolean orderUserAutoRenew = orderReferDto.getUserAutoRenew();
            String requestUserAutoRenew = MapUtils.getString(requestRefer, USER_AUTO_RENEW);
            compareField(differences, "refer." + USER_AUTO_RENEW, orderUserAutoRenew, requestUserAutoRenew, uid, skuId);

            // 2. 比较signOrderCode - 在refer外部直接比较
            String orderSignOrderCode = orderReferDto.getSignOrderCode();
            String requestSignOrderCode = MapUtils.getString(requestRefer, SIGN_ORDER_CODE);
            compareField(differences, "refer." + SIGN_ORDER_CODE, orderSignOrderCode, requestSignOrderCode, uid, skuId);

            // 3. 比较dutTaskType - 在refer外部直接比较
            String orderDutTaskType = orderReferDto.getDutTaskType();
            String requestDutTaskType = MapUtils.getString(requestRefer, DUT_TASK_TYPE);
            compareField(differences, "refer." + DUT_TASK_TYPE, orderDutTaskType, requestDutTaskType, uid, skuId);

            // 4 比较merchantNo
            String merchantNo = orderReferDto.getMerchantNo();
            String requestMerchantNo = MapUtils.getString(requestRefer, MERCHANT_NO);
            compareField(differences, "refer." + MERCHANT_NO, merchantNo, requestMerchantNo, uid, skuId);

            // 4. 比较businessProperty嵌套属性
            Map<String, Object> orderBusinessProperty = orderReferDto.getBusinessProperty();
            if (MapUtils.isNotEmpty(orderBusinessProperty)) {
                LOGGER.info("[compareOrderWithRequest] Comparing businessProperty attributes");

                for (Entry<String, Object> entry : orderBusinessProperty.entrySet()) {
                    String key = entry.getKey();
                    Object orderValue = entry.getValue();

                    // OrderCreationRequest中的businessProperty有前缀，需要去掉前缀比较
                    String requestKey = "businessProperty." + key;
                    String requestValue = MapUtils.getString(requestRefer, requestKey);

                    // 注意: 当orderValue为Boolean类型时可能需要特殊处理
                    if (orderValue == null && requestValue == null) {
                        // 两者都为空,视为相等
                        continue;
                    } else if (orderValue == null) {
                        // Order中缺少该属性
                        differences.put("refer.businessProperty." + key, "Missing in Order, Request value: " + requestValue);
                        EagleMeterReporter.incrOrderPropertyInconsistency(skuId, "refer.businessProperty." + key, requestValue);
                    } else if (requestValue == null) {
                        // Request中缺少该属性
                        differences.put("refer.businessProperty." + key, "Missing in Request, Order value: " + String.valueOf(orderValue));
                        EagleMeterReporter.incrOrderPropertyInconsistency(skuId, "refer.businessProperty." + key, String.valueOf(orderValue));
                    } else {
                        // 两者都不为空,比较值是否相等
                        String orderValueStr = String.valueOf(orderValue);
                        if (!orderValueStr.equals(requestValue)) {
                            differences.put("refer.businessProperty." + key, "Different values - Order: " + orderValueStr + ", Request: " + requestValue);
                            EagleMeterReporter.incrOrderPropertyInconsistency(skuId, "refer.businessProperty." + key, orderValueStr + "&&&" + requestValue);
                        }
                    }
                }
            }
        }
    }

    /**
     * 比较单个字段值
     * @param differences 差异结果集
     * @param fieldName 字段名
     * @param orderValue Order中的值
     * @param requestValue Request中的值
     * @param uid 用户ID
     * @param skuId 商品ID
     */
    private void compareField(Map<String, String> differences, String fieldName, Object orderValue, Object requestValue, Long uid, String skuId) {
        // 如果两者都为null,认为相等
        if (orderValue == null && requestValue == null) {
            return;
        }
        
        // 如果orderValue为null但requestValue不为null
        if (orderValue == null) {
            String requestValueStr = requestValue == null ? "null" : requestValue.toString();
            differences.put(fieldName, "Missing in Order, Request value: " + requestValueStr);
            EagleMeterReporter.incrOrderPropertyInconsistency(skuId, fieldName, requestValueStr);
            return;
        }
        
        // 如果requestValue为null但orderValue不为null
        if (requestValue == null) {
            String orderValueStr;
            if (orderValue instanceof Boolean) {
                orderValueStr = ((Boolean) orderValue).toString();
            } else {
                orderValueStr = orderValue.toString();
            }
            
            differences.put(fieldName, "Missing in Request, Order value: " + orderValueStr);
            EagleMeterReporter.incrOrderPropertyInconsistency(skuId, fieldName, orderValueStr);
            return;
        }
        
        // 如果两者都不为null,比较是否相等
        boolean isEqual = false;
        
        // 特殊处理Boolean类型
        if (orderValue instanceof Boolean && requestValue instanceof String) {
            Boolean boolOrderValue = (Boolean) orderValue;
            String strRequestValue = (String) requestValue;
            isEqual = boolOrderValue.toString().equalsIgnoreCase(strRequestValue);
        } 
        // 特殊处理Number类型
        else if (orderValue instanceof Number && requestValue instanceof String) {
            try {
                if (orderValue instanceof Integer) {
                    isEqual = ((Integer) orderValue).equals(Integer.parseInt((String) requestValue));
                } else if (orderValue instanceof Long) {
                    isEqual = ((Long) orderValue).equals(Long.parseLong((String) requestValue));
                } else {
                    isEqual = orderValue.toString().equals(requestValue.toString());
                }
            } catch (NumberFormatException e) {
                isEqual = false;
            }
        } 
        // 默认比较
        else {
            isEqual = Objects.equals(orderValue.toString(), requestValue.toString());
        }
        
        if (!isEqual) {
            String orderValueStr = (orderValue instanceof Boolean) ? 
                ((Boolean) orderValue).toString() : orderValue.toString();
            String requestValueStr = requestValue.toString();
            
            differences.put(fieldName, "Different values - Order: " + orderValueStr + ", Request: " + requestValueStr);
            EagleMeterReporter.incrOrderPropertyInconsistency(skuId, fieldName, orderValueStr + "&&&" + requestValueStr);
        }
    }
}
