package com.qiyi.boss.service.impl;

import com.qiyi.boss.db.DataSource;
import com.qiyi.boss.db.DataSourceEnum;
import com.qiyi.boss.service.CommonAutorenewDutConfigService;
import com.qiyi.vip.commons.enums.PayChannelEnum;
import com.qiyi.vip.trade.autorenew.domain.CommonAutoRenewDutConfig;
import com.qiyi.vip.trade.autorenew.mapper.CommonAutoRenewDutConfigMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Created by IntelliJ IDEA.
 * <AUTHOR>
 * Date: 2018-04-18
 * Time: 17:15
 */
@Component(value = "commonAutorenewDutConfigService")
public class CommonAutorenewDutConfigManager implements CommonAutorenewDutConfigService {

	@Resource
	private CommonAutoRenewDutConfigMapper commonAutoRenewDutConfigMapper;

	/**
	 * 查询有效代扣配置信息.
	 */
	//@DataSource(DataSourceEnum.SLAVE)
	@Override
	public List<CommonAutoRenewDutConfig> findAll(Integer category, Long vipType, Integer payChannel, Integer status) {
		return commonAutoRenewDutConfigMapper.findAll(category, vipType, payChannel, status);
	}

	/**
	 * 根据id查询代扣配置信息.
	 */
	//@DataSource(DataSourceEnum.SLAVE)
	@Override
	public CommonAutoRenewDutConfig findById(Long id) {
		return commonAutoRenewDutConfigMapper.selectByPrimaryKey(id);
	}

    //@DataSource(DataSourceEnum.SLAVE)
    @Override
    public CommonAutoRenewDutConfig findFirstExcludeFilteredConfigById(Long nextId, PayChannelEnum payChannelEnum) {
        Objects.requireNonNull(nextId, "代扣配置信息查询! id 不能为空.");
        CommonAutoRenewDutConfig commonAutoRenewDutConfig = commonAutoRenewDutConfigMapper.selectByPrimaryKey(nextId);
        while (commonAutoRenewDutConfig != null) {
            if (commonAutoRenewDutConfig.getPayChannel() != payChannelEnum.getValue()) {
                return commonAutoRenewDutConfig;
            }
            if (commonAutoRenewDutConfig.getNextId() == null) {
                return null;
            }
            commonAutoRenewDutConfig = commonAutoRenewDutConfigMapper.selectByPrimaryKey(commonAutoRenewDutConfig.getNextId());
        }
        return null;
    }
}
