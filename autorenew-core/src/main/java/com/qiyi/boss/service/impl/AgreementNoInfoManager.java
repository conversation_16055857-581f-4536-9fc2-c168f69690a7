package com.qiyi.boss.service.impl;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.outerinvoke.CommodityProxy;
import com.qiyi.boss.outerinvoke.result.CommodityInfo;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.mapper.AgreementNoInfoMapper;

import static com.qiyi.boss.outerinvoke.result.CommodityInfo.RIGHTS_TIME_LENGTH_ZERO;
import static com.qiyi.boss.outerinvoke.result.CommodityInfo.SKU_IDENTIFIER_PURE_SIGN;

/**
 * Created at: 2022-05-31
 *
 * <AUTHOR>
 */
@Component
public class AgreementNoInfoManager {

    @Resource
    private AgreementNoInfoMapper agreementNoInfoMapper;

    @Resource
    private CommodityProxy commodityProxy;
    @Resource
    private AgreementTemplateManager agreementTemplateManager;

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementNoInfo_getById", cacheType= CacheType.LOCAL)
    public AgreementNoInfo getById(Integer id) {
        if (id == null) {
            return null;
        }
        return agreementNoInfoMapper.selectByPrimaryKey(id);
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementNoInfo_getByDutType", cacheType= CacheType.LOCAL)
    public List<AgreementNoInfo> getByDutType(Integer dutType) {
        if (dutType == null) {
            return Lists.newArrayList();
        }
        return agreementNoInfoMapper.selectByDutType(dutType);
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementNoInfo_getAgreementNoByTemplateCode", cacheType= CacheType.LOCAL)
    public List<Integer> getAgreementNoByTemplateCode(String templateCode) {
        if (StringUtils.isBlank(templateCode)) {
            return Lists.newArrayList();
        }
        List<AgreementNoInfo> agreementNoInfos = agreementNoInfoMapper.selectByTemplateCode(templateCode);
        if (CollectionUtils.isEmpty(agreementNoInfos)) {
            return Lists.newArrayList();
        }
        return agreementNoInfos.stream().map(AgreementNoInfo::getId).collect(Collectors.toList());
    }

    public List<AgreementNoInfo> getValidByDutType(Integer dutType) {
        AgreementNoInfoManager thisObj = (AgreementNoInfoManager) AopContext.currentProxy();
        return thisObj.getByDutType(dutType).stream()
            .filter(AgreementNoInfo::online)
            .collect(Collectors.toList());
    }

    public List<Integer> getAgreementNoByDutType(Integer dutType) {
        if (dutType == null) {
            return Lists.newArrayList();
        }
        AgreementNoInfoManager thisObj = (AgreementNoInfoManager) AopContext.currentProxy();
        return thisObj.getByDutType(dutType).stream()
            .map(AgreementNoInfo::getId)
            .collect(Collectors.toList());
    }

    /**
     * 切换时长时，获取对应的协议编号
     */
    public AgreementNoInfo getDefaultWhenChangeAmount(Integer dutType, Integer amount, Short priority) {
        AgreementNoInfoManager thisObj = (AgreementNoInfoManager) AopContext.currentProxy();
        return thisObj.getByDutType(dutType).stream()
            .filter(item -> item.getAmount().equals(amount)
                && item.getPriority().equals(priority)
                && item.defaultNo()
                && item.online())
            .max(Comparator.comparing(AgreementNoInfo::getCreateTime))
            .orElse(null);
    }

    public AgreementNoInfo getByDutTypeAndAmount(Integer dutType, Integer amount) {
        AgreementNoInfoManager thisObj = (AgreementNoInfoManager) AopContext.currentProxy();
        return thisObj.getByDutType(dutType).stream()
            .filter(item -> item.getAmount().equals(amount) && item.defaultNo())
            .max(Comparator.comparing(AgreementNoInfo::getCreateTime))
            .orElse(null);
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementNoInfo_getAutoRenewAgreement", cacheType= CacheType.LOCAL)
    public List<AgreementNoInfo> getAutoRenewAgreement(Long sourceVipType, Long vipType, Integer agreementType, Integer amount, Integer payChannel, Short priority) {
        List<AgreementNoInfo> agreementNoInfos = agreementNoInfoMapper.selectAutoRenewAgreement(sourceVipType, vipType, agreementType, amount, payChannel, priority);
        if (CollectionUtils.isEmpty(agreementNoInfos)) {
            return Collections.emptyList();
        }
        return agreementNoInfos;
    }

    public AgreementNoInfo getByAgreementNoOrDutType(Integer agreementNo, Integer dutType, Integer amount) {
        AgreementNoInfoManager thisObj = (AgreementNoInfoManager) AopContext.currentProxy();
        AgreementNoInfo agreementNoInfo = null;
        if (agreementNo != null) {
            agreementNoInfo = thisObj.getById(agreementNo);
        } else {
            agreementNoInfo = thisObj.getByDutTypeAndAmount(dutType, amount);
        }
        return agreementNoInfo;
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementNoInfo_getAutoRenewAgreementWithoutUpgrade", cacheType= CacheType.LOCAL)
    public AgreementNoInfo getAutoRenewAgreementWithoutUpgrade(Long vipType, Integer amount, Integer payChannel, Short priority) {
        return agreementNoInfoMapper.selectAutoRenewAgreementNoWithoutUpgrade(vipType, amount, payChannel, priority);
    }

    public boolean pureSignCommonAutoRenew(AutorenewRequest autorenewRequest) {
        String skuId = autorenewRequest.getSkuId();
        Integer dutType = autorenewRequest.getDutType();
        AgreementNoInfo agreementNoInfo = this.getById(dutType);
        if (!autorenewRequest.isAutoRenewRequest()) {
            return false;
        }
        if (agreementNoInfo == null || !AgreementTypeEnum.commonAutoRenew(agreementNoInfo.getType())) {
            return false;
        }
        if (StringUtils.isBlank(skuId)) {
            return false;
        }
        CommodityInfo commodityInfo = commodityProxy.queryCommodity(skuId);
        if (commodityInfo == null) {
            return false;
        }
        boolean pureSignSku = ObjectUtils.equals(commodityInfo.getSkuIdentifier(), SKU_IDENTIFIER_PURE_SIGN)
            && ObjectUtils.equals(commodityInfo.getRightsTimeLength(), RIGHTS_TIME_LENGTH_ZERO);
        return pureSignSku || Objects.equals(commodityInfo.getSkuIdentifier(), CommodityInfo.SKU_IDENTIFIER_EXCHANGE_UPGRADE);
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "skuId_getSkuIdByAgreementNo", cacheType= CacheType.LOCAL)
    public String getSkuIdByAgreementNo(Integer agreementNo) {
        AgreementNoInfo agreementNoInfo = agreementNoInfoMapper.selectByPrimaryKey(agreementNo);
        if (agreementNoInfo == null) {
            return null;
        }
        AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
        return agreementTemplate != null ? agreementTemplate.getSkuId() : null;
    }

    public String getSkuIdByDutTypeAndAmount(Integer dutType, Integer amount) {
        AgreementNoInfo agreementNoInfo = getByDutTypeAndAmount(dutType, amount);
        if (agreementNoInfo == null) {
            return null;
        }
        AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
        return agreementTemplate != null ? agreementTemplate.getSkuId() : null;
    }

}
