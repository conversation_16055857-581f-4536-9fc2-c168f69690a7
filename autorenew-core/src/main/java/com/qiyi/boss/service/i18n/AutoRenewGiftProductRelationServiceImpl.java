package com.qiyi.boss.service.i18n;

import com.qiyi.boss.service.impl.QiYueProductNewService;

import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * Created by IntelliJ IDEA.
 * <AUTHOR>
 * Date: 2018-04-18
 * Time: 17:15
 */
@Component(value = "autoRenewGiftProductRelationService")
public class AutoRenewGiftProductRelationServiceImpl implements AutoRenewGiftProductRelationService {

    @Resource
    private QiYueProductNewService qiYueProductNewService;

    @Override
	public String getActiveRelation(String sourceProductCode){
        Objects.requireNonNull(sourceProductCode, "sourceProductCode不可能为空");
        return qiYueProductNewService.selectRelatedDayProductCode(sourceProductCode);
    }
}
