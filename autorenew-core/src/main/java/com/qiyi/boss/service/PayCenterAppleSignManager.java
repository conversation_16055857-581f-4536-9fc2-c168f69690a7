package com.qiyi.boss.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import com.qiyi.boss.outerinvoke.PayCenterApi;
import com.qiyi.boss.outerinvoke.result.PayCenterAppleDutInfoResult;

/**
 * Created at: 2022-12-16
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PayCenterAppleSignManager {

    @Resource
    private PayCenterApi payCenterApi;

    public PayCenterAppleDutInfoResult getSpecifiedSignInfo(Long userId, String appId) {
        List<PayCenterAppleDutInfoResult> userAppleSignInfo = payCenterApi.getUserAppleSignInfo(userId);
        if (CollectionUtils.isEmpty(userAppleSignInfo)) {
            return null;
        }
        PayCenterAppleDutInfoResult signInfo = userAppleSignInfo.stream()
            .filter(item -> item.getProductId().equals(appId))
            .findFirst()
            .orElse(null);
        if (signInfo == null) {
            return null;
        }
        return signInfo;
    }

}
