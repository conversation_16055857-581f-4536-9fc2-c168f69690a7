package com.qiyi.boss.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.qiyi.boss.Constants;
import com.qiyi.boss.async.queue.AsyncTaskFactory;
import com.qiyi.boss.async.task.AbstractTask;
import com.qiyi.boss.async.task.PayCenterAccountUnbindTask;
import com.qiyi.boss.async.task.SyncVipInfoToDutTask;
import com.qiyi.boss.autorenew.dto.OpenDutSetLogDesp;
import com.qiyi.boss.component.AgreementHandler;
import com.qiyi.boss.component.AgreementHandlerFactory;
import com.qiyi.boss.component.AgreementPureSignComponent;
import com.qiyi.boss.dto.AgreementCancelReqParam;
import com.qiyi.boss.dto.AgreementListQueryParam;
import com.qiyi.boss.dto.AgreementOptDto;
import com.qiyi.boss.dto.AgreementQueryParam;
import com.qiyi.boss.dto.AgreementSignReqParam;
import com.qiyi.boss.dto.AgreementSignResp;
import com.qiyi.boss.dto.OpenAutoRenewOptDto;
import com.qiyi.boss.dto.PayCenterPureSignResult;
import com.qiyi.boss.dto.UserAgreementRespDto;
import com.qiyi.boss.dto.UserAgreementSimpleInfoReqParam;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.BindStatusEnum;
import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.enums.PricingStrategyEnum;
import com.qiyi.boss.exception.BizException;
import com.qiyi.boss.model.AgreementPureSignContext;
import com.qiyi.boss.model.BaseResponse.CodeEnum;
import com.qiyi.boss.model.RenewPriceDto;
import com.qiyi.boss.model.UserAgreementContext;
import com.qiyi.boss.model.UserInfo;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.outerinvoke.PayCenterApi;
import com.qiyi.boss.service.AgreementOperateService;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.NumberFormatUtils;
import com.qiyi.boss.utils.SignKeyGenerator;
import com.qiyi.boss.utils.TipsUtils;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.dao.UserAgreementMapper;
import com.qiyi.vip.trade.autorenew.domain.AgreementMaterial;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTempPayType;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.AsyncTask;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewUpgradeConfig;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.SimplifiedUserAgreementInfo;
import com.qiyi.vip.trade.qiyue.domain.QiYueProductNew;

/**
 * Created at: 2021-06-22
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AgreementOperateServiceImpl implements AgreementOperateService {

    @Resource
    QiYueProductNewService productNewService;
    @Resource
    UserAgreementService userAgreementService;
    @Resource
    AgreementTemplateManager agreementTemplateManager;
    @Resource
    AgreementNoInfoManager agreementNoInfoManager;
    @Resource
    UserAgreementMapper userAgreementMapper;
    @Resource
    PayCenterApi payCenterApi;
    @Resource
    AgreementPureSignComponent agreementPureSignComponent;
    @Resource
    SignDetailUIHandlerFactory signDetailUIHandlerFactory;
    @Resource
    AgreementHandlerFactory agreementHandlerFactory;
    @Resource
    DutPriceActServiceImpl dutPriceActService;
    @Resource
    AutoRenewUpgradeConfigManager autoRenewUpgradeConfigManager;

    @Override
    public AgreementSignResp pureSign(UserInfo userInfo, AgreementSignReqParam param) {
        Long userId = param.getUid();
        Integer agreementNo = param.getAgreementNo();
        AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
        if (agreementNoInfo == null || agreementNoInfo.offline()) {
            throw BizException.newParamException("协议编号不存在");
        }
        if (!agreementNoInfo.supportPureSign()) {
            throw BizException.newParamException("协议不支持纯签约");
        }
        List<AgreementTempPayType> payTypeList = agreementTemplateManager.getPayTypeInfo(agreementNo, agreementNoInfo.getPayChannel());
        if (CollectionUtils.isEmpty(payTypeList)) {
            throw BizException.newParamException("不支持的支付渠道");
        }
        DutUserNew dutUserNew = userAgreementService.getEffectiveByAgreementNo(userId, agreementNo);
        if (dutUserNew != null) {
            throw BizException.newException(CodeEnum.ALREADY_SIGNED);
        }
        Integer agreementType = agreementNoInfo.getType();
        if (AgreementTypeEnum.ZHIMA_GO.getValue() == agreementType) {
            List<DutUserNew> dutUserNews = userAgreementService.getEffectiveByAgreementType(userId, agreementType);
            if (CollectionUtils.isNotEmpty(dutUserNews)) {
                throw BizException.newException(CodeEnum.ALREADY_SIGNED);
            }
        }

        AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
        AgreementPureSignContext pureSignContext = new AgreementPureSignContext(agreementNoInfo, agreementTemplate, payTypeList.get(0));
        String pureSignUrl = agreementPureSignComponent.genPureSignUrl(pureSignContext, param, UserInfo.getSubParamDisplayAccount(userInfo));
        PayCenterPureSignResult payCenterPureSignResult = payCenterApi.doDutBind(pureSignUrl);
        AgreementSignResp agreementSignResp = AgreementSignResp.buildFromPayCenterResult(payCenterPureSignResult);
        if (agreementSignResp == null || agreementSignResp.invalidResp()) {
            throw BizException.newException(CodeEnum.DUT_BIND_EXCEPTION);
        }
        return agreementSignResp;
    }

    @Override
    public DutUserNew openByPureSign(VipUser vipUser, AgreementOptDto agreementOptDto) {
        AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(agreementOptDto.getAgreementNo());
        if (agreementNoInfo == null) {
            log.error("openByPureSign occurred exception[agreementNoInfo not exists], params: {}", agreementOptDto);
            throw BizException.newSystemException("协议编号不存在");
        }
        AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
        if (agreementTemplate == null) {
            log.error("openByPureSign occurred exception[agreementTemplate not exists], params: {}", agreementOptDto);
            throw BizException.newSystemException("协议模板不存在");
        }
        if (!agreementOptDto.getDutType().equals(agreementNoInfo.getDutType())) {
            log.error("openByPureSign occurred exception[dutType and agreementNo not match], params: {}", agreementOptDto);
            throw BizException.newSystemException("dutType和agreementNo不匹配");
        }

        UserAgreementContext userAgreementContext = new UserAgreementContext(agreementNoInfo, agreementTemplate);
        DutUserNew dutUserNew = userAgreementService.openByPureSign(vipUser, userAgreementContext, agreementOptDto);
        AgreementTypeEnum agreementType = AgreementTypeEnum.valueOf(agreementTemplate.getType());
        agreementHandlerFactory.getHandler(agreementType).doSomethingAfterPureSign(dutUserNew);
        return dutUserNew;
    }

    @Override
    public DutUserNew openBySignPay(AutorenewRequest autoRenewRequest, AgreementNoInfo agreementNoInfo) {
        AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
        if (agreementTemplate == null) {
            log.error("[agreementTemplate not found] [Params:{}]", autoRenewRequest);
            return null;
        }
        AgreementTypeEnum agreementType = AgreementTypeEnum.valueOf(agreementTemplate.getType());
        DutUserNew dutUserNew = agreementHandlerFactory.getHandler(agreementType).openBySignPay(autoRenewRequest, agreementNoInfo, agreementTemplate);
        if (CloudConfigUtil.needUnbindBySignPay(autoRenewRequest.getPayChannel()) && CloudConfigUtil.needUnbindWhenCancel(autoRenewRequest.getUserId())) {
            try {
                PayCenterAccountUnbindTask unbindTask = PayCenterAccountUnbindTask.buildBySignPay(autoRenewRequest.getUserId(),
                    autoRenewRequest.getVipType(), agreementType.getValue(), autoRenewRequest.getPayChannel(), agreementNoInfo.getDutType());
                if (autoRenewRequest.isAppleOrder()) {
                    unbindTask.setBundleID(autoRenewRequest.getBundleID());
                    unbindTask.setPayType(autoRenewRequest.getPayType());
                }
                AsyncTaskFactory.getInstance().insertIntoDB(unbindTask, 0, AsyncTask.Task_PRIORITY_2, DateHelper.getDateTime());
            } catch (Exception e) {
                log.error("after signPay save PayCenterAccountUnbindTask exception, autoRenewRequest:{}", autoRenewRequest, e);
            }
        }
        return dutUserNew;
    }

    @Override
    public DutUserNew openAutoRenew(VipUser vipUser, AgreementNoInfo agreementNoInfo, OpenAutoRenewOptDto openAutoRenewOptDto) {
        Long userId = vipUser.getId();
        Long vipType = vipUser.getTypeId();
        Long sourceVipType = openAutoRenewOptDto.getSourceVipType();
        Integer agreementNo = agreementNoInfo.getId();
        Integer agreementType = openAutoRenewOptDto.getAgreementType();
        DutUserNew dutUserNew = userAgreementService.getByAgreementNoOrDutType(userId, agreementNo, agreementNoInfo.getDutType(), vipType);
        if (dutUserNew == null) {
            dutUserNew = new DutUserNew();
            dutUserNew.setUserId(userId);
            dutUserNew.setAgreementType(agreementType);
            dutUserNew.setOperateTime(DateHelper.getDateTime());
        }
        dutUserNew.setAgreementNo(agreementNo);
        dutUserNew.setType(agreementNoInfo.getDutType());
        dutUserNew.setSignKey(SignKeyGenerator.getSignKey(userId, dutUserNew.getAgreementType()));
        dutUserNew.setStatus(BindStatusEnum.BIND.getValue());
        dutUserNew.setAutoRenew(DutUserNew.RENEW_AUTO);
        dutUserNew.setAmount(openAutoRenewOptDto.getAmount());
        dutUserNew.setSignTime(DateHelper.getDateTime());
        dutUserNew.setUpdateTime(DateHelper.getDateTime());
        dutUserNew.setVipType(vipType);
        Integer vipCategory = vipType == Constants.VIP_USER_TW
            ? Constants.VIP_CATEGORY_TAIWAN : Constants.VIP_CATEGORY_MAINLAND;
        dutUserNew.setVipCategory(vipCategory);
        //设置升级信息
        if (sourceVipType != null) {
            dutUserNew.setSourceVipType(sourceVipType);
            AutoRenewUpgradeConfig autoRenewUpgradeConfig = autoRenewUpgradeConfigManager.getByVipType(sourceVipType, vipType, agreementNoInfo.getPriority());
            if (autoRenewUpgradeConfig != null) {
                dutUserNew.setPcode(autoRenewUpgradeConfig.getMonthProductCode());
                dutUserNew.setRenewPrice(autoRenewUpgradeConfig.getMonthRenewPrice());
            }
        }
        dutUserNew.setExt(null);
        dutUserNew.setOrderCode(openAutoRenewOptDto.getOrderCode());
        dutUserNew.setPlatform(openAutoRenewOptDto.getPlatformId());
        dutUserNew.setPlatformCode(openAutoRenewOptDto.getPlatformCode());
        dutUserNew.setRenewPrice(openAutoRenewOptDto.getRenewPrice());
        dutUserNew.setContractPrice(openAutoRenewOptDto.getRenewPrice());

        if (vipUser.getDeadline() != null) {
            dutUserNew.setDeadline(vipUser.getDeadline());
        } else {
            //增加开通自动续费同步会员信息逻辑
            AbstractTask task = new SyncVipInfoToDutTask(userId, vipType, vipCategory, 0);
            AsyncTaskFactory.getInstance().insertIntoDB(task, AsyncTask.POOL_TYPE_NOTIFY_RENEW_SET_INFO, AsyncTask.Task_PRIORITY_1, Timestamp.from(Instant.now().plusSeconds(20)));
        }
        // 更新活动信息
        dutPriceActService.updateUserActInfo(dutUserNew, dutUserNew.getRenewPrice());

        String openSetLogDesp = OpenDutSetLogDesp.buildOpenSetLogDesp(dutUserNew, openAutoRenewOptDto.getFc(), openAutoRenewOptDto.getFv(),
            openAutoRenewOptDto.getThirdUid(), openAutoRenewOptDto.getOperateScene(), agreementNoInfo.getPayChannel(), null);
        DutRenewSetLog openSetLog = DutRenewSetLog.buildSetLog(dutUserNew, openSetLogDesp, OperateTypeEnum.OPEN, dutUserNew.getSignTime());
        userAgreementService.saveAutoRenew(dutUserNew, openSetLog);
        return dutUserNew;
    }

    @Override
    public boolean cancel(AgreementCancelReqParam param) {
        Integer agreementNo = param.getAgreementNo();
        AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
        if (agreementNoInfo == null) {
            throw BizException.newParamException("协议编号不存在");
        }
        AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
        if (agreementTemplate == null || agreementTemplate.invalid()) {
            throw BizException.newParamException("协议模板不存在");
        }
        DutUserNew dutUserNew = userAgreementService.getEffectiveByAgreementNo(param.getUid(), agreementNo);
        if (dutUserNew == null || dutUserNew.notValidStatus()) {
            throw BizException.newParamException("用户没有生效中的协议");
        }
        AgreementOptDto agreementOptDto = AgreementOptDto.buildCancelOptParam(param);
        batchCancelDutUserNews(agreementNo, agreementOptDto, Collections.singletonList(dutUserNew));
        return true;
    }

    @Override
    public void batchCancelDutUserNews(Integer agreementNo, AgreementOptDto agreementOptDto, List<DutUserNew> dutUserNews) {
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return;
        }
        for (DutUserNew dutUserNew : dutUserNews) {
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
            agreementOptDto.setDutType(agreementNoInfo.getDutType());
            agreementOptDto.setPayChannel(agreementNoInfo.getPayChannel());
            AgreementTypeEnum agreementType = AgreementTypeEnum.valueOf(agreementNoInfo.getType());
            AgreementHandler agreementHandler = agreementHandlerFactory.getHandler(agreementType);
            agreementHandler.cancelAgreement(dutUserNew, agreementNoInfo, agreementOptDto);
        }
    }

    @Override
    public boolean cancelSpecifiedDutType(UserAgreementContext userAgreementContext, AgreementOptDto agreementOptDto) {
        AgreementNoInfo agreementNoInfo = userAgreementContext.getAgreementNoInfo();
        AgreementTypeEnum agreementType = AgreementTypeEnum.valueOf(agreementNoInfo.getType());
        AgreementHandler agreementHandler = agreementHandlerFactory.getHandler(agreementType);
        agreementHandler.cancelAgreement(userAgreementContext.getDutUserNew(), agreementNoInfo, agreementOptDto);
        return true;
    }

    @Override
    public UserAgreementRespDto querySignInfo(AgreementQueryParam param) {
        AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(param.getAgreementNo());
        if (agreementNoInfo == null) {
            throw BizException.newParamException("协议编号不存在");
        }
        AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
        if (agreementTemplate == null) {
            throw BizException.newParamException("协议模板不存在");
        }

        Long uid = param.getUid();
        QiYueProductNew qiYueProductNew = productNewService.getProductByCode(agreementTemplate.getPid());
        String productName = qiYueProductNew != null ? qiYueProductNew.getName() : null;
        Optional<DutUserNew> latestUserAgreementOptional = userAgreementService.getUserLatestAgreement(uid, param.getAgreementNo());
        if (!latestUserAgreementOptional.isPresent()) {
            log.info("querySignInfo-user is new sign!userId:{},agreementNo:{}", uid, param.getAgreementNo());
            return UserAgreementRespDto.createUnSignedAgreementRespDto(agreementNoInfo.getId(), agreementTemplate, uid, productName);
        }
        AgreementTypeEnum agreementType = AgreementTypeEnum.valueOf(agreementTemplate.getType());
        SignDetailUIHandler uiHandler = signDetailUIHandlerFactory.getDetailUIHandler(agreementType);
        return uiHandler.constructRespDto(agreementNoInfo, agreementTemplate, latestUserAgreementOptional.get(), qiYueProductNew);
    }

    @Override
    public SimplifiedUserAgreementInfo getSimpleInfo(UserAgreementSimpleInfoReqParam param) {
        List<DutUserNew> dutUserNews = userAgreementService.getByAgreementNo(param.getUid(), param.getAgreementNo(), null);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return null;
        }

        DutUserNew dutUserNew = dutUserNews.stream().filter(DutUserNew::notInvalidAndFinished)
            .findFirst().orElse(dutUserNews.get(0));
        SimplifiedUserAgreementInfo simpleInfo = new SimplifiedUserAgreementInfo(dutUserNew);
        AgreementMaterial agreementMaterial = agreementTemplateManager.getMaterial(param.getAgreementNo());
        if (agreementMaterial != null) {
            simpleInfo.setDescription(getAgreementDescription(dutUserNew, agreementMaterial));
            simpleInfo.setDetailUrl(agreementMaterial.getDetailUrl());
        }
        AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(param.getAgreementNo());
        if (agreementNoInfo != null) {
            simpleInfo.setAgreementName(agreementNoInfo.getName());
        }
        return simpleInfo;
    }

    private String getAgreementDescription(DutUserNew dutUserNew, AgreementMaterial agreementMaterial) {
        if (agreementMaterial.getDescription() != null) {
            return agreementMaterial.getDescription();
        }
        if (dutUserNew.getAgreementType() != AgreementTypeEnum.AUTO_RENEW.getValue() || dutUserNew.getSourceVipType() != null) {
            return null;
        }
        Integer renewPrice = dutUserNew.getRenewPrice();
        if (renewPrice == null) {
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(dutUserNew.getAgreementNo());
            AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
            PricingStrategyEnum pricingStrategyEnum = PricingStrategyEnum.valueOf(agreementTemplate.getPricingStrategy());
            AgreementHandler agreementHandler = agreementHandlerFactory.getHandler(AgreementTypeEnum.valueOf(dutUserNew.getAgreementType()));
            RenewPriceDto renewPriceDto = agreementHandler.getRenewPrice(dutUserNew, agreementNoInfo, pricingStrategyEnum);
            renewPrice = renewPriceDto.getFee();
        }
        if (renewPrice == null) {
            return null;
        }
        String renewPriceStr = NumberFormatUtils.formatToStr(renewPrice, "#.#");
        return String.format(TipsUtils.AGREEMENT_DESCRIPTION_OF_AUTO_RENEW_TIPS.get(dutUserNew.getAmount()), renewPriceStr);
    }

    @Override
    public List<SimplifiedUserAgreementInfo> getAgreementList(AgreementListQueryParam param) {
        // 查询用户的协议签约状态
        List<DutUserNew> dutUserNewList = userAgreementMapper.selectByAgreementInfo(param.getUid(), param.getAgreementNo(), param.getAgreementTypeList(), param.getAgreementStatusList());
        if (CollectionUtils.isEmpty(dutUserNewList)) {
            return Collections.emptyList();
        }
        return dutUserNewList.stream()
            .map(dutUserNew -> {
                AgreementNoInfo agreementNoInfo = null;
                if (dutUserNew.getAgreementNo() != null) {
                    agreementNoInfo = agreementNoInfoManager.getById(dutUserNew.getAgreementNo());
                } else {
                    agreementNoInfo = agreementNoInfoManager.getByDutTypeAndAmount(dutUserNew.getType(), dutUserNew.getAmount());
                }
                SimplifiedUserAgreementInfo userAgreementInfo = new SimplifiedUserAgreementInfo(dutUserNew);
                if (agreementNoInfo != null) {
                    userAgreementInfo.setAgreementName(agreementNoInfo.getName());
                    userAgreementInfo.setPayChannel(agreementNoInfo.getPayChannel());
                }
                return userAgreementInfo;
            })
            .collect(Collectors.toList());
    }

}
