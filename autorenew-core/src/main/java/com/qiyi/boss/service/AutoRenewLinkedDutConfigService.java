package com.qiyi.boss.service;

import com.qiyi.boss.model.VipUser;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewLinkedDutConfig;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: l<PERSON><PERSON>qiang
 * Date: 2018-6-24
 * Time: 21:29
 */
public interface AutoRenewLinkedDutConfigService {

	/**
	 * 查询有效代扣配置信息.
	 * @param jobType 任务类型.
	 * @param vipType 会员类型.
	 * @param payChannel 代扣渠道类型.
	 * @param category 代扣分类.
	 * @return listDutTypes
	 */
	AutoRenewLinkedDutConfig find(Integer jobType, Long vipType, Long sourceVipType, Integer agreementType, Integer payChannel, String category);

	/**
	 * 根据id查询代扣配置信息.
	 * @param id 配置id
	 * @return autoRenewLinkedDutConfig
	 */
	AutoRenewLinkedDutConfig findById(Long id);

	/**
	 * 根据当前的代扣配置与代扣类型查找下次对应的支付宝链式代扣配置
	 * @param currentLinkedDutConfig
	 * @param dutBindType
	 * @param vipUser
	 * @return
	 */

}
