package com.qiyi.boss.service;


import com.qiyi.boss.autorenew.dto.PasswordFreeRespDto;
import com.qiyi.vip.trade.autorenew.domain.UserPasswordFree;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2020/9/27
 */
public interface UserPasswordFreeService {
    PasswordFreeRespDto getList(Long userId, List<Integer> dutTypeList);

    void saveOrUpdate(UserPasswordFree userPasswordFree);

    PasswordFreeRespDto getOpenedList(Long userId);

    void closePasswordFree(Long userId, Integer dutType, String source);

    List<UserPasswordFree> getUserPasswordFree(Long userId, Integer payChannel, Integer status);
}
