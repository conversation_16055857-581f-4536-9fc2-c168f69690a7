package com.qiyi.boss.service.impl;

import com.qiyi.boss.autorenew.dto.IntroductoryActDto;
import com.qiyi.boss.db.DataSource;
import com.qiyi.boss.db.DataSourceEnum;
import com.qiyi.vip.trade.autorenew.domain.IntroductoryPriceAct;
import com.qiyi.vip.trade.autorenew.mapper.IntroductoryPriceActMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> sip peng
 * @date : 2018/3/19
 */
@Component
public class IntroductoryPriceActManager {

    @Resource
    private IntroductoryPriceActMapper introductoryPriceActMapper;

    @Resource
    private AutoRenewDutTypeManager autoRenewDutTypeManager;

    /**
     * 实际上应该只会命中一个活动
     */
    //@DataSource(DataSourceEnum.SLAVE)
    public Optional<IntroductoryPriceAct> findParticipatedAct(IntroductoryActDto priceActDto) {
        if (priceActDto == null) {
            return Optional.empty();
        }
        List<IntroductoryPriceAct> actlist = introductoryPriceActMapper.findPriceAct(priceActDto);
        if (CollectionUtils.isEmpty(actlist)) {
            return Optional.empty();
        }
        for (IntroductoryPriceAct priceAct : actlist) {
            // 活动支持 的platform listDutTypes
            // 活动支持 的 payType listDutTypes
            if (isSupportPlatformAndPayType(priceAct, priceActDto.getPlatform(), priceActDto.getPayType())) {
                return Optional.ofNullable(priceAct);
            }
        }
        return Optional.empty();
    }

    //@DataSource(DataSourceEnum.SLAVE)
    public boolean isSupportPlatformAndPayType(IntroductoryPriceAct act, String platformCode, Integer payType) {
        if (StringUtils.isBlank(platformCode) || payType == null) {
            return false;
        }
        if (act.getActPeriods() == null || act.getActPrice() == null) {
            return false;
        }
        return act.getSupportPlatformList().contains(platformCode) && act.getSupportPayTypeList().contains(payType);
    }

    public Optional<IntroductoryPriceAct> findValidAct(IntroductoryActDto actDto, Integer dutType) {
        Optional<IntroductoryPriceAct> act = this.findParticipatedAct(actDto);
        if (act.isPresent() && !autoRenewDutTypeManager.dutTypeUnSupportAct(dutType)) {
            if (act.get().getActPeriods() != null && act.get().getActPeriods() > 1) {
                return act;
            }
        }
        return Optional.empty();
    }

}
