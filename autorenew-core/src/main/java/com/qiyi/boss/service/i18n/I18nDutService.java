package com.qiyi.boss.service.i18n;

import com.qiyi.boss.dto.RtdnCancelDetailDto;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

import java.sql.Timestamp;
import java.util.List;

public interface I18nDutService {
    List<DutUserNew> listDutUserNew(Long userId, List<Long> vipType, Integer autoRenewStatus);


    /**
     * 处理取消自动续费RTDN通知
     *
     * @param dutUserNew          用户自动续费信息
     * @param rtdnCancelDetailDto RTDN中用户取消自动续费的详细信息
     * @return true：取消自动续费成功，false：取消自动续费失败
     */
    boolean rtdnCancel(DutUserNew dutUserNew, RtdnCancelDetailDto rtdnCancelDetailDto, Integer payChannel);

    /**
     * 宽限期需求
     * 设计文档：http://wiki.qiyi.domain/pages/viewpage.action?pageId=789746921
     * 此处不对用户的deadline进行处理
     *
     * @param dutUserNew
     * @param appId      谷歌的AppId
     * @param oriOrderId 签约时的谷歌订单号
     * @param interval   可再次享受的时间间隔
     * @return
     */
    boolean gracePeriod(DutUserNew dutUserNew, String appId, String oriOrderId, Integer interval);

    /**
     * 根据userId获取谷歌支付有效签约关系
     *
     * @param userId 用户userId
     * @return 谷歌签约关系
     */
    DutUserNew getGoogleDutUser(Long userId);

    /**
     * 批量修改用户的权益到期时间
     *
     * @param dutUserNews 签约关系
     * @param deadline    权益到期时间
     */
    void batchUpdateDutUserDeadline(List<DutUserNew> dutUserNews, Timestamp deadline);

}
