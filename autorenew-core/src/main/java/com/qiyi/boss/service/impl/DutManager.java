package com.qiyi.boss.service.impl;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import com.qiyi.boss.Constants;
import com.qiyi.boss.async.queue.AsyncTaskFactory;
import com.qiyi.boss.async.task.AbstractTask;
import com.qiyi.boss.async.task.SyncVipInfoToDutTask;
import com.qiyi.boss.autorenew.dto.AddAutoRenewDto;
import com.qiyi.boss.autorenew.dto.AddRenewSetLogDto;
import com.qiyi.boss.autorenew.dto.CancelDutSetLogDesp;
import com.qiyi.boss.autorenew.dto.IntroductoryActDto;
import com.qiyi.boss.autorenew.dto.OpenDutSetLogDesp;
import com.qiyi.boss.autorenew.dto.SaveDutUserDto;
import com.qiyi.boss.autorenew.dto.UpdateUserRenewCountDto;
import com.qiyi.boss.autorenew.enumerate.ActTypeEnum;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.dto.AccountBindInfo;
import com.qiyi.boss.dto.AccountResponse;
import com.qiyi.boss.dto.AccountUnbindReqDto;
import com.qiyi.boss.dto.AppleTicketInfoDto;
import com.qiyi.boss.dto.CancelAutoRenewDto;
import com.qiyi.boss.dto.RtdnCancelDetailDto;
import com.qiyi.boss.dto.UpdateDutTimeReqDto;
import com.qiyi.boss.enums.AgreementStatusEnum;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.BindStatusEnum;
import com.qiyi.boss.exception.BizException;
import com.qiyi.boss.exception.DutRenewLogException;
import com.qiyi.boss.model.BaseResponse.CodeEnum;
import com.qiyi.boss.model.SpecialVipUser;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.outerinvoke.result.PayCenterAppleDutInfoResult;
import com.qiyi.boss.processor.DutProcessor;
import com.qiyi.boss.service.AgreementOperateService;
import com.qiyi.boss.service.AutoRenewService;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.boss.service.DutService;
import com.qiyi.boss.service.DutUserService;
import com.qiyi.boss.service.LockService;
import com.qiyi.boss.service.PayCenterAppleSignManager;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.utils.ApplicationContextUtil;
import com.qiyi.boss.utils.AutoRenewConfig;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.DutAccountApi;
import com.qiyi.boss.utils.DutUserNewUtil;
import com.qiyi.boss.utils.JacksonUtils;
import com.qiyi.boss.utils.RequestUtils;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AsyncTask;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewUpgradeConfig;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.IntroductoryPriceAct;
import com.qiyi.vip.trade.autorenew.domain.Lock;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import com.qiyi.vip.trade.autorenew.domain.SimpleDutUserNew;
import com.qiyi.vip.trade.autorenew.domain.vo.DutUserNewVO;
import com.qiyi.vip.trade.autorenew.repository.DutRenewLogRepository;
import com.qiyi.vip.trade.autorenew.repository.DutUserRepository;
import com.qiyi.vip.trade.autorenew.service.DutRenewLogService;
import com.qiyi.vip.trade.autorenew.service.DutRenewSetLogService;
import com.qiyi.vip.trade.qiyue.domain.AppProduct;
import com.qiyi.vip.trade.qiyue.domain.QiYuePlatform;

import static com.qiyi.boss.autorenew.dto.OpenDutSetLogDesp.TYPE_CHANGE;
import static com.qiyi.boss.constants.AgreementConstants.EXCLUDE_AGREEMENT_TYPES;
import static com.qiyi.boss.utils.DateHelper.MOST_COMMON_PATTERN;

/**
 * 代扣业务管理类
 *
 * <AUTHOR>
 * @version 11-12-20 - 下午3:12
 */
@SuppressWarnings("AlibabaTransactionMustHaveRollback")
@Component(value = "dutService")
public class DutManager implements DutService {

    private static final int OFFSET_ONE_DAY = 1;
    public static final int MORE_THAN_ONE = 2;

    @Resource
    private DutProcessor dutProcessor;

    private static final Logger LOGGER = LoggerFactory.getLogger(Constants.LOG_MODULE_ORDER);

    @Resource
    private IntroductoryPriceActManager introductoryPriceActManager;

    @Resource
    private DutRenewLogRepository dutRenewLogRepository;

    @Resource
    private DutUserRenewStatusManager dutUserRenewStatusManager;

    @Resource
    private LockService lockService;

    @Resource
    private PaymentDutTypeManager paymentDutTypeManager;

    @Resource
    private AppProductManager appProductManager;

    @Resource
    private AutoRenewUpgradeConfigManager autoRenewUpgradeConfigManager;

    @Resource
    private AutoRenewDutTypeManager autoRenewDutTypeManager;

    @Resource
    private AutoRenewVipTypeUpgradeConfigManager autoRenewVipTypeUpgradeConfigManager;

    @Resource(name = "dutAccountApi")
    private DutAccountApi accountApi;

    @Resource
    private DutRenewSetLogService dutRenewSetLogService;

    @Resource
    private DutUserRepository dutUserRepository;

    @Resource
    private DutUserService dutUserService;
    @Resource
    private UserAgreementService userAgreementService;

    @Resource
    private DutRenewLogService dutRenewLogService;

    @Resource
    private DutPriceActServiceImpl dutPriceActService;
    @Resource
    private AutoRenewService autoRenewService;
    @Resource
    private PayCenterAppleSignManager payCenterAppleSignManager;
    @Resource
    private AgreementOperateService agreementOperateService;
    @Resource
    private AgreementManager agreementManager;
    @Resource
    private AgreementNoInfoManager agreementNoInfoManager;

    /**
     * 多渠道增加自动续费
     */
    @Override
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    public List<Integer> addAutoRenews(Long uid, Integer agreementType, Integer[] types, VipUser vipUser, String fc, String fv, String operateScene) {
        List<Integer> bindTypes = getAutoRenewDutTypeBindList(uid);
        //过滤不支持的代扣渠道
        bindTypes = autoRenewDutTypeManager.filterUnSupportDutTypes(bindTypes, vipUser.getTypeId(), agreementType);
        //bind type: 1:支付宝，3：百度钱包，4：微信，5：微信APP
        //根据用户绑定的渠道，可以开通多个渠道的自动续费，扣费的时候，优先根据最后开通的渠道进行扣费
        List<Integer> supportDirectOpenDutTypes = getSupportDirectOpenDutTypes(uid, vipUser.getTypeId(), agreementType);
        AutoRenewDutType autoRenewDutType;
        for (Integer type : types) {

            if (CollectionUtils.isNotEmpty(supportDirectOpenDutTypes) && !supportDirectOpenDutTypes.contains(type)) {
                continue;
            }

            autoRenewDutType = autoRenewDutTypeManager.getByDutType(type);
            if (autoRenewDutType == null || autoRenewDutType.expired()) {
                continue;
            }

            if (bindTypes != null && bindTypes.contains(type)) {
                Integer amount = dutUserService.getAmountFromDutUserNew(uid, type, vipUser.getTypeId(), agreementType);
                Integer renewPrice = paymentDutTypeManager.getRenewPrice(vipUser.getTypeId(), amount, null, type, uid);
                //增加或更改自动续费用户记录
                AddAutoRenewDto addAutoRenewDto = AddAutoRenewDto.builder()
                        .userId(uid)
                        .dutType(type)
                        .renewPrice(renewPrice)
                        .platformId(Constants.PLATFORM_PC_ID)
                        .platformCode(Constants.PLATFORM_DEFAULT_CODE)
                        .vipUser(vipUser)
                        .amount(amount)
                        .fc(fc)
                        .fv(fv)
                        .operateScene(operateScene)
                        .payChannel(autoRenewDutType.getPayChannel())
                        .build();
                ApplicationContextUtil.getBean(DutManager.class).addAutoRenewNew(addAutoRenewDto);
            } else {
                LOGGER.info("[module:addAutoRenews][uid{}] [type{}] not bind", new Object[]{uid, type});
            }
        }
        return bindTypes;
    }

    /**
     * 保存自动续费记录
     *
     * @param saveDutUserDto {@link SaveDutUserDto}
     * @return true: 自动续费状态改变， false：自动续费状态没变
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveNew(SaveDutUserDto saveDutUserDto) {
        //todo 增加获取vip信息逻辑
        DutUserNew dutUserNew;
        Long vipType;
        AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(saveDutUserDto.getType());
        if (autoRenewDutType == null) {
            return false;
        }
        if (null != saveDutUserDto.getVipUser() && null != saveDutUserDto.getVipUser().getTypeId()) {
            vipType = saveDutUserDto.getVipUser().getTypeId();
        } else {
            vipType = autoRenewDutType.getVipType();
        }
        dutUserNew = userAgreementService.getByDutTypeAndVipType(saveDutUserDto.getUserId(), saveDutUserDto.getType(), vipType);
        Integer autoRenewStatus = null;
        if (dutUserNew == null) {
            dutUserNew = new DutUserNew();
            dutUserNew.setUserId(saveDutUserDto.getUserId());
            dutUserNew.setType(saveDutUserDto.getType());
            dutUserNew.setAgreementType(autoRenewDutType.getAgreementType());
            LOGGER.info("[module:pay] [action:alipayDut] [step:save] [userId:{}] [type:{}] [dutUserNew not exists]",
                    saveDutUserDto.getUserId(), saveDutUserDto.getType());
        } else {
            autoRenewStatus = dutUserNew.getAutoRenew();
            LOGGER.info("[module:pay] [action:alipayDut] [step:save] " + dutUserNew.toString());
        }

        //设置自动续费用户的属性
        dutUserNew.setAutoRenew(saveDutUserDto.getAutoRenew());
        dutUserNew.setSource(saveDutUserDto.getSource());
        dutUserNew.setStatus(saveDutUserDto.getStatus());
        dutUserNew.setOperateTime(DateHelper.getDateTime());
        dutUserNew.setUpdateTime(DateHelper.getDateTime());
        dutUserNew.setPlatform(saveDutUserDto.getPlatform());
        dutUserNew.setPlatformCode(saveDutUserDto.getPlatformCode());
        if (saveDutUserDto.getAmount() != null) {
            dutUserNew.setAmount(saveDutUserDto.getAmount());
        }
        // 保存自动续费签约价
        if (dutUserNew.getRenewPrice() == null) {
            dutUserNew.setRenewPrice(saveDutUserDto.getRenewPrice());
        }
        if (saveDutUserDto.getReturnUrl() != null) {
            dutUserNew.setReturnUrl(saveDutUserDto.getReturnUrl());
        }
        Timestamp deadline = null;
        if (null != saveDutUserDto.getVipUser()) {
            deadline = saveDutUserDto.getVipUser().getDeadline();
            dutUserNew.setDeadline(deadline);
            dutUserNew.setVipType(vipType);
            dutUserNew.setVipCategory(Constants.VIP_CATEGORY_MAINLAND);
        }
        // 更新活动信息
        dutPriceActService.updateUserActInfo(dutUserNew, dutUserNew.getRenewPrice());
        // 处理重复开通的场景
        autoRenewService.doRepeatOpeningOld(saveDutUserDto.getVipUser(), autoRenewDutType, saveDutUserDto.getAmount(), saveDutUserDto.getFc(), saveDutUserDto.getFv());
//        doRepeatOpening(vipType, saveDutUserDto.getUserId(), saveDutUserDto.getType(), saveDutUserDto.getAmount(),
//                saveDutUserDto.getFc(), saveDutUserDto.getFv(), deadline);

        dutUserRepository.save(dutUserNew);

        LOGGER.info("[module:pay] [action:alipayDut] [step:save] [bind success] [id:{}] [userId:{}] [type:{}] [status:{}] [autoRenew:{}]",
                dutUserNew.getId(), saveDutUserDto.getUserId(), saveDutUserDto.getType(), saveDutUserDto.getStatus(), saveDutUserDto.getAutoRenew());
        // 如果自动续费状态改变了，则更新自动续费日志 ，并返回true

        //增加开通自动续费同步会员信息逻辑
        if (null == saveDutUserDto.getVipUser()) {
            AbstractTask task = new SyncVipInfoToDutTask(saveDutUserDto.getUserId(), dutUserNew.getVipType(), Constants.VIP_CATEGORY_MAINLAND, 0);
            AsyncTaskFactory.getInstance()
                    .insertIntoDB(task, AsyncTask.POOL_TYPE_NOTIFY_RENEW_SET_INFO, AsyncTask.Task_PRIORITY_1,
                            DateHelper.getCurrentTime());
        }

        if (shouldSaveAutoRenewSetLog(autoRenewStatus, saveDutUserDto.getAutoRenew())) {
            DutRenewSetLog dutRenewSetLog = new DutRenewSetLog();
            dutRenewSetLog.setUserId(saveDutUserDto.getUserId());
            dutRenewSetLog.setType(saveDutUserDto.getType());
            dutRenewSetLog.setOperator(saveDutUserDto.getAutoRenew());
            dutRenewSetLog.setPlatform(saveDutUserDto.getPlatform());
            dutRenewSetLog.setPlatformCode(saveDutUserDto.getPlatformCode());
            Integer amount = null == dutUserNew.getAmount()
                    ? Constants.AMOUNT_OF_COMMON_AUTORENEW
                    : dutUserNew.getAmount();
            dutRenewSetLog.setAmount(amount);
            dutRenewSetLog.setVipType(vipType);
            dutRenewSetLog.setOperateTime(DateHelper.getDateTime());
            dutRenewSetLog.setDescription(OpenDutSetLogDesp.buildOpenSetLogDespWhenDirectOpen(dutUserNew,
                    saveDutUserDto.getFc(), saveDutUserDto.getFv(), null, saveDutUserDto.getOperateScene(),
                    getPayChannelByDutType(saveDutUserDto.getType())));
            dutRenewSetLog.setSerialRenewCount(0);
            dutRenewSetLog.setAgreementType(autoRenewDutType.getAgreementType());
            dutRenewSetLogService.addDutRenewSetLog(dutRenewSetLog);
            return true;
        }
        return false;
    }

    /**
     * 保存自动续费记录
     *
     * @param saveDutUserDto SaveDutUserDto
     */
    @Override
    public boolean save(SaveDutUserDto saveDutUserDto) {
        boolean isAutoRenewChange = false;
        boolean isSaveSuccess = false;
        try {
            if (lockService.lock(Lock.LOCK_DUT_SIGN_FOR_DEAL_PREFIX + saveDutUserDto.getUserId() + "" +
                    saveDutUserDto.getType())) {
                isAutoRenewChange = ApplicationContextUtil.getBean(DutManager.class).saveNew(saveDutUserDto);
            }
            isSaveSuccess = true;
        } catch (Exception e) {
            LOGGER.error("[module:pay] [action:alipayDut] [step:save]", e);
        } finally {
            lockService.unlock(Lock.LOCK_DUT_SIGN_FOR_DEAL_PREFIX + saveDutUserDto.getUserId() + "" + saveDutUserDto
                    .getType());
        }

        return isSaveSuccess;
    }

    /**
     * 是否需要发送自动续费状态变更消息
     */
    public boolean shouldSendAutoRenewStatusChangeMQ(Integer currentAutoRenew, Long userId, Long vipType, Integer dutType) {
        return DutUserNew.RENEW_AUTO == currentAutoRenew || DutUserNew.RENEW_NOT_AUTO == currentAutoRenew
                && !isAutoRenewUserExcludeType(userId, vipType, Collections.singletonList(dutType));
    }

    /**
     * 是否需要发送自动续费签约关系变更消息
     */
    public boolean shouldSendAutoRenewSignRelationChangeMQ(Integer operate) {
        return CloudConfigUtil.shouldSendAutoRenewSignRelationChangeMQ(operate);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DutUserNew addAutoRenewNew(AddAutoRenewDto addAutoRenewDto) {
        AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(addAutoRenewDto.getDutType());
        if (autoRenewDutType == null) {
            return null;
        }
        Long vipType = null == addAutoRenewDto.getVipUser() ? Constants.VIP_USER_SUPER : addAutoRenewDto.getVipUser().getTypeId();
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(addAutoRenewDto.getUserId(), addAutoRenewDto.getDutType(), vipType);
        if (dutUserNew != null) {
            if (dutUserNew.isNotAutoRenewUser()) {
                if (addAutoRenewDto.getAgreementNo() == null && dutUserNew.getAgreementNo() != null) {
                    dutUserNew.setAgreementNo(null);
                    dutUserNew.setSignKey(null);
                }
            }
            //把状态改为开通状态
            if (dutUserNew.isNotAutoRenewUser()) {
                if (addAutoRenewDto.getAgreementNo() == null && dutUserNew.getAgreementNo() != null) {
                    dutUserNew.setAgreementNo(null);
                    dutUserNew.setSignKey(null);
                }
            }
            dutUserNew.setAutoRenew(DutUserNew.RENEW_AUTO);
            dutUserNew.setStatus(BindStatusEnum.BIND.getValue());
            dutUserNew.setUpdateTime(DateHelper.getDateTime());
        } else { //插入自动续费用户记录
            dutUserNew = new DutUserNew();
            dutUserNew.setUserId(addAutoRenewDto.getUserId());
            dutUserNew.setStatus(BindStatusEnum.BIND.getValue());
            dutUserNew.setAutoRenew(DutUserNew.RENEW_AUTO);
            dutUserNew.setOperateTime(DateHelper.getDateTime());
            dutUserNew.setUpdateTime(DateHelper.getDateTime());
            dutUserNew.setAgreementType(autoRenewDutType.getAgreementType());
        }
        if (addAutoRenewDto.getSourceVipType() != null) {
            dutUserNew.setSourceVipType(addAutoRenewDto.getSourceVipType());
            AutoRenewUpgradeConfig autoRenewUpgradeConfig = autoRenewUpgradeConfigManager
                    .getByVipType(addAutoRenewDto.getSourceVipType(), addAutoRenewDto.getVipUser().getTypeId(), autoRenewDutType.getPriority());
            if (autoRenewUpgradeConfig != null) {
                dutUserNew.setPcode(autoRenewUpgradeConfig.getMonthProductCode());
                dutUserNew.setRenewPrice(autoRenewUpgradeConfig.getMonthRenewPrice());
            }
        }

        // 对外合作开通自动续费保存订单号
        if (StringUtils.isNotBlank(addAutoRenewDto.getPartnerNo()) && StringUtils.isNotBlank(addAutoRenewDto.getOrderCode())) {
            dutUserNew.setOrderCode(addAutoRenewDto.getOrderCode());
        }

        dutUserNew.setType(addAutoRenewDto.getDutType());
        dutUserNew.setPlatform(addAutoRenewDto.getPlatformId());
        dutUserNew.setPlatformCode(addAutoRenewDto.getPlatformCode());
        // 保存自动续费签约价
        if (dutUserNew.getRenewPrice() == null
                || null == dutUserNew.getAmount()
                || hasAmountChanged(addAutoRenewDto, dutUserNew)) {
            dutUserNew.setRenewPrice(addAutoRenewDto.getRenewPrice());
            dutUserNew.setContractPrice(addAutoRenewDto.getRenewPrice());
        }
        Integer amount = null != addAutoRenewDto.getAmount()
                ? addAutoRenewDto.getAmount() : Constants.AMOUNT_OF_COMMON_AUTORENEW;
        Integer vipCategory = addAutoRenewDto.isTwVipUser()
                ? Constants.VIP_CATEGORY_TAIWAN : Constants.VIP_CATEGORY_MAINLAND;
        dutUserNew.setAmount(amount);
        Timestamp deadline = null;
        if (null != addAutoRenewDto.getVipUser()) {
            deadline = addAutoRenewDto.getVipUser().getDeadline();
            dutUserNew.setDeadline(deadline);
            dutUserNew.setVipType(addAutoRenewDto.getVipUser().getTypeId());
            dutUserNew.setVipCategory(vipCategory);
        } else {
            //增加开通自动续费同步会员信息逻辑
            AbstractTask task = new SyncVipInfoToDutTask(addAutoRenewDto.getUserId(), dutUserNew.getVipType(), vipCategory, 0);
            AsyncTaskFactory.getInstance().insertIntoDB(task, AsyncTask.POOL_TYPE_NOTIFY_RENEW_SET_INFO,
                    AsyncTask.Task_PRIORITY_1, DateHelper.getCurrentTime());
        }
        // 更新活动信息
        dutPriceActService.updateUserActInfo(dutUserNew, dutUserNew.getRenewPrice());
        autoRenewService.doRepeatOpeningOld(addAutoRenewDto.getVipUser(), autoRenewDutType, amount, addAutoRenewDto.getFc(), addAutoRenewDto.getFv());

        dutUserNew.setOperateSceneExt(OperateSceneEnum.parseValue(addAutoRenewDto.getOperateScene()));

        dutUserRepository.save(dutUserNew);

        AddRenewSetLogDto addRenewSetLogDto = AddRenewSetLogDto.builder()
                .userId(addAutoRenewDto.getUserId())
                .type(addAutoRenewDto.getDutType())
                .description(OpenDutSetLogDesp.buildOpenSetLogDespWhenDirectOpen(dutUserNew, addAutoRenewDto))
                .platform(dutUserNew.getPlatform())
                .amount(amount)
                .vipType(dutUserNew.getVipType())
                .vipCategory(dutUserNew.getVipCategory())
                .operator(DutRenewSetLog.RENEW_SET)
                .operateTime(DateHelper.getDateTime())
                .serialRenewCount(0)
                .fc(addAutoRenewDto.getFc())
                .agreementType(autoRenewDutType.getAgreementType())
                .build();
        //添加自动续费日志
        addAutoRenewNewLog(addRenewSetLogDto);
        return dutUserNew;
    }

    /**
     * 奇异果增加或更改自动续费记录
     *
     * @param uid  用户ID
     * @param type 代扣类型：19：微信
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addShardAutoRenewPlatform(Long uid, Integer type, QiYuePlatform platform, SpecialVipUser specialVipUser,
                                          String fc, String fv, String operateScene) {
        AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(type);
        if (autoRenewDutType == null) {
            return;
        }
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(uid, type, specialVipUser.getType().longValue());
        if (dutUserNew != null) {
            //把状态改为开通状态
            dutUserNew.setAutoRenew(DutUserNew.RENEW_AUTO);
            dutUserNew.setStatus(BindStatusEnum.BIND.getValue());
            dutUserNew.setUpdateTime(DateHelper.getDateTime());
        } else { //插入自动续费用户记录
            dutUserNew = new DutUserNew();
            dutUserNew.setUserId(uid);
            dutUserNew.setStatus(BindStatusEnum.BIND.getValue());
            dutUserNew.setAutoRenew(DutUserNew.RENEW_AUTO);
            dutUserNew.setOperateTime(DateHelper.getDateTime());
            List<Integer> tvDutTypeList = autoRenewDutTypeManager.getDutTypeListByVipType(Constants.VIP_USER_TV, autoRenewDutType.getAgreementType());
            if (tvDutTypeList.contains(type)) {
                dutUserNew.setPcode(Constants.PRODUCT_TVKIWI_CODE);
            }
            dutUserNew.setUpdateTime(DateHelper.getDateTime());
            dutUserNew.setAgreementType(autoRenewDutType.getAgreementType());
        }
        dutUserNew.setType(type);
        dutUserNew.setPlatform(platform.getId());
        dutUserNew.setPlatformCode(platform.getCode());
        dutUserNew.setRenewPrice(getRenewPriceWhenOpenByApi(dutUserNew));
        dutUserNew.setContractPrice(dutUserNew.getRenewPrice());

        //增加开通自动续费同步会员信息逻辑
        Integer vipCategory = Constants.VIP_CATEGORY_MAINLAND;
        Timestamp deadline = dutUserNew.getDeadline();

        vipCategory = specialVipUser.getType() == Constants.VIP_USER_TW
                ? Constants.VIP_CATEGORY_TAIWAN : Constants.VIP_CATEGORY_MAINLAND;
        deadline = specialVipUser.getDeadline();
        dutUserNew.setDeadline(deadline);
        dutUserNew.setVipType(Long.valueOf(specialVipUser.getType()));
        dutUserNew.setVipCategory(vipCategory);
        dutUserRepository.save(dutUserNew);

        Integer amount = null == dutUserNew.getAmount() ? Constants.AMOUNT_OF_COMMON_AUTORENEW : dutUserNew.getAmount();
        // 处理重复放开通的场景
//        doRepeatOpening(dutUserNew.getVipType(), uid, type, amount, fc, fv, deadline);
        autoRenewService.doRepeatOpeningOld(specialVipUser.toVipUser(), autoRenewDutType, amount, fc, fv);

        //添加自动续费日志
        DutRenewSetLog dutRenewSetLog = new DutRenewSetLog();
        dutRenewSetLog.setUserId(uid);
        dutRenewSetLog.setType(type);
        dutRenewSetLog.setDescription(OpenDutSetLogDesp.buildOpenSetLogDespWhenDirectOpen(dutUserNew, fc, fv, null, operateScene, getPayChannelByDutType(type)));
        dutRenewSetLog.setPlatform(dutUserNew.getPlatform());
        dutRenewSetLog.setPlatformCode(dutUserNew.getPlatformCode());
        dutRenewSetLog.setAmount(amount);
        dutRenewSetLog.setVipType(dutUserNew.getVipType());
        dutRenewSetLog.setVipCategory(vipCategory);
        dutRenewSetLog.setOperateTime(DateHelper.getDateTime());
        dutRenewSetLog.setAgreementType(autoRenewDutType.getAgreementType());
        shardAddAutoRenewNewLog(dutRenewSetLog, fc);
    }

    public Integer getPayChannelByDutType(Integer type) {
        AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(type);
        return autoRenewDutType != null ? autoRenewDutType.getPayChannel() : null;
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    public void addAutoRenewNewLog(AddRenewSetLogDto addRenewSetLogDto) {
        DutRenewSetLog dutRenewSetLog = new DutRenewSetLog();
        BeanUtils.copyProperties(addRenewSetLogDto, dutRenewSetLog);
        dutRenewSetLogService.addDutRenewSetLog(dutRenewSetLog);
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    public void onlyCancelAutoRenewSetLog(AddRenewSetLogDto addRenewSetLogDto) {
        DutRenewSetLog dutRenewSetLog = new DutRenewSetLog();
        BeanUtils.copyProperties(addRenewSetLogDto, dutRenewSetLog);
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(addRenewSetLogDto.getUserId()
                , addRenewSetLogDto.getType(), addRenewSetLogDto.getVipType());
        Integer amount = Constants.AMOUNT_OF_COMMON_AUTORENEW;
        if (null != dutUserNew && null != dutUserNew.getAmount()) {
            amount = dutUserNew.getAmount();
        }
        dutRenewSetLog.setAmount(amount);
        dutRenewSetLog.setVipType(addRenewSetLogDto.getVipType());
        dutRenewSetLog.setOperator(addRenewSetLogDto.getOperator());

        dutRenewSetLogService.addDutRenewSetLog(dutRenewSetLog);
    }

    private void addAutoRenewSetLog(AddRenewSetLogDto addRenewSetLogDto) {
        DutRenewSetLog dutRenewSetLog = new DutRenewSetLog();
        BeanUtils.copyProperties(addRenewSetLogDto, dutRenewSetLog);
        dutRenewSetLogService.addDutRenewSetLog(dutRenewSetLog);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void cancelAutoRenewByRtdn(DutUserNew dutUserNew, RtdnCancelDetailDto rtdnCancelDetailDto, Integer payChannel) {
        if (dutUserNew.isNotAutoRenewUser()) {
            LOGGER.info("Already canceled, not need to cancel auto renew. dutUserNew:{}, rtdnCancelDetailDto:{}",
                    dutUserNew, rtdnCancelDetailDto);
            return;
        }
        LOGGER.info("cancel auto renew dutUserNew:{}, rtdnCancelDetailDto:{}", dutUserNew,
                rtdnCancelDetailDto);
        AddRenewSetLogDto addRenewSetLogDto = AddRenewSetLogDto.builder()
                .userId(dutUserNew.getUserId())
                .type(dutUserNew.getType())
                .vipType(dutUserNew.getVipType())
                .description(CancelDutSetLogDesp
                        .buildCancelSetLogDescIncludingExtraInfo(dutUserNew, null, null,
                                rtdnCancelDetailDto, OperateSceneEnum.CANCEL_PAYCENTERNOTIFY.getValue(), payChannel))
                .platform(dutUserNew.getPlatform())
                .platformCode(dutUserNew.getPlatformCode())
                .serialRenewCount(dutUserNew.getSerialRenewCount())
                .vipCategory(dutUserNew.getVipCategory())
                .operator(DutRenewSetLog.RENEW_CANCEL)
                .amount(dutUserNew.getAmount())
                .operateTime(rtdnCancelDetailDto.getOperationTime())
                .fc("")
                .build();
        cancelAutoRenewSelective(dutUserNew, Constants.OPERATION_AUTORENEW_CANCEL, null);
        Integer agreementType = dutUserNew.getAgreementType();
        resetAutoRenewStatus(dutUserNew.getUserId(), Collections.singletonList(dutUserNew.getType()), agreementType);
        onlyCancelAutoRenewSetLog(addRenewSetLogDto);
    }

    /**
     * 取消自动续费
     *
     * @param cancelAutoRenewDto CancelAutoRenewDto
     */
    private void cancelByDto(CancelAutoRenewDto cancelAutoRenewDto) {
        AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(cancelAutoRenewDto.getType());
        // 第三方扣费代扣方式,不能直接取消
        if (cancelAutoRenewDto.getOperationType() == Constants.OPERATION_AUTORENEW_CANCEL && autoRenewDutType != null && autoRenewDutType.thirdDut()) {
            LOGGER.warn("第三方代扣方式:{},不能直接取消自动续费!", cancelAutoRenewDto.getType());
            return;
        }
        Long vipType = null;
        if (autoRenewDutType != null && Constants.VIP_USER_TW == autoRenewDutType.getVipType()) {
            vipType = Constants.VIP_USER_TW;
        } else if (null != cancelAutoRenewDto.getVipType()) {
            vipType = cancelAutoRenewDto.getVipType();
        } else {
            vipType = Constants.VIP_USER_SUPER;
        }
        DutUserNew dutUser = getInEffectDutUserNewFromMaster(cancelAutoRenewDto.getUid(), cancelAutoRenewDto.getType(), vipType);
        if (dutUser == null || dutUser.isNotAutoRenewUser()) {
            return;
        }
        LOGGER.info("cancel autoRenew, dutUser:{}", dutUser);
        //保存取消自动续费的日志
        Integer payChannel = autoRenewDutType != null ? autoRenewDutType.getPayChannel() : null;
        Integer agreementType = dutUser.getAgreementType();
        AddRenewSetLogDto addRenewSetLogDto = AddRenewSetLogDto.builder()
                .userId(cancelAutoRenewDto.getUid())
                .type(cancelAutoRenewDto.getType())
                .description(CancelDutSetLogDesp.buildCancelSetLogDesp(dutUser, "cancelAutoRenew", payChannel, cancelAutoRenewDto))
                .platform(dutUser.getPlatform())
                .platformCode(dutUser.getPlatformCode())
                .serialRenewCount(dutUser.getSerialRenewCount())
                .vipType(dutUser.getVipType())
                .vipCategory(dutUser.getVipCategory())
                .operator(DutRenewSetLog.RENEW_CANCEL)
                .operateTime(DateHelper.getDateTime())
                .fc(cancelAutoRenewDto.getFc())
                .agreementType(agreementType)
                .build();

        cancelAutoRenewSelective(dutUser, cancelAutoRenewDto.getOperationType(), null);

        // 保存日志
        onlyCancelAutoRenewSetLog(addRenewSetLogDto);
        //更新DutUserRenewStatus续费次数和续费提醒开关
        resetAutoRenewStatus(cancelAutoRenewDto.getUid(), Collections.singletonList(dutUser.getType()), agreementType);
        LOGGER.info("cancel autoRenew success, uid:{}, dutType:{}", cancelAutoRenewDto.getUid(), dutUser.getType());

    }

    /**
     * 取消自动续费
     *
     * @param cancelAutoRenewDto CancelAutoRenewDto
     */
    private void cancelRenew(CancelAutoRenewDto cancelAutoRenewDto) {
        List<DutUserNew> dutUserList = shardGetDutUserNew(cancelAutoRenewDto.getUid(), cancelAutoRenewDto.getType());
        Integer agreementType = null;
        for (DutUserNew dutUser : dutUserList) {
            if (dutUser == null) {
                continue;
            }
            LOGGER.info("取消自动续费 dutUser:{}", dutUser);

            if (dutUser.isNotAutoRenewUser()) {
                LOGGER.info("已经取消自动续费! dutUser:{}", dutUser);
                continue;
            }

            AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutUser.getType());
            if (cancelAutoRenewDto.getOperationType() == Constants.OPERATION_AUTORENEW_CANCEL && autoRenewDutType != null && autoRenewDutType.thirdDut()) {
                LOGGER.info("第三方代扣方式，不可直接取消自动续费. uid:{}, type:{}", cancelAutoRenewDto.getUid(), dutUser.getType());
                continue;
            }

            Integer payChannel = autoRenewDutType != null ? autoRenewDutType.getPayChannel() : null;
            agreementType = dutUser.getAgreementType();
            AddRenewSetLogDto addRenewSetLogDto = AddRenewSetLogDto.builder()
                    .userId(cancelAutoRenewDto.getUid())
                    .type(cancelAutoRenewDto.getType())
                    .description(CancelDutSetLogDesp.buildCancelSetLogDesp(dutUser, "cancelAutoRenew", payChannel, cancelAutoRenewDto))
                    .platform(dutUser.getPlatform())
                    .platformCode(dutUser.getPlatformCode())
                    .serialRenewCount(dutUser.getSerialRenewCount())
                    .vipType(dutUser.getVipType())
                    .vipCategory(dutUser.getVipCategory())
                    .operator(DutRenewSetLog.RENEW_CANCEL)
                    .operateTime(DateHelper.getDateTime())
                    .fc(cancelAutoRenewDto.getFc())
                    .agreementType(agreementType)
                    .build();

            cancelAutoRenewSelective(dutUser, cancelAutoRenewDto.getOperationType(), null);
            onlyCancelAutoRenewSetLog(addRenewSetLogDto);
            LOGGER.info("取消自动续费成功! userId:{}, dutType:{}", dutUser.getUserId(), dutUser.getType());
        }
        resetAutoRenewStatus(cancelAutoRenewDto.getUid(), Collections.singletonList(cancelAutoRenewDto.getType()), agreementType);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void cancelAutoRenew(CancelAutoRenewDto cancelAutoRenewDto) {
        AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(cancelAutoRenewDto.getType());
        if (autoRenewDutType == null) {
            LOGGER.warn("取消自动续费-代扣方式不存在! {}", cancelAutoRenewDto);
            return;
        }
        if (needUnbind(autoRenewDutType, cancelAutoRenewDto.getOperateScene())) {
            AccountUnbindReqDto accountUnbindReqDto = AccountUnbindReqDto.builder()
                    .userId(cancelAutoRenewDto.getUid())
                    .type(cancelAutoRenewDto.getType())
                    .build();
            boolean unbindSuccess = accountApi.cancelDutAccountBind(accountUnbindReqDto);
            LOGGER.warn("取消自动续费-账户中心解绑: 解绑结果={}, param: {}", unbindSuccess, JacksonUtils.toJsonString(cancelAutoRenewDto));
        }

        if (validOperationType(cancelAutoRenewDto) && autoRenewDutType.thirdDut()) {
            LOGGER.info("第三方代扣方式，不可直接取消自动续费. params:{}.", cancelAutoRenewDto);
            return;
        }
        if (cancelAutoRenewDto.getVipType() != null) {
            cancelByDto(cancelAutoRenewDto);
        } else {
            cancelRenew(cancelAutoRenewDto);
        }
    }

    /**
     * 是否需要解绑
     * @param autoRenewDutType
     * @param cancelScene
     */
    private boolean needUnbind(AutoRenewDutType autoRenewDutType, String cancelScene) {
        boolean dutTypeNeedCheckCancelSceneWhenUnbind = CloudConfigUtil.dutTypeNeedCheckCancelSceneWhenUnbind(autoRenewDutType.getDutType());
        boolean cancelSceneCanUnbind = CloudConfigUtil.cancelSceneCanUnbind(cancelScene);
        return autoRenewDutType.supportUnBindWhenCancelAutoRenew() && (!dutTypeNeedCheckCancelSceneWhenUnbind || cancelSceneCanUnbind);
    }

    private Boolean validOperationType(CancelAutoRenewDto cancelAutoRenewDto) {
        return cancelAutoRenewDto.closeAccountCancel() || cancelAutoRenewDto.directCancel();
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void cancelAutoRenewByTicket(DutUserNew dutUserNew, AppleTicketInfoDto appleTicketInfo) {
        if (dutUserNew != null && dutUserNew.isNotAutoRenewUser()) {
            LOGGER.info("Already canceled, not need to cancel auto renew dutUserNew:{}, appleTicketInfo:{}", dutUserNew, appleTicketInfo);
            return;
        }
        LOGGER.info("cancel auto renew dutUserNew:{}, appleTicketInfo:{}", dutUserNew, appleTicketInfo);
        if (dutUserNew == null) {
            LOGGER.info("cancel auto renew find dutUserNew is null , appleTicketInfo:{}", appleTicketInfo);
            return;
        }
        AddRenewSetLogDto addRenewSetLogDto = AddRenewSetLogDto.builder()
                .userId(dutUserNew.getUserId())
                .type(dutUserNew.getType())
                .vipType(dutUserNew.getVipType())
                .description(CancelDutSetLogDesp.buildCancelSetLogDespForAppleAutoRenewUser(
                        dutUserNew, "cancelAutoRenew", appleTicketInfo, "",
                        OperateSceneEnum.CANCEL_PAYCENTERNOTIFY.getValue(), getPayChannelByDutType(dutUserNew.getType())))
                .platform(dutUserNew.getPlatform())
                .platformCode(dutUserNew.getPlatformCode())
                .serialRenewCount(dutUserNew.getSerialRenewCount())
                .vipCategory(dutUserNew.getVipCategory())
                .operator(DutRenewSetLog.RENEW_CANCEL)
                .amount(dutUserNew.getAmount())
                .operateTime(appleTicketInfo.getRequestDate())
                .fc("")
                .build();
        /*
         *  用户变更productCode 或者 取消自动续费；
         *  如果用户变更了productCode，需要将原先的记录取消
         *  票据信息信息取消用户自动续费状态，那么也需要取消用户权益；
         */
        //用来做条件字段更新
        DutUserNewVO.DutUserNewVOBuilder dutUserNewVOBuilder = DutUserNewVO.builder()
                .id(dutUserNew.getId())
                .userId(dutUserNew.getUserId());
        // 苹果用户需要将appId作为actCode，存入自动续费用户
        dutUserNewVOBuilder.actCode(appleTicketInfo.getAutoRenewProductId());
        dutUserNewVOBuilder.interruptFlag(DutUserNew.INTERRUPT_FLAG_YES);
        dutUserNewVOBuilder.operateTime(appleTicketInfo.getRequestDate());
        if (appleTicketInfo.getExpiresDate() != null) {
            dutUserNewVOBuilder.nextDutTime(appleTicketInfo.getExpiresDate());
        }
        cancelAutoRenewSelective(dutUserNew, Constants.OPERATION_AUTORENEW_CANCEL, dutUserNewVOBuilder);
        resetAutoRenewStatus(dutUserNew.getUserId(), Collections.singletonList(dutUserNew.getType()), AgreementTypeEnum.AUTO_RENEW.getValue());
        onlyCancelAutoRenewSetLog(addRenewSetLogDto);
    }

    public void modifySubscribeAutoRenewAppId(DutUserNew dutUserNew, AppleTicketInfoDto appleTicketInfo) {
        if (dutUserNew != null && dutUserNew.isNotAutoRenewUser()) {
            LOGGER.info("Already canceled, not need to cancel auto renew dutUserNew:{}, appleTicketInfo:{}", dutUserNew, appleTicketInfo);
            return;
        }
        LOGGER.info("cancel auto renew dutUserNew:{}, appleTicketInfo:{}", dutUserNew, appleTicketInfo);
        if (dutUserNew == null) {
            LOGGER.info("cancel auto renew find dutUserNew is null , appleTicketInfo:{}", appleTicketInfo);
            return;
        }
        AddRenewSetLogDto addRenewSetLogDto = AddRenewSetLogDto.builder()
            .userId(dutUserNew.getUserId())
            .type(dutUserNew.getType())
            .vipType(dutUserNew.getVipType())
            .description(CancelDutSetLogDesp.buildCancelSetLogDespForAppleAutoRenewUser(
                dutUserNew, "cancelAutoRenew", appleTicketInfo, "",
                OperateSceneEnum.CANCEL_PAYCENTERNOTIFY.getValue(), getPayChannelByDutType(dutUserNew.getType())
            ))
            .platform(dutUserNew.getPlatform())
            .platformCode(dutUserNew.getPlatformCode())
            .serialRenewCount(dutUserNew.getSerialRenewCount())
            .vipCategory(dutUserNew.getVipCategory())
            .operator(DutRenewSetLog.RENEW_CANCEL)
            .amount(dutUserNew.getAmount())
            .operateTime(appleTicketInfo.getRequestDate())
            .fc("")
            .build();
        onlyCancelAutoRenewSetLog(addRenewSetLogDto);
    }

    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    @Override
    public void save(DutUserNew dutUserNew) {
        dutUserRepository.save(dutUserNew);
    }

    @Override
    public DutRenewLog getDutRenewLogByOrderCode(Long userId, String orderCode) {
        return dutRenewLogService.getDutRenewLogByOrderCode(userId, orderCode);
    }

    @Override
    public int getCurrentDutRenewType(Long uid, List<Integer> bindTypes,
        Long vipType, List<DutUserNew> dutUserNews, Integer agreementType) {
        List<DutRenewSetLog> dutRenewSetLogs = new ArrayList<>();

        if (dutUserNews == null) {
            dutUserNews = findByUidAndVipTypeAndAutoRenew(uid, agreementType, vipType, DutUserNew.RENEW_AUTO);
        }

        for (DutUserNew dutUserNew : dutUserNews) {
            List<Integer> mobileDutTypeList = autoRenewDutTypeManager.getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_MOBILE);
            if (mobileDutTypeList.contains(dutUserNew.getType())) {
                return dutUserNew.getType();
            }
            DutRenewSetLog dutRenewSetLog = dutRenewSetLogService.getRecentlyDutRenewSetLog(uid, agreementType, dutUserNew.getType(), DutRenewSetLog.RENEW_SET);
            if (dutRenewSetLog != null) {
                dutRenewSetLogs.add(dutRenewSetLog);
            }
        }

        dutRenewSetLogs.sort((o1, o2) -> o2.getOperateTime().compareTo(o1.getOperateTime()));
        if (dutRenewSetLogs.size() > 0) {
            return dutRenewSetLogs.get(0).getType();
        }
        return -1;
    }

    @Override
    public List<DutUserNew> getDutUserAutoRenew(Long userId, Integer agreementType, List<Integer> typeList) {
        return dutUserRepository.getShardAutoRenewUsers(agreementType, userId, typeList, Constants.VIP_USER_SUPER);
    }

    @Override
    public List<DutUserNew> getShardAutoRenewUsers(Long userId, Integer agreementType, Long vipType, List<Integer> typeList) {
        return dutUserRepository.getShardAutoRenewUsers(agreementType, userId, typeList, vipType);
    }

    @Override
    public List<DutUserNew> getShardExcludeTypesAutoRenewUsers(Long userId, Integer agreementType, Long vipType, List<Integer> excludeTypeList) {
        return dutUserRepository.getShardExcludeTypesAutoRenewUsers(userId, agreementType, excludeTypeList, null, vipType);
    }

    @Override
    public List<DutUserNew> getShardExcludeTypesUpgradeAutoRenewUsers(Long userId, Integer agreementType, Long sourceVipType,
        Long targetVipType, List<Integer> excludeTypeList) {
        return dutUserRepository.getShardExcludeTypesAutoRenewUsers(userId, agreementType, excludeTypeList, sourceVipType, targetVipType);
    }

    public void reProcessMonthlyRenew(AutorenewRequest autorenewRequest) throws Exception {
        AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(autorenewRequest.getDutType());
        if (agreementNoInfo != null) {
            autorenewRequest.setAgreementType(agreementNoInfo.getType());
            agreementOperateService.openBySignPay(autorenewRequest, agreementNoInfo);
        } else {
            DutManager thisObj = (DutManager) AopContext.currentProxy();
            thisObj.processMonthlyRenew(autorenewRequest);
        }
    }

    /**
     * 处理代扣
     */
    @Transactional(rollbackFor = Exception.class)
    public void processMonthlyRenew(AutorenewRequest autorenewRequest) throws Exception {
        VipUser vipUser = autorenewRequest.generateVipUser();
        if (vipUser == null) {
            LOGGER.warn("Order message endTime or productSubtype is null. {}", autorenewRequest);
            return;
        }
        AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(autorenewRequest.getDutType());
        if (autoRenewDutType == null) {
            LOGGER.warn("[dutType invalid.] [Params:{}]", autorenewRequest);
            return;
        }
        Integer agreementType = autoRenewDutType.getAgreementType();
        autorenewRequest.setAgreementType(agreementType);
        boolean isAutoRenewUser = shardIsAutoRenewUser(autorenewRequest.getUserId(), autorenewRequest.getVipType(), agreementType);

        // 处理升级购买的场景
        dealUpgrade(autorenewRequest);

        // 处理IAP自动续费切换的场景,根据切换前的续费类型生成一条取消的操作日志
        saveRenewSetLogForIAPChangedAmount(vipUser.getTypeId(), autorenewRequest.getUserId(), autoRenewDutType, autorenewRequest);

        Integer amount = autorenewRequest.getOpeningAmount();
        // 处理重复开通的场景
//        doRepeatOpening(vipUser.getTypeId(), autorenewRequest.getUserId(), autorenewRequest.getDutType(), amount,
//            autorenewRequest.getFc(), autorenewRequest.getFv(), vipUser.getDeadline());
        autoRenewService.doRepeatOpeningOld(vipUser, autoRenewDutType, amount, autorenewRequest.getFc(), autorenewRequest.getFv());

        PayCenterAppleDutInfoResult appleSignInfo = null;
        if (autorenewRequest.isAppleOrder()) {
            appleSignInfo = payCenterAppleSignManager.getSpecifiedSignInfo(vipUser.getId(), autorenewRequest.getActCode());
        }

        OperateSceneEnum operateScene = isAutoRenewUser ? OperateSceneEnum.OPEN_BUY_EXTENDSIGN : OperateSceneEnum.OPEN_BUY;
        // 开通自动续费
        DutUserNew dutUser = doOpen(operateScene, vipUser, autorenewRequest, autoRenewDutType, appleSignInfo);
        if (dutUser == null) {
            return;
        }

        // 保存操作日志
        doSetLog(vipUser, autorenewRequest, autoRenewDutType, dutUser, operateScene);
    }

    private void doSetLog(VipUser vipUser, AutorenewRequest autorenewRequest, AutoRenewDutType autoRenewDutType, DutUserNew dutUser, OperateSceneEnum operateScene) {
        if (dutUser == null) {
            return;
        }
        AddRenewSetLogDto addRenewSetLogDto = AddRenewSetLogDto.builder()
            .userId(autorenewRequest.getUserId())
            .type(autoRenewDutType.getDutType())
            .agreementNo(dutUser.getAgreementNo())
            .signKey(dutUser.getSignKey())
            .description(OpenDutSetLogDesp.buildOpenSetLogDesp(dutUser, autorenewRequest.getFc(), autorenewRequest.getFv(), autorenewRequest.getThirdUid(), operateScene, autoRenewDutType.getPayChannel(), null))
            .platform(autorenewRequest.getPlatformId())
            .platformCode(autorenewRequest.getPlatformCode())
            .amount(dutUser.getAmount())
            .vipCategory(dutUser.getVipCategory())
            .vipType(vipUser.getTypeId())
            .operator(DutRenewSetLog.RENEW_SET)
            .operateTime(DateHelper.getDateTime())
            .serialRenewCount(0)
            .fc(autorenewRequest.getFc())
            .build();
        addAutoRenewNewLog(addRenewSetLogDto);
    }

    private DutUserNew doOpen(OperateSceneEnum operateSceneEnum, VipUser vipUser, AutorenewRequest autorenewRequest,
        AutoRenewDutType autoRenewDutType, PayCenterAppleDutInfoResult appleSignInfo) throws Exception {
        DutUserNew dutUser = userAgreementService.getByDutTypeAndVipType(autorenewRequest.getUserId(), autoRenewDutType.getDutType(), vipUser.getTypeId());
        boolean alreadyNotAutoRenew = dutUser != null && dutUser.isNotAutoRenewUser();
        Integer agreementType = autorenewRequest.getAgreementType();
        if (dutUser == null) {
            dutUser = new DutUserNew();
            dutUser.setVipType(vipUser.getTypeId());
            dutUser.setType(autoRenewDutType.getDutType());
            dutUser.setUserId(autorenewRequest.getUserId());
            dutUser.setOperateTime(DateHelper.getDateTime());
            dutUser.setUpdateTime(DateHelper.getDateTime());
            dutUser.setAgreementType(agreementType);
        } else if (dutUser.isTwVipDutUser() && validSpgateway(autorenewRequest, autoRenewDutType, dutUser)) {
            if (dutUser.isAutoRenewUser() && !autoRenewDutType.isAppleDutType()) {
                LOGGER.info("[DutManager] [processMonthlyRenew] [is already autorenew user] [uid:{}] [dutType:{}]",
                    autorenewRequest.getUserId(), autoRenewDutType.getDutType());
                return null;
            }
        }
        //已存在记录且为协议模式，清除协议信息
        if (alreadyNotAutoRenew && dutUser.getAgreementNo() != null) {
            dutUser.setAgreementNo(null);
            dutUser.setSignKey(null);
        }
        dutUser.setAutoRenew(DutUserNew.RENEW_AUTO);
        dutUser.setStatus(BindStatusEnum.BIND.getValue());
        dutUser.setAmount(autorenewRequest.getOpeningAmount());
        dutUser.setOrderCode(autorenewRequest.getOrderCode());
        if (appleSignInfo != null) {
            dutUser.setSignTime(appleSignInfo.getRecentSubscriptionStartDate());
        } else {
            //保存用户的签约时间和时区信息，其中时区信息可以为空
            dutUser.setSignTime(autorenewRequest.getPayTime());
        }
        Integer renewPrice = calculateRenewPrice(autoRenewDutType.getDutType(), autorenewRequest);
        dutUser.setRenewPrice(renewPrice);
        dutUser.setPlatform(autorenewRequest.getPlatformId());
        dutUser.setPlatformCode(autorenewRequest.getPlatformCode());
        dutUser.setVipType(vipUser.getTypeId());
        dutUser.setPcode(autoRenewDutType.getProductCode());
        dutUser.setCurrencyUnit(autorenewRequest.getCurrencyUnit());
        dutUser.setTimeZone(autorenewRequest.getTimeZone());

        //IAP或Google续费,保存第三方的下次代扣时间
        resetNextDutTime(autoRenewDutType, autorenewRequest, dutUser);
        //resetNextDutTime可能会修改价格
        dutUser.setContractPrice(dutUser.getRenewPrice());

        // 处理活动情况
        doAct(autoRenewDutType.getDutType(), dutUser, autorenewRequest);

        // 处理升级场景
        doUpgrade(autoRenewDutType, autorenewRequest, dutUser);

        dutUser.setDeadline(vipUser.getDeadline());
        Integer vipCategory = vipUser.getTypeId() == Constants.VIP_USER_TW
            ? Constants.VIP_CATEGORY_TAIWAN : Constants.VIP_CATEGORY_MAINLAND;
        dutUser.setVipCategory(vipCategory);
        if (autorenewRequest.getActCode() != null) {
            dutUser.setActCode(autorenewRequest.getActCode());
        }
        dutUser.setOperateSceneExt(operateSceneEnum);
        dutUserRepository.save(dutUser);
        return dutUser;
    }

    private void dealUpgrade(AutorenewRequest autorenewRequest) {
        try {
            Integer agreementType = autorenewRequest.getAgreementType();
            if (autorenewRequest.isUpgrade()) {
                List<DutUserNew> dutUserNewList = findByUidAndVipTypeAndAutoRenew(autorenewRequest.getUserId(), agreementType, autorenewRequest.getSourceVipType(), DutUserNew.RENEW_AUTO);
                //手机话费代扣 或 第三方渠道发起的代扣-不能直接取消自动续费
                List<Integer> mobileDutTypeList = autoRenewDutTypeManager.getDutTypeListByVipTypeAndPayChannel(
                    autorenewRequest.getSourceVipType(), agreementType, PaymentDutType.PAY_CHANNEL_MOBILE);
                Map<Integer, AutoRenewDutType> thirdDutTypeMap = autoRenewDutTypeManager.getDutTypeInfoListByVipTypeAndPayChannelType(agreementType,
                    autorenewRequest.getSourceVipType(), PaymentDutType.PAY_CHANNEL_TYPE_THIRD_DUT_PAY);
                for (DutUserNew dutUserNew : dutUserNewList) {
                    Integer dutType = dutUserNew.getType();
                    if (mobileDutTypeList.contains(dutType) ||
                        (thirdDutTypeMap.containsKey(dutType) && thirdDutTypeMap.get(dutType).cannotDirectCancel())) {
                        continue;
                    }
                    CancelAutoRenewDto cancelAutoRenewDto = CancelAutoRenewDto.builder()
                        .uid(dutUserNew.getUserId())
                        .type(dutUserNew.getType())
                        .vipType(dutUserNew.getVipType())
                        .operationType(DutRenewSetLog.RENEW_CANCEL)
                        .fc("")
                        .operateScene(OperateSceneEnum.CANCEL_CHANGEUPGRADE.getValue())
                        .build();
                    cancelAutoRenew(cancelAutoRenewDto);
                }
            }
        } catch (Exception e) {
            LOGGER.error("[module:VIPAutoRenewMessageProcessor] [action:process] " +
                "[dealUpgrade error.] [params:{}]", autorenewRequest.toString(), e);
        }
    }

    private boolean validSpgateway(AutorenewRequest autorenewRequest, AutoRenewDutType autoRenewDutType, DutUserNew dutUser) {
        return !autoRenewDutType.isSpGatewayDutType() || isSpgatewaySameMonthAutoRenew(autoRenewDutType, dutUser, autorenewRequest.getOpeningAmount());
    }

    private boolean validateAutoRenew(DutUserNew dutUser) {
        return dutUser.getAutoRenew() != null && dutUser.getAutoRenew() == DutUserNew.RENEW_AUTO;
    }

    private void saveRenewSetLogForIAPChangedAmount(Long vipType, long userId,
        AutoRenewDutType autoRenewDutType,
        AutorenewRequest autorenewRequest) {
        if (!autoRenewDutType.isAppleDutType()) {
            return;
        }
        DutUserNew dutUser = userAgreementService.getByDutTypeAndVipType(userId, autoRenewDutType.getDutType(), vipType);
        if (null == dutUser || DutUserNew.RENEW_AUTO != dutUser.getAutoRenew()) {
            return;
        }
        DutRenewSetLog dutRenewSetLog = new DutRenewSetLog();
        dutRenewSetLog.setUserId(userId);
        dutRenewSetLog.setType(autoRenewDutType.getDutType());
        dutRenewSetLog.setOperator(DutRenewSetLog.RENEW_CANCEL);
        dutRenewSetLog.setVipType(vipType);
        dutRenewSetLog.setSerialRenewCount(dutUser.getSerialRenewCount());
        dutRenewSetLog.setPlatform(autorenewRequest.getPlatformId());
        dutRenewSetLog.setPlatformCode(autorenewRequest.getPlatformCode());
        Integer amount = null == dutUser.getAmount() ? Constants.AMOUNT_OF_COMMON_AUTORENEW : dutUser.getAmount();
        dutRenewSetLog.setAmount(amount);
        dutRenewSetLog.setOperateTime(DateHelper.getDateTime());
        dutRenewSetLog.setUpdateTime(DateHelper.getDateTime());
        String desc = OpenDutSetLogDesp.buildSetLogDesp(dutUser, TYPE_CHANGE, autorenewRequest.getFc(),
            autorenewRequest.getFv(), OperateSceneEnum.OPEN_BUY, autoRenewDutType.getPayChannel());
        dutRenewSetLog.setDescription(desc);

        dutRenewSetLogService.addDutRenewSetLog(dutRenewSetLog);
    }

    @Override
    public Integer calculateRenewPrice(Integer dutType, AutorenewRequest autorenewRequest) {
        Integer amount = autorenewRequest.getOpeningAmount();
        Long userId = autorenewRequest.getUserId();
        Integer renewPrice = paymentDutTypeManager.getRenewPrice(autorenewRequest.getVipType(), amount, autorenewRequest.getActCode(), dutType, userId);
        if (renewPrice == null && null != autorenewRequest.getVipType()) {
            renewPrice = paymentDutTypeManager.getCommonAutorenewDutPrice(autorenewRequest.getVipType().intValue(), amount);
        }
        return renewPrice;
    }

    private void doUpgrade(AutoRenewDutType autoRenewDutType, AutorenewRequest autorenewRequest, DutUserNew dutUser) {
        if (autorenewRequest.getSourceVipType() == null) {
            return;
        }
        AutoRenewUpgradeConfig autoRenewUpgradeConfig = autoRenewUpgradeConfigManager
            .getByVipType(autorenewRequest.getSourceVipType(), autorenewRequest.getVipType(), autoRenewDutType.getPriority());
        if (autoRenewUpgradeConfig == null) {
            return;
        }

        dutUser.setSourceVipType(autorenewRequest.getSourceVipType());
        dutUser.setPcode(autoRenewUpgradeConfig.getMonthProductCode());
        dutUser.setRenewPrice(autoRenewUpgradeConfig.getMonthRenewPrice());
    }

    private void doAct(Integer dutType, DutUserNew dutUser, AutorenewRequest autorenewRequest) throws Exception {
        IntroductoryActDto actDto = IntroductoryActDto.builder()
            .fee(autorenewRequest.getOrderFee())
            .pid(autorenewRequest.getPid())
            .payTime(autorenewRequest.getPayTime())
            .payType(autorenewRequest.getPayType())
            .platform(autorenewRequest.getPlatformCode())
            .amount(autorenewRequest.getAmount())
            .build();

        Optional<IntroductoryPriceAct> act = introductoryPriceActManager.findValidAct(actDto, dutType);
        if (!act.isPresent()) {
            return;
        }
        Integer agreementType = autorenewRequest.getAgreementType();
        List<DutUserNew> openedDutUsers = findAllByUserIdAndVipType(dutUser.getUserId(), agreementType, dutUser.getVipType());
        //支持活动的且与开通时长相同的签约关系
        List<DutUserNew> openedDutUsersOfSupportAct = openedDutUsers.stream()
            .filter(item -> !autoRenewDutTypeManager.dutTypeUnSupportAct(item.getType())
                && Objects.equals(dutUser.getAmount(), item.getAmount()))
            .collect(Collectors.toList());
        IntroductoryPriceAct introductoryPriceAct = act.get();
        if (ActTypeEnum.STOP_AFTER_X.getValue().equals(introductoryPriceAct.getActType())) {
            if (CollectionUtils.isNotEmpty(openedDutUsers) && openedDutUsers.get(0).getRemainPeriods() != null) {
                int remainPeriods = openedDutUsers.get(0).getRemainPeriods() - 1;
                dutUser.initActInfo(introductoryPriceAct, remainPeriods);
                dutPriceActService.changeVipActPeriods(openedDutUsersOfSupportAct, remainPeriods);
            } else {
                int remainPeriods = introductoryPriceAct.getActPeriods() - 1;
                dutUser.initActInfo(introductoryPriceAct, remainPeriods);
                dutPriceActService.participateVipDutPriceAct(dutUser, openedDutUsersOfSupportAct, remainPeriods, introductoryPriceAct);
            }
        } else {
            int remainPeriods = introductoryPriceAct.getActPeriods() - 1;
            dutUser.initActInfo(introductoryPriceAct, remainPeriods);
            dutPriceActService.participateVipDutPriceAct(dutUser, openedDutUsersOfSupportAct, remainPeriods, introductoryPriceAct);
        }
    }

    private boolean isSpgatewaySameMonthAutoRenew(AutoRenewDutType autoRenewDutType, DutUserNew dutUser, Integer amount) {
        return autoRenewDutType.isSpGatewayDutType() && dutUser.getAmount().equals(amount);
    }

    private void resetNextDutTime(AutoRenewDutType autoRenewDutType, AutorenewRequest autorenewRequest, DutUserNew dutUser) {
        if (autoRenewDutType.getVipType() == Constants.VIP_USER_TW) {
            dutUser.setNextDutTime(Constants.caculateTaiWanDutTime());
        }
        if (null != autorenewRequest.getExpireTime()) {
            dutUser.setNextDutTime(autorenewRequest.getExpireTime());
        }
    }

    @Transactional
    public void processRenewConfirmInternal(Long userId, String orderCode) {
        DutRenewLog dutRenewLog = getDutRenewLogByOrderCode(userId, orderCode);
        if (dutRenewLog == null) {
            throw new DutRenewLogException("dutRenewLog is null for orderCode:" + orderCode);
        }

        if (dutRenewLog.getStatus() != DutRenewLog.PAY_SUCCESS) {
            //续费不成功再次续费的续费更新数据
            dutRenewLog.setErrorCode(null);
            dutRenewLog.setStatus(DutRenewLog.PAY_SUCCESS);
            dutRenewLogRepository.save(dutRenewLog);

            //保存续费次数
            DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(dutRenewLog.getUserId(), dutRenewLog.getType(), dutRenewLog.getVipType());
            UpdateUserRenewCountDto renewCountDto = UpdateUserRenewCountDto.builder()
                .renewCount(dutUserNew.getRenewCount() + 1)
                .serialRenewCount(dutUserNew.getSerialRenewCount() + 1)
                .interruptFlag(DutUserNew.INTERRUPT_FLAG_NO)
                .vipType(dutUserNew.getVipType())
                .dutType(dutUserNew.getType())
                .amount(dutUserNew.getAmount())
                .userId(dutUserNew.getUserId()).build();
            updateRenewCountInfo(renewCountDto);

            //自动续费状态表中续费总次数和连续续费次数分别加1
            dutUserRenewStatusManager.initOrIncrement(userId);
        }
    }

    @Transactional
    public void incrementRenewCount(Long userId, Long vipType, Integer dutType) {
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, dutType, vipType);
        if (dutUserNew == null) {
            return;
        }
        //保存续费次数
        UpdateUserRenewCountDto renewCountDto = UpdateUserRenewCountDto.builder()
            .renewCount(dutUserNew.getRenewCount() + 1)
            .serialRenewCount(dutUserNew.getSerialRenewCount() + 1)
            .interruptFlag(DutUserNew.INTERRUPT_FLAG_NO)
            .vipType(dutUserNew.getVipType())
            .dutType(dutUserNew.getType())
            .amount(dutUserNew.getAmount())
            .userId(dutUserNew.getUserId()).build();
        updateRenewCountInfo(renewCountDto);

        //自动续费状态表中续费总次数和连续续费次数分别加1
        dutUserRenewStatusManager.initOrIncrement(userId);
    }

    @Deprecated
    @Override
    public DutUserNew shardGetDutUserNew(Long userId, Integer type, Long vipType) {
        return dutUserRepository.getShardDutUserNew(userId, type, vipType);
    }

    @Override
    public DutUserNew getInEffectDutUserNewFromMaster(Long userId, Integer type, Long vipType) {
        return dutUserRepository.getInEffectDutUserNewFromMaster(userId, type, vipType);
    }

    @Override
    public DutUserNew shardGetUpgradeDutUserNew(Long userId, Integer type, Long sourceVipType, Long vipType) {
        return dutUserRepository.getShardUpgradeDutUserNew(userId, type, sourceVipType, vipType);
    }

    @Override
    public DutUserNew getInEffectUpgradeDutUserNewFromMaster(Long userId, Integer type, Long sourceVipType, Long vipType) {
        return dutUserRepository.getInEffectUpgradeDutUserNewFromMaster(userId, type, sourceVipType, vipType);
    }

    /**
     * 台湾自动续费过度方法 以下方法暂时仅限台湾自动续费使用，逐步迁移至大陆自动续费逻辑
     */
    @Override
    public List<DutUserNew> shardGetDutUserNew(Long userId, Integer type) {
        return dutUserRepository.getDutUserNew(userId, null, type);
    }

    //@DataSource(DataSourceEnum.SLAVE)
    public List<DutUserNew> findDutUserNews(Long userId, Integer agreementType) {
        return dutUserRepository.findDutUserNews(userId, agreementType);
    }

    public List<DutRenewLog> searchDutUsers(Long userId, String startTime, String endTime, Integer status) {
        return dutRenewLogRepository.search(userId, startTime, endTime, status);
    }

    /**
     * 创建新的自动续费操作日志
     */
    public void shardAddAutoRenewNewLog(DutRenewSetLog dutRenewSetLog, String fc) {
        dutRenewSetLog.setOperator(DutRenewSetLog.RENEW_SET);
        dutRenewSetLog.setSerialRenewCount(0);
        dutRenewSetLogService.insertDutRenewSetLog(dutRenewSetLog);
    }

    @Override
    public void updateShardDutUserNew(DutUserNew dutUserNew) {
        LOGGER.info("update dut user set nextDutTime={},interruptFlag={},renewCount={},serialRenewCount={} where id={},uid={}",
            dutUserNew.getNextDutTime(), dutUserNew.getInterruptFlag(), dutUserNew.getRenewCount(),
            dutUserNew.getSerialRenewCount(), dutUserNew.getId(), dutUserNew.getUserId());
        dutUserRepository.updateShardDutUserNew(dutUserNew);
    }

    private void resetAutoRenewStatus(Long userId, List<Integer> excludeDutTypes, Integer agreementType) {
        List<DutUserNew> remainDutUserNews = dutUserRepository.getShardExcludeTypesAutoRenewUsers(userId, agreementType, excludeDutTypes);
        if (CollectionUtils.isNotEmpty(remainDutUserNews)) {
            return;
        }
        //更新DutUserRenewStatus续费次数
        dutUserRenewStatusManager.resetAutoRenewStatus(userId);
    }

    /**
     * 获得用户的开通日志
     *
     * @param uid 用户ID
     * @param type 绑定类型: 1:支付宝，3：百度钱包，4：微信，5：微信APP
     * @param operator 操作员
     * @return true or false
     */
    @Override
    public DutRenewSetLog shardGetDutRenewSetLog(long uid, Integer type, Integer operator) {
        return dutRenewSetLogService.getRecentlyDutRenewSetLog(uid, null, type, operator);
    }

    /**
     * 获得用户的开通日志
     *
     * @param uid 用户ID
     * @param operator 操作员
     * @return true or false
     */
    public List<DutRenewSetLog> shardGetDutRenewSetLogList(long uid, List<Integer> dutTypes, Integer operator) {
        return dutRenewSetLogService.getDutRenewSetLogList(uid, null, dutTypes, operator);
    }

    /**
     * 判断指定类型用户最近2天（前2天~当天）是否完成自动续费
     *
     * @param userId 用户ID
     * @return true：已自动续费，false:未自动续费
     */
    @Override
    public boolean isAutoRenewRecentDay(Long userId, Integer agreementType, List<Integer> dutTypeList) {
        String startDate = DateHelper.getFormatDate(DateHelper.caculateTime(
            DateHelper.getDateTime(), -2, Constants.PRODUCT_PERIODUNIT_DAY), "yyyy-MM-dd");
        String endDate = DateHelper.getFormatDate(DateHelper.getDateTime(), "yyyy-MM-dd");
        String begintimeStr = startDate + " 00:00:00";
        String endtimeStr = endDate + " 23:59:59";
        Timestamp begintime = DateHelper.getDateTime(DateHelper.getDateFromStr(begintimeStr, DateHelper.MOST_COMMON_PATTERN));
        Timestamp endtime = DateHelper.getDateTime(DateHelper.getDateFromStr(endtimeStr, DateHelper.MOST_COMMON_PATTERN));

        List<DutRenewLog> dutRenewLogList = dutRenewLogRepository.getDutRenewLogs(userId,
            begintime, endtime, DutRenewLog.PAY_SUCCESS, dutTypeList, true);
        return CollectionUtils.isNotEmpty(dutRenewLogList);
    }

    /**
     * 获取指定类别用户最近1天（前一天~当天）自动续费日志
     *
     * @param userId 用户ID
     * @return true：已自动续费，false:未自动续费
     */
    public DutRenewLog getRecentOneDayDutRenewLog(Long userId, Integer vipCategory) {
        String startDate = DateHelper.getFormatDate(DateHelper.caculateTime(
            DateHelper.getDateTime(), -1, Constants.PRODUCT_PERIODUNIT_DAY), "yyyy-MM-dd");
        String endDate = DateHelper.getFormatDate(DateHelper.getDateTime(), "yyyy-MM-dd");
        String begintimeStr = startDate + " 00:00:00";
        String endtimeStr = endDate + " 23:59:59";
        Timestamp begintime = DateHelper.getDateTime(DateHelper.getDateFromStr(begintimeStr, DateHelper.MOST_COMMON_PATTERN));
        Timestamp endtime = DateHelper.getDateTime(DateHelper.getDateFromStr(endtimeStr, DateHelper.MOST_COMMON_PATTERN));
        List<DutRenewLog> dutRenewLogList = dutRenewLogRepository.getDutRenewLogByTime(
            userId, begintime, endtime, vipCategory);
        return dutRenewLogList != null && dutRenewLogList.size() > 0 ? dutRenewLogList.get(0) : null;
    }

    /**
     * 获取指定会员类型用户当天的自动续费日志
     *
     * @param userId 用户ID
     * @param vipType 会员类型
     * @return listPageSelective
     */
    @Override
    public List<DutRenewLog> findTodayDutRenewLog(Long userId, Integer agreementType, Long vipType) {
        String nowDate = DateHelper.getFormatDate(DateHelper.getDateTime(), "yyyy-MM-dd");
        String begintimeStr = nowDate + " 00:00:00";
        String endtimeStr = nowDate + " 23:59:59";
        Timestamp begintime = DateHelper.getDateTime(DateHelper.getDateFromStr(begintimeStr, DateHelper.MOST_COMMON_PATTERN));
        Timestamp endtime = DateHelper.getDateTime(DateHelper.getDateFromStr(endtimeStr, DateHelper.MOST_COMMON_PATTERN));

        return dutRenewLogRepository.findDutRenewLogByTime(userId, begintime, endtime, agreementType, vipType);
    }

    /**
     * 查询最近4天最后一次代扣的代扣日志.
     *
     * @param userId 用户ID
     * @param dutType 代扣类型
     * @param vipType 会员类型
     * @return DutRenewLog
     */
    @Override
    public DutRenewLog getLastDutRenewLog(Long userId, Integer dutType, Long vipType, Timestamp beginTime, Timestamp endTime) {
        return dutRenewLogRepository.getLastDutRenewLogAtTimeRange(userId, dutType, vipType, beginTime, endTime);
    }

    @Override
    public DutRenewLog getLastDutRenewLog(Long userId, Integer dutType, Long vipType) {
        return dutRenewLogRepository.getRecentlyDutRenewLog(userId, dutType, vipType);
    }

    /**
     * 获取 最近几天 自动续费日志
     */
    @Override
    public DutRenewLog getLastRecentlyNumDaysDutRenewLog(Long userId, int dutType, Long vipType, int num) {
        String endDayBegin = DateHelper.getFormatDate(DateHelper.caculateTime(DateHelper.getDateTime(),
            OFFSET_ONE_DAY, Constants.PRODUCT_PERIODUNIT_DAY),
            "yyyy-MM-dd 00:00:00");
        Timestamp endTime = DateHelper.getDateTime(DateHelper.getDateFromStr(endDayBegin, MOST_COMMON_PATTERN));
        Timestamp beginTime = DateHelper.caculateTime(endTime, -num, Constants.PRODUCT_PERIODUNIT_DAY);

        return dutRenewLogRepository.getLastDutRenewLogAtTimeRange(userId, dutType, vipType, beginTime, endTime);
    }

    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    @Override
    public void shardSaveRenewLog(DutRenewLog dutRenewLog) {
        dutRenewLogRepository.save(dutRenewLog);
    }

    @Override
    public boolean shardIsAutoRenewUser(Long uid, Long vipType, Integer agreementType) {
        List<DutUserNew> dutUserNewList = findAllByUserIdAndVipType(uid, agreementType, vipType);
        for (DutUserNew dutUserNew : dutUserNewList) {
            if (!EXCLUDE_AGREEMENT_TYPES.contains(dutUserNew.getAgreementType()) && validateAutoRenew(dutUserNew)) {
                return true;
            }
        }
        return false;
    }

    private boolean isAutoRenewUserExcludeType(Long uid, Long vipType, List<Integer> typeList) {
        List<DutUserNew> dutUserNewList = findAllByUserIdAndVipType(uid, null, vipType);
        for (DutUserNew dutUserNew : dutUserNewList) {
            if (dutUserNew.isAutoRenewUser() && !typeList.contains(dutUserNew.getType()) && !EXCLUDE_AGREEMENT_TYPES.contains(dutUserNew.getAgreementType())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean shardIsAutoRenewUserIncludeType(Long uid, Long vipType, List<Integer> typeList) {
        List<DutUserNew> dutUserNewList = findAllByUserIdAndVipType(uid, null, vipType)
            .stream()
            .filter(d -> !EXCLUDE_AGREEMENT_TYPES.contains(d.getAgreementType()))
            .collect(Collectors.toList());
        for (DutUserNew dutUserNew : dutUserNewList) {
            if (null != dutUserNew.getAutoRenew()
                && DutUserNew.RENEW_AUTO == dutUserNew.getAutoRenew()
                && typeList.contains(dutUserNew.getType())) {
                return true;
            }
        }
        return false;
    }

    public List<DutRenewSetLog> queryDutLogsByUserId(Long userId, String startDate,
        String endDate, Integer operator) {
        return dutRenewSetLogService.queryDutSetLogsByUserId(userId, null, startDate, endDate, operator)
            .stream()
            .filter(s -> !EXCLUDE_AGREEMENT_TYPES.contains(s.getAgreementType()))
            .collect(Collectors.toList());
    }

    @Override
    public List<DutRenewLog> shardGetDutRenewLog(Long userId, List<Integer> dutTypeList) {
        String startDate = DateHelper.getFormatDate(DateHelper.caculateTime(
            DateHelper.getDateTime(), -3, Constants.PRODUCT_PERIODUNIT_DAY), DateHelper.SIMPLE_PATTERN);
        String endDate = DateHelper.getFormatDate(DateHelper.getDateTime(), DateHelper.SIMPLE_PATTERN);
        String begintimeStr = startDate + " 00:00:00";
        String endtimeStr = endDate + " 23:59:59";
        Timestamp beginTime = DateHelper.getDateTime(DateHelper.getDateFromStr(begintimeStr, MOST_COMMON_PATTERN));
        Timestamp endTime = DateHelper.getDateTime(DateHelper.getDateFromStr(endtimeStr, MOST_COMMON_PATTERN));

        return dutRenewLogRepository.getDutRenewLog(userId, beginTime, endTime, dutTypeList, null);
    }

    @Override
    public List<DutUserNew> findAllByUserIdAndVipType(Long userId, Integer agreementType, Long vipType) {
        return dutUserRepository.findAllByUserIdAndVipType(userId, agreementType, vipType);
    }

    @Override
    public List<DutUserNew> findIgnoreAutoRenewStatus(Long userId, Integer agreementType, Long sourceVipType, Long targetVipType) {
        return dutUserRepository.findIgnoreAutoRenewStatus(userId, agreementType, sourceVipType, targetVipType);
    }

    @Override
    public List<DutUserNew> findByUidAndVipTypeAndAutoRenew(Long userId, Integer agreementType, Long vipType, Integer autoRenewStatus) {
        return dutUserRepository.findByUidAndVipTypeAndAutoRenew(userId, agreementType, vipType, autoRenewStatus);
    }

    @Override
    public List<DutUserNew> findByUidDutTypeVipTypeAutoRenew(Long userId, Long vipType, Integer dutType, Integer autoRenewStatus) {
        return dutUserRepository.listDutUserByVipTypDutType(userId, null, vipType, dutType, autoRenewStatus);
    }

    public int updateAll(DutUserNew dutUser) {
        return dutUserRepository.updateMainlandUserDeadline(dutUser);
    }

    public int updateShardByUserIdAndVipType(DutUserNew dutUserNew) {
        return dutUserRepository.updateByUserIdAndVipType(dutUserNew);
    }

    public boolean updateUserDeadlineByUserIdAndDutType(Long userId, Long vipType, Integer dutType,
        Date deadline, Date originalDeadline) {
        return dutUserRepository.updateDutUserDeadline(userId, vipType, dutType, deadline, originalDeadline);
    }

    /**
     * 仅用于大陆续费用户信息查询
     */
    public List<DutUserNew> getSyncDutUserNews(Long userId) {
        return dutUserRepository.findAllByUserIdAndVipType(userId, null, Constants.VIP_USER_SUPER);
    }

    public void updateDutUsers(Long userId, Long typeId, Timestamp deadline, int vipCategoryMain) {
        DutUserNew dutUserNew = new DutUserNew();
        dutUserNew.setUserId(userId);
        dutUserNew.setDeadline(deadline);
        dutUserNew.setVipCategory(vipCategoryMain);
        dutUserNew.setVipType(typeId);
        updateAll(dutUserNew);
    }

    public List<DutUserNew> getSpecialSyncDutUserNews(Long uid, int vipCategory) {
        return dutUserRepository.getDutUserNewList(uid, null, vipCategory);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateDutUserByTicket(DutUserNew dutUser, AppleTicketInfoDto appleTicketInfo, AppProduct appProductNew) {
        // 兼容用户在自动续费期间 变更自动续费套餐时长；
        DutUserNew dutUserNew = dutUser.cloneParams();
        DutUserNewVO dutUserUpdater = buildDutUserUpdaterVO(dutUserNew, appleTicketInfo, appProductNew);

        modifySubscribeAutoRenewAppId(dutUser, appleTicketInfo);

        dutUserRepository.updateSelective(dutUserUpdater);

        AddRenewSetLogDto addRenewSetLogDto = AddRenewSetLogDto.builder()
            .userId(dutUserNew.getUserId())
            .type(dutUserNew.getType())
            .description(OpenDutSetLogDesp.buildChangeIAPSetLogDespByTicket(dutUserNew, appleTicketInfo, TYPE_CHANGE,
                OperateSceneEnum.OPEN_APPLECHANGE.getValue(), getPayChannelByDutType(dutUserNew.getType())))
            .platform(dutUserNew.getPlatform())
            .platformCode(dutUserNew.getPlatformCode())
            .serialRenewCount(dutUserNew.getSerialRenewCount())
            .vipType(dutUserNew.getVipType())
            .vipCategory(dutUserNew.getVipCategory())
            .operator(DutRenewSetLog.RENEW_SET)
            .amount(dutUserNew.getAmount())
            .operateTime(appleTicketInfo.getRequestDate())
            .fc("")
            .build();
        addAutoRenewSetLog(addRenewSetLogDto);
    }

    public void updateSpecialDutUsers(Long userId, Timestamp deadline, Integer type, Integer vipCategory) {
        DutUserNew dutUserNew = new DutUserNew();
        dutUserNew.setDeadline(deadline);
        if (type != null) {
            dutUserNew.setVipType(Long.valueOf(type));
        }
        dutUserNew.setVipCategory(vipCategory);
        dutUserNew.setUserId(userId);
        updateShardByUserIdAndVipType(dutUserNew);
    }

    /**
     * 分表查询代扣用户信息
     *
     * @param startTime 到期时间开始
     * @param deadline 到期时间结束
     * @param excludeType 排除的代扣类型
     * @param shardTblName 分表名称
     * @return 代扣用户列表
     */
    public List<DutUserNew> getSyncAutoRenewListByTblIndex(Integer agreementType, Long vipType, Timestamp startTime, Timestamp deadline, List<Integer> excludeType, String shardTblName) {
        List<DutUserNew> dutUserNews = dutUserRepository.getAutoRenewUsersWithExcludeTypes(agreementType, startTime, deadline, null, vipType, DutUserNew.RENEW_AUTO, excludeType, shardTblName);
        return DutUserNewUtil.distinctByUserId(dutUserNews);
    }

    /**
     * 分表查询代扣用户信息
     *
     * @param sourceVipType 升级源会员类型
     * @param targetVipType 升级目标会员类型
     * @param startTime 到期时间开始
     * @param deadline 到期时间结束
     * @param excludeType 排除的代扣类型
     * @param shardTblName 分表名称
     * @return 代扣用户列表
     */
    public List<DutUserNew> getSyncUpgradeAutoRenewListByTblIndex(Integer agreementType, Long sourceVipType, Long targetVipType, Timestamp startTime,
        Timestamp deadline, List<Integer> excludeType, String shardTblName) {
        List<DutUserNew> dutUserNews = dutUserRepository.getAutoRenewUsersWithExcludeTypes(agreementType,
            startTime, deadline, sourceVipType, targetVipType, DutUserNew.RENEW_AUTO, excludeType, shardTblName);
        return DutUserNewUtil.distinctByUserId(dutUserNews);
    }

    /**
     * 获取同步类型的TV奇异果自动续费用户
     */
    @Override
    public List<DutUserNew> getAutoRenewList(Integer agreementType, Timestamp startTime, Timestamp deadline, Long vipType, List<Integer> dutTypeList, Integer autoRenewStatus, String shardTblName) {
        return dutUserRepository.getAutoRenewUsersByVipDeadline(agreementType, startTime, deadline, vipType, dutTypeList, autoRenewStatus, shardTblName);
    }

    /**
     * 根据会员到期时间查询用户.
     *
     * @param advanceHours 代扣提前时间(小时)
     * @param interval 发起代扣时间区间(单位:小时)
     * @param vipType 会员类型
     * @param dutTypeList 代扣类型
     * @param autoRenewStatus 自动续费状态
     * @return 用户列表
     */
    @Override
    public List<DutUserNew> getDutUsersByVipDeadline(Integer advanceHours, Integer interval, Long vipType,
        List<Integer> dutTypeList, Integer autoRenewStatus, String shardTblName) {
        String beginTimeStr = DateHelper.getFormatDate(DateHelper.caculateTime(DateHelper.getDateTime(),
            advanceHours, Constants.PRODUCT_PERIODUNIT_HOUR), "yyyy-MM-dd HH:00:00");
        Timestamp beginTime = DateHelper.getDateTime(DateHelper.getDateFromStr(beginTimeStr, MOST_COMMON_PATTERN));
        Timestamp deadline = DateHelper.caculateTime(beginTime, interval, Constants.PRODUCT_PERIODUNIT_HOUR);
        return dutUserRepository.getAutoRenewUsersByVipDeadline(AgreementTypeEnum.AUTO_RENEW.getValue(), beginTime, deadline, vipType, dutTypeList, autoRenewStatus, shardTblName);
    }

    /**
     * 根据下次代扣时间查询用户.
     *
     * @param advanceHours 代扣提前时间(小时)
     * @param interval 发起代扣时间区间(单位:小时)
     * @param vipType 会员类型
     * @param dutTypeList 代扣类型
     * @param autoRenewStatus 自动续费状态
     * @return 用户列表
     */
    @Override
    public List<DutUserNew> getDutUsersByNextDutTime(Integer agreementType, Integer advanceHours, Integer interval, Long vipType,
        List<Integer> dutTypeList, Integer autoRenewStatus, String shardTblName) {
        String beginTimeStr = DateHelper.getFormatDate(DateHelper.caculateTime(DateHelper.getDateTime(),
            advanceHours, Constants.PRODUCT_PERIODUNIT_HOUR), "yyyy-MM-dd HH:00:00");
        Timestamp beginTime = DateHelper.getDateTime(DateHelper.getDateFromStr(beginTimeStr, MOST_COMMON_PATTERN));
        Timestamp deadline = DateHelper.caculateTime(beginTime, interval, Constants.PRODUCT_PERIODUNIT_HOUR);
        return dutUserRepository.getDutUsersByNextDutTime(agreementType, beginTime, deadline, vipType, dutTypeList, autoRenewStatus, shardTblName);
    }

    /**
     * 获取异步类型的 自动续费用户
     *
     * @param includeTypes 包括的异步续费类型
     */
    public List<DutUserNew> getAsyncAutoRenewListNew(Timestamp startTime, Timestamp deadline,
        List<Integer> includeTypes, String shardTblName, Integer agreementType) {
        List<DutUserNew> dutUserNews = dutUserRepository.getShardAsyncAutoRenewUsers(
            startTime, deadline, Constants.VIP_USER_SUPER, DutUserNew.RENEW_AUTO, includeTypes, Lists.newArrayList(7), shardTblName, agreementType);
        return DutUserNewUtil.distinctByUserId(dutUserNews);
    }

    /**
     * 根据 vipType 、 userid 、 autorenew 状态 获取 续费用户
     *
     * @param userId 用户id
     * @param vipType 会员类型
     * @param autoRenew 自动续费状态
     * @return 某一个用户，一种vipType 多种代扣方式 的集合
     */
    public List<DutUserNew> getDutUserNewsByVipType(Long userId, Integer agreementType, Long vipType, Integer autoRenew) {
        return dutUserRepository.findByUidAndVipTypeAndAutoRenew(userId, agreementType, vipType, autoRenew);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetUpgradeInfo(DutUserNew dutUserNew, AutoRenewUpgradeConfig autoRenewUpgradeConfig) {
        dutUserNew.setRenewPrice(autoRenewUpgradeConfig.getFinalRenewPrice());
        dutUserNew.setPcode(autoRenewUpgradeConfig.getFinalProductCode());
        dutUserRepository.save(dutUserNew);
    }

    /**
     * 更新 DutUserNew中 续费统计的部分
     *
     * @param updateDto UpdateUserRenewCountDto
     */
    private void updateRenewCountInfo(UpdateUserRenewCountDto updateDto) {
        if (RequestUtils.hasAnyNullObject(updateDto.getUserId(), updateDto.getDutType(), updateDto.getVipType(), updateDto.getAmount())) {
            return;
        }
        dutUserRepository.executeUpdate(updateDto.getUserId(), updateDto.updateSql(), updateDto.whereSql());
    }

    @Override
    public Integer getRenewPrice(DutUserNew dutUserNew) {
        Integer renewPrice = dutUserNew.getRenewPrice();
        if (renewPrice == null) {
            Integer amount = null != dutUserNew.getAmount() ? dutUserNew.getAmount() : Constants.AMOUNT_OF_COMMON_AUTORENEW;
//            renewPrice = calculateRenewPrice(dutUserNew.getType(), new AutorenewRequest(amount, dutUserNew.getVipType(), Constants.QIYUE_ORDER_AUTORENEW_FIRST_LARGE));
            renewPrice = agreementManager.getDefaultRenewPrice(dutUserNew.getType(), amount);
        }
        return renewPrice;
    }

    private DutUserNewVO buildDutUserUpdaterVO(DutUserNew dutUserNew, AppleTicketInfoDto appleTicketInfo, AppProduct appProductNew) {
        Long vipType = appProductManager.getVipTypeByAppId(appleTicketInfo.getAutoRenewProductId());
        Integer renewPrice = agreementManager.getDefaultRenewPrice(dutUserNew.getType(), appProductNew.getQuantity());
        dutUserNew.setAmount(appProductNew.getQuantity());
        dutUserNew.setPlatform(appProductNew.getPlatformId());
        dutUserNew.setPlatformCode(appProductNew.getPlatformCode());
        dutUserNew.setRenewPrice(renewPrice);
        dutUserNew.setPcode(appProductNew.getProductCode());
        dutUserNew.setVipType(vipType);
        if (appleTicketInfo.getExpiresDate() != null) {
            dutUserNew.setNextDutTime(appleTicketInfo.getExpiresDate());
        }
        dutUserNew.setUpdateTime(DateHelper.getCurrentTime());
        dutUserNew.setOperateTime(appleTicketInfo.getRequestDate());
        //收集变更字段
        DutUserNewVO.DutUserNewVOBuilder dutUserNewVOBuilder = DutUserNewVO
            .builder()
            .id(dutUserNew.getId())
            .userId(dutUserNew.getUserId());
        dutUserNewVOBuilder.vipType(vipType);
        dutUserNewVOBuilder.amount(appProductNew.getQuantity());
        dutUserNewVOBuilder.platform(appProductNew.getPlatformId());
        dutUserNewVOBuilder.renewPrice(renewPrice);
        dutUserNewVOBuilder.pcode(appProductNew.getProductCode());
        if (appleTicketInfo.getExpiresDate() != null) {
            dutUserNewVOBuilder.nextDutTime(appleTicketInfo.getExpiresDate());
        }
        dutUserNewVOBuilder.operateTime(appleTicketInfo.getRequestDate());
        return dutUserNewVOBuilder.build();
    }

    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    public void updateDutUserNewOperateTime(DutUserNew dutUserNew, AppleTicketInfoDto appleTicketInfo) {
        DutUserNewVO.DutUserNewVOBuilder dutUserNewVOBuilder = DutUserNewVO.builder().id(dutUserNew.getId()).userId(dutUserNew.getUserId());
        if (appleTicketInfo.getExpiresDate() != null) {
            dutUserNewVOBuilder.nextDutTime(appleTicketInfo.getExpiresDate());
        }
        dutUserNewVOBuilder.operateTime(appleTicketInfo.getRequestDate());

        dutUserRepository.updateSelective(dutUserNewVOBuilder.build());
    }

    /**
     * 更新下次代扣时间(苹果的过期时间当做打下代扣时间)、价格等信息
     *
     * @param autoRenewRequest {@link AutorenewRequest}
     */
    public void updateDutUserByAutoRenewRequest(final AutorenewRequest autoRenewRequest) throws Exception {
        Long userId = autoRenewRequest.getUserId();
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, autoRenewRequest.getDutType(), autoRenewRequest.getVipType());
        if (dutUserNew == null && !autoRenewRequest.isAppleOrder()) {
            LOGGER.error("找不到对应的签约记录! AutoRenewRequest={}", autoRenewRequest);
            return;
        }

        PayCenterAppleDutInfoResult appleSignInfo = null;
        if (autoRenewRequest.isAppleOrder()) {
            appleSignInfo = payCenterAppleSignManager.getSpecifiedSignInfo(userId, autoRenewRequest.getActCode());
        }
        //无签约关系时，重新保存签约关系
        if (dutUserNew == null) {
            if (Constants.QIYUE_ORDER_AUTORENEW_DUT.equals(autoRenewRequest.getAutoRenew())) {
                autoRenewRequest.setAutoRenew(Constants.QIYUE_ORDER_AUTORENEW_FIRST_LARGE);
            }
            AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(autoRenewRequest.getDutType());
            autoRenewService.doRepeatOpeningOld(autoRenewRequest.generateVipUser(), autoRenewDutType,
                autoRenewRequest.getOpeningAmount(), autoRenewRequest.getFc(), autoRenewRequest.getFv());
            VipUser vipUser = autoRenewRequest.generateVipUser();
            OperateSceneEnum operateSceneEnum = OperateSceneEnum.OPEN_APPLE_REOPEN;
            DutUserNew dutUser = doOpen(operateSceneEnum, vipUser, autoRenewRequest, autoRenewDutType, appleSignInfo);
            doSetLog(vipUser, autoRenewRequest, autoRenewDutType, dutUser, operateSceneEnum);
            return;
        }

        DutUserNewVO.DutUserNewVOBuilder dutUserNewVOBuilder = DutUserNewVO.builder().id(dutUserNew.getId()).userId(dutUserNew.getUserId());
        //苹果代扣需要更新签约价格
        if (PaymentDutType.isThirdPayChannel(autoRenewRequest.getPayChannel())) {
            if (appleSignInfo != null) {
                dutUserNewVOBuilder.signTime(appleSignInfo.getRecentSubscriptionStartDate());
            }
            Integer renewPrice = paymentDutTypeManager.getRenewPrice(autoRenewRequest.getVipType(), autoRenewRequest.getAmount(),
                autoRenewRequest.getActCode(), autoRenewRequest.getDutType(), userId);
            // 同步更新appId、amount和renewPrice，解决自动续费管理页商品名称显示错误问题
            dutUserNewVOBuilder.renewPrice(renewPrice);
            dutUserNewVOBuilder.amount(autoRenewRequest.getAmount());
            dutUserNewVOBuilder.actCode(autoRenewRequest.getActCode());
        }
        if (autoRenewRequest.getExpireTime() != null) {
            dutUserNewVOBuilder.nextDutTime(autoRenewRequest.getExpireTime());
        }

        //如果用户当前为非自动续费状态且代扣渠道为谷歌，恢复其自动续费状态
        if (dutUserNew.getAutoRenew() == DutUserNew.RENEW_NOT_AUTO) {
            if (autoRenewRequest.isGoogleBillingOrder() || autoRenewRequest.isAppleOrder()) {
                // 特别说明：谷歌自动续费订单恢复自动续费，autorenew 2 -> 3，解决autoRenew为2时将季、年和周等非月卡amount修改为1无法获取renewPrice的问题
                if (Constants.QIYUE_ORDER_AUTORENEW_DUT.equals(autoRenewRequest.getAutoRenew())) {
                    autoRenewRequest.setAutoRenew(Constants.QIYUE_ORDER_AUTORENEW_FIRST_LARGE);
                }
                AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(autoRenewRequest.getDutType());
                VipUser vipUser = autoRenewRequest.generateVipUser();
                OperateSceneEnum operateScene = autoRenewRequest.isAppleOrder() ? OperateSceneEnum.OPEN_APPLE_REOPEN : OperateSceneEnum.OPEN_GOOGLEREOPEN;
                DutUserNew dutUser = doOpen(operateScene, vipUser, autoRenewRequest, autoRenewDutType, appleSignInfo);
                doSetLog(vipUser, autoRenewRequest, autoRenewDutType, dutUser, operateScene);
                return;
            }
        }

        if (CloudConfigUtil.enableStudentDutTimesFix() && dutUserNew.getVipType() == Constants.VIP_USER_STUDENT) {
            if (dutUserNew.getActType() == null) {
                dutUserNewVOBuilder.actType(ActTypeEnum.STOP_AFTER_X.getValue());
                dutUserNewVOBuilder.actTotalPeriods(24);
                int remainPeriods = 24 - dutUserNew.getSerialRenewCount();
                dutUserNewVOBuilder.remainPeriods(remainPeriods < 0 ? 24 : remainPeriods);
            }
        }

        dutUserRepository.updateSelective(dutUserNewVOBuilder.build());
    }

    private void cancelAutoRenewSelective(DutUserNew dutUserNew, int operationType, DutUserNewVO.DutUserNewVOBuilder dutUserNewVOBuilder) {
        if (dutUserNewVOBuilder == null) {
            dutUserNewVOBuilder = DutUserNewVO.builder()
                .id(dutUserNew.getId())
                .userId(dutUserNew.getUserId());
        }
        dutUserNewVOBuilder.autoRenew(DutUserNew.RENEW_NOT_AUTO);
        dutUserNewVOBuilder.onceAutoRenew(DutUserNew.ONCE_AUTO_RENEW_YES);
        dutUserNewVOBuilder.serialRenewCount(0);

        // 处于活动状态 , reset成原价
        if (!dutPriceActService.isNotDutAct(dutUserNew.getRemainPeriods(), dutUserNew.getType())) {
            dutUserNewVOBuilder.renewPrice(dutUserService.getOriginalDutPrice(dutUserNew));
        }
        // 解绑的情况
        if (operationType == Constants.OPERATION_AUTORENEW_UNBIND) {
            dutUserNewVOBuilder.status(BindStatusEnum.UNBIND.getValue());
            // 台湾 使用，大陆不使用
            dutUserNewVOBuilder.nextDutTime(null);
            dutUserNewVOBuilder.renewPrice(null);
        }
        // 代扣方式过期
        if (operationType == Constants.OPERATION_AUTORENEW_ABANDONED) {
            dutUserNewVOBuilder.renewPrice(null);
        }
        if (!ActTypeEnum.STOP_AFTER_X.getValue().equals(dutUserNew.getActType())) {
            dutUserNewVOBuilder.remainPeriods(0);
            dutUserNewVOBuilder.actTotalPeriods(0);
            dutUserNewVOBuilder.actCode("");
            dutUserNewVOBuilder.contractPrice(null);
            dutUserNewVOBuilder.actType(null);
        }
        dutUserRepository.cancelAutoRenew(dutUserNewVOBuilder.build(), dutUserNew.getActType(), operationType);
    }

    private boolean shouldSaveAutoRenewSetLog(Integer currentAutoRenewStatus, Integer requestAutoReNwewStatus) {
        return currentAutoRenewStatus != null && !requestAutoReNwewStatus.equals(currentAutoRenewStatus)
            || null == currentAutoRenewStatus && requestAutoReNwewStatus.equals(DutUserNew.RENEW_AUTO);
    }

    private boolean hasAmountChanged(AddAutoRenewDto addAutoRenewDto, DutUserNew dutUserNew) {
        return null != addAutoRenewDto.getAmount()
            && null != dutUserNew.getAmount()
            && !addAutoRenewDto.getAmount().equals(dutUserNew.getAmount());
    }

    private Integer getRenewPriceWhenOpenByApi(DutUserNew dutUser) {
        if (dutUser.getRenewPrice() != null) {
            return dutUser.getRenewPrice();
        }
        return agreementManager.getDefaultRenewPrice(dutUser.getType(), dutUser.getAmount());
//        return paymentDutTypeManager.calculateRenewPrice(dutUser.getVipType(), dutUser.getAmount(), dutUser.getType(), dutUser.getUserId());
    }

    /**
     * 查找支持纯签约的代扣方式.
     *
     * @param vipType 会员类型
     * @param payChannel 渠道类型
     * @param amount 签约时长
     * @return dutType 代扣方式
     */
    //@DataSource(DataSourceEnum.SLAVE)
    public Integer findPureSignDutType(Long userId, Long sourceVipType, Long vipType, Integer agreementType, Integer payChannel, Integer amount) {
        Short priority = getPriority(userId, sourceVipType, vipType, agreementType, payChannel, amount);
        return findPureSignDutTypeWithPriority(sourceVipType, vipType, agreementType, payChannel, amount, priority);
    }

    public Short getPriority(Long userId, Long sourceVipType, Long vipType, Integer agreementType, Integer payChannel, Integer amount) {

        List<DutUserNew> dutUserNewList = findIgnoreAutoRenewStatus(userId, agreementType, sourceVipType, vipType);
        Short existMaxPriority = getExistMaxPriority(autoRenewDutTypeManager.getBySourceVipTypeAndVipType(sourceVipType, vipType, agreementType));
        List<AutoRenewDutType> autoRenewDutTypes = autoRenewDutTypeManager.findByVipTypeAndPayChannel(sourceVipType, vipType, agreementType, payChannel);
        Short existMaxPriorityOfPayChannel = getExistMaxPriority(autoRenewDutTypes);

        // 非自动续费用户签约当前最高优先级代扣方式
        boolean autoRenewStatus = dutUserNewList.stream().anyMatch(DutUserNew::isAutoRenewUser);
        if (!autoRenewStatus) {
            return existMaxPriorityOfPayChannel;
        }

        boolean sameAmount = dutUserNewList.stream()
            .filter(DutUserNew::isAutoRenewUser)
            .allMatch(dutUserNew -> amount != null && amount.equals(dutUserNew.getAmount()));
        if (!sameAmount) {
            return existMaxPriorityOfPayChannel;
        }

        List<AutoRenewDutType> autoRenewDutTypeList = dutUserNewList.stream()
            .filter(DutUserNew::isAutoRenewUser)
            .map(dutUserNew -> autoRenewDutTypeManager.getByDutType(dutUserNew.getType()))
            .collect(Collectors.toList());
        Short currentPriority = getCurrentPriority(autoRenewDutTypeList);
        boolean isPriceInsured = isPriceInsured(existMaxPriority, currentPriority);
        // 非保价用户签约最高优先级代扣方式
        if (!isPriceInsured) {
            return existMaxPriorityOfPayChannel;
        }

        boolean isBeingPriceInsuredPeriod = autoRenewDutTypeList.stream().noneMatch(AutoRenewDutType::expired);
        // 非保价期内用户签约最高优先级代扣方式
        if (!isBeingPriceInsuredPeriod) {
            return existMaxPriorityOfPayChannel;
        }

        List<AutoRenewDutType> autoRenewDutTypesOfPayChannel = autoRenewDutTypes.stream()
            .filter(autoRenewDutType -> Objects.equals(currentPriority, autoRenewDutType.getPriority()))
            .collect(Collectors.toList());
        // 当前渠道存在与已开通渠道优先级一致的代扣方式，签约当前优先级代扣方式
        // 否则签约目标渠道下最高优先级代扣方式
        if (CollectionUtils.isNotEmpty(autoRenewDutTypesOfPayChannel)) {
            return currentPriority;
        } else {
            return existMaxPriorityOfPayChannel;
        }
    }

    private boolean isPriceInsured(Short existMaxPriority, Short currentPriority) {
        return existMaxPriority != null && currentPriority != null && existMaxPriority > currentPriority;
    }

    private Short getCurrentPriority(List<AutoRenewDutType> autoRenewDutTypeList) {
        Optional<AutoRenewDutType> currentPriorityOptional = autoRenewDutTypeList.stream()
            .max(Comparator.comparing(AutoRenewDutType::getPriority));
        return currentPriorityOptional.map(AutoRenewDutType::getPriority).orElse(null);
    }

    private Short getExistMaxPriority(List<AutoRenewDutType> dutTypeList) {
        Optional<AutoRenewDutType> existMaxPriorityOptional = dutTypeList.stream()
            .max(Comparator.comparing(AutoRenewDutType::getPriority));
        return existMaxPriorityOptional.map(AutoRenewDutType::getPriority).orElse(null);
    }

    /**
     * 查找支持纯签约的代扣方式.
     *
     * @param vipType 会员类型
     * @param payChannel 渠道类型
     * @param amount 签约时长
     * @return dutType 代扣方式
     */
    //@DataSource(DataSourceEnum.SLAVE)
    public Integer findPureSignDutTypeWithPriority(Long sourceVipType, Long vipType, Integer agreementType, Integer payChannel, Integer amount, Short priority) {
        List<AutoRenewDutType> autoRenewDutTypes = autoRenewDutTypeManager.findByVipTypeAndPayChannelAndPriority(sourceVipType, vipType, agreementType, payChannel, priority);
        if (CollectionUtils.isEmpty(autoRenewDutTypes)) {
            return null;
        }
        //过滤非默认 partner
        autoRenewDutTypes = autoRenewDutTypes.stream().filter(AutoRenewDutType::partnerIsNull).collect(Collectors.toList());
        List<Integer> dutTypeList = autoRenewDutTypes.stream()
            .filter(AutoRenewDutType::supportPureSign)
            .map(AutoRenewDutType::getDutType)
            .collect(Collectors.toList());
        List<Integer> bindTypesOfAmount = paymentDutTypeManager.getDutTypesByVipTypeAndAmount(vipType.intValue(), amount);
        List<Integer> pureSignDutTypeList = Lists.newArrayList(dutTypeList);
        pureSignDutTypeList.retainAll(bindTypesOfAmount);

        if (CollectionUtils.isNotEmpty(pureSignDutTypeList)) {
            return pureSignDutTypeList.get(0);
        }

        return null;
    }

    public List<Integer> getSupportDirectOpenDutTypes(Long uid, Long vipType, Integer agreementType) {
        List<DutUserNew> dutUserNewList = findAllByUserIdAndVipType(uid, agreementType, vipType);
        if (CollectionUtils.isEmpty(dutUserNewList)) {
            return Collections.emptyList();
        }

        List<AutoRenewDutType> autoRenewDutTypes = Lists.newArrayList();
        AutoRenewDutType autoRenewDutType;
        for (DutUserNew dutUserNew : dutUserNewList) {
            autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutUserNew.getType());
            if (autoRenewDutType != null && !autoRenewDutType.thirdDut() && !autoRenewDutType.expired()) {
                autoRenewDutTypes.add(autoRenewDutType);
            }
        }

        if (!hasDiffDutTypePriority(autoRenewDutTypes)) {
            return Collections.emptyList();
        }

        return getMaxPriorityBindTypes(autoRenewDutTypes).stream().map(AutoRenewDutType::getDutType).collect(Collectors.toList());
    }

    private boolean hasDiffDutTypePriority(List<AutoRenewDutType> autoRenewDutTypes) {
        return autoRenewDutTypes.stream().map(AutoRenewDutType::getPriority).distinct().count() >= MORE_THAN_ONE;
    }

    private List<AutoRenewDutType> getMaxPriorityBindTypes(List<AutoRenewDutType> autoRenewDutTypes) {
        Optional<AutoRenewDutType> optionalShort = autoRenewDutTypes.stream()
            .filter(Objects::nonNull)
            .max(Comparator.comparing(AutoRenewDutType::getPriority));
        if (!optionalShort.isPresent()) {
            return Collections.emptyList();
        }
        return autoRenewDutTypes.stream().filter(autoRenewDutType -> autoRenewDutType.getPriority().equals(optionalShort.get().getPriority()))
            .sorted(Comparator.comparing(AutoRenewDutType::getPriority).reversed())
            .collect(Collectors.toList());
    }

    public List<Integer> getAutoRenewDutTypeBindList(Long uid) {
        Optional<AccountResponse> accountResponse = dutProcessor.queryAccountBindInfos(uid);
        return accountResponse.map(response -> response.getData().stream()
                .filter(AccountBindInfo::isBind)
                .filter(accountBindInfo -> {
                    AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(accountBindInfo.getType());
                    return autoRenewDutType != null && !autoRenewDutType.expired()
                            && autoRenewDutType.isAutoRenewAgreementType();
                })
                .map(AccountBindInfo::getType)
                .collect(Collectors.toList())).orElseGet(Lists::newArrayList);
    }

    public List<Integer> getPasswordFreeBindList(Long uid) {
        Optional<AccountResponse> accountResponse = dutProcessor.queryAccountBindInfos(uid);
        return accountResponse.map(response -> response.getData().stream()
            .filter(AccountBindInfo::isBind)
            .filter(accountBindInfo -> validPasswordFreeDutType(accountBindInfo.getType()))
            .map(AccountBindInfo::getType)
            .collect(Collectors.toList())
        ).orElseGet(Lists::newArrayList);
    }

    public List<Integer> filterBindList(List<Integer> bindTypes, Long vipType, Long uid, Integer agreementType) {
        if (CollectionUtils.isEmpty(bindTypes)) {
            return Collections.emptyList();
        }
        List<AutoRenewDutType> filteredAutoRenewDutTypes = Lists.newArrayList();
        List<Integer> bindedDutTypePrioritys = Lists.newArrayList();
        AutoRenewDutType autoRenewDutType;
        for (Integer bindType : bindTypes) {
            autoRenewDutType = autoRenewDutTypeManager.getByDutType(bindType);
            if (validDutType(autoRenewDutType) && ObjectUtils.equals(autoRenewDutType.getAgreementType(), agreementType)) {
                filteredAutoRenewDutTypes.add(autoRenewDutType);
                bindedDutTypePrioritys.add(autoRenewDutType.getPriority().intValue());
            }
        }
        if (CollectionUtils.isEmpty(bindedDutTypePrioritys)) {
            return Collections.emptyList();
        }

        List<DutUserNew> dutUserNewList = findAllByUserIdAndVipType(uid, agreementType, vipType);
        if (CollectionUtils.isEmpty(dutUserNewList)) {
            return getMaxPriorityBindTypes(filteredAutoRenewDutTypes).stream().map(AutoRenewDutType::getDutType).collect(Collectors.toList());
        }
        Optional<Integer> maxOpendDutTypePriority = dutUserNewList.stream()
            .map(dutUserNew -> autoRenewDutTypeManager.getByDutType(dutUserNew.getType()))
            .filter(this::validDutType)
            .map(AutoRenewDutType::getPriority)
            .map(Short::intValue)
            .max(Comparator.comparing(Integer::intValue));

        bindedDutTypePrioritys.sort(Comparator.reverseOrder());
        Integer maxBindedPriority = bindedDutTypePrioritys.get(0);
        if (maxBindedPriority < maxOpendDutTypePriority.orElse(maxBindedPriority)) {
            return Collections.emptyList();
        }
        return getMaxPriorityBindTypes(filteredAutoRenewDutTypes).stream().map(AutoRenewDutType::getDutType).collect(Collectors.toList());
    }

    private boolean validDutType(AutoRenewDutType autoRenewDutType) {
        if (autoRenewDutType == null) {
            return false;
        }
        return !autoRenewDutType.thirdDut() && !autoRenewDutType.expired();
    }

    public Boolean validPasswordFreeDutType(Integer dutType) {
        AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutType);
        return autoRenewDutType != null && !autoRenewDutType.expired() && autoRenewDutType.getVipType() == null;
    }

    @Override
    public List<SimpleDutUserNew> getZhiMaGoUsersByNextDutTimeRange(Long vipType, Integer agreementType, Timestamp startTime, Timestamp endTime, String shardTblName) {
        List<SimpleDutUserNew> dutUserNews = dutUserRepository.getByNextDutTimeRange(AgreementTypeEnum.ZHIMA_GO,
            startTime, endTime, null, vipType, AgreementStatusEnum.VALID.getValue(), shardTblName);
        return DutUserNewUtil.distinctByUserIdNew(dutUserNews);
    }

    @Override
    public List<SimpleDutUserNew> getWeChatPayScoreUsersByDeadlineRange(Long vipType, Timestamp startTime, Timestamp endTime, String shardTblName) {
        List<SimpleDutUserNew> dutUserNews = dutUserRepository.getByDeadlineRange(AgreementTypeEnum.WECHAT_PAY_SCORE,
            startTime, endTime, null, vipType, AgreementStatusEnum.VALID.getValue(), shardTblName);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return dutUserNews;
        }
        Set<Long> userIdSet = Sets.newHashSet();
        List<SimpleDutUserNew> resultDutUserList = Lists.newLinkedList();
        for (SimpleDutUserNew dutUserNew : dutUserNews) {
            Timestamp signTime = dutUserNew.getSignTime();
            //微信支付分一期是系统自动发起创单，二期是手动发起创单，用户按需创单。
            //由于一期不再投放，所以根据签约时间判断是自动发起创单还是手动创单
            if (signTime.after(RenewTaskComponent.WECHAT_PAY_SCORE_MANUAL_CREATE_ORDER_TIME)) {
                continue;
            }
            if (!userIdSet.contains(dutUserNew.getUserId())){
                resultDutUserList.add(dutUserNew);
                userIdSet.add(dutUserNew.getUserId());
            }
        }
        return resultDutUserList;
    }

    @Override
    public List<SimpleDutUserNew> getWeChatPayScoreUsersByNextDutTimeRange(Long vipType, Integer agreementType, Timestamp startTime, Timestamp endTime, String shardTblName) {
        List<SimpleDutUserNew> dutUserNews = dutUserRepository.getByNextDutTimeRange(AgreementTypeEnum.WECHAT_PAY_SCORE,
            startTime, endTime, null, vipType, AgreementStatusEnum.VALID.getValue(), shardTblName);
        return DutUserNewUtil.distinctByUserIdNew(dutUserNews);
    }

    @Override
    public List<SimpleDutUserNew> getByAgreementTypesAndDutTimeRange(Long vipType, Timestamp startTime, Timestamp endTime, String shardTblName, Set<Integer> agreementTypeSet) {
        List<Integer> agreementTypeValueList = new ArrayList<>(agreementTypeSet);
        List<SimpleDutUserNew> dutUserNews = dutUserRepository.getByAgreementTypeListAndNextDutTimeRange(agreementTypeValueList,
            startTime, endTime, null, vipType, AgreementStatusEnum.VALID.getValue(), shardTblName);
        ListMultimap<Integer, SimpleDutUserNew> simpleDutUserNewMultimap = ArrayListMultimap.create();
        dutUserNews.stream().forEach(dutUser -> simpleDutUserNewMultimap.put(dutUser.getAgreementType(), dutUser));
        if (simpleDutUserNewMultimap.isEmpty()) {
            return Collections.EMPTY_LIST;
        }
        List<SimpleDutUserNew> finalDutUserList = Lists.newArrayList();
        for (Integer agreementType : simpleDutUserNewMultimap.keySet()) {
            List<SimpleDutUserNew> tempUserList = simpleDutUserNewMultimap.get(agreementType);
            finalDutUserList.addAll(DutUserNewUtil.distinctByUserIdNew(tempUserList));
        }
        return finalDutUserList;
    }

    @Transactional(propagation=Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void updateNextDutTime(List<UpdateDutTimeReqDto> updateDutTimeReqDto) {
        Long uid = updateDutTimeReqDto.get(0).getUid();
        List<DutUserNew> dutUserNews = findDutUserNews(uid, AgreementTypeEnum.AUTO_RENEW.getValue());
        if (CollectionUtils.isEmpty(dutUserNews)) {
            throw new BizException(CodeEnum.ERROR_USER_NOT_AUTORENEW);
        }
        updateDutTimeReqDto.stream().forEach(
            reqDto -> doUpdateNextDutTime(reqDto.getUid(), reqDto.getVipType(), reqDto.getDutType(), reqDto.getNextDutTime(), reqDto.getPayChannel()));
    }


    public void doUpdateNextDutTime(Long userId, Long vipType, Integer dutType, Timestamp nextDutTime, Integer payChannel) {
        Assert.notNull(userId, "userId不能为空");
        Assert.notNull(vipType, "vipType不能为空");
        Assert.notNull(dutType, "dutType不能为空");
        Assert.notNull(nextDutTime, "nextDutTime不能为空");
        AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutType);
        if (autoRenewDutType == null || !autoRenewDutType.getPayChannel().equals(payChannel)) {
            throw new BizException(CodeEnum.ERROR_PARAM);
        }
        dutUserRepository.updateNextDutTime(userId, autoRenewDutType.getAgreementType(), vipType, dutType, nextDutTime);
    }

    public int getManualDutSuccessTimes(Long userId, Long vipType, String beginTime, String endTime) {
        return (int) searchDutUsers(userId, beginTime, endTime, DutRenewLog.PAY_SUCCESS)
            .stream()
            .filter(dutRenewLog -> dutRenewLog.getVipType().equals(vipType))
            .filter(DutRenewLog::manualDutLog).count();
    }

    public List<DutRenewLog> findDutRenewLogByTime(Long userId, Timestamp beginTime, Timestamp endTime, Integer agreementType, Long vipType){
        return dutRenewLogRepository.findDutRenewLogByTime(userId, beginTime, endTime, agreementType, vipType);
    }

    public int getPoolType(Long userId, Integer poolTypeOffset) {
        return (int) (userId % ApplicationContextUtil.getBean(AutoRenewConfig.class).getAutoRenewTaskServerNum()) + poolTypeOffset;
    }
}
