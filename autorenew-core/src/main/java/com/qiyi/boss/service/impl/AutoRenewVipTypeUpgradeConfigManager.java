package com.qiyi.boss.service.impl;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.qiyi.vip.trade.autorenew.domain.AutoRenewVipTypeUpgradeConfig;
import com.qiyi.vip.trade.autorenew.mapper.AutoRenewVipTypeUpgradeConfigMapper;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2020-03-18
 * Time: 16:17
 */
@Component
public class AutoRenewVipTypeUpgradeConfigManager {

	@Resource
	private AutoRenewVipTypeUpgradeConfigMapper autoRenewVipTypeUpgradeConfigMapper;

	/**
	 * 获取节点配置
	 * @param vipType 会员类型
	 * @return list
	 */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewVipTypeUpgradeConfig_getByVipType", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public AutoRenewVipTypeUpgradeConfig getByVipType(Long vipType, Integer agreementType) {
        AutoRenewVipTypeUpgradeConfig query = new AutoRenewVipTypeUpgradeConfig();
        query.setVipType(vipType);
        query.setAgreementType(agreementType);
		List<AutoRenewVipTypeUpgradeConfig> autoRenewVipTypeUpgradeConfigList = autoRenewVipTypeUpgradeConfigMapper.listSelective(query);
		if (CollectionUtils.isNotEmpty(autoRenewVipTypeUpgradeConfigList)) {
			return autoRenewVipTypeUpgradeConfigList.get(0);
		}

		return null;
	}

	/**
	 * 获取会员类型分组配置
	 * @param upgradeGroup 会员类型分组ID
	 * @return list
	 */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewVipTypeUpgradeConfig_getByUpgradeGroup", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public List<AutoRenewVipTypeUpgradeConfig> getByUpgradeGroup(Integer upgradeGroup) {
		AutoRenewVipTypeUpgradeConfig query = new AutoRenewVipTypeUpgradeConfig();
		query.setUpgradeGroup(upgradeGroup);
		return autoRenewVipTypeUpgradeConfigMapper.listSelective(query);
	}

	/**
	 * 获取会员类型分组配置
	 * @param vipType 会员类型
	 * @return list
	 */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewVipTypeUpgradeConfig_getSameGroupVipTypes", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public List<Long> getSameGroupVipTypes(Long vipType, Integer agreementType) {
		AutoRenewVipTypeUpgradeConfig autoRenewVipTypeUpgradeConfig = getByVipType(vipType, agreementType);
		if (autoRenewVipTypeUpgradeConfig == null) {
			return Collections.singletonList(vipType);
		}

		List<AutoRenewVipTypeUpgradeConfig> autoRenewVipTypeUpgradeConfigList = getByUpgradeGroup(autoRenewVipTypeUpgradeConfig.getUpgradeGroup());
		if (CollectionUtils.isEmpty(autoRenewVipTypeUpgradeConfigList)) {
			return Collections.singletonList(vipType);
		}

		return autoRenewVipTypeUpgradeConfigList.stream()
				.map(AutoRenewVipTypeUpgradeConfig::getVipType)
				.distinct()
				.collect(Collectors.toList());
	}

	/**
	 * 获取会员类型分组配置
	 * @param area 会员类型
	 * @return list
	 */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewVipTypeUpgradeConfig_listVipTypesByArea", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public List<Long> listVipTypesByArea(String area) {
		List<AutoRenewVipTypeUpgradeConfig> configs = autoRenewVipTypeUpgradeConfigMapper.listUpgradeConfigByArea(area);
		return configs.stream()
				.map(AutoRenewVipTypeUpgradeConfig::getVipType)
				.collect(Collectors.toList());
	}

}
