package com.qiyi.boss.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

import com.qiyi.boss.Constants;
import com.qiyi.boss.exception.BizException;
import com.qiyi.boss.model.BaseResponse.CodeEnum;
import com.qiyi.boss.outerinvoke.PayInfoApi;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AgreementInfoDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeReqDTO;
import com.qiyi.vip.service.PaymentDutTypeClient;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> liuwanqiang
 * Date: 2017-03-09
 * Time: 23:08
 */
@Component
@Slf4j
public class PaymentDutTypeManager {

    @Resource
    private PaymentDutTypeClient paymentDutTypeClient;

    @Resource
    private AutoRenewDutTypeManager autoRenewDutTypeManager;

    //@DataSource(DataSourceEnum.SLAVE)
    public Integer getDutTypeWithSourceVipType(Integer payType, Integer payChannel, Integer sourceVipType, Integer vipType, String actCode, Integer amount, Long uid, String skuId) {
        AgreementInfoDTO agreementInfoDTO = getDutTypeWithSourceVipTypeFromPayInfo(payType, payChannel, sourceVipType, vipType, actCode, amount, uid, skuId);
        return agreementInfoDTO != null ? agreementInfoDTO.getDutType() : null;
    }

    private AgreementInfoDTO getDutTypeWithSourceVipTypeFromPayInfo(Integer payType, Integer payChannel, Integer sourceVipType, Integer vipType, String actCode, Integer amount, Long uid, String skuId) {
        PaymentDutTypeReqDTO paymentDutTypeReqDTO = new PaymentDutTypeReqDTO();
        paymentDutTypeReqDTO.setPayType(payType == null ? null : payType.longValue());
        paymentDutTypeReqDTO.setPayChannel(payChannel);
        paymentDutTypeReqDTO.setSourceVipType(sourceVipType == null ? null : sourceVipType.longValue());
        paymentDutTypeReqDTO.setActCode(StringUtils.isBlank(actCode) ? null : actCode);
        paymentDutTypeReqDTO.setAmount(amount);
        paymentDutTypeReqDTO.setVipType(vipType == null ? null : vipType.longValue());
        paymentDutTypeReqDTO.setSkuId(skuId);
        Callable<SingleResponse<AgreementInfoDTO>> invokePayInfo = () -> paymentDutTypeClient.getAgreementInfo(paymentDutTypeReqDTO);
        SingleResponse<AgreementInfoDTO> response = PayInfoApi.invokePayInfoForSingleResponse(invokePayInfo);
        if (response != null && response.isSuccess()) {
            AgreementInfoDTO respData = response.getData();
            if (respData == null) {
                log.error("getDutTypeWithSourceVipTypeFromPayInfo get null: uid:{}, dutTypeFromPayInfo:{}, param:{}",uid, response,  paymentDutTypeReqDTO);
                return null;
            }
            log.info("getDutTypeWithSourceVipTypeFromPayInfo: uid:{}, dutTypeFromPayInfo:{}, param:{}", uid, respData,  paymentDutTypeReqDTO);
            return respData;
        } else {
            log.error("getDutTypeWithSourceVipTypeFromPayInfo failed, uid:{}, param:{},  response:{}", uid, paymentDutTypeReqDTO, response);
            throw BizException.newException(CodeEnum.ERROR_PARAM);
        }
    }

    //@DataSource(DataSourceEnum.SLAVE)
    public Integer getRenewPrice(Long vipType, Integer amount, String actCode, Integer dutType, Long uid) {
        return getRenewPriceFromPayInfo(vipType, amount, actCode, dutType, uid);
    }

    private Integer getRenewPriceFromPayInfo(Long vipType, Integer amount, String actCode, Integer dutType, Long uid) {
        Callable<SingleResponse<PaymentDutTypeDTO>> invokePayInfo = () -> paymentDutTypeClient.getRenewPrice(vipType, amount, actCode, Collections.singletonList(dutType));
        SingleResponse<PaymentDutTypeDTO> response = PayInfoApi.invokePayInfoForSingleResponse(invokePayInfo);
        if (response != null && response.isSuccess()) {
            PaymentDutTypeDTO paymentDutTypeDTO = response.getData();
            Integer renewPriceFromPayInfo = paymentDutTypeDTO == null ? null : paymentDutTypeDTO.getRenewPrice();
            log.info("getRenewPriceFromPayInfo success! paymentDutTypeDTO:{} uid:{}, vipType:{}, amount:{}, actCode:{}, dutType:{}", paymentDutTypeDTO, uid, vipType, amount, actCode, dutType);
            return renewPriceFromPayInfo;
        }
        log.error("getRenewPrice error! uid:{}, vipType:{}, amount:{}, actCode:{}, dutType:{}, response:{}", uid, vipType, amount, actCode, dutType, response);
        throw BizException.newException(CodeEnum.ERROR_PARAM);
    }

    /**
     * 微信不同代扣方式可能对应不同时长
     * 包月(6)/青春套餐(22)：amount=1
     * 包季(23)：amount=3
     * 包年(24)：amount=12
     *
     * @param dutType 代扣类型
     * @return Integer
     */
    //@DataSource(DataSourceEnum.SLAVE)
    public Integer getWechatAmount(Integer dutType) {
        return getWechatAmountByDutTypeFromPayInfo(dutType);
    }

    public Integer getWechatAmountByDutTypeFromPayInfo(Integer dutType) {
        Callable<SingleResponse<Integer>> invokePayInfo = () -> paymentDutTypeClient.getWechatAmountByDutType(dutType);
        try {
            SingleResponse<Integer> response = PayInfoApi.invokePayInfoForSingleResponse(invokePayInfo);
            log.info("getWechatAmountByDutTypeFromPayInfo, dutType:{}, ressponse:{}", dutType, response);
            if (response != null && response.isSuccess() && response.getData() != null) {
                return response.getData();
            }
            return Constants.AMOUNT_OF_COMMON_AUTORENEW;
        } catch (Exception e) {
            log.error("getWechatAmountByDutTypeFromPayInfo error:", e);
            return Constants.AMOUNT_OF_COMMON_AUTORENEW;
        }

    }


    public Integer getCommonAutorenewDutPrice(Integer vipType, Integer amount){
        return getCommonAutorenewDutPriceFromPayInfo(vipType, amount);
    }

    private Integer getCommonAutorenewDutPriceFromPayInfo(Integer vipType, Integer amount) {
        List<PaymentDutTypeDTO> paymentDutTypeDTOS = getDutTypeExcludeActCodeFromPayInfo(vipType, amount).stream()
            .filter(paymentDutTypeDTO -> dutTypeInEffect(autoRenewDutTypeManager.getByDutType(paymentDutTypeDTO.getDutType())))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(paymentDutTypeDTOS)) {
            return null;
        }
        paymentDutTypeDTOS.sort(Comparator.comparing(PaymentDutTypeDTO::getRenewPrice));
        return paymentDutTypeDTOS.get(0).getRenewPrice();
    }

    public List<Integer> getDutTypesByVipTypeAndAmount(Integer vipType, Integer amount) {
        return getDutTypeExcludeActCodeFromPayInfo(vipType, amount).stream()
            .map(PaymentDutTypeDTO::getDutType)
            // 此处校验时间是因为支付渠道服务不判断时间
            .filter(dutType -> dutTypeInEffect(autoRenewDutTypeManager.getByDutType(dutType)))
            .distinct()
            .collect(Collectors.toList());
    }

    private List<PaymentDutTypeDTO> getDutTypeExcludeActCodeFromPayInfo(Integer vipType, Integer amount) {
        Callable<MultiResponse<PaymentDutTypeDTO>> invokePayInfo = () -> paymentDutTypeClient.getDutTypeExcludeActCode(vipType.longValue(), amount, null);
        try {
            MultiResponse<PaymentDutTypeDTO> response = PayInfoApi.invokePayInfoForMultiResponse(invokePayInfo);
            log.info("getDutTypeExcludeActCodeFromPayInfo, vip:{}, amount:{}", vipType, amount);
            if (response != null && response.isSuccess() && response.getData() != null) {
                return response.getData();
            }
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("getDutTypeExcludeActCodeFromPayInfo error:", e);
            return Collections.emptyList();
        }
    }

    private boolean dutTypeInEffect(AutoRenewDutType dutType) {
        return dutType != null && (dutType.getValidEndTime() == null || !dutType.expired());
    }

    //@DataSource(DataSourceEnum.SLAVE)
    public PaymentDutTypeDTO getDutTypeByActCode(String actCode) {
        return getDutTypeByActCodeFromPayInfo(actCode);
    }


    private PaymentDutTypeDTO getDutTypeByActCodeFromPayInfo(String actCode) {
        Callable<SingleResponse<PaymentDutTypeDTO>> invokePayInfo = () -> paymentDutTypeClient.getDutTypeByActCode(actCode);
        try {
            SingleResponse<PaymentDutTypeDTO> response = PayInfoApi.invokePayInfoForSingleResponse(invokePayInfo);
            log.info("getDutTypeByActCodeFromPayInfo, actCode:{}, resonse:{}", actCode, response);
            if (response != null && response.isSuccess() && response.getData() != null) {
                return response.getData();
            }
        } catch (Exception e) {
            log.error("getDutTypeByActCodeFromPayInfo error:", e);
        }
        return null;
    }

    //@DataSource(DataSourceEnum.SLAVE)
    public List<Integer> getPayTypes(Integer payChannel, Integer vipType) {
        return getPaymentTypeByPayChannelFromPayInfo(payChannel, vipType)
            .stream()
            .map(PaymentDutTypeDTO::getPayType)
            .distinct()
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    public List<PaymentDutTypeDTO> getPaymentTypeByPayChannelFromPayInfo(Integer payChannel, Integer vipType) {
        Callable<MultiResponse<PaymentDutTypeDTO>> invokePayInfo = () -> paymentDutTypeClient.getPayTypeByPayChannel(payChannel, vipType);
        try {
            MultiResponse<PaymentDutTypeDTO> response = PayInfoApi.invokePayInfoForMultiResponse(invokePayInfo);
            log.info("getPaymentTypeByPayChannelFromPayInfo, payChannel:{}, vipType:{}", payChannel, vipType);
            if (response != null && response.isSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
                return response.getData();
            }
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("getPaymentTypeByPayChannelFromPayInfo error:", e);
            return Collections.emptyList();
        }

    }

    public PaymentDutTypeDTO getPasswordFreeDutType(Integer payType) {
        return getPasswordFreeDutTypeFromPayInfo(payType);
    }

    public PaymentDutTypeDTO getPasswordFreeDutTypeFromPayInfo(Integer payType) {
        Callable<MultiResponse<PaymentDutTypeDTO>> invokePayInfo = () -> paymentDutTypeClient.getDutTypeByPayType(payType);
        try {
            MultiResponse<PaymentDutTypeDTO> response = PayInfoApi.invokePayInfoForMultiResponse(invokePayInfo);
            log.info("getPasswordFreeDutTypeFromPayInfo, payType:{}, response:{}", payType, response);
            if (response != null && response.isSuccess() && response.getData() != null) {
                return response.getData()
                    .stream()
                    .filter(paymentDutTypeDTO -> paymentDutTypeDTO.getVipType() == null)
                    .findFirst().orElse(null);
            }
            return null;
        } catch (Exception e) {
            log.error("getPasswordFreeDutTypeFromPayInfo error:", e);
            return null;
        }
    }

    public Integer getMaxDutTypePriority(Integer payType, Integer vipType, Integer amount) {
        List<Integer> dutTypes = getPaymentDutTypeDTOFromPayInfo(payType, vipType, amount)
            .stream()
            .map(PaymentDutTypeDTO::getDutType)
            .collect(Collectors.toList());
        return dutTypes.stream()
            .map(type -> autoRenewDutTypeManager.getByDutType(type))
            .filter(Objects::nonNull)
            .filter(AutoRenewDutType::supportPureSign)
            .filter(autoRenewDutType -> autoRenewDutType.getValidEndTime() == null || !autoRenewDutType.expired())
            .map(AutoRenewDutType::getPriority)
            .map(Short::intValue)
            .reduce(Integer::max).orElse(null);
    }

    private List<PaymentDutTypeDTO> getPaymentDutTypeDTOFromPayInfo(Integer payType, Integer vipType, Integer amount) {
        Callable<MultiResponse<PaymentDutTypeDTO>> invokePayInfo = () -> paymentDutTypeClient.getDutTypes(payType.longValue(), vipType, amount);
        try {
            MultiResponse<PaymentDutTypeDTO> response = PayInfoApi.invokePayInfoForMultiResponse(invokePayInfo);
            log.info("getPaymentDutTypeDTOFromPayInfo, payType:{}, vipType:{}, amount:{}", payType, vipType, amount);
            if (response != null && response.isSuccess() && response.getData() != null) {
                return response.getData();
            }
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("getPaymentDutTypeDTOFromPayInfo error:", e);
            return Collections.emptyList();
        }
    }
}
