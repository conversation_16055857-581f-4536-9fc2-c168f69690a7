package com.qiyi.boss.service;

import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.Map;
import java.util.Objects;

import com.qiyi.boss.Constants;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.utils.GsonUtils;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;

import static com.qiyi.boss.Constants.ORDER_FINISHED_REAL_FEE;

/**
 * <AUTHOR>
 * @date: 2017/8/22
 */
@Data
public class AutorenewRequest {
    private static final Logger LOGGER = LoggerFactory.getLogger(AutorenewRequest.class);

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 记录结算价
     * 需要根据结算价关联自动续费活动
     */
    private Integer orderFee;

    private String pid;

    /**
     * 支付时间，
     * 活动时间范围判定
     */
    private Timestamp payTime;

    private Integer dutType;

    private Integer payType;

    private Integer productType;

    private Long vipType;

    private Integer amount;

    private String orderCode;

    private Long platformId;

    private Timestamp startTime;

    private Timestamp deadline;

    private String actCode;

    private String autoRenew;

    private Integer status;

    private String platformCode;

    private Timestamp expireTime;

    private String fc;

    private String fv;

    private Long sourceVipType;

    private String currencyUnit;

    private String refer;

    private String timeZone;

    private Integer payChannel;

    private String partner;

    private String thirdUid;
    private String tradeNo;
    /**
     * 订单传递的签约代扣周期，如果dutAmount不为空，优先使用该值
     */
    private Integer dutAmount;
    /**
     * 是否为新的协议模式
     */
    private boolean isAgreementMode;
    private Integer type;

    private Integer agreementType;

    private String skuId;

    private Timestamp noAppleMaxNextDutTime;

    private boolean newUpgradeMode;

    private Integer realFee;

    private String bundleID;

    private String source;
    /**
     * 是否是签约转普通
     */
    private Boolean signTransferCommon;

    public AutorenewRequest() {
    }

    public AutorenewRequest(Integer amount, Long vipType, Integer agreementType) {
        this.amount = amount;
        this.vipType = vipType;
        this.agreementType = agreementType;
    }

    public AutorenewRequest(Integer amount, Long vipType, Integer agreementType, Integer dutType) {
        this.amount = amount;
        this.vipType = vipType;
        this.dutType = dutType;
    }

    public AutorenewRequest(Integer amount, Long vipType, String autoRenew, Integer agreementType) {
        this.amount = amount;
        this.vipType = vipType;
        this.autoRenew = autoRenew;
        this.agreementType = agreementType;
    }

    public static AutorenewRequest transferFromMessage(Map<String, String> data, String msgId) {
        AutorenewRequest autorenewRequest = new AutorenewRequest();
        try {
            resetRequest(data, autorenewRequest);
        } catch (Exception e) {
            LOGGER.warn("Error happen when build AutorenewRequest from order finished message, msg:{}, msgId:{}", data, msgId, e);
        }
        return autorenewRequest;
    }

    public static AutorenewRequest transferFromMessage(Map<String, String> data) {
        AutorenewRequest autorenewRequest = new AutorenewRequest();
        try {
            resetRequest(data, autorenewRequest);
        } catch (Exception e) {
            LOGGER.warn("Error happen when build AutorenewRequest from order finished message, msg:{}, msgIds:{}", e, data);
        }
        return autorenewRequest;
    }

    private static void resetRequest(Map<String, String> data, AutorenewRequest autorenewRequest) {
        String uid = data.get(Constants.ORDER_FINISHED_MSG_KEY_UID);
        if (notNull(uid)) {
            autorenewRequest.setUserId(Long.valueOf(uid));
        }
        String userId = data.get(Constants.ORDER_FINISHED_MSG_KEY_USERID);
        if (notNull(userId)) {
            autorenewRequest.setUserId(Long.valueOf(userId));
        }
        String autoRenew = data.get(Constants.ORDER_FINISHED_MSG_KEY_AUTORENEW);
        if (notNull(autoRenew)) {
            autorenewRequest.setAutoRenew(autoRenew);
        }
        String patType = data.get(Constants.ORDER_FINISHED_MSG_KEY_PAYTYPE);
        if (notNull(patType)) {
            autorenewRequest.setPayType(Integer.valueOf(patType));
        }
        String productType = data.get(Constants.ORDER_FINISHED_MSG_KEY_PRODUCTTYPE);
        if (notNull(productType)) {
            autorenewRequest.setProductType(Integer.valueOf(productType));
        }
        String productSubType = data.get(Constants.ORDER_FINISHED_MSG_KEY_PRODUCTSUBTYPE);
        if (notNull(productSubType)) {
            autorenewRequest.setVipType(Long.valueOf(productSubType));
        }
        String amount = data.get(Constants.ORDER_FINISHED_MSG_KEY_AMOUNT);
        if (notNull(amount)) {
            autorenewRequest.setAmount(Integer.valueOf(amount));
        }
        String orderCode = data.get(Constants.ORDER_FINISHED_MSG_KEY_ORDERCODE);
        if (notNull(orderCode)) {
            autorenewRequest.setOrderCode(orderCode);
        }
        String platformCode = data.get(Constants.ORDER_FINISHED_MSG_KEY_PLATFORMCODE);
        if (notNull(platformCode)) {
            autorenewRequest.setPlatformCode(platformCode);
        }

        Long platformId = MapUtils.getLong(data, Constants.ORDER_FINISHED_MSG_KEY_PLATFORM);
        if (platformId != null) {
            autorenewRequest.setPlatformId(platformId);
        }
        String startTime = data.get(Constants.ORDER_FINISHED_MSG_KEY_STARTTIME);
        if (notNull(startTime)) {
            autorenewRequest.setStartTime(Timestamp.valueOf(startTime));
        }
        String endTime = data.get(Constants.ORDER_FINISHED_MSG_KEY_ENDTIME);
        if (notNull(endTime)) {
            autorenewRequest.setDeadline(Timestamp.valueOf(endTime));
        }
        String actCode = data.get(Constants.ORDER_FINISHED_MSG_KEY_ACTCODE);
        if (notNull(actCode)) {
            autorenewRequest.setActCode(actCode);
        }
        String status = data.get(Constants.ORDER_FINISHED_MSG_KEY_STATUS);
        if (notNull(status)) {
            autorenewRequest.setStatus(Integer.valueOf(status));
        }
        String expireTime = data.get(Constants.ORDER_FINISHED_MSG_KEY_EXPIRETIME);
        if (notNull(expireTime)) {
            autorenewRequest.setExpireTime(Timestamp.valueOf(expireTime));
        }
        String orderFee = data.get(Constants.ORDER_FINISHED_MSG_KEY_ORDERFEE);
        if (notNull(orderFee)) {
            autorenewRequest.setOrderFee(Integer.valueOf(orderFee));
        }
        String payTime = data.get(Constants.ORDER_FINISHED_MSG_KEY_PAYTIME);
        if (notNull(payTime)) {
            autorenewRequest.setPayTime(Timestamp.valueOf(payTime));
        }
        String pid = data.get(Constants.ORDER_FINISHED_MSG_KEY_PID);
        if (notNull(pid)) {
            autorenewRequest.setPid(pid);
        }
        String fc = data.get(Constants.ORDER_FINISHED_MSG_KEY_FC);
        if (notNull(fc)) {
            autorenewRequest.setFc(fc);
        }
        String fv = data.get(Constants.ORDER_FINISHED_MSG_KEY_FV);
        if (StringUtils.isNotBlank(fv)) {
            autorenewRequest.setFv(fv);
        }
        String sourceType = data.get(Constants.ORDER_FINISHED_MSG_KEY_SOURCETYPE);
        if (notNull(sourceType)) {
            autorenewRequest.setSourceVipType(Long.valueOf(sourceType));
        }
        String currencyUnit = data.get(Constants.ORDER_FINISHED_MSG_KEY_CURRENCYUNIT);
        if (notNull(currencyUnit)) {
            autorenewRequest.setCurrencyUnit(currencyUnit);
        }
        String timeZone = data.get(Constants.ORDER_FINISHED_MSG_KEY_TIMEZONE);
        if (notNull(timeZone)) {
            autorenewRequest.setTimeZone(timeZone);
        }
        String payChannel = data.get(Constants.ORDER_FINISHED_MSG_KEY_PAYCHANNEL);
        if (notNull(payChannel)) {
            autorenewRequest.setPayChannel(Integer.valueOf(payChannel));
        }
        String partner = data.get(Constants.ORDER_FINISHED_MSG_KEY_PARTNER);
        if (StringUtils.isNotBlank(partner)) {
            autorenewRequest.setPartner(partner);
        }
        String thirdUid = data.get(Constants.ORDER_FINISHED_MSG_KEY_THIRD_UID);
        if (notNull(thirdUid)) {
            autorenewRequest.setThirdUid(thirdUid);
        }
        String tradeNo = data.get(Constants.ORDER_FINISHED_MSG_KEY_TRADE_NO);
        if (notNull(tradeNo)) {
            autorenewRequest.setTradeNo(tradeNo);
        }
        String renewType = data.get(Constants.ORDER_FINISHED_MSG_KEY_RENEW_TYPE);
        if (notNull(renewType)) {
            autorenewRequest.setDutType(Integer.valueOf(renewType));
        }
        String orderType = data.get(Constants.ORDER_FINISHED_MSG_KEY_TYPE);
        if (notNull(orderType)) {
            autorenewRequest.setType(Integer.valueOf(orderType));
        }
        String skuId = data.get(Constants.ORDER_FINISHED_MSG_KEY_SKU_ID);
        if (notNull(skuId)) {
            autorenewRequest.setSkuId(skuId);
        }
        Integer dutAmount = getDutAmount(data);
        if (dutAmount != null) {
            autorenewRequest.setDutAmount(dutAmount);
        }
        autorenewRequest.setRealFee(MapUtils.getIntValue(data, ORDER_FINISHED_REAL_FEE, 0));
        if (autorenewRequest.isAppleOrder()) {
            String refer = MapUtils.getString(data, Constants.ORDER_FINISHED_MSG_KEY_REFER);
            Map referMap = GsonUtils.parseObject(refer, Map.class);
            autorenewRequest.setBundleID(MapUtils.getString(referMap, Constants.ORDER_REFER_KEY_BUNDLE_ID));
        }
    }

    private static boolean notNull(String value) {
        return StringUtils.isNotBlank(value) && !"null".equalsIgnoreCase(value);
    }

    public static Integer getDutAmount(Map<String, String> data) {
        String businessPropertyStr = MapUtils.getString(data, Constants.ORDER_FINISHED_MSG_KEY_BUSINESS_PROPERTY);
        if (StringUtils.isBlank(businessPropertyStr)) {
            return null;
        }
        Map businessPropertyMap = GsonUtils.parseObject(businessPropertyStr, Map.class);
        if (MapUtils.isEmpty(businessPropertyMap) || !businessPropertyMap.containsKey(Constants.ORDER_FINISHED_MSG_KEY_DUT_AMOUNT)) {
            return null;
        }
        return MapUtils.getInteger(businessPropertyMap, Constants.ORDER_FINISHED_MSG_KEY_DUT_AMOUNT, null);
    }

    /**
     * 是否是开通自动续费的请求
     */
    public boolean isAutoRenewRequest() {
        return Constants.QIYUE_ORDER_AUTORENEW_FIRST.equals(getAutoRenew())
                || Constants.QIYUE_ORDER_AUTORENEW_FIRST_LARGE.equals(getAutoRenew());
    }

    public boolean isDutRequest() {
        return Constants.QIYUE_ORDER_AUTORENEW_DUT.equals(getAutoRenew());
    }

    /**
     * 是否是对外合作订单
     */
    public boolean isPartnerOrder() {
        return StringUtils.isNotBlank(getPartner());
    }

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this);
    }

    public VipUser generateVipUser() {
        if (AgreementTypeEnum.commonAutoRenew(agreementType)) {
            if (StringUtils.isBlank(String.valueOf(this.getDeadline()))
                || this.userId == null
                || StringUtils.isBlank(String.valueOf(this.getVipType()))) {
                return null;
            }
        }
        VipUser vipUser = new VipUser();
        vipUser.setId(this.getUserId());
        vipUser.setDeadline(this.getDeadline());
        vipUser.setTypeId(this.getVipType());
        return vipUser;
    }

    /**
     * 获取即将签约的时长
     */
    public Integer getOpeningAmount() {
        Integer amount;
        if (this.dutAmount != null) {
            amount = this.dutAmount;
        } else if (this.isNewUpgradeMode()) {
            amount = Constants.AMOUNT_OF_COMMON_AUTORENEW;
        } else if (largeMonthsAutoRenew()) {
            amount = this.amount;
        } else {
            amount = Constants.AMOUNT_OF_COMMON_AUTORENEW;
        }
        return amount;
    }

    /**
     * 大时长自动续费
     */
    public boolean largeMonthsAutoRenew() {
        if (this.amount == null) {
            return false;
        }
        return isLargeAutoRenew() || isTwIapPayType() || isCnIapPayType();
    }

    private boolean isCnIapPayType() {
        return null != this.getPayType() && Constants.PAY_TYPE_APPLEIAP_CNFIRSTDUT == this.getPayType();
    }

    private boolean isTwIapPayType() {
        return null != this.getPayType() && Constants.PAY_TYPE_APPLEIAP_TWFIRSTDUT == this.getPayType();
    }

    private boolean isLargeAutoRenew() {
        return Objects.equals(Constants.QIYUE_ORDER_AUTORENEW_FIRST_LARGE, this.autoRenew);
    }

    public boolean isUpgrade() {
        return sourceVipType != null;
    }

    public boolean isAppleOrder() {
        return payChannel != null && payChannel == PaymentDutType.PAY_CHANNEL_IAP;
    }

    public boolean isGoogleBillingOrder() {
        return payChannel != null && payChannel == PaymentDutType.PAY_CHANNEL_GOOGLE_BILLING;
    }

    public boolean isNewUpgradeMode() {
        return newUpgradeMode;
    }

}
