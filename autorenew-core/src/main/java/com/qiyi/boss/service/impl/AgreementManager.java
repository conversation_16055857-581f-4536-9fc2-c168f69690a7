package com.qiyi.boss.service.impl;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTempPrice;

/**
 * @author: guojing
 * @date: 2024/1/10 15:04
 */
@Component
public class AgreementManager {

    @Resource
    private AgreementNoInfoManager agreementNoInfoManager;
    @Resource
    private AgreementTemplateManager agreementTemplateManager;

    public Integer getDefaultRenewPrice(Long vipType, Integer amount, Integer payChannel, Short priority) {
        AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getAutoRenewAgreementWithoutUpgrade(vipType, amount, payChannel, priority);
        if (agreementNoInfo == null) {
            return null;
        }
        AgreementTempPrice priceInfo = agreementTemplateManager.getDefaultPriceByCode(agreementNoInfo.getTemplateCode());
        if (priceInfo == null) {
            priceInfo = agreementTemplateManager.getDefaultPrice(agreementNoInfo.getId());
        }
        return priceInfo != null ? priceInfo.getPrice() : null;
    }

    public Integer getDefaultRenewPrice(Integer dutType, Integer amount) {
        AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getByDutTypeAndAmount(dutType, amount);
        if (agreementNoInfo == null) {
            return null;
        }
        AgreementTempPrice priceInfo = agreementTemplateManager.getDefaultPriceByCode(agreementNoInfo.getTemplateCode());
        if (priceInfo == null) {
            priceInfo = agreementTemplateManager.getDefaultPrice(agreementNoInfo.getId());
        }
        return priceInfo != null ? priceInfo.getPrice() : null;
    }

}
