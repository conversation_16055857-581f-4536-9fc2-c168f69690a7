package com.qiyi.boss.service.impl;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.qiyi.boss.Constants;
import com.qiyi.boss.async.queue.AsyncTaskFactory;
import com.qiyi.boss.async.task.BaseAgreementRenewTask;
import com.qiyi.boss.async.task.WeChatPayScoreCreateTask;
import com.qiyi.boss.autorenew.dto.DutRenewLogDesp;
import com.qiyi.boss.autorenew.dto.OpenDutSetLogDesp;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.constants.ParamKeyConstants;
import com.qiyi.boss.dto.AccountResponse;
import com.qiyi.boss.dto.AgreementOptDto;
import com.qiyi.boss.dto.DataDto;
import com.qiyi.boss.dto.DutParamsDto;
import com.qiyi.boss.dto.OrderCreationRequest;
import com.qiyi.boss.dto.PayCenterDutResult;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.DutFailedEnum;
import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.enums.ResultCodeEnum;
import com.qiyi.boss.model.RenewPriceDto;
import com.qiyi.boss.model.UserAgreementContext;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.outerinvoke.result.CommodityInfo;
import com.qiyi.boss.param.QiyiParam;
import com.qiyi.boss.processor.DutProcessor;
import com.qiyi.boss.service.AgreementOperateService;
import com.qiyi.boss.service.OrderService;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.JacksonUtils;
import com.qiyi.boss.utils.MailSender;
import com.qiyi.boss.utils.PassportApi;
import com.qiyi.boss.utils.PayUtils;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.constants.TaskConstants;
import com.qiyi.vip.trade.autorenew.dao.UserAgreementMapper;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.AsyncTask;
import com.qiyi.vip.trade.autorenew.domain.AutorenewDutConfig;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.SimpleDutUserNew;
import com.iqiyi.vip.order.dal.model.Order;

import static com.qiyi.boss.Constants.TRADE_TYPE_KEY;
import static com.qiyi.boss.Constants.TRADE_TYPE_VALUE;

/**
 * Created at: 2021-06-30
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RenewTaskComponent {

    private static final int THIRD_ERROR_MSG_MAX_LENGTH = 250;
    private static final long MAX_NOTIFY_COUNT = 6;
    private static final String RESPONSE_THIRD_ERROR_CODE = "third_error_code";
    private static final String RESPONSE_THIRD_ERROR_MSG = "third_error_msg";
    private static final String RESPONSE_REQ_ERROR_TYPE = "req_error_type";
    public static final Timestamp WECHAT_PAY_SCORE_MANUAL_CREATE_ORDER_TIME = Timestamp.valueOf("2021-11-20 00:00:00");

    @Resource
    PassportApi passportApi;
    @Resource
    DutProcessor dutProcessor;
    @Resource
    OrderService orderService;
    @Resource
    AgreementOperateService agreementOperateService;
    @Resource
    UserAgreementLogManager userAgreementLogManager;
    @Resource
    UserAgreementMapper userAgreementMapper;
    @Resource
    AutorenewDutConfigManager autorenewDutConfigManager;
    @Resource
    UserAgreementService userAgreementService;

    public DutRenewLog buildRenewLogWhenDutUserNewIsNull(BaseAgreementRenewTask renewTask) {
        DutRenewLog dutRenewLog = new DutRenewLog();
        dutRenewLog.setUserId(renewTask.getUserId());
        dutRenewLog.setSignKey(renewTask.getSignKey());
        dutRenewLog.setAgreementNo(renewTask.getAgreementNo());
        dutRenewLog.setAgreementType(renewTask.getAgreementType());
        dutRenewLog.setType(renewTask.getDutType());
        dutRenewLog.setCreateTime(DateHelper.getDateTime());
        dutRenewLog.setVipType(renewTask.getVipType());
        dutRenewLog.setOperateType(OperateTypeEnum.DUT.getValue());
        dutRenewLog.setFailedInfo(DutFailedEnum.DUT_USER_IS_NULL_OR_INVALID);
        return dutRenewLog;
    }

    public DutRenewLog buildRenewLogWhenCommodityNotExists(BaseAgreementRenewTask renewTask) {
        DutRenewLog dutRenewLog = new DutRenewLog();
        dutRenewLog.setUserId(renewTask.getUserId());
        dutRenewLog.setSignKey(renewTask.getSignKey());
        dutRenewLog.setAgreementNo(renewTask.getAgreementNo());
        dutRenewLog.setAgreementType(renewTask.getAgreementType());
        dutRenewLog.setType(renewTask.getDutType());
        dutRenewLog.setCreateTime(DateHelper.getDateTime());
        dutRenewLog.setVipType(renewTask.getVipType());
        dutRenewLog.setOperateType(OperateTypeEnum.DUT.getValue());
        dutRenewLog.setFailedInfo(DutFailedEnum.COMMODITY_NOT_EXISTS);
        return dutRenewLog;
    }

    /**
     * 协议状态不是生效状态
     *
     * @param dutUserNew
     * @param dutRenewLog
     * @return
     */
    public boolean agreementStatusNotValid(BaseAgreementRenewTask renewTask, DutUserNew dutUserNew, DutRenewLog dutRenewLog) {
        if (dutUserNew.agreementValid()) {
            return false;
        }
        log.warn("[{}] [协议不是生效状态][dutUserNew:{}]", renewTask.getClass().getSimpleName(), JacksonUtils.toJsonString(dutUserNew));
        dutRenewLog.setFailedInfo(DutFailedEnum.DUT_USER_IS_NULL_OR_INVALID);
        return true;
    }

    /**
     * 校验passport账户是否被注销
     *
     * @param renewTask
     * @param dutUserNew
     * @param dutRenewLog
     * @return
     */
    public boolean accountClosed(BaseAgreementRenewTask renewTask, DutUserNew dutUserNew, DutRenewLog dutRenewLog) {
        Long userId = dutUserNew.getUserId();
        boolean isAccountClosed = passportApi.isAccountClosed(userId);
        if (!isAccountClosed) {
            return false;
        }
        log.warn("[{}] [msg:passport账户不存在!] [uid:{}]", renewTask.getClass().getSimpleName(), userId);
        dutRenewLog.setReqErrorType("THIRD_ERROR");
        dutRenewLog.setFailedInfo(DutFailedEnum.PASSPORT_ACCOUNT_NOT_EXISTS);
        return true;
    }

    /**
     * 会员是否冻结状态
     *
     * @param renewTask
     * @param dutUserNew
     * @param dutRenewLog
     * @return
     */
    public boolean vipStatusFrozenForever(VipUser vipUser, BaseAgreementRenewTask renewTask, DutUserNew dutUserNew, DutRenewLog dutRenewLog) {
        if (!vipUser.hasBeenFrozenForever()) {
            return false;
        }
        log.error("[{}] [VipUser has been frozen.] [uid:{}] [vipType:{}]", renewTask.getClass().getSimpleName(), dutUserNew.getUserId(), dutUserNew.getVipType());
        dutRenewLog.setFailedInfo(DutFailedEnum.USER_HAS_BEEN_FROZEN_FOREVER);
        return true;
    }

    /**
     * 协议已过期
     *
     * @param renewTask
     * @param dutUserNew
     * @param dutRenewLog
     */
    public boolean agreementNoExpired(BaseAgreementRenewTask renewTask, DutUserNew dutUserNew, DutRenewLog dutRenewLog) {
        AgreementNoInfo agreementNoInfo = renewTask.getAgreementNoInfo();
        if (agreementNoInfo.online()) {
            return false;
        }
        log.error("[{}}] [agreementNo expired] uid:{}, agreementNo:{}, endTime:{}", renewTask.getClass().getSimpleName(),
            dutUserNew.getUserId(), agreementNoInfo.getId(), DateHelper.getMostCommonPatternStr(agreementNoInfo.getValidEndTime()));
        UserAgreementContext userAgreementContext = new UserAgreementContext(agreementNoInfo, renewTask.getAgreementTemplate(), dutUserNew);
        AgreementOptDto agreementOptDto = AgreementOptDto.buildFromDutUserNew(dutUserNew, agreementNoInfo.getPayChannel(), OperateSceneEnum.CANCEL_AGREEMENT_NO_EXPIRED);
        agreementOperateService.cancelSpecifiedDutType(userAgreementContext, agreementOptDto);

        dutRenewLog.setFailedInfo(DutFailedEnum.AGREEMENT_NO_EXPIRED);
        return true;
    }

    /**
     * 查询绑定关系
     *
     * @param dutUserNew
     * @return
     */
    public AccountResponse getAccountBindInfo(DutUserNew dutUserNew, Integer dutType) {
        Long userId = dutUserNew.getUserId();
        String partner = CloudConfigUtil.getPartnerByVipType(dutUserNew.getVipType());
        Optional<AccountResponse> accountResponse = dutProcessor.queryAccountBindInfos(userId, dutType, partner, dutUserNew.getPartnerOrderCode());
        return accountResponse.orElse(null);
    }

    /**
     * 构建代扣参数信息
     *
     * @param dutUserNew
     * @param dutRenewLog
     * @return
     */
    public Map<String, String> organizeParams(BaseAgreementRenewTask renewTask, CommodityInfo commodityInfo,
        DutUserNew dutUserNew, DutRenewSetLog openSetLog, DutRenewLog dutRenewLog, RenewPriceDto renewPriceDto) {
        AgreementNoInfo agreementNoInfo = renewTask.getAgreementNoInfo();

        DutParamsDto dutParamsDto = renewTask.getDutParamsDto();
        Map<String, String> params = Maps.newHashMap();
        dutParamsDto.setAgreementType(agreementNoInfo.getType());
        dutParamsDto.setVipType(dutUserNew.getVipType());
        params.put("pid", commodityInfo.getProductCode());
        params.put("skuId", commodityInfo.getSkuId());
        dutParamsDto.setProductName(commodityInfo.getDisplayName());
        Integer dutType = agreementNoInfo.getDutType();
        params.put("dutType", dutType.toString());
        dutParamsDto.setDutType(dutType);
        dutParamsDto.setAgreementNo(agreementNoInfo.getId());
        dutParamsDto.setPayChannel(agreementNoInfo.getPayChannel());
        params.put("amount", dutUserNew.getAmount().toString());
        params.put("skuAmount", String.valueOf(dutUserNew.getAmount()));
        dutParamsDto.setAmount(dutUserNew.getAmount());
        params.put("serviceCode", commodityInfo.getBusinessCode());
        dutParamsDto.setServiceCode(commodityInfo.getBusinessCode());
        dutParamsDto.setPartnerId(agreementNoInfo.getPartnerId());
        int renewPrice = renewPriceDto != null ? renewPriceDto.getFee() : 0;
        if (renewPrice > 0) {
            params.put("fee", String.valueOf(renewPrice));
            dutParamsDto.setFee(renewPrice);
            dutParamsDto.setRealFee(renewPriceDto.getRealFee() == null ? renewPrice : renewPriceDto.getRealFee());
            dutParamsDto.setSettlementFee(renewPriceDto.getSettlementFee() == null ? renewPrice : renewPriceDto.getSettlementFee());
        }
        params.put("BACKEND-AUTO-RENEW", "1");
        params.put("uid", dutUserNew.getUserId().toString());
        dutParamsDto.setUid(dutUserNew.getUserId());
        params.put("payType", agreementNoInfo.getDutPayType().toString());
        dutParamsDto.setPayType(agreementNoInfo.getDutPayType());
        params.put("platform", dutUserNew.getPlatformCode());
        dutParamsDto.setPlatform(dutUserNew.getPlatformCode());

        String fc = "";
        OpenDutSetLogDesp openSetLogDesp = openSetLog.parseOpenSetLogDescription();
        if (openSetLogDesp != null && openSetLogDesp.getFc() != null) {
            fc = openSetLogDesp.getFc();
        }
        params.put("fc", fc);
        dutParamsDto.setFc(fc);
        String fv = openSetLogDesp != null ? openSetLogDesp.getFv() : null;
        params.put("fv", fv);
        dutParamsDto.setFv(fv);
        if (openSetLogDesp != null && StringUtils.isNotBlank(openSetLogDesp.getPartnerNo())) {
            params.put("partnerNo", openSetLogDesp.getPartnerNo());
            dutParamsDto.setPartnerNo(openSetLogDesp.getPartnerNo());
        }
        if (StringUtils.isNotBlank(dutUserNew.getOrderCode())) {
            params.put("signOrderCode", dutUserNew.getOrderCode());
            dutParamsDto.setSignOrderCode(dutUserNew.getOrderCode());
        }

        // 供支付中心标记重试订单
        if (!Constants.TASK_TYPE_NORMAL.equals(renewTask.getTaskType())) {
            params.put("orderType", "autoRenewRetry");
            dutParamsDto.setOrderType("autoRenewRetry");
        }
        params.put("taskType", renewTask.getTaskType());
        dutParamsDto.setTaskType(renewTask.getTaskType());
        if (dutRenewLog.getDescription().contains("signFlag")) {
            Map<String, Object> despMap = dutRenewLog.parseLogDesp();
            params.put("signFlag", String.valueOf(despMap.get("signFlag")));
            dutParamsDto.setSignFlag(String.valueOf(despMap.get("signFlag")));
        }
        Map<String, Object> businessProperty = new HashMap<>();
        businessProperty.put(ParamKeyConstants.SERIAL_RENEW_COUNT, dutUserNew.getSerialRenewCount() + 1);
        if (StringUtils.isNotBlank(dutParamsDto.getPartnerId())) {
            businessProperty.put(ParamKeyConstants.PARTNER_ID, dutParamsDto.getPartnerId());
        }
        if (StringUtils.isNotBlank(dutUserNew.getSignKey())) {
            businessProperty.put(ParamKeyConstants.SIGN_KEY, dutUserNew.getSignKey());
        }
        businessProperty.put(TRADE_TYPE_KEY, TRADE_TYPE_VALUE);
        dutParamsDto.setBusinessProperty(businessProperty);

        String sign = PayUtils.getQiyueSign(params);
        params.put("sign", sign);
        return params;
    }

    /**
     * 处理代扣接口返回结果
     *
     * @param order
     * @param payCenterDutResult
     * @param dutUserNew
     * @param dutRenewLog
     */
    public void handlerDutResponse(Order order, OrderCreationRequest orderCreationRequest, PayCenterDutResult payCenterDutResult, BaseAgreementRenewTask renewTask, DutUserNew dutUserNew, DutRenewLog dutRenewLog) {
        Long userId = dutUserNew.getUserId();
        DutParamsDto dutParamsDto = renewTask.getDutParamsDto();
        if (payCenterDutResult != null && payCenterDutResult.isReqSuccess()) {
            String resultString = JacksonUtils.toJsonString(orderService.transfer(payCenterDutResult, order, orderCreationRequest, dutParamsDto.getSignOrderCode()));
            doIfRequestSuccess(renewTask, dutRenewLog, dutUserNew, resultString);

            AgreementTemplate agreementTemplate = renewTask.getAgreementTemplate();
            AgreementTypeEnum agreementTypeEnum = AgreementTypeEnum.valueOf(agreementTemplate.getType());
            if (agreementTypeEnum == AgreementTypeEnum.ZHIMA_GO) {
                //芝麻GO是定时发起扣费，请求支付中心成功即更新下次代扣时间，最后一期代扣发起后无需更新下次续费时间
                updateNextDutTimeAfterReqPayCenter(dutUserNew, agreementTemplate);
            }
            return;
        }
        if (payCenterDutResult != null && StringUtils.isNotBlank(payCenterDutResult.getCode())) {
            dutRenewLog.setErrorCode(payCenterDutResult.getCode());
            String msg = payCenterDutResult.getMsg();
            String message = payCenterDutResult.getMessage();
            String thirdErrorMsg = StringUtils.isNotBlank(msg) ? msg : message;
            if (thirdErrorMsg.length() > THIRD_ERROR_MSG_MAX_LENGTH) {
                thirdErrorMsg = thirdErrorMsg.substring(0, THIRD_ERROR_MSG_MAX_LENGTH);
            }
            dutRenewLog.setThirdErrorMsg(thirdErrorMsg);
        }
        //调用奇悦接口出现异常，保存错误日志
        if (StringUtils.isBlank(dutRenewLog.getErrorCode())) {
            dutRenewLog.setErrorCode("Q00333");
        }
        dutRenewLog.setStatus(DutRenewLog.PAY_FAILURE);
        log.error("[handlerDutResponse] dut response failed. [uid:{}] [response:{}] ", userId, JacksonUtils.toJsonString(payCenterDutResult));
        userAgreementLogManager.saveRenewLog(dutRenewLog);

        String code = payCenterDutResult != null ? payCenterDutResult.getCode() : null;
        DataDto dataDto = payCenterDutResult != null ? payCenterDutResult.getData() : null;
        if (QiyiParam.DUT_TIME_ERROR.equals(code) || QiyiParam.DUT_FEE_ERROR.equals(code)) {
            log.error("pay center gateway request failed, errorCode: {}, userId: {}, agreementNo:{}, orderCode:{}", code, userId, renewTask.getAgreementNo(), order != null ? order.getOrderCode() : orderCreationRequest.getOrderCode());
            if (renewTask.getAgreementType() != AgreementTypeEnum.ZHIMA_GO.getValue()) {
                return;
            }
            Timestamp nextPlannedTriggerTime = dataDto != null ? dataDto.getNextPlannedTriggerTime() : null;
            saveAgreementRetryTask(renewTask, nextPlannedTriggerTime, dutRenewLog);
        }
    }

    public void doIfRequestSuccess(BaseAgreementRenewTask renewTask, DutRenewLog dutRenewLog, DutUserNew dutUserNew, String response) {
        Map<String, Object> respJson = JacksonUtils.parseMap(response);
        String code = MapUtils.getString(respJson, "code");
        Object data = MapUtils.getObject(respJson, "data");
        Map<String, Object> dataMap = null;
        if (data != null) {
            dataMap = (Map<String, Object>) data;
            String orderCode = MapUtils.getString(dataMap, "orderCode");
            if (StringUtils.isNotBlank(orderCode) && StringUtils.isBlank(dutRenewLog.getOrderCode())) {
                dutRenewLog.setOrderCode(orderCode);
            }
            if (dataMap.containsKey("fee")) {
                Integer fee = (Integer) dataMap.get("fee");
                dutRenewLog.setFee(fee);
            }
        }
        if (!ResultCodeEnum.SUCCESS.value().equals(code)) {
            dutRenewLog.setErrorCode(code);
        }
        if (ResultCodeEnum.SUCCESS.value().equals(code)) {
            doIfPaySuccess(renewTask, dutRenewLog, dutUserNew);
        } else if (ResultCodeEnum.NEED_ASYNC_CONFIRM.value().equals(code)) {
            dutRenewLog.setStatus(DutRenewLog.PAY_CONFIRM);
            userAgreementLogManager.saveRenewLog(dutRenewLog);
        } else {
            doIfPayFailed(dutRenewLog, dataMap, renewTask.getTaskType());
        }
    }

    private void doIfPaySuccess(BaseAgreementRenewTask renewTask, DutRenewLog dutRenewLog, DutUserNew dutUserNew) {
        String fc = renewTask.getDutParamsDto().getFc();
        String taskType = renewTask.getTaskType();
        dutRenewLog.setStatus(DutRenewLog.PAY_SUCCESS);
        dutRenewLog.setErrorCode(null);
        //判断是否已经插入了，支付那边异步调用，有可能失败一次，保存了失败的记录，又成功了，这时候只需要更新下数据就行了
        DutRenewLog storedRenewLog = userAgreementLogManager.getRenewLogByOrderCode(dutUserNew.getUserId(), dutRenewLog.getOrderCode());
        if (storedRenewLog != null) {
            if (storedRenewLog.getStatus() == null || storedRenewLog.getStatus() != DutRenewLog.PAY_SUCCESS) {
                storedRenewLog.setStatus(DutRenewLog.PAY_SUCCESS);
                storedRenewLog.setErrorCode(null);
                storedRenewLog.setFee(dutRenewLog.getFee());
                storedRenewLog.setPlatform(dutUserNew.getPlatform());
                storedRenewLog.setPlatformCode(dutUserNew.getPlatformCode());
                storedRenewLog.setAmount(dutUserNew.getAmount());
                storedRenewLog.setVipType(dutUserNew.getVipType());

                DutRenewLogDesp desp = DutRenewLogDesp.builder()
                        .actCode(StringUtils.defaultIfEmpty(dutUserNew.getActCode(), ""))
                        .orderFc(fc)
                        .actPeriods(dutUserNew.getRemainPeriods() == null ? 0 : dutUserNew.getRemainPeriods())
                        .actType(dutUserNew.getActType())
                        .taskType(taskType)
                        .serialRenewCount(dutUserNew.getSerialRenewCount() + 1)
                        .build();
                storedRenewLog.setDescription(JacksonUtils.toJsonString(desp));
                storedRenewLog.setVipCategory(dutUserNew.getVipCategory());
                userAgreementLogManager.saveRenewLog(storedRenewLog);
            }
        } else {
            userAgreementLogManager.saveRenewLog(dutRenewLog);
        }

        userAgreementService.incrementRenewCount(dutUserNew.getId(), dutUserNew.getUserId());
        log.info("dut success dutRenewLog:{}, taskType:{}", dutRenewLog, taskType);
    }

    private void doIfPayFailed(DutRenewLog dutRenewLog, Map<String, Object> dataMap, String taskType) {
        log.info("dut failed! uid:{}, taskType:{}, dutRenewLog:{}, dataMap:{}", dutRenewLog.getUserId(), taskType, dutRenewLog, dataMap);
        if ((dataMap != null) && (dataMap.get(RESPONSE_THIRD_ERROR_CODE) != null)
                && StringUtils.isNotBlank((String) dataMap.get(RESPONSE_THIRD_ERROR_CODE))) {
            dutRenewLog.setThirdErrorCode((String) dataMap.get(RESPONSE_THIRD_ERROR_CODE));
        }
        if ((dataMap != null) && (dataMap.get(RESPONSE_THIRD_ERROR_MSG) != null)
                && StringUtils.isNotBlank((String) dataMap.get(RESPONSE_THIRD_ERROR_MSG))) {
            dutRenewLog.setThirdErrorMsg((String) dataMap.get(RESPONSE_THIRD_ERROR_MSG));
        }
        if ((dataMap != null) && (dataMap.get(RESPONSE_REQ_ERROR_TYPE) != null)
                && StringUtils.isNotBlank((String) dataMap.get(RESPONSE_REQ_ERROR_TYPE))) {
            dutRenewLog.setReqErrorType((String) dataMap.get(RESPONSE_REQ_ERROR_TYPE));
        }
        dutRenewLog.setStatus(DutRenewLog.PAY_FAILURE);
        userAgreementLogManager.saveRenewLog(dutRenewLog);
    }

    /**
     * 保存重试任务
     * @param renewTask
     * @param executeTime
     * @param dutRenewLog
     */
    public void saveAgreementRetryTask(BaseAgreementRenewTask renewTask, Timestamp executeTime, DutRenewLog dutRenewLog) {
        String taskType = renewTask.getTaskType();
        Integer notifyCount = renewTask.getNotifyCount();
        if (notifyCount > MAX_NOTIFY_COUNT) {
            log.error("[{}] [saveRetryTask] [notifyCount greater than 6] [taskType:{}] [params:{}]", renewTask.getClass().getSimpleName(), taskType, renewTask);
            MailSender.sendErrorMail(renewTask, null, null, renewTask.getClass().getSimpleName() + " notifyCount greater than 6");

            dutRenewLog.setFailedInfo(DutFailedEnum.VIP_USER_IS_NULL);
            userAgreementLogManager.saveRenewLog(dutRenewLog);
            return;
        }

        log.warn("[{} retry at {} times] [taskType:{}] [taskInfo:{}]", renewTask.getClass().getSimpleName(), notifyCount, taskType, renewTask);
        if (executeTime == null) {
            String times = Constants.NOTIFY_TIME[notifyCount];
            executeTime = DateHelper.caculateTime(DateHelper.getDateTime(), times);
        }
        AsyncTaskFactory.getInstance().delayInsertIntoDB(renewTask.buildRetryTask(), 0, TaskConstants.PRIORITY_HIGH, executeTime);
    }

    /**
     * 更新下次续费时间
     * 芝麻GO是定时发起扣费，请求支付中心成功即更新下次代扣时间
     * 最后一期代扣发起后无需更新下次续费时间
     * @param dutUserNew
     * @param agreementTemplate
     */
    public boolean updateNextDutTimeAfterReqPayCenter(DutUserNew dutUserNew, AgreementTemplate agreementTemplate) {
        boolean openedBySignPay = OperateSceneEnum.isSignPayScene(dutUserNew.getOperateSceneExt());
        int performedPeriods = openedBySignPay ? dutUserNew.getSerialRenewCount() + 1 : dutUserNew.getSerialRenewCount();
        Timestamp nextDutTime = agreementTemplate.calcNextDutTime(performedPeriods, dutUserNew.getSignTime());
        nextDutTime = performedPeriods + 1 < agreementTemplate.getPeriods() ? nextDutTime : null;
        userAgreementMapper.updateNextDutTime(dutUserNew.getId(), dutUserNew.getUserId(), nextDutTime);
        return true;
    }

    /**
     * 生成微信支付分创单Task
     * @param dutUserNew
     */
    public void genWeChatPayScoreCreateTask(DutUserNew dutUserNew) {
        Timestamp signTime = dutUserNew.getSignTime();
        //微信支付分一期是系统自动发起创单，二期是手动发起创单，用户按需创单。
        //由于一期不再投放，所以根据签约时间判断是自动发起创单还是手动创单
        if (signTime.after(WECHAT_PAY_SCORE_MANUAL_CREATE_ORDER_TIME)) {
            return;
        }
        boolean currentDeadlineSupportTriggerTask = WeChatPayScoreCreateTask.currentDeadlineSupportTriggerTask(dutUserNew.getDeadline(), WeChatPayScoreCreateTask.DEFAULT_ADVANCES_DAY);
        if (!currentDeadlineSupportTriggerTask) {
            log.info("[genWeChatPayScoreCreateTask] not generate WeChatPayScoreTask，vip deadline:{}", dutUserNew.getDeadline());
            return;
        }

        List<AutorenewDutConfig> autorenewDutConfigs = autorenewDutConfigManager.findByVipType(dutUserNew.getSourceVipType(), dutUserNew.getVipType(), AutorenewDutConfig.JOB_TYPE_COMMON, null, AutorenewDutConfig.STATUS_VALID);
        if (CollectionUtils.isEmpty(autorenewDutConfigs)) {
            log.error("未查询到指定会员类型对应的autorenew_dut_config配置，dutUserNew:{}", dutUserNew);
        }
        AutorenewDutConfig autorenewDutConfig = autorenewDutConfigs.get(0);
        SimpleDutUserNew simpleDutUserNew = SimpleDutUserNew.buildFromDutUserNew(dutUserNew);
        WeChatPayScoreCreateTask renewTask = WeChatPayScoreCreateTask.buildFromDutUserNewAndDutConfig(simpleDutUserNew, autorenewDutConfig);
        Timestamp currentTime = DateHelper.getDateTime();
        Timestamp deadline = dutUserNew.getDeadline();
        Timestamp needExecuteTime = deadline != null && deadline.after(currentTime) ? deadline : currentTime;
        int beforeTriggerWeChatPayScoreCreateTaskMinutes = CloudConfigUtil.beforeTriggerWeChatPayScoreCreateTaskMinutes();
        Timestamp executeTime = DateHelper.minusMinutes(needExecuteTime, beforeTriggerWeChatPayScoreCreateTaskMinutes);
        executeTime = executeTime.before(currentTime) ? currentTime : executeTime;
        AsyncTaskFactory.getInstance().insertIntoDB(renewTask, 0, AsyncTask.Task_PRIORITY_1, executeTime);
    }

}
