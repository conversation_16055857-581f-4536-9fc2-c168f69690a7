package com.qiyi.boss.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import com.qiyi.boss.Constants;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.dto.CancelAutoRenewOptDto;
import com.qiyi.boss.enums.AgreementStatusEnum;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.outerinvoke.OrderCoreProxy;
import com.qiyi.boss.service.AutoRenewService;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import com.iqiyi.vip.order.dal.model.Order;
import static com.qiyi.boss.constants.AgreementConstants.EXCLUDE_AGREEMENT_TYPES;

/**
 * <AUTHOR> peng
 * 取消用户自动续费
 */
@Component
@Slf4j
public class RefundHandleService {

    @Resource
    private DutManager dutManager;

    @Resource
    private AutoRenewDutTypeManager autoRenewDutTypeManager;
    @Resource
    AutoRenewService autoRenewService;
    @Resource
    AgreementNoInfoManager agreementNoInfoManager;
    @Resource
    private UserAgreementService userAgreementService;

    @Resource
    private OrderCoreProxy orderCoreProxy;

    private final static Set<Integer> NOT_SUPPORT_PAY_TYPE = Sets.newHashSet(
            Constants.PAY_TYPE_APPLEIAP_CNDUT,
            Constants.PAY_TYPE_APPLEIAP_CNFIRSTDUT,
            Constants.PAY_TYPE_APPLEIAP_TWFIRSTDUT,
            Constants.PAY_TYPE_APPLEIAP_TWDUT,
            Constants.PAY_TYPE_TAIWAN_TELECOM,
            Constants.PAY_TYPE_TAIWAN_CREDIT,
            Constants.PAY_TYPE_GPB_TWFIRSTDUT,
            Constants.PAY_TYPE_GPB_TWDUT);

    private final static Set<Integer> NOT_SUPPORT_DUT_TYPE = Sets.newHashSet(
            DutUserNew.MAINLAND_IOS_BIND_TYPE,
            DutUserNew.MAINLAND_TENNIS_IOS_BIND_TYPE,
            DutUserNew.TAIWAN_IOS_BIND_TYPE,
            DutUserNew.TAIWAN_GOOGLE_BILLING_MONTH_BIND_TYPE,
            DutUserNew.TAIWAN_TELECOM_BIND_TYPE,
            DutUserNew.TAIWAN_CREDIT_BIND_TYPE,
            DutUserNew.TAIWAN_GOOGLE_BILLING_QUARTER_BIND_TYPE,
            DutUserNew.TAIWAN_GOOGLE_BILLING_YEAR_BIND_TYPE);

    private final static Set<Integer> NOT_SUPPORT_PAY_CHANNEL = Sets.newHashSet(
            PaymentDutType.PAY_CHANNEL_IAP,
            PaymentDutType.PAY_CHANNEL_GASH,
            PaymentDutType.PAY_CHANNEL_GOOGLE_BILLING);
    /**
     * 根据 userId，vipType
     * 取消自动续费.
     * 首先会查询是否已经取消。
     * 如果是开通状态才进行取消。
     *
     * @param userId 用户id
     * @param vipType 会员类型
     */
    public void handle(Long userId, Long vipType, String fc, String orderCode) {
        if (null == userId || null == vipType) {
            log.info("有参数为空 userId:{} vipType:{}", userId, vipType);
        }
        List<DutUserNew> dutUserNewList;
        Order order = orderCoreProxy.getByOrderCode(orderCode);
        if (cancelAutoRenewByOrderCode(order)) {
            dutUserNewList = userAgreementService.getByAgreementTypeAndVipType(userId, AgreementTypeEnum.FAMILY_CARD_PACKAGE, vipType, AgreementStatusEnum.VALID)
                .stream()
                .filter(d -> ObjectUtils.equals(order.getTradeCode(), d.getOrderCode()))
                .collect(Collectors.toList());
        } else {
            dutUserNewList = userAgreementService.getByExcludeAgreementTypesAndVipTypes(userId, EXCLUDE_AGREEMENT_TYPES, Lists.newArrayList(vipType), AgreementStatusEnum.VALID);
        }
        if (CollectionUtils.isEmpty(dutUserNewList)) {
            log.info("找不到DutUser userId:{} vipType:{}", userId, vipType);
            return;
        }

        for (DutUserNew dutUserNew : dutUserNewList) {
            log.info("用户自动续费状态正在取消. userId:{} agreementNo:{} dutType:{} vipType:{}", userId, dutUserNew.getAgreementNo(), dutUserNew.getType(), vipType);
            if (dutUserNew.getType() == null && dutUserNew.getAgreementNo() == null) {
                continue;
            }
            Integer payChannel = null;
            if (dutUserNew.getAgreementNo() != null) {
                AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(dutUserNew.getAgreementNo());
                payChannel = agreementNoInfo != null ? agreementNoInfo.getPayChannel() : null;
            } else {
                AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutUserNew.getType());
                payChannel = autoRenewDutType != null ? autoRenewDutType.getPayChannel() : null;
            }
            if (!isSupportPayChannel(payChannel)) {
                log.info("不支持的代扣渠道");
                continue;
            }

            CancelAutoRenewOptDto cancelAutoRenewOptDto = CancelAutoRenewOptDto.buildFromDutUserNew(dutUserNew, OperateSceneEnum.CANCEL_REFUND, fc, false);
            autoRenewService.cancelAutoRenew(dutUserNew, cancelAutoRenewOptDto);
            log.info("dutType:{} 代扣类型 取消完成. userId:{} vipType:{}", dutUserNew.getType(), userId, vipType);
        }
        log.info("取消自动续费操作全部完成. userId:{} vipType:{}", userId,  vipType);

    }

    private boolean cancelAutoRenewByOrderCode(Order order) {
        if (order == null) {
            return false;
        }
        Integer renewType = order.getRenewType();
        AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(renewType);
        if (agreementNoInfo == null) {
            return false;
        }
        return AgreementTypeEnum.familyType(agreementNoInfo.getType())
            && order.getAutoRenew() != null
            && (order.getAutoRenew() == 1 || order.getAutoRenew() == 3);
    }

    /**
     * 支持自动续费的产品，现在是 黄金、 网球、TV
     *
     * 不包括 台湾
     * @param productType 产品类型
     * @return 返回true表示是支持自动续费的产品类型
     */
    public static boolean isRefundProductType(Integer productType) {
        if (productType == null) {
            log.info("产品类型为空");
            return false;

        }
		if (productType != Constants.PRODUCT_TYPE_MONTHLY
				&& productType != Constants.PRODUCT_TYPE_TV_MONTHLY
				&& productType != Constants.PRODUCT_TYPE_TENNIS_MONTHLY
				&& productType != Constants.PRODUCT_TYPE_TAIWAN_MONTHLY
				&& productType != Constants.PRODUCT_TYPE_TV_CHILD_MONTHLY) {
			log.info("产品类型不支持, {}", productType);
            return false;
        }
        return true;
    }

    /**
     * 订单是 autorenew 订单，即 autorenew = 1 ， 2， ，3
     *
     * @param autoRenewStatus 订单自动续费状态
     * @return 返回true表示只自动续费订单
     */
    public static boolean isRefundAutoRenewOrder(String autoRenewStatus) {
        if (!Constants.QIYUE_ORDER_AUTORENEW_FIRST.equals(autoRenewStatus)
                && !Constants.QIYUE_ORDER_AUTORENEW_FIRST_LARGE.equals(autoRenewStatus)
                && !Constants.QIYUE_ORDER_AUTORENEW_DUT.equals(autoRenewStatus)
                ) {
            log.info("非自动续费订单");
            return false;
        }
        return true;
    }

    /**
     * 是否是支持的支付类型
     * 去掉 IOS 开通、代扣
     */
    public static boolean isSupportPayType(Integer payType) {

        if (payType == null) {
            return false;
        }
        if (NOT_SUPPORT_PAY_TYPE.contains(payType)) {
            log.info("支付方式不支持, 当前支付方式为{}", payType);
            return false;
        }
        return true;
    }

    public static boolean isSupportDutType(Integer dutType){

        if (dutType == null) {
            return false;
        }
        if (NOT_SUPPORT_DUT_TYPE.contains(dutType)) {
            log.info("代扣方式不支持, 当前代扣方式为{}", dutType);
            return false;
        }
        return true;
    }

    private static boolean isSupportPayChannel(Integer payChannel){
        if (payChannel == null){
            return false;
        }
        if (NOT_SUPPORT_PAY_CHANNEL.contains(payChannel)){
            log.info("代扣方式渠道不支持, 当前代扣渠道为{}", payChannel);
            return false;
        }
        return true;
    }
}
