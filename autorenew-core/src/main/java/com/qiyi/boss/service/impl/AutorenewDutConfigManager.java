package com.qiyi.boss.service.impl;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import com.qiyi.boss.service.AutorenewDutConfigService;
import com.qiyi.vip.trade.autorenew.domain.AutorenewDutConfig;
import com.qiyi.vip.trade.autorenew.mapper.AutorenewDutConfigMapper;

/**
 * Created by IntelliJ IDEA.
 * <AUTHOR>
 * Date: 2017-12-24
 * Time: 23:38
 */
@Component(value = "autorenewDutConfigService")
public class AutorenewDutConfigManager implements AutorenewDutConfigService {

	public static final int DEFAULT_DUT_ADVANCE_DAY = 0;
	@Resource
	private AutorenewDutConfigMapper autorenewDutConfigMapper;

	/**
	 * 查询有效代扣配置信息.
	 *
	 * @param status 状态
	 * @return listDutTypes
	 */
	@Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutorenewDutConfig_findByJobTypeAndStatus", cacheType= CacheType.LOCAL)
	public List<AutorenewDutConfig> findAutorenewDutConfig(Integer jobType, Integer status, Integer agreementType) {
        AutorenewDutConfig query = AutorenewDutConfig.builder()
            .jobType(jobType)
            .status(status)
            .agreementType(agreementType)
            .build();
	    return autorenewDutConfigMapper.list(query);
	}

	/**
	 * 根据id查询代扣配置信息.
	 * @param id id
	 * @return AutorenewDutConfig
	 */
	@Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutorenewDutConfig_findById", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public AutorenewDutConfig findById(Long id) {
		return autorenewDutConfigMapper.selectByPrimaryKey(id);
	}

	/**
	 * 查询有效代扣配置信息.
	 *
	 * @param sourceVipType 源会员类型
	 * @param vipType 会员类型
	 * @param status 状态
	 * @return listDutTypes
	 */
	@Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutorenewDutConfig_findByVipType", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public List<AutorenewDutConfig> findByVipType(Long sourceVipType, Long vipType, Integer jobType, Integer agreementType, Integer status) {
		AutorenewDutConfig query = AutorenewDutConfig.builder()
            .sourceVipType(sourceVipType)
            .vipType(vipType)
            .jobType(jobType)
            .agreementType(agreementType)
            .status(status)
            .build();
		return autorenewDutConfigMapper.list(query);
	}

	@Override
	public Integer getAdvanceDay(Integer jobType, Integer status, Integer agreementType) {
        AutorenewDutConfigManager thisObj = (AutorenewDutConfigManager) AopContext.currentProxy();
        List<AutorenewDutConfig> autorenewDutConfigs = thisObj.findAutorenewDutConfig(jobType, status, agreementType);
        if (CollectionUtils.isEmpty(autorenewDutConfigs)) {
            return DEFAULT_DUT_ADVANCE_DAY;
        }
        return autorenewDutConfigs.stream().map(AutorenewDutConfig::getAdvanceDays).findFirst().orElse(DEFAULT_DUT_ADVANCE_DAY);
	}
}
