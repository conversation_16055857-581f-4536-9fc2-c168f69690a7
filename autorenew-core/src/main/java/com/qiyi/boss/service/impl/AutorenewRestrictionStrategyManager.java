package com.qiyi.boss.service.impl;

import com.qiyi.boss.Constants;
import com.qiyi.boss.dto.RestrictionStrategyInfo;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.commons.constant.QueryConstants;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewRestrictionStrategy;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewRestrictionStrategy.categoryEnum;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.mapper.AutoRenewRestrictionStrategyMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2020-11-20
 * Time: 15:00
 */
@Service
public class AutorenewRestrictionStrategyManager {

    public static final int LENGTH_OF_MAIN_ = 5;

	@Resource
	private AutoRenewRestrictionStrategyMapper autoRenewRestrictionStrategyMapper;

	public Optional<AutoRenewRestrictionStrategy> query(Integer category, String actCode) {
		AutoRenewRestrictionStrategy query = AutoRenewRestrictionStrategy.builder()
				.category(category)
				.actCode(actCode)
				.status(Constants.STATUS_VALID)
				.build();

		List<AutoRenewRestrictionStrategy> autoRenewRestrictionStrategyList = autoRenewRestrictionStrategyMapper.list(query);
		if (CollectionUtils.isNotEmpty(autoRenewRestrictionStrategyList)) {
			return autoRenewRestrictionStrategyList.stream().findFirst();
		}
		return Optional.empty();
	}

    public RestrictionStrategyInfo restrictDateToCancel(DutUserNew dutUserNew, Timestamp recentlySetLogOperateTime) {
        if (recentlySetLogOperateTime == null) {
            return null;
        }
        AutoRenewRestrictionStrategy restrictionStrategy = getRestrictionStrategy(dutUserNew);
        if (restrictionStrategy == null) {
            return null;
        }

        Timestamp restrictEndTime = DateHelper.caculateTime(recentlySetLogOperateTime, restrictionStrategy.getDuration(), Constants.PRODUCT_PERIODUNIT_DAY);
        Date restrictEndDay = DateHelper.getDate(restrictEndTime);
        LocalDate restrictEndDate = ZonedDateTime.ofInstant(restrictEndDay.toInstant(), ZoneId.systemDefault()).toLocalDate();
        LocalDate currentDate = ZonedDateTime.ofInstant(DateHelper.getCurrentDate().toInstant(), ZoneId.systemDefault()).toLocalDate();
        if (restrictEndDate.isBefore(currentDate)) {
            return null;
        }
        return RestrictionStrategyInfo.builder()
            .restrictEndDate(restrictEndDate)
            .duration(restrictionStrategy.getDuration())
            .vipType(dutUserNew.getVipType())
            .build();
    }

    public RestrictionStrategyInfo intlRestrictDateToCancel(DutUserNew dutUserNew, Timestamp recentlySetLogOperateTime) {
        if (recentlySetLogOperateTime == null) {
            return null;
        }
        AutoRenewRestrictionStrategy restrictionStrategy = getRestrictionStrategy(dutUserNew);
        if (restrictionStrategy == null) {
            return null;
        }

        Integer duration = restrictionStrategy.getDuration();
        Timestamp restrictEndTime = DateHelper.caculateTime(recentlySetLogOperateTime, duration, Constants.PRODUCT_PERIODUNIT_DAY);
        if (restrictEndTime.before(DateHelper.getCurrentTime())) {
            return null;
        }
        return RestrictionStrategyInfo.builder()
            .restrictEndTime(restrictEndTime)
            .duration(duration)
            .vipType(dutUserNew.getVipType())
            .build();
    }

    private AutoRenewRestrictionStrategy getRestrictionStrategy(DutUserNew dutUserNew) {
        if (dutUserNew == null || StringUtils.isBlank(dutUserNew.getActCode())) {
            return null;
        }
        Integer category = categoryEnum.RESTRICT_TO_CANCEL_AUTORENEW.getCategory();
        Optional<AutoRenewRestrictionStrategy> autoRenewRestrictionStrategyOptional = query(category, getMainActCode(dutUserNew.getActCode()));
        if (!autoRenewRestrictionStrategyOptional.isPresent()) {
            return null;
        }
        AutoRenewRestrictionStrategy autoRenewRestrictionStrategy = autoRenewRestrictionStrategyOptional.get();
        return autoRenewRestrictionStrategy.getDuration() == null ? null : autoRenewRestrictionStrategy;
    }

    public String getMainActCode(String actCode) {
        if (StringUtils.isBlank(actCode)) {
            return actCode;
        }
        return Arrays.stream(actCode.split(QueryConstants.COMMA))
            .filter(code -> !StringUtils.isBlank(code) && code.startsWith("main_"))
            .findFirst()
            .map(code -> code.substring(LENGTH_OF_MAIN_))
            .orElse(actCode);
    }

}
