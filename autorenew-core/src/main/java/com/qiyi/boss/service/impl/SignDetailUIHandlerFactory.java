package com.qiyi.boss.service.impl;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

import com.qiyi.boss.enums.AgreementTypeEnum;

/**
 * 协议签约详情 UI 处理 Factory
 *
 * <AUTHOR>
 * @date 2021/7/26 11:38
 */
@Component
@Slf4j
public class SignDetailUIHandlerFactory implements InitializingBean, ApplicationContextAware {

    private ApplicationContext applicationContext;

    private static final Map<AgreementTypeEnum, SignDetailUIHandler> detailUIHandlerDict = Maps.newEnumMap(AgreementTypeEnum.class);

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("SignDetailUIHandlerFactory-init start!");
        applicationContext.getBeansOfType(SignDetailUIHandler.class)
            .values()
            .forEach(handler -> {
                log.info("SignDetailUIHandlerFactory-init:register handler {}  for {} agreement;", handler.getClass()
                    .getSimpleName(), handler.getAgreementType());
                detailUIHandlerDict.put(handler.getAgreementType(), handler);
            });
        log.info("SignDetailUIHandlerFactory-init end!");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public SignDetailUIHandler getDetailUIHandler(AgreementTypeEnum agreementType) {
        return detailUIHandlerDict.get(agreementType);
    }
}
