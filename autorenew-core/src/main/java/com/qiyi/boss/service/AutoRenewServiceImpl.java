package com.qiyi.boss.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.qiyi.boss.Constants;
import com.qiyi.boss.async.queue.AsyncTaskFactory;
import com.qiyi.boss.async.task.AbstractTask;
import com.qiyi.boss.async.task.SyncVipInfoToDutTask;
import com.qiyi.boss.autorenew.dto.OpenDutSetLogDesp;
import com.qiyi.boss.autorenew.enumerate.ActTypeEnum;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.component.AgreementHandlerFactory;
import com.qiyi.boss.dto.AccountUnbindReqDto;
import com.qiyi.boss.dto.CancelAutoRenewOptDto;
import com.qiyi.boss.dto.OpenAutoRenewOptDto;
import com.qiyi.boss.dto.UserAutoRenewInfo;
import com.qiyi.boss.enums.AgreementStatusEnum;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.BindStatusEnum;
import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.outerinvoke.result.PayCenterAppleDutInfoResult;
import com.qiyi.boss.service.impl.AgreementManager;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.AgreementTemplateManager;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.service.impl.AutoRenewUpgradeConfigManager;
import com.qiyi.boss.service.impl.AutoRenewVipTypeUpgradeConfigManager;
import com.qiyi.boss.service.impl.DutPriceActServiceImpl;
import com.qiyi.boss.service.impl.DutUserRenewStatusManager;
import com.qiyi.boss.service.impl.PaymentDutTypeManager;
import com.qiyi.boss.service.impl.UserAgreementLogManager;
import com.qiyi.boss.service.impl.UserManager;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.DutAccountApi;
import com.qiyi.boss.utils.EagleMeterReporter;
import com.qiyi.boss.utils.SignKeyGenerator;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.AsyncTask;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewUpgradeConfig;
import com.qiyi.vip.trade.autorenew.domain.AutorenewDutConfig;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import com.qiyi.vip.trade.autorenew.repository.DutUserRepository;

import static com.qiyi.boss.autorenew.dto.OpenDutSetLogDesp.TYPE_CHANGE;
import static com.qiyi.boss.constants.AgreementConstants.EXCLUDE_AGREEMENT_TYPES;
import static com.qiyi.vip.trade.autorenew.domain.PaymentDutType.PAY_CHANNEL_TYPE_VIP_DUT_PAY;

@Slf4j
@Component
public class AutoRenewServiceImpl implements AutoRenewService {

    public static final int MORE_THAN_ONE = 2;

    @Resource
    private DutUserRepository dutUserRepository;
    @Resource
    AutoRenewDutTypeManager autoRenewDutTypeManager;
    @Resource
    private AutorenewDutConfigService autorenewDutConfigService;
    @Resource
    DutUserRenewStatusManager dutUserRenewStatusManager;
    @Resource
    UserAgreementService userAgreementService;
    @Resource
    AutoRenewVipTypeUpgradeConfigManager autoRenewVipTypeUpgradeConfigManager;
    @Resource
    DutAccountApi dutAccountApi;
    @Resource
    UserAgreementLogManager userAgreementLogManager;
    @Resource
    PaymentDutTypeManager paymentDutTypeManager;
    @Resource
    UserManager userManager;
    @Resource
    AgreementNoInfoManager agreementNoInfoManager;
    @Resource
    AgreementTemplateManager agreementTemplateManager;
    @Resource
    AgreementOperateService agreementOperateService;
    @Resource
    DutPriceActServiceImpl dutPriceActService;
    @Resource
    AutoRenewUpgradeConfigManager autoRenewUpgradeConfigManager;
    @Resource
    PayCenterAppleSignManager payCenterAppleSignManager;
    @Resource
    private AgreementManager agreementManager;
    @Resource
    private AgreementHandlerFactory agreementHandlerFactory;

    @Override
    public List<DutUserNew> findDutUsers(FindDutUserRequest findDutUserRequest) {
        assert findDutUserRequest.getUserId() != null;
        Integer autoRenew = getAutoRenew(findDutUserRequest);
        if (Constants.TW_REGION.equals(findDutUserRequest.getLocale())) {
            int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
            return dutUserRepository.findDutUsers(findDutUserRequest.getUserId(), agreementType, autoRenew, findDutUserRequest.getDutType());
        } else {
            return dutUserRepository.getDutUsers(findDutUserRequest.getUserId(), null, autoRenew, findDutUserRequest.getDutType(), Constants.VIP_USER_SUPER).stream()
                .filter(d -> !EXCLUDE_AGREEMENT_TYPES.contains(d.getAgreementType()))
                .collect(Collectors.toList());
        }
    }

    private Integer getAutoRenew(FindDutUserRequest findDutUserRequest) {
        if (findDutUserRequest.getIsAutoRenew() == null) {
            return null;
        }
        return findDutUserRequest.getIsAutoRenew() ? 1 : 0;
    }

    @Override
    public UserAutoRenewInfo getFirstDutRenewInfo(List<DutUserNew> dutUserNews, Long vipType) {
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return null;
        }
        try {
            DutUserNew firstDutUserNew = getFirstDutUserNew(dutUserNews);
            if (firstDutUserNew == null) {
                log.warn("getFirstDutRenewInfo get null, dutUserNews:{}", dutUserNews);
                return null;
            }
            log.info("getFirstDutRenewInfo by dutUserNews:{}", dutUserNews);
            AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(firstDutUserNew.getType());
            Integer payChannel = autoRenewDutType != null ? autoRenewDutType.getPayChannel() : null;
            Timestamp nextDutTime = firstDutUserNew.getNextDutTime();
            return UserAutoRenewInfo.buildFromDutUserNew(firstDutUserNew, nextDutTime, payChannel);
        } catch (Exception e) {
            Long uid = dutUserNews.get(0).getUserId();
            log.error("查询自动续费信息失败！dutUserNews:{}, viptype:{}, error:", uid, vipType, e);
            return null;
        }
    }

    private DutUserNew getFirstDutUserNew(List<DutUserNew> dutUserNews) {
        boolean familyCardPackage = dutUserNews.stream().anyMatch(d -> d.isAutoRenewUser() && d.familyCardPackage());
        // 亲情卡限制普通自动续费不续费
        if (familyCardPackage) {
            dutUserNews.removeIf(this::dutByDeadline);
        }
        dutUserNews.forEach(this::resetOpenedNextDutTime);
        return dutUserNews.stream().filter(d -> d.getNextDutTime() != null).min(Comparator.comparing(DutUserNew::getNextDutTime)).orElse(null);
    }

    private void resetOpenedNextDutTime(DutUserNew dutUserNew) {
        if (!dutByDeadline(dutUserNew)) {
            return;
        }
        AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutUserNew.getType());
        Integer payChannel = autoRenewDutType.getPayChannel();
        Integer agreementType = dutUserNew.getAgreementType();
        Integer offset;
        if (ObjectUtils.equals(PaymentDutType.PAY_CHANNEL_MOBILE, payChannel)) {
            offset = autorenewDutConfigService.getAdvanceDay(AutorenewDutConfig.JOB_TYPE_MOBILE, AutorenewDutConfig.STATUS_VALID, agreementType);
        } else {
            offset = autorenewDutConfigService.getAdvanceDay(AutorenewDutConfig.JOB_TYPE_COMMON, AutorenewDutConfig.STATUS_VALID, agreementType);
        }
        Timestamp realNextDutTime = caculateNextDutTimeByOffset(offset.longValue(), dutUserNew);
        dutUserNew.setNextDutTime(realNextDutTime);
    }

    public boolean dutByDeadline(DutUserNew dutUserNew) {
        Integer agreementType = dutUserNew.getAgreementType();
        AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutUserNew.getType());
        // 由爱奇艺发起的自动续费
        if (ObjectUtils.equals(AgreementTypeEnum.AUTO_RENEW.getValue(), agreementType) && ObjectUtils.equals(autoRenewDutType.getPayChannelType(), PAY_CHANNEL_TYPE_VIP_DUT_PAY)) {
            return true;
        }
        return false;
    }

    @Override
    public DutUserNew getFirstDutUserForManualDut(List<DutUserNew> dutUserNews) {
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return null;
        }
        List<DutUserNew> matchedPayChannels = dutUserNews.stream()
            .filter(dutUserNew -> matchManualDutChannel(dutUserNew.getType()))
            .sorted(Comparator.comparing(DutUserNew::getOperateTime).reversed())
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(matchedPayChannels)) {
            return null;
        }
        return matchedPayChannels.stream()
            .filter(DutUserNew::upgradeDutUser)
            .findFirst().orElseGet(() -> matchedPayChannels.get(0));
    }

    private boolean matchManualDutChannel(Integer dutType) {
        AutoRenewDutType type = autoRenewDutTypeManager.getByDutType(dutType);
        if (type == null) {
            return false;
        }
        return CloudConfigUtil.supportManualDutPayChannels().contains(type.getPayChannel());
    }

    private Timestamp caculateNextDutTimeByOffset(Long offset, DutUserNew userNew) {
        if (userNew.getDeadline() == null) {
            Long vipType = userNew.getVipType();
            Long uid = userNew.getUserId();
            VipUser vipInfo = userManager.getVipInfo(uid, Collections.singletonList(vipType));
            if (vipInfo == null) {
                return null;
            }
            userNew.setDeadline(vipInfo.getDeadline());
        }
        if (offset == null) {
            return userNew.getNextDutTime() == null ? userNew.getDeadline() : userNew.getNextDutTime();
        }
        return DateHelper.caculateTime(userNew.getDeadline(), -24L * 3600 * 1000 * offset);
    }

    @Override
    public void resetUserRenewStatusWhenCancel(Long userId, List<Integer> excludeDutTypes) {
        List<Integer> finalExcludeDutTypes = excludeDutTypes == null ? Collections.emptyList() : excludeDutTypes;
        List<DutUserNew> allDutUserNews = userAgreementService.getEffectiveByUserId(userId);
        if (CollectionUtils.isNotEmpty(allDutUserNews) && CollectionUtils.isEmpty(excludeDutTypes)) {
            return;
        }
        List<DutUserNew> otherDutUserNews = allDutUserNews.stream()
            .filter(item -> !finalExcludeDutTypes.contains(item.getType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(otherDutUserNews)) {
            return;
        }
        dutUserRenewStatusManager.resetAutoRenewStatus(userId);
    }

    @Override
    public void cancelAllAutoRenew(CancelAutoRenewOptDto cancelAutoRenewOptDto) {
        Long userId = cancelAutoRenewOptDto.getUid();
        List<DutUserNew> dutUserNews = userAgreementService.getEffectiveByUserId(userId);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return;
        }
        for (DutUserNew dutUserNew : dutUserNews) {
            if (EXCLUDE_AGREEMENT_TYPES.contains(dutUserNew.getAgreementType())) {
                continue;
            }
            cancelAutoRenewOptDto.setDutType(dutUserNew.getType());
            cancelAutoRenew(dutUserNew, cancelAutoRenewOptDto);
        }
        dutUserRenewStatusManager.resetAutoRenewStatus(userId);
    }

    @Override
    public void cancelAutoRenew(CancelAutoRenewOptDto cancelAutoRenewOptDto) {
        Integer dutType = cancelAutoRenewOptDto.getDutType();
        Integer agreementNo = cancelAutoRenewOptDto.getAgreementNo();
        DutUserNew dutUserNew = userAgreementService.getByAgreementNoAndDutType(cancelAutoRenewOptDto.getUid(), agreementNo, dutType);
        if (dutUserNew == null || dutUserNew.isNotAutoRenewUser()) {
            return;
        }
        cancelAutoRenew(dutUserNew, cancelAutoRenewOptDto);
    }

    @Override
    public void cancelAutoRenew(DutUserNew dutUserNew, CancelAutoRenewOptDto cancelAutoRenewOptDto) {
        if (dutUserNew == null) {
            return;
        }
        Long userId = dutUserNew.getUserId();
        Integer dutType = dutUserNew.getType();
        Integer agreementNo = dutUserNew.getAgreementNo();
        Integer payChannel = null;
        boolean thirdDut = false;
        boolean supportUnBindWhenCancel = false;
        boolean supportDirectCancel = false;
        Integer payChannelType = null;
        if (agreementNo != null) {
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
            if (agreementNoInfo == null) {
                log.warn("取消自动续费-协议编号不存在! agreementNo:{}", agreementNo);
                return;
            }
            payChannel = agreementNoInfo.getPayChannel();
            thirdDut = agreementNoInfo.thirdDut();
            supportUnBindWhenCancel = agreementNoInfo.supportUnBindWhenCancelAutoRenew();
            supportDirectCancel = agreementNoInfo.supportDirectCancel();
            payChannelType = agreementNoInfo.getPayChannelType();
            dutType = agreementNoInfo.getDutType();
        } else {
            AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutType);
            if (autoRenewDutType == null) {
                log.warn("取消自动续费-代扣方式不存在! dutType:{}", dutType);
                return;
            }
            payChannel = autoRenewDutType.getPayChannel();
            thirdDut = autoRenewDutType.thirdDut();
            supportUnBindWhenCancel = autoRenewDutType.supportUnBindWhenCancelAutoRenew();
            supportDirectCancel = autoRenewDutType.supportDirectCancel();
            payChannelType = autoRenewDutType.getPayChannelType();
        }

        cancelAutoRenewOptDto.setPayChannel(payChannel);
        if (needUnbind(supportUnBindWhenCancel, supportDirectCancel, cancelAutoRenewOptDto.getOperateScene())) {
            AccountUnbindReqDto accountUnbindReqDto = new AccountUnbindReqDto(userId, dutType);
            boolean unbindSuccess = dutAccountApi.cancelDutAccountBind(accountUnbindReqDto);
            log.info("取消自动续费-账户中心解绑: 解绑结果={}, userId: {}, agreementNo:{}, dutType:{}", unbindSuccess, userId, agreementNo, dutType);
        }
        if (thirdDut) {
            if (!supportDirectCancel && cancelAutoRenewOptDto.notKeFuOperationType()) {
                log.info("第三方代扣方式，不可直接取消自动续费. agreementNo:{}, dutType:{}, payChannel:{}, payChannelType:{}",
                    agreementNo, dutType, payChannel, payChannelType);
                return;
            }
        }
        if (dutUserNew.isNotAutoRenewUser()) {
            return;
        }
        userAgreementService.cancelAutoRenew(dutUserNew, cancelAutoRenewOptDto);
    }

    /**
     * 是否需要解绑
     */
    private boolean needUnbind(boolean supportUnBindWhenCancel, boolean supportDirectCancel, OperateSceneEnum cancelScene) {
        boolean cancelSceneCanUnbind = CloudConfigUtil.cancelSceneCanUnbind(cancelScene.getValue());
        return supportUnBindWhenCancel || (supportDirectCancel && cancelSceneCanUnbind);
    }

    @Override
    public DutUserNew openAutoRenew(VipUser vipUser, AgreementNoInfo agreementNoInfo, OpenAutoRenewOptDto openAutoRenewOptDto) {
        Integer agreementType = agreementNoInfo.getType();
        List<DutUserNew> openedDutUsers = userAgreementService.getByAgreementTypeAndVipType(vipUser.getId(), AgreementTypeEnum.valueOf(agreementType), vipUser.getTypeId(), AgreementStatusEnum.VALID);
        doRepeatOpening(vipUser, agreementNoInfo, openAutoRenewOptDto.getAmount(), openedDutUsers, openAutoRenewOptDto.getFc(), openAutoRenewOptDto.getFv(), null);
        return agreementOperateService.openAutoRenew(vipUser, agreementNoInfo, openAutoRenewOptDto);
    }

    /**
     * 签约支付方式开通自动续费
     */
    @Override
    public DutUserNew openBySignPay(AutorenewRequest autorenewRequest, AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate) {
        VipUser vipUser = autorenewRequest.generateVipUser();
        Integer agreementType = agreementNoInfo.getType();
        if (vipUser == null) {
            log.error("Order message endTime or productSubtype is null. {}", autorenewRequest);
            return null;
        }
        PayCenterAppleDutInfoResult appleSignInfo = null;
        appleSignInfo = getAppleSignInfo(autorenewRequest, appleSignInfo);
        // 打包购签约、代扣单没有会员类型
        if (autorenewRequest.getVipType() == null) {
            autorenewRequest.setVipType(agreementNoInfo.getVipType());
        }
        log.info("openBySignPay, autorenewRequest:{}, agreementNo:{}", autorenewRequest, agreementNoInfo.getId());
        List<DutUserNew> openedDutUsers = userAgreementService.getByAgreementTypeAndVipType(autorenewRequest.getUserId(), AgreementTypeEnum.valueOf(agreementType), autorenewRequest.getVipType(), AgreementStatusEnum.VALID);
        OperateSceneEnum operateScene = CollectionUtils.isNotEmpty(openedDutUsers)
            ? OperateSceneEnum.OPEN_BUY_EXTENDSIGN : OperateSceneEnum.OPEN_BUY;
        Integer amount = agreementTemplate.getAmount();
        dealUpgrade(autorenewRequest);
        saveRenewSetLogForIAPChangedAmount(openedDutUsers, agreementNoInfo, autorenewRequest);
        doRepeatOpening(vipUser, agreementNoInfo, amount, openedDutUsers, autorenewRequest.getFc(), autorenewRequest.getFv(), autorenewRequest);

        return openByAutoRenewRequest(agreementNoInfo, agreementTemplate, autorenewRequest, operateScene, appleSignInfo);
    }

    private PayCenterAppleDutInfoResult getAppleSignInfo(AutorenewRequest autorenewRequest, PayCenterAppleDutInfoResult appleSignInfo) {
        boolean appleOrder = autorenewRequest.isAppleOrder();
        if (!appleOrder) {
            return null;
        }
        Long userId = autorenewRequest.getUserId();
        return payCenterAppleSignManager.getSpecifiedSignInfo(userId, autorenewRequest.getActCode());
    }

    /**
     * 处理升级购买的场景，若用户开通升级前的会员类型自动续费，将其取消
     */
    private void dealUpgrade(AutorenewRequest autorenewRequest) {
        if (!autorenewRequest.isUpgrade()) {
            return;
        }
        Long sourceVipType = autorenewRequest.getSourceVipType();
        Integer agreementType = autorenewRequest.getAgreementType();
        try {
            List<DutUserNew> dutUserNewList = userAgreementService.getByAgreementTypeAndVipType(autorenewRequest.getUserId(), AgreementTypeEnum.valueOf(agreementType), sourceVipType, AgreementStatusEnum.VALID);
            //手机话费代扣 或 第三方渠道发起的代扣-不能直接取消自动续费
            List<Integer> mobileDutTypeList = autoRenewDutTypeManager.getDutTypeListByVipTypeAndPayChannel(sourceVipType, agreementType, PaymentDutType.PAY_CHANNEL_MOBILE);
            Map<Integer, AutoRenewDutType> thirdDutTypeMap = autoRenewDutTypeManager.getDutTypeInfoListByVipTypeAndPayChannelType(agreementType, sourceVipType, PaymentDutType.PAY_CHANNEL_TYPE_THIRD_DUT_PAY);
            for (DutUserNew dutUserNew : dutUserNewList) {
                if (mobileDutTypeList.contains(dutUserNew.getType()) ||
                    (thirdDutTypeMap.containsKey(dutUserNew.getType()) && thirdDutTypeMap.get(dutUserNew.getType()).cannotDirectCancel())) {
                    continue;
                }
                CancelAutoRenewOptDto cancelAutoRenewOptDto = CancelAutoRenewOptDto.buildFromDutUserNew(dutUserNew, OperateSceneEnum.CANCEL_CHANGEUPGRADE, false);
                cancelAutoRenew(dutUserNew, cancelAutoRenewOptDto);
            }
        } catch (Exception e) {
            log.error("[module:VIPAutoRenewMessageProcessor] [action:process] [dealUpgrade error.] [params:{}]", autorenewRequest, e);
        }
    }

    /**
     * 处理IAP自动续费切换的场景,根据切换前的续费类型生成一条取消的操作日志
     */
    private void saveRenewSetLogForIAPChangedAmount(List<DutUserNew> dutUserNews, AgreementNoInfo agreementNoInfo, AutorenewRequest autorenewRequest) {
        if (!agreementNoInfo.isAppleChannel() || CollectionUtils.isEmpty(dutUserNews)) {
            return;
        }
        Integer dutType = agreementNoInfo.getDutType();
        DutUserNew dutUser = dutUserNews.stream().filter(dutUserNew -> Objects.equals(dutUserNew.getType(), dutType)).findFirst().orElse(null);
        if (null == dutUser || dutUser.isNotAutoRenewUser()) {
            return;
        }
        String desc = OpenDutSetLogDesp.buildSetLogDesp(dutUser, OpenDutSetLogDesp.TYPE_CHANGE, autorenewRequest.getFc(),
            autorenewRequest.getFv(), OperateSceneEnum.OPEN_BUY, agreementNoInfo.getPayChannel());
        DutRenewSetLog dutRenewSetLog = DutRenewSetLog.buildSetLog(dutUser, desc, OperateTypeEnum.CANCEL, DateHelper.getDateTime());
        dutRenewSetLog.setPlatform(autorenewRequest.getPlatformId());
        dutRenewSetLog.setPlatformCode(autorenewRequest.getPlatformCode());
        userAgreementLogManager.addSetLog(dutRenewSetLog);
    }

    @Override
    public void doRepeatOpening(VipUser vipUser, AgreementNoInfo agreementNoInfo, Integer openingAmount, List<DutUserNew> openedDutUsers,
        String fc, String fv, AutorenewRequest autorenewRequest) {
        Long vipType = vipUser.getTypeId();
        if (vipType != null && vipType == Constants.VIP_USER_TW) {
            return;
        }
        try {
            Integer agreementType = agreementNoInfo.getType();
            doChangeVipType(vipType, vipUser.getId(), agreementType);
            if (CollectionUtils.isEmpty(openedDutUsers)) {
                return;
            }
            for (DutUserNew openedDutUser : openedDutUsers) {
                doSameVipTypeAndAgreementType(agreementNoInfo, null, openingAmount, openedDutUser, fc, fv, vipUser.getDeadline(), autorenewRequest);
            }
        } catch (Exception e) {
            log.error("Do repeat opening meets an error. userId:{}, vipType:{}, amount:{}", vipUser.getId(), vipType, openingAmount, e);
        }
    }

    @Override
    public void doRepeatOpeningOld(VipUser vipUser, AutoRenewDutType openingAutoRenewDutType, Integer openingAmount, String fc, String fv) {
        Long userId = vipUser.getId();
        Long vipType = vipUser.getTypeId();
        if (vipType != null && vipType == Constants.VIP_USER_TW) {
            return;
        }
        try {
            Integer agreementType = openingAutoRenewDutType.getAgreementType();
            doChangeVipType(vipType, userId, agreementType);

            List<DutUserNew> dutUserNewList = userAgreementService.getByAgreementTypeAndVipType(userId, AgreementTypeEnum.valueOf(agreementType), vipType, AgreementStatusEnum.VALID);
            if (CollectionUtils.isEmpty(dutUserNewList)) {
                return;
            }
            for (DutUserNew openedDutUser : dutUserNewList) {
                doSameVipTypeAndAgreementType(null, openingAutoRenewDutType, openingAmount, openedDutUser, fc, fv, vipUser.getDeadline(), null);
            }
        } catch (Exception e) {
            log.error("Do repeat opening meets an error. userId:{}, vipType:{}, amount:{}", userId, vipType, openingAmount, e);
        }
    }

    /**
     * 同一会员体系只能开通一种会员类型自动续费
     */
    private void doChangeVipType(Long vipType, Long userId, Integer agreementType) {
        try {
            List<Long> sameGroupVipTypes = autoRenewVipTypeUpgradeConfigManager.getSameGroupVipTypes(vipType, agreementType);
            if (sameGroupVipTypes.size() < MORE_THAN_ONE) {
                return;
            }
            String upgradeFc = "";
            for (Long itemVipType : sameGroupVipTypes) {
                if (itemVipType.equals(vipType)) {
                    continue;
                }
                List<DutUserNew> userList = userAgreementService.getByAgreementTypeAndVipType(userId, AgreementTypeEnum.valueOf(agreementType), itemVipType, AgreementStatusEnum.VALID);
                if (CollectionUtils.isEmpty(userList)) {
                    continue;
                }

                if (Constants.VIP_USER_SUPER == vipType && Constants.VIP_USER_STUDENT == itemVipType) {
                    upgradeFc = Constants.FROM_STUDENT_TO_SUPER_CANCEL_FC;
                }
                Map<Integer, AutoRenewDutType> thirdDutTypeMap = autoRenewDutTypeManager.getDutTypeInfoListByVipTypeAndPayChannelType(agreementType, itemVipType, PaymentDutType.PAY_CHANNEL_TYPE_THIRD_DUT_PAY);
                for (DutUserNew dutUserNew : userList) {
                    if (MapUtils.isNotEmpty(thirdDutTypeMap)
                        && thirdDutTypeMap.containsKey(dutUserNew.getType())
                        && thirdDutTypeMap.get(dutUserNew.getType()).cannotDirectCancel()) {
                        continue;
                    }
                    CancelAutoRenewOptDto cancelAutoRenewOptDto = CancelAutoRenewOptDto.buildFromDutUserNew(dutUserNew, OperateSceneEnum.CANCEL_CHANGEVIPTYPE, upgradeFc, false);
                    cancelAutoRenew(dutUserNew, cancelAutoRenewOptDto);
                }
            }
        } catch (Exception e) {
            log.error("Do change vipType meets an error. userId:{}, vipType:{}", userId, vipType, e);
        }
    }

    /**
     * 已同一会员类型、同一协议类型则更新时长及签约价,代扣方式不支持更新时取消
     * @param openingAgreementNoInfo 当前开通的协议号信息, 协议模式此值不为空
     * @param openingAutoRenewDutType 当前开通的自动续费类型，非协议模式此值不为空
     */
    private void doSameVipTypeAndAgreementType(AgreementNoInfo openingAgreementNoInfo, AutoRenewDutType openingAutoRenewDutType, Integer openingAmount, DutUserNew openedDutUser, String fc, String fv, Timestamp deadline, AutorenewRequest autorenewRequest) {
        Integer openingDutType = openingAgreementNoInfo != null ? openingAgreementNoInfo.getDutType() : openingAutoRenewDutType.getDutType();
        Short openingPriority = openingAgreementNoInfo != null ? openingAgreementNoInfo.getPriority() : openingAutoRenewDutType.getPriority();
        Integer openingAgreementNo = openingAgreementNoInfo != null ? openingAgreementNoInfo.getId() : null;
        Integer openedAgreementNo = openedDutUser.getAgreementNo();
        Integer openedDutType = openedDutUser.getType();
        boolean openedDutUserIsAgreementMode = openedAgreementNo != null;
        AgreementNoInfo openedAgreementNoInfo = null;
        if (openedDutUserIsAgreementMode) {
            openedAgreementNoInfo = agreementNoInfoManager.getById(openedAgreementNo);
            openedDutType = openedAgreementNoInfo.getDutType();
        }
        AutoRenewDutType openedAutoRenewDutType = autoRenewDutTypeManager.getByDutType(openedDutType);
        if (openedAutoRenewDutType == null) {
            return;
        }
        if (openingAgreementNo != null && Objects.equals(openingAgreementNo, openedAgreementNo)) {
            return;
        }
        Short openedPriority = openedAgreementNoInfo != null ? openedAgreementNoInfo.getPriority() : openedAutoRenewDutType.getPriority();
        // 处理已开通低优先级代扣方式的场景, 主要针对提价保价期内购买的情况
        if (cancelLowerPriorityDutType(openingPriority, openedPriority, openedDutUser)) {
            return;
        }

        Integer openedPayChannel = openedAgreementNoInfo != null ? openedAgreementNoInfo.getPayChannel() : openedAutoRenewDutType.getPayChannel();
        boolean openedSupportChangeAmount = openedAgreementNoInfo != null ? openedAgreementNoInfo.supportChangeAmount() : openedAutoRenewDutType.supportChangeAmount();
        boolean openedSupportDirectCancel = openedAgreementNoInfo != null ? openedAgreementNoInfo.supportDirectCancel() : openedAutoRenewDutType.supportDirectCancel();
        boolean normalAgreementOfOpening = openingAgreementNoInfo == null || openingAgreementNoInfo.defaultNo();
        //开通非正价协议时需要取消所有已存在的签约关系。签约时长一样时，协议编号不一样但是dutType一样，需要取消已开通的协议
        Integer agreementType = openedDutUser.getAgreementType();
        if (openedDutUser.getAmount() != null && Objects.equals(openedDutUser.getAmount(), openingAmount) && normalAgreementOfOpening) {
            if (openedAgreementNo == null || Objects.equals(openedAgreementNo, openingAgreementNo)) {
                return;
            } else {
                if (Objects.equals(openedDutType, openingDutType)) {
                    if (openedSupportDirectCancel) {
                        CancelAutoRenewOptDto cancelAutoRenewOptDto = CancelAutoRenewOptDto.buildFromDutUserNew(openedDutUser, OperateSceneEnum.CANCEL_CHANGEAMOUNT, fc, false);
                        cancelAutoRenew(openedDutUser, cancelAutoRenewOptDto);
                    }
                } else {
                    agreementHandlerFactory.getHandler(AgreementTypeEnum.valueOf(agreementType)).resetOpenedNextDutTime(autorenewRequest, openedDutUser);
                    return;
                }
            }
        }

        if (openedSupportChangeAmount && normalAgreementOfOpening) {
            AgreementNoInfo needReplaceAgreementNoInfo = null;
            if (openedDutUserIsAgreementMode) {
                needReplaceAgreementNoInfo = agreementNoInfoManager.getDefaultWhenChangeAmount(openedDutType, openingAmount, openedPriority);
            }
            //未找到可切换的协议号直接取消
            if (openedDutUserIsAgreementMode && needReplaceAgreementNoInfo == null) {
                log.error("not find replace agreementNo when change amount, openedDutType:{}, openedPriority:{}, openingAmount:{}",
                    openedDutType, openedPriority, openingAmount);
                EagleMeterReporter.incrMissReplaceableAgreementNo(openedDutType, openingAmount, openedPriority);
                return;
            }
            String cancelSetLogDesp = OpenDutSetLogDesp.buildSetLogDesp(openedDutUser, TYPE_CHANGE, fc, fv,
                OperateSceneEnum.CANCEL_CHANGEAMOUNT, openedPayChannel);
            DutRenewSetLog cancelSetLog = DutRenewSetLog.buildSetLog(openedDutUser, cancelSetLogDesp, OperateTypeEnum.CANCEL, DateHelper.getDateTime());
            userAgreementLogManager.addSetLog(cancelSetLog);

            // 切换前清空活动信息
            if (!ActTypeEnum.STOP_AFTER_X.getValue().equals(openedDutUser.getActType())) {
                openedDutUser.setRemainPeriods(0);
                openedDutUser.setActTotalPeriods(0);
                openedDutUser.setActCode("");
                openedDutUser.setContractPrice(null);
                openedDutUser.setActType(null);
            }

            openingAmount = needReplaceAgreementNoInfo != null ? needReplaceAgreementNoInfo.getAmount() : openingAmount;
            openedDutUser.setAmount(openingAmount);

            if (openedDutUserIsAgreementMode) {
                openedDutUser.setAgreementNo(needReplaceAgreementNoInfo.getId());
                openedDutUser.setSignKey(SignKeyGenerator.getSignKey(openedDutUser.getUserId(), agreementType));
                openedDutUser.setOperateTime(DateHelper.getDateTime());
                openedDutUser.setSerialRenewCount(0);
                openedDutUser.setRenewPrice(null);
                openedDutUser.setContractPrice(null);
                AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(needReplaceAgreementNoInfo.getTemplateCode());
                userAgreementService.initRenewPrice(agreementTemplate.getPricingStrategy(), needReplaceAgreementNoInfo, openedDutUser);
            } else {
                Integer renewPrice = agreementManager.getDefaultRenewPrice(openedDutUser.getType(), openingAmount);
                openedDutUser.setRenewPrice(renewPrice);
                openedDutUser.setContractPrice(renewPrice);
            }
            if (AgreementTypeEnum.commonAutoRenew(agreementType) && deadline != null) {
                openedDutUser.setDeadline(deadline);
            }
            openedDutUser.setNextDutTime(null);

            DutRenewSetLog openSetLog = null;
            if (!Objects.equals(openingDutType, openedDutType)) {
                OperateSceneEnum operateScene = OperateSceneEnum.OPEN_BUY;
                String openSetLogDesp = OpenDutSetLogDesp.buildSetLogDesp(openedDutUser, TYPE_CHANGE, fc, fv, operateScene, openedPayChannel);
                openedDutUser.setOperateSceneExt(operateScene);
                openSetLog = DutRenewSetLog.buildSetLog(openedDutUser, openSetLogDesp, OperateTypeEnum.OPEN, DateHelper.getDateTime());
            }
            userAgreementService.saveAutoRenew(openedDutUser, openSetLog);
        } else if (openedSupportDirectCancel) {
            CancelAutoRenewOptDto cancelAutoRenewOptDto = CancelAutoRenewOptDto.buildFromDutUserNew(openedDutUser, OperateSceneEnum.CANCEL_CHANGEAMOUNT, fc, false);
            cancelAutoRenew(openedDutUser, cancelAutoRenewOptDto);
        }
    }

    /**
     * 取消已开通的低优先级自动续费
     */
    private boolean cancelLowerPriorityDutType(Short openingPriority, Short openedPriority, DutUserNew openedDutUser) {
        if (openingPriority != null && openedPriority != null && openingPriority > openedPriority) {
            CancelAutoRenewOptDto cancelAutoRenewOptDto = CancelAutoRenewOptDto.buildFromDutUserNew(openedDutUser, OperateSceneEnum.CANCEL_CHANGEPRIORITY, false);
            cancelAutoRenew(openedDutUser, cancelAutoRenewOptDto);
            return true;
        }
        return false;
    }

    @Override
    public DutUserNew openByAutoRenewRequest(AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate, AutorenewRequest autorenewRequest,
        OperateSceneEnum operateScene, PayCenterAppleDutInfoResult appleSignInfo) {
        Long userId = autorenewRequest.getUserId();
        Long vipType = autorenewRequest.getVipType();
        Integer openingAmount = autorenewRequest.getOpeningAmount();
        Integer openingDutType = agreementNoInfo.getDutType();
        Integer agreementNo = agreementNoInfo.getId();
        Integer agreementType = autorenewRequest.getAgreementType();


        boolean needUpdate = true;
        DutUserNew openingDutUser = userAgreementService.getByAgreementNoOrDutType(userId, agreementNo, agreementNoInfo.getDutType(), vipType);
        if (openingDutUser == null) {
            openingDutUser = new DutUserNew();
            openingDutUser.setUserId(userId);
            openingDutUser.setAgreementType(agreementNoInfo.getType());
            openingDutUser.setSignKey(SignKeyGenerator.getSignKey(userId, openingDutUser.getAgreementType()));
            openingDutUser.setAgreementNo(agreementNo);
            openingDutUser.setType(openingDutType);
            openingDutUser.setOperateTime(DateHelper.getDateTime());
        } else if (openingDutUser.isTwVipDutUser() && validSpgateway(agreementNoInfo, openingDutUser.getAmount(), openingAmount)) {
            if (openingDutUser.isAutoRenewUser() && !agreementNoInfo.isAppleChannel()) {
                log.info("[openAutoRenewByOrderMsg] [is already autorenew user] [uid:{}] [dutType:{}] [agreementNo:{}]",
                    userId, openingDutType, openingDutUser.getAgreementNo());
                return openingDutUser;
            }
        } else if (openingDutUser.isNotAutoRenewUser()) {
            openingDutUser.setAgreementNo(agreementNo);
            openingDutUser.setSignKey(SignKeyGenerator.getSignKey(userId, openingDutUser.getAgreementType()));
            openingDutUser.setRenewPrice(null);
            openingDutUser.setContractPrice(null);
            openingDutUser.setSerialRenewCount(0);
            openingDutUser.setExt(null);
        } else if (openingDutUser.isAutoRenewUser()) {
            if (openingDutUser.getAgreementNo() == null) {
                openingDutUser.setAgreementNo(agreementNo);
                if (openingDutUser.getSignKey() == null) {
                    openingDutUser.setSignKey(SignKeyGenerator.getSignKey(userId, openingDutUser.getAgreementType()));
                }
            }
            needUpdate = false;
        }
        String orderCode = openingDutUser.getOrderCode();
        if (ObjectUtils.equals(orderCode, autorenewRequest.getOrderCode())) {
            log.warn("sign order is duplicate, orderCode:{}", orderCode);
            return openingDutUser;
        }
        if (needUpdate) {
            openingDutUser.setSignTime(autorenewRequest.getPayTime());
        }

        // 此处处理特殊协议类型的到期时间
        agreementHandlerFactory.getHandler(AgreementTypeEnum.valueOf(agreementType)).setOpeningNextDutTime(autorenewRequest, openingDutUser, null, agreementTemplate, appleSignInfo);
        openingDutUser.setAgreementType(agreementType);
        openingDutUser.setAutoRenew(DutUserNew.RENEW_AUTO);
        openingDutUser.setStatus(BindStatusEnum.BIND.getValue());
        openingDutUser.setAmount(agreementTemplate.getAmount());
        openingDutUser.setOrderCode(autorenewRequest.getOrderCode());
        openingDutUser.setPlatform(autorenewRequest.getPlatformId());
        openingDutUser.setPlatformCode(autorenewRequest.getPlatformCode());
        openingDutUser.setSourceVipType(autorenewRequest.getSourceVipType());
        openingDutUser.setVipType(vipType);
        Integer vipCategory = vipType == Constants.VIP_USER_TW ? Constants.VIP_CATEGORY_TAIWAN : Constants.VIP_CATEGORY_MAINLAND;
        openingDutUser.setVipCategory(vipCategory);
        Timestamp deadline = autorenewRequest.getDeadline();
        if (AgreementTypeEnum.commonAutoRenew(agreementType) && deadline != null) {
            openingDutUser.setDeadline(deadline);
        } else {
            //增加同步会员信息逻辑
            AbstractTask task = new SyncVipInfoToDutTask(userId, vipType, vipCategory, 0);
            AsyncTaskFactory.getInstance().insertIntoDB(task, AsyncTask.POOL_TYPE_NOTIFY_RENEW_SET_INFO,
                AsyncTask.Task_PRIORITY_1, Timestamp.from(Instant.now().plusSeconds(20)));
        }
        openingDutUser.setPcode(agreementTemplate.getPid());
        openingDutUser.setCurrencyUnit(autorenewRequest.getCurrencyUnit());
        openingDutUser.setUpdateTime(DateHelper.getDateTime());
        openingDutUser.setTimeZone(autorenewRequest.getTimeZone());
        Integer templateRenewCount = null;
        if (agreementNoInfo.notDefaultNo() && !agreementNoInfo.thirdDut()) {
            templateRenewCount = userAgreementLogManager.successDutLogCount(userId, agreementNoInfo.getTemplateCode(), null);
        }
        openingDutUser.buildExt(operateScene, templateRenewCount);
        if (appleSignInfo != null) {
            openingDutUser.setSignTime(appleSignInfo.getRecentSubscriptionStartDate());
            openingDutUser.setNextDutTime(appleSignInfo.getExpiresDate());
        }
        userAgreementService.initRenewPrice(agreementTemplate.getPricingStrategy(), agreementNoInfo, openingDutUser);

        //IAP或Google续费,保存第三方的下次代扣时间
        if (vipType == Constants.VIP_USER_TW) {
            openingDutUser.setNextDutTime(Constants.caculateTaiWanDutTime());
        }
        // 苹果
        if (null != autorenewRequest.getExpireTime()) {
            openingDutUser.setNextDutTime(autorenewRequest.getExpireTime());
        }
        dutPriceActService.doAct(openingDutUser, autorenewRequest);
        doUpgrade(agreementNoInfo.getPriority(), openingDutUser);
        if (autorenewRequest.getActCode() != null) {
            openingDutUser.setActCode(autorenewRequest.getActCode());
        }

        String openSetLogDesp = OpenDutSetLogDesp.buildOpenSetLogDesp(openingDutUser, autorenewRequest.getFc(), autorenewRequest.getFv(),
            autorenewRequest.getThirdUid(), operateScene, agreementNoInfo.getPayChannel(), autorenewRequest.getSource());
        DutRenewSetLog openSetLog = DutRenewSetLog.buildSetLog(openingDutUser, openSetLogDesp, OperateTypeEnum.OPEN, DateHelper.getDateTime());
        if (needLogDutUserInfoBeforeSave(agreementType)) {
            log.info("openingDutUser before save:{}", openingDutUser);
        }
        userAgreementService.saveAutoRenew(openingDutUser, openSetLog);
        return openingDutUser;
    }

    private boolean needLogDutUserInfoBeforeSave(Integer agreementType) {
        return CloudConfigUtil.needLogDutUserInfoBeforeSave()
            && (AgreementTypeEnum.jointType(agreementType) || AgreementTypeEnum.familyType(agreementType));
    }

    private boolean noNeedProcess(AutorenewRequest autorenewRequest) {
        return false;
    }

    private boolean validSpgateway(AgreementNoInfo agreementNoInfo, Integer openedAmount, Integer openingAmount) {
        return !agreementNoInfo.isSpGatewayChannel() || (agreementNoInfo.isSpGatewayChannel() && openedAmount.equals(openingAmount));
    }

    /**
     * 升级场景设置续费价和产品
     */
    private void doUpgrade(Short dutTypePriority, DutUserNew openingDutUser) {
        if (openingDutUser.getSourceVipType() == null) {
            return;
        }
        AutoRenewUpgradeConfig autoRenewUpgradeConfig = autoRenewUpgradeConfigManager.getByVipType(openingDutUser.getSourceVipType(),
            openingDutUser.getVipType(), dutTypePriority);
        if (autoRenewUpgradeConfig == null) {
            return;
        }
        openingDutUser.setPcode(autoRenewUpgradeConfig.getMonthProductCode());
        openingDutUser.setRenewPrice(autoRenewUpgradeConfig.getMonthRenewPrice());
    }

    /**
     * 获取续费价格
     * 1. 签约关系上有续费价格，直接放回
     * 2. 若为协议模式，则计算后返回
     * 3. 否则从payment_dut_type表查询
     */
    @Override
    public Integer getDisplayRenewPrice(DutUserNew dutUserNew) {
        Integer renewPrice = userAgreementService.getAutoRenewDisplayRenewPrice(dutUserNew);
        if (renewPrice != null) {
            return renewPrice;
        }
        renewPrice = paymentDutTypeManager.getRenewPrice(dutUserNew.getVipType(), dutUserNew.getAmount(),
            dutUserNew.getActCode(), dutUserNew.getType(), dutUserNew.getUserId());
        log.info("[get defaultPrice progress][vipType:{}, firstRenewPrice:{}]", dutUserNew.getVipType(), renewPrice);
        return renewPrice;
    }

}

