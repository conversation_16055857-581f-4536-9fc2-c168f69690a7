package com.qiyi.boss.service.impl;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.dto.UserAgreementPerformanceRecord;
import com.qiyi.boss.dto.UserAgreementRespDto;
import com.qiyi.boss.dto.UserAgreementSettlementInfo;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.OperateStatusEnum;
import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.enums.PeriodUnitEnum;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementSettlementRule;
import com.qiyi.vip.trade.autorenew.domain.AgreementTempPrice;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.qiyue.domain.QiYueProductNew;

/**
 * 芝麻 GO 签约详情接口 UI Handler
 *
 * <AUTHOR>
 * @date 2021/7/26 11:36
 */
@Slf4j
@Component
public class ZhiMaGoSignDetailUIHandler implements SignDetailUIHandler {

    private static final int FIRST_PERIOD_NO = 1;

    @Resource
    UserAgreementLogManager userAgreementLogManager;
    @Resource
    AgreementTemplateManager agreementTemplateManager;

    @Override
    public AgreementTypeEnum getAgreementType() {
        return AgreementTypeEnum.ZHIMA_GO;
    }

    @Override
    public UserAgreementRespDto constructRespDto(AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate, DutUserNew dutUserNew, QiYueProductNew qiYueProductNew) {
        boolean openedBySignPay = OperateSceneEnum.isSignPayScene(dutUserNew.getOperateSceneExt());
        UserAgreementRespDto signedAgreementRespDto = createSignedAgreementRespDto(agreementNoInfo, agreementTemplate, dutUserNew, qiYueProductNew.getName());

        // 如果用户协议为履约完成或已取消，那么一定会有取消协议的 setLog
        if (dutUserNew.invalidOrFinished()) {
            OperateTypeEnum operateType = dutUserNew.invalid() ? OperateTypeEnum.CANCEL : OperateTypeEnum.FINISHED;
            DutRenewSetLog cancelSetLog = userAgreementLogManager.getSetLogBySignKeyFromMaster(dutUserNew.getUserId(), dutUserNew.getSignKey(), operateType);
            if (Objects.nonNull(cancelSetLog)) {
                Integer serialRenewCount = cancelSetLog.getSerialRenewCount();
                signedAgreementRespDto.setEndTime(cancelSetLog.getOperateTime());
                signedAgreementRespDto.setPerformedPeriods(openedBySignPay ? serialRenewCount + 1 : serialRenewCount);
            } else {
                log.error("user:{} invalid zhiMaGo agreementNo [{}] lost set log!signKey:{}", dutUserNew.getUserId(), dutUserNew.getAgreementNo(), dutUserNew.getSignKey());
            }
        }

        AgreementSettlementRule agreementSettlementRule = agreementTemplateManager.getSettlementRule(dutUserNew.getAgreementNo());
        // 获取每一期的代扣记录
        Map<Integer, DutRenewLog> userPerformanceRecord = userAgreementLogManager.listRenewLogBySignKeyFromMaster(dutUserNew.getUserId(), dutUserNew.getSignKey());
        List<AgreementTempPrice> agreementPricesList = agreementTemplateManager.getPriceByCode(agreementTemplate.getCode());
        Map<Integer, AgreementTempPrice> periodNoToPriceMap = agreementPricesList.stream().collect(Collectors.toMap(AgreementTempPrice::getPeriodNo, a -> a, (k1, k2) -> k1));
        AgreementTempPrice defaultPrice = MapUtils.getObject(periodNoToPriceMap, null, periodNoToPriceMap.get(0));
        AgreementTempPrice firstPeriodPrice = MapUtils.getObject(periodNoToPriceMap, FIRST_PERIOD_NO, defaultPrice);

        ChronoUnit timeUnit = PeriodUnitEnum.valueOf(agreementTemplate.getPeriodUnit()).getUnit();
        List<UserAgreementPerformanceRecord> performanceRecordList = initPerformanceRecordList(dutUserNew, firstPeriodPrice, openedBySignPay);
        // 实际享受的折扣金额
        int discountPrice = openedBySignPay ? firstPeriodPrice.getOriginalPrice() - firstPeriodPrice.getPrice() : 0;
        Timestamp lastDutRecordTime = CollectionUtils.isNotEmpty(performanceRecordList) ? performanceRecordList.get(0).getDutTime() : null;
        int startPeriodNo = openedBySignPay ? 2 : 1;
        for (int i = startPeriodNo; i <= agreementTemplate.getPeriods(); i++) {
            int dutPeriodNo = openedBySignPay ? i - 1 : i;
            DutRenewLog dutRenewLog = userPerformanceRecord.get(dutPeriodNo);
            AgreementTempPrice tempPrice = MapUtils.getObject(periodNoToPriceMap, i, defaultPrice);
            UserAgreementPerformanceRecord record;
            if (Objects.isNull(dutRenewLog)) {
                record = UserAgreementPerformanceRecord.createUnPerformanceRecord(dutUserNew, i, tempPrice);
                if (Objects.isNull(lastDutRecordTime)) {
                    lastDutRecordTime = dutUserNew.getSignTime();
                } else {
                    lastDutRecordTime = DateHelper.calculateEndTime(lastDutRecordTime, 1, timeUnit);
                }
                record.setDutTime(lastDutRecordTime); //设置预计代扣时间
            } else {
                lastDutRecordTime = dutRenewLog.getCreateTime();
                record = UserAgreementPerformanceRecord.createPerformanceRecord(dutUserNew, i, tempPrice, dutRenewLog);
                //优惠金额为承诺任务期内的原价-续费价
                if (dutRenewLog.isPaySuccess() && i <= agreementSettlementRule.getPromisePeriods()) {
                    discountPrice += record.getOriginalPrice() - record.getPrice();
                }
            }
            performanceRecordList.add(record);
        }
        signedAgreementRespDto.setDiscountAmount(discountPrice);
        signedAgreementRespDto.setPeriodInfoList(performanceRecordList);
        DutRenewLog settleRenewLog = userAgreementLogManager.getSettleRenewLogBySignKeyFromMaster(dutUserNew.getUserId(), dutUserNew.getSignKey());
        UserAgreementSettlementInfo settlementInfo = constructSettlementInfo(agreementSettlementRule, signedAgreementRespDto.getEndTime(), dutUserNew, settleRenewLog);
        signedAgreementRespDto.setSettlementInfo(settlementInfo);
        return signedAgreementRespDto;
    }

    public List<UserAgreementPerformanceRecord> initPerformanceRecordList(DutUserNew dutUserNew, AgreementTempPrice firstPeriodPrice, boolean openedBySignPay) {
        List<UserAgreementPerformanceRecord> performanceRecordList = Lists.newArrayList();
        if (openedBySignPay) {
            DutRenewLog signOrderLog = DutRenewLog.builder()
                .orderCode(dutUserNew.getOrderCode())
                .createTime(dutUserNew.getSignTime())
                .status(DutRenewLog.PAY_SUCCESS)
                .fee(firstPeriodPrice.getPrice())
                .build();
            performanceRecordList.add(UserAgreementPerformanceRecord.createPerformanceRecord(dutUserNew, FIRST_PERIOD_NO, firstPeriodPrice, signOrderLog));
        }
        return performanceRecordList;
    }

    private UserAgreementSettlementInfo constructSettlementInfo(AgreementSettlementRule agreementSettlementRule, Timestamp settlementTime, DutUserNew userNew, DutRenewLog settleRenewLog) {
        DutRenewSetLog settleSetLog = userAgreementLogManager.getSetLogBySignKeyFromMaster(userNew.getUserId(), userNew.getSignKey(), OperateTypeEnum.SETTLE);
        UserAgreementSettlementInfo info = new UserAgreementSettlementInfo();
        info.setSettlementTime(settlementTime);
        info.setExemptionPeriod(agreementSettlementRule.getExemptionPeriod());
        info.setPromisePeriods(agreementSettlementRule.getPromisePeriods());
        if (userNew.invalidOrFinished()) {
            info.setSettlementTime(settleSetLog.getOperateTime());
            info.setSettlementStatus(OperateStatusEnum.DUT_SUCCESS.getValue());
            info.setOrderCode(settleRenewLog.getOrderCode());
            info.setSettlementFee(settleRenewLog.getFee());
        } else if (userNew.settleInProgress()) {
            info.setSettlementTime(settleSetLog.getOperateTime());
            if (Objects.nonNull(settleRenewLog)) {
                info.setSettlementFee(settleRenewLog.getFee());
                info.setOrderCode(settleRenewLog.getOrderCode());
                info.setSettlementStatus(settleRenewLog.isPaySuccess() ? OperateStatusEnum.DUT_SUCCESS.getValue() : OperateStatusEnum.DUT_IN_PROGRESS.getValue());
            } else {
                info.setSettlementFee(0);
                info.setSettlementStatus(OperateStatusEnum.DUT_IN_PROGRESS.getValue());
            }
        } else {
            info.setSettlementFee(0);
            info.setSettlementStatus(OperateStatusEnum.DUT_PENDING.getValue());
        }
        return info;
    }

    /**
     * 组装创建签约协议信息
     */
    private UserAgreementRespDto createSignedAgreementRespDto(AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate, DutUserNew dutUserNew, String productName) {
        UserAgreementRespDto userAgreementRespDto = UserAgreementRespDto.createSignedAgreementBasicRespDto(agreementNoInfo, agreementTemplate, dutUserNew, productName);
        ChronoUnit timeUnit = PeriodUnitEnum.valueOf(agreementTemplate.getPeriodUnit()).getUnit();
        // 计划完成时间 -- 如果用户已完成，使用用户实际完成时间
        Timestamp endTime = DateHelper.calculateEndTime(dutUserNew.getSignTime(), agreementTemplate.getPeriods(), timeUnit);
        userAgreementRespDto.setEndTime(endTime);

        return userAgreementRespDto;
    }
}
