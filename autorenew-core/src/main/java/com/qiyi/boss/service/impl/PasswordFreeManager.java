package com.qiyi.boss.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Map;
import java.util.UUID;

import com.qiyi.boss.Constants;
import com.qiyi.boss.autorenew.dto.PasswordFreePureSignParam;
import com.qiyi.boss.autorenew.dto.PasswordFreeSetLogDesp;
import com.qiyi.boss.dto.NotifyPasswordFreeReqDto;
import com.qiyi.boss.model.UserInfo;
import com.qiyi.boss.outerinvoke.AutoRenewMarketingProxy;
import com.qiyi.boss.payment.QiyiParam;
import com.qiyi.boss.service.UserPasswordFreeLogService;
import com.qiyi.boss.service.UserPasswordFreeService;
import com.qiyi.boss.utils.AppConfig;
import com.qiyi.boss.utils.EncodeUtils;
import com.qiyi.boss.utils.PayUtils;
import com.qiyi.boss.utils.RespResultUtils;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.domain.UserPasswordFree;
import com.qiyi.vip.trade.autorenew.domain.UserPasswordFreeLog;
import com.qiyi.vip.trade.qiyue.domain.Gateway;
import com.iqiyi.v.eagle.EagleMonitor;
import com.iqiyi.v.eagle.bo.EagleParam;

@Component
@Slf4j
public class PasswordFreeManager {

    @Resource
    UserPasswordFreeLogService passwordFreeLogService;
    @Resource
    UserPasswordFreeService passwordFreeService;
    @Resource
    private AutoRenewMarketingProxy autoRenewMarketingProxy;
    @Resource
    private GatewayManager gatewayManager;

    private static final String QIYI_INTER_DUT_BIND_URL = AppConfig.getProperty("inter.pay.dut.bind.url");

    private static final String QIYI_PASSWORFREE_BIND_NOTIFY_URL = AppConfig.getProperty("pay.passwordfree.bind.notify.url");

    @Transactional(rollbackFor = Exception.class)
    public void updatePasswordFree(UserPasswordFree userPasswordFree, UserPasswordFreeLog userPasswordFreeLog) {
        passwordFreeService.saveOrUpdate(userPasswordFree);
        passwordFreeLogService.save(userPasswordFreeLog);
    }

    public void saveuserPasswordFreeLog(UserPasswordFreeLog userPasswordFreeLog) {
        passwordFreeLogService.save(userPasswordFreeLog);
    }

    public String buildPassFreeLogExtraInformation(String orderCode, String fc) {
        PasswordFreeSetLogDesp passwordFreeSetLogDesp = PasswordFreeSetLogDesp.builder()
                .orderCode(orderCode)
                .fc(fc)
                .build();
        return JSON.toJSONString(passwordFreeSetLogDesp);

    }

    public String constructPureSignUrl(UserInfo user, String platformCode, String payCenterCode, PasswordFreePureSignParam passwordFreePureSignParam) throws Exception {
        String returnUrl = passwordFreePureSignParam.getReturnUrl();
        String uuid = UUID.randomUUID().toString();
        Map<String, String> reqMap = Maps.newHashMap();
        reqMap.put("request_serial", uuid);
        reqMap.put("uid", user.getId().toString());
        reqMap.put("partner", Constants.DEFAULT_PARTNER);
        reqMap.put("platform_code", platformCode);
        reqMap.put("notify_url", QIYI_PASSWORFREE_BIND_NOTIFY_URL);
        reqMap.put("version", QiyiParam.VERSION);
        reqMap.put("charset", "UTF-8");
        reqMap.put("pay_type", payCenterCode);
        String extendParamsValue = "subParam_channel=ALIPAYAPP&subParam_schemaOfAddress=alipays";
        reqMap.put("extend_params", extendParamsValue);

        StringBuilder url = new StringBuilder(QIYI_INTER_DUT_BIND_URL);
        url.append("?request_serial=").append(uuid)
            .append("&uid=").append(reqMap.get("uid"))
            .append("&partner=").append(reqMap.get("partner"))
            .append("&platform_code=").append(platformCode)
            .append("&notify_url=").append(QIYI_PASSWORFREE_BIND_NOTIFY_URL)
            .append("&version=").append(QiyiParam.VERSION)
            .append("&charset=UTF-8")
            .append("&pay_type=").append(payCenterCode)
            .append("&extend_params=").append(EncodeUtils.urlEncode(extendParamsValue));

        Map<String, String> extraCommonParamsMap = buildExtraCommonParamsMap(passwordFreePureSignParam);
        if (MapUtils.isNotEmpty(extraCommonParamsMap)) {
            String extraCommonParamsStr = Joiner.on("&").withKeyValueSeparator("=").join(extraCommonParamsMap);
            reqMap.put("extra_common_params", extraCommonParamsStr);
            url.append("&extra_common_params=").append(EncodeUtils.urlEncode(extraCommonParamsStr));
        }
        if (StringUtils.isNotBlank(returnUrl)) {
            reqMap.put("return_url", EncodeUtils.urlDecode(returnUrl));
            url.append("&return_url=").append(returnUrl);
        }
        url.append("&sign=").append(PayUtils.signMessageRequest(reqMap, PayUtils.getPayCenterSignKey()));
        return url.toString();
    }

    private Map<String, String> buildExtraCommonParamsMap(PasswordFreePureSignParam param) {
        String fc = param.getFc();
        String fv = param.getFv();
        Map<String, String> extraCommonParamsMap = Maps.newHashMap();
        if (StringUtils.isNotBlank(fc)) {
            extraCommonParamsMap.put("fc", fc);
        }
        if (StringUtils.isNotBlank(fv)) {
            extraCommonParamsMap.put("fv", fv);
        }
        return extraCommonParamsMap;
    }

    public void processPayCenterNotify(NotifyPasswordFreeReqDto notifyPasswordFreeReqDto, HttpServletRequest servletRequest) {
        Integer status = notifyPasswordFreeReqDto.getStatus();
        Long uid = notifyPasswordFreeReqDto.getUid();
        if (!UserPasswordFree.PASSWORD_FREE_OPEN.equals(status)) {
            RespResultUtils.renderText("fail");
            return;
        }
        Map<String, String> extraCommonParams = constructExtraCommonParams(notifyPasswordFreeReqDto.getExtra_common_params());
        String fc = MapUtils.getString(extraCommonParams, "fc");
        if (CloudConfigUtil.needSendGiftWhenPasswordFreeSign()) {
            autoRenewMarketingProxy.passwordFreeActRegister(uid, fc);
        }
        passwordFreeSignRecord(fc);
    }

    private Map<String, String> constructExtraCommonParams(String extraCommonParams) {
        if (StringUtils.isBlank(extraCommonParams)) {
            return Maps.newHashMap();
        }
        try {
            extraCommonParams = URLDecoder.decode(extraCommonParams, "UTF-8");
            return Splitter.on("&").withKeyValueSeparator("=").split(extraCommonParams);
        } catch (UnsupportedEncodingException e) {
            log.info("解析免密纯签约支付中心回调extraCommonParams异常,extraCommonParams:{}", extraCommonParams);
            return Maps.newHashMap();
        }
    }

    /**
     * 记录打点
     */
    private void passwordFreeSignRecord(String fc) {
        try {
            Gateway gateway = gatewayManager.getGatewayByCode(fc);
            String fcDesc = gateway != null ? gateway.getName() : fc;
            EagleParam passwordFreeSignCountEagleParam = new EagleParam("password_free_sign_count_total")
                .tag("fc", fc)
                .tag("fcDesc", fcDesc);
            EagleMonitor.counterInc(passwordFreeSignCountEagleParam);
        } catch (Exception e) {
            log.error("passwordFreeSignRecord EagleMonitor counterInc error", e);
        }
    }

}
