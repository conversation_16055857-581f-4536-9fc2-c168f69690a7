package com.qiyi.boss.service.impl;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.List;

import com.qiyi.vip.trade.qiyue.domain.VipType;
import com.qiyi.vip.trade.qiyue.mapper.VipTypeMapper;

/**
 * Created by IntelliJ IDEA.
 * <AUTHOR>
 * Date: 2017/9/20.
 * Time: 下午3:50
 * To change this template use File | Settings | File Templates.
 */
@Component
public class VipTypeManager {

    @Resource
    private VipTypeMapper vipTypeMapper;

    /**
     * 根据产品的code取会员类型对象
     *
     * @param code 会员类型code
     * @return 会员类型
     */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "VipType_getVipTypeByCode", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public VipType getVipTypeByCode(String code) {
        return this.vipTypeMapper.getVipTypeByCode(code);
    }

    /**
     * 根据id取会员类型对象
     *
     * @param id 会员类型id
     * @return 会员类型
     */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "VipType_getVipTypeById", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public VipType getVipTypeById(Long id) {
        return this.vipTypeMapper.getVipTypeById(id);
    }

    public List<VipType> getAll() {
        return vipTypeMapper.getAll();
    }
}

