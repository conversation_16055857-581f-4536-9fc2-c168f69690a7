package com.qiyi.boss.service.impl;

import com.qiyi.boss.Constants;
import com.qiyi.vip.trade.autorenew.domain.ManagementConfig;
import com.qiyi.vip.trade.autorenew.mapper.ManagementConfigMapper;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> liuwanqiang
 * Date: 2019-1-24
 * Time: 23:02
 */
@Service
public class ManagementConfigService {

	@Resource
	private ManagementConfigMapper managementConfigMapper;

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "ManagementConfig_searchBy", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public ManagementConfig searchBy(Long vipType, Integer agreementType, Integer payChannel, Integer amount) {
		ManagementConfig query = ManagementConfig.builder()
                .vipType(vipType)
                .agreementType(agreementType)
                .payChannel(payChannel)
                .amount(amount)
                .status(Constants.STATUS_VALID)
                .build();
		List<ManagementConfig> managementConfigList = managementConfigMapper.listSelective(query);
		if (CollectionUtils.isEmpty(managementConfigList)) {
			return null;
		}
		return managementConfigList.get(0);
	}
}
