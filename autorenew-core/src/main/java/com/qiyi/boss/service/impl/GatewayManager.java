package com.qiyi.boss.service.impl;

import com.qiyi.vip.trade.qiyue.domain.Gateway;
import com.qiyi.vip.trade.qiyue.mapper.GatewayMapper;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Repository
public class GatewayManager implements GatewayMapper {

    @Resource
    private GatewayMapper gatewayMapper;

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "Gateway_getGatewayByCode", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public Gateway getGatewayByCode(String code) {
        return gatewayMapper.getGatewayByCode(code);
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "Gateway_getGatewayById", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public Gateway getGatewayById(Long id) {
        return gatewayMapper.getGatewayById(id);
    }
}
