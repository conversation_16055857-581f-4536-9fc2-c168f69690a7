package com.qiyi.boss.service.impl;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.commons.lang.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import com.qiyi.vip.trade.autorenew.domain.Platform;
import com.qiyi.vip.trade.autorenew.mapper.BossPlatformMapper;
import com.qiyi.vip.trade.qiyue.domain.QiYuePlatform;
import com.qiyi.vip.trade.qiyue.mapper.QiYuePlatformMapper;

@Component
public class QiYuePlatformManager {
    @Resource
    private QiYuePlatformMapper qiYuePlatformMapper;
    @Resource
    private BossPlatformMapper bossPlatformMapper;

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "QiYuePlatform_getPlatformById", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public QiYuePlatform getPlatformById(Long id) {
        if (id == null) {
            return null;
        }
        return qiYuePlatformMapper.getPlatformById(id);
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "QiYuePlatform_getPlatformByCode", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public QiYuePlatform getPlatformByCode(String code) {
        return qiYuePlatformMapper.getPlatformByCode(code);
    }

    public String getPlatformCodeById(Long id) {
        QiYuePlatformManager thisObj = (QiYuePlatformManager) AopContext.currentProxy();
        QiYuePlatform platform = thisObj.getPlatformById(id);
        return platform != null ? platform.getCode() : null;
    }

    public long getPlatformIdByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return 1L;
        }
        QiYuePlatformManager thisObj = (QiYuePlatformManager) AopContext.currentProxy();
        QiYuePlatform platform = thisObj.getPlatformByCode(code);
        return platform != null ? platform.getId() : 1L;
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "BossPlatform_getPlatformById", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public Platform getBossPlatformById(Long id) {
        if (id == null) {
            return null;
        }
        return bossPlatformMapper.selectByPrimaryKey(id.intValue());
    }

    public String getPlatformCodeByBossPlatformId(Long id) {
        QiYuePlatformManager thisObj = (QiYuePlatformManager) AopContext.currentProxy();
        Platform platform = thisObj.getBossPlatformById(id);
        return platform != null ? platform.getCode() : null;
    }

    public String getPlatformName(String qiyuePlatformCode, Long bossPlatformId) {
        QiYuePlatformManager thisObj = (QiYuePlatformManager) AopContext.currentProxy();
        String platformName = null;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(qiyuePlatformCode)) {
            QiYuePlatform qiYuePlatform = thisObj.getPlatformByCode(qiyuePlatformCode);
            platformName = qiYuePlatform != null ? qiYuePlatform.getName() : null;
        } else {
            Platform platform = thisObj.getBossPlatformById(bossPlatformId);
            platformName = platform != null ? platform.getName() : null;
        }
        return platformName;
    }

}
