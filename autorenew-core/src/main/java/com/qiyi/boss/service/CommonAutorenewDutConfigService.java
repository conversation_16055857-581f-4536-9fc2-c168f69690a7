package com.qiyi.boss.service;

import com.qiyi.vip.commons.enums.PayChannelEnum;
import com.qiyi.vip.trade.autorenew.domain.CommonAutoRenewDutConfig;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 * <AUTHOR> <PERSON><PERSON><PERSON>qi<PERSON>
 * Date: 2018-04-18
 * Time: 17:17
 */
public interface CommonAutorenewDutConfigService {

	/**
	 * 查询有效代扣配置信息.
	 */
	List<CommonAutoRenewDutConfig> findAll(Integer category, Long vipType, Integer payChannel, Integer status);

	/**
	 * 根据id查询代扣配置信息.
	 */
	CommonAutoRenewDutConfig findById(Long id);

	/**
	 * 查询下一个非 智付通 的代扣配置信息
	 */
	CommonAutoRenewDutConfig findFirstExcludeFilteredConfigById(Long nextId, PayChannelEnum payChannelEnum);
}
