package com.qiyi.boss.service;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import com.qiyi.boss.component.SmartJedisClient;
import com.qiyi.boss.constants.CacheConstants;
import com.qiyi.boss.dto.DutDiscountTemplate;
import com.qiyi.boss.dto.UserDutDiscountInfo;
import com.qiyi.boss.model.DutDiscountOptParam;
import com.qiyi.boss.outerinvoke.AutoRenewMarketingProxy;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;

/**
 * Created at: 2020-12-02
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserDutDiscountService {

    private static final int RETRY_TIMES = 3;

    @Resource
    SmartJedisClient smartJedisClient;
    @Resource
    AutoRenewMarketingProxy autoRenewMarketingProxy;

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "DutDiscountTemplate_getDutDiscountTemplate", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public DutDiscountTemplate getDutDiscountTemplate(String batchNo) {
        DutDiscountTemplate dutDiscountTemplate = null;
        try {
            dutDiscountTemplate = smartJedisClient.get(CacheConstants.buildDutDiscountTemplateCacheKey(batchNo), new TypeReference<DutDiscountTemplate>(){}, true);
        } catch (Exception e) {
            log.error("getDutDiscountTemplate from cache failed. batchNo: {}", batchNo, e);
            boolean retrySuccess = false;
            for (int i = 0; i < RETRY_TIMES; i++) {
                try {
                    dutDiscountTemplate = smartJedisClient.get(CacheConstants.buildDutDiscountTemplateCacheKey(batchNo), new TypeReference<DutDiscountTemplate>(){}, true);
                    retrySuccess = true;
                    break;
                } catch (Exception retryException) {
                    log.error("Retry getDutDiscountTemplate from cache failed. batchNo: {}", batchNo, retryException);
                }
            }
            if (!retrySuccess && CloudConfigUtil.getDutDiscountByHttpWhenRedisFailed()) {
                try {
                    dutDiscountTemplate = autoRenewMarketingProxy.getTemplateInfoByBatchNo(batchNo);
                } catch (Exception apiException) {
                    log.error("getDutDiscountTemplate failed by api. batchNo: {}", batchNo, apiException);
                }
            }
        }
        return dutDiscountTemplate;
    }



    /**
     * 匹配立减优惠
     */
    private List<UserDutDiscountInfo> canConsumeDutDiscounts(DutDiscountTemplate dutDiscountTemplate, DutDiscountOptParam discountOptParam, List<UserDutDiscountInfo> userDutDiscounts) {
        if (dutDiscountTemplate == null) {
            log.info("dutDiscountTemplate == null, param:{}", discountOptParam);
            return null;
        }
        Integer value = dutDiscountTemplate.getValue();
        Integer templateId = dutDiscountTemplate.getId();
        if (value == null) {
            log.info("dutDiscountTemplate.getValue() == null, templateId:{}", templateId);
            return null;
        }
        Integer vipType = discountOptParam.getVipType();
        if (!dutDiscountTemplate.getVipType().equals(vipType)) {
            log.info("!dutDiscountTemplate.getVipType().equals(vipType), templateId:{}, param:{}", templateId, discountOptParam);
            return null;
        }
        if (discountOptParam.getRenewPrice() < dutDiscountTemplate.getMinimumLimit()) {
            log.info("renewPrice not match minimumLimit(), templateId:{}, param:{}", templateId, discountOptParam);
            return null;
        }
        Integer minAmount = dutDiscountTemplate.getMinAmount();
        if (minAmount != null && discountOptParam.getAmount() < minAmount) {
            log.info("amount not match minAmount, templateId:{}, param:{}", templateId, discountOptParam);
            return null;
        }
        Integer maxAmount = dutDiscountTemplate.getMaxAmount();
        if (maxAmount != null && discountOptParam.getAmount() > maxAmount) {
            log.info("amount not match maxAmount, templateId:{}, param:{}", templateId, discountOptParam);
            return null;
        }
        List<UserDutDiscountInfo> collect = userDutDiscounts.stream().filter(UserDutDiscountInfo::canUse).collect(Collectors.toList());
        List<Long> disCountInfoIds = collect.stream().map(UserDutDiscountInfo::getId).collect(Collectors.toList());
        log.info("canUse disCountInfo Ids:{}, param:{}", disCountInfoIds, discountOptParam);
        return collect;
    }

}
