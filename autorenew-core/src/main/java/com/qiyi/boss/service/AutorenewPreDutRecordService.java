package com.qiyi.boss.service;

import com.qiyi.vip.trade.autorenew.domain.AutorenewPreDutRecord;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2020-6-28
 * Time: 22:09
 */
public interface AutorenewPreDutRecordService {

	void save(AutorenewPreDutRecord autorenewPreDutRecord);

	List<AutorenewPreDutRecord> list(AutorenewPreDutRecord autorenewPreDutRecord);

	AutorenewPreDutRecord getAutorenewPreDutRecord(Long userId, Long vipType, Integer dutType, Integer amount);

}
