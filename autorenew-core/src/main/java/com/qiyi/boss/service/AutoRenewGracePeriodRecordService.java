package com.qiyi.boss.service;

import com.qiyi.vip.trade.autorenew.domain.AutoRenewGracePeriodRecord;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * Date: 2018-10-31
 * Time: 0:56
 */
public interface AutoRenewGracePeriodRecordService {

	/**
	 * 查询宽限期记录
	 * @param userId 用户id
	 * @param appId 苹果商品id
	 * @param amount 赠送时长
	 * @param interval 间隔天数
	 * @return listDutTypes
	 */
	List<AutoRenewGracePeriodRecord> find(Long userId, String appId, Integer amount, Integer interval);
}
