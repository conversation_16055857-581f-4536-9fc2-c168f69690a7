package com.qiyi.boss.service;

import java.util.List;

import com.qiyi.boss.dto.AgreementCancelReqParam;
import com.qiyi.boss.dto.AgreementListQueryParam;
import com.qiyi.boss.dto.AgreementOptDto;
import com.qiyi.boss.dto.AgreementQueryParam;
import com.qiyi.boss.dto.AgreementSignReqParam;
import com.qiyi.boss.dto.AgreementSignResp;
import com.qiyi.boss.dto.OpenAutoRenewOptDto;
import com.qiyi.boss.dto.UserAgreementRespDto;
import com.qiyi.boss.dto.UserAgreementSimpleInfoReqParam;
import com.qiyi.boss.model.UserAgreementContext;
import com.qiyi.boss.model.UserInfo;
import com.qiyi.boss.model.VipUser;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.SimplifiedUserAgreementInfo;

/**
 * Created at: 2021-06-22
 *
 * <AUTHOR>
 */
public interface AgreementOperateService {

    /**
     * 生成纯签url
     * @param userInfo
     * @param param
     */
    AgreementSignResp pureSign(UserInfo userInfo, AgreementSignReqParam param);

    /**
     * 通过纯签约签署协议
     * @param vipUser
     * @param agreementOptDto
     */
    DutUserNew openByPureSign(VipUser vipUser, AgreementOptDto agreementOptDto);

    DutUserNew openBySignPay(AutorenewRequest autoRenewRequest, AgreementNoInfo agreementNoInfo);

    DutUserNew openAutoRenew(VipUser vipUser, AgreementNoInfo agreementNoInfo, OpenAutoRenewOptDto openAutoRenewOptDto);

    /**
     * 关闭协议
     * @param param
     */
    boolean cancel(AgreementCancelReqParam param);

    /**
     * 批量取消协议
     */
    void batchCancelDutUserNews(Integer agreementNo, AgreementOptDto agreementOptDto, List<DutUserNew> dutUserNews);

    /**
     * 关闭指定dutType协议
     * @param userAgreementContext
     * @param agreementOptDto
     */
    boolean cancelSpecifiedDutType(UserAgreementContext userAgreementContext, AgreementOptDto agreementOptDto);

    /**
     * 查询协议信息
     * @param param
     */
    UserAgreementRespDto querySignInfo(AgreementQueryParam param);

    /**
     * 查询用户签约信息列表
     */
    SimplifiedUserAgreementInfo getSimpleInfo(UserAgreementSimpleInfoReqParam param);

    /**
     * 查询用户签约信息列表
     */
    List<SimplifiedUserAgreementInfo> getAgreementList(AgreementListQueryParam param);

}
