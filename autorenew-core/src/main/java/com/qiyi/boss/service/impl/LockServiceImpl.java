package com.qiyi.boss.service.impl;

import com.qiyi.boss.db.DataSource;
import com.qiyi.boss.db.DataSourceEnum;
import com.qiyi.vip.trade.autorenew.domain.Lock;
import com.qiyi.boss.service.LockService;
import com.qiyi.vip.trade.autorenew.mapper.BossLockMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Objects;


/**
 * 数据库实现的集群环境下并封锁管理
 * <AUTHOR> lishunlong
 * Date: 11-08-10
 * Time: 下午3:48
 * To change this template use File | Settings | File Templates.
 * 并发锁管理
 */
@Component(value = "lockService")
public class LockServiceImpl implements LockService {

    private static Logger logger = LoggerFactory.getLogger(LockServiceImpl.class);

    private static final int ONE_MINUTE = 1000;

    @Resource
    private BossLockMapper lockMapper;

    /**
     * 锁定:
     * 锁存在且未过期，则休眠10秒循环
     * 锁存在且已过期，则删除过期锁，创建新锁，返回true
     * 锁不存在, 创建锁, 返回true
     *
     * @param lockName 锁
     * @return 锁定成功true  锁定失败false
     */
    //@DataSource(DataSourceEnum.MASTER)
    @Override
    public boolean lock(String lockName) {
        return this.lock(lockName, LOCK_TIMEOUT_SECOND);
    }

    @Override
    //@DataSource(DataSourceEnum.MASTER)
    public boolean lock(String lockName, long timeout) {
        Objects.requireNonNull(lockName, "锁名称不能为空");
        logger.debug("lock start lockName={}", lockName);
        Lock lock = lockMapper.getByName(lockName);
        boolean locked = false;

        while (!locked) {
            if (null == lock) {
                //锁不存在，则创建锁
                lock = new Lock();
                lock.setName(lockName);
                lock.setCreateTime(new Timestamp(System.currentTimeMillis()));
                lock.setTimeout(timeout);
                try {
                    lockMapper.insertSelective(lock);
                    locked = true;
                    logger.debug("lock create lockName={}", lockName);
                    //锁定成功，跳出循环
                    break;
                } catch (Exception e) {
                    locked = false;
                    logger.error("lock create error lockName={}", lockName, e);
                }
            } else if (isTimeout(lock)) {
                //锁存在，但已超时
                try {
                    lockMapper.deleteByPrimaryKey(lock.getId());
                    locked = false;
                    logger.debug("lock delete lockName={}", lockName);
                } catch (Exception e) {
                    logger.error("lock delete error lockName={}", lockName, e);
                    //有几率出现死循环
                    locked = true;
                }
            }
            try {
                logger.debug("lock sleep {}'s", LOCK_THREAD_SLEEP_SECOND);
                Thread.sleep(0);
            } catch (InterruptedException e) {
                logger.error("lock sleep error", e);
            }
            lock = lockMapper.getByName(lockName);
        }

        logger.debug("lock end lockName={} locked={}", lockName, locked);
        return locked;
    }

    /**
     * 解锁
     * 锁存在，删除锁，返回true
     * 锁不存在，返回true
     * 异常返回false
     *
     * @param lockName 锁
     * @return 解锁成功true  解锁失败false
     */
    @Override
    public boolean unlock(String lockName) {
        logger.debug("unlock start lockName={}", lockName);
        boolean unlocked = true;
        try {
            //TODO 有问题， 可能删除别人加的锁
            Lock lock = lockMapper.getByName(lockName);
            if (lock != null) {
                lockMapper.deleteByPrimaryKey(lock.getId());
                logger.debug("unlock delete lockName={}", lockName);
            }
        } catch (Exception e) {
            logger.error("unlock error lockName={}", lockName, e);
            unlocked = false;
        }
        logger.debug("unlock end lockName={} unlocked={}", lockName, unlocked);
        return unlocked;
    }

    /**
     * 检查锁是否超时, 如果锁创建时间超过1分钟，则超时
     */
    private boolean isTimeout(Lock lock) {
        boolean result = false;
        long seconds = (System.currentTimeMillis() - lock.getCreateTime().getTime());

        long timeout = LOCK_TIMEOUT_SECOND;
        if (lock.getTimeout() != null && lock.getTimeout() > 0) {
            timeout = lock.getTimeout();
        }

        if (seconds > timeout * ONE_MINUTE) {
            result = true;
        }

        logger.debug("lock isTimeout lockName={} seconds={} result={}", lock.getName(), seconds, result);
        return result;
    }
}
