package com.qiyi.boss.service.impl;

import com.qiyi.vip.trade.qiyue.domain.Business;
import com.qiyi.vip.trade.qiyue.mapper.BusinessMapper;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Repository
public class BusinessManager implements BusinessMapper {

    @Resource
    private BusinessMapper businessMapper;

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "Business_getBusinessByCode", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public Business getBusinessByCode(String code) {
        return businessMapper.getBusinessByCode(code);
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "Business_getBusinessById", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public Business getBusinessById(Long id) {
        return businessMapper.getBusinessById(id);
    }
}
