package com.qiyi.boss.service;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.qiyi.boss.dto.NeedUnbindInfo;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.service.impl.AutoRenewVipTypeUpgradeConfigManager;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import com.qiyi.vip.trade.autorenew.repository.DutUserRepository;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;

import static com.qiyi.boss.constants.AgreementConstants.EXCLUDE_AGREEMENT_TYPES;

/**
 * @author: guojing
 * @date: 2024/4/25 17:11
 */
@Component
public class PayCenterAccountUnbindManager {

    @ConfigJsonValue("${apple.aca.payType:[490,491,492]}")
    private List<Integer> appleAcaPayType;

    @Resource
    private AutoRenewVipTypeUpgradeConfigManager autoRenewVipTypeUpgradeConfigManager;
    @Resource
    private AutoRenewDutTypeManager autoRenewDutTypeManager;
    @Resource
    private DutUserRepository dutUserRepository;

    /**
     * 购买触发解绑时，查询需要解绑的dutType列表
     * @param userId
     * @param vipType
     * @param payChannel
     */
    public List<Integer> needUnbindDutTypesWhenBuy(Long userId, Long vipType, Integer payChannel, Integer agreementType) {
        List<Long> vipTypesInGroup = autoRenewVipTypeUpgradeConfigManager.getSameGroupVipTypes(vipType, agreementType);
        List<Integer> payChannelDutTypes = autoRenewDutTypeManager.listByPayChannelAndAgreementType(payChannel, agreementType).stream()
            .map(AutoRenewDutType::getDutType).collect(Collectors.toList());
        List<DutUserNew> dutUserNews = dutUserRepository.listDutUserNew(agreementType, userId, vipTypesInGroup, null)
            .stream()
            .filter(d -> !EXCLUDE_AGREEMENT_TYPES.contains(d.getAgreementType()))
            .collect(Collectors.toList());
        return findNeedUnbindDutTypes(dutUserNews, payChannelDutTypes);
    }

    /**
     * 用户需要解绑的dutType列表
     * @param userId
     * @param vipType 指定会员类型所属的体系
     * @param payChannels 需要解绑的支付渠道
     */
    public List<NeedUnbindInfo> userNeedUnbindDutTypes(Long userId, Long vipType, List<Integer> payChannels, Integer openingDutType, Integer agreementType) {
        if (CollectionUtils.isEmpty(payChannels)) {
            return Collections.emptyList();
        }
        List<Long> vipTypesInGroup = autoRenewVipTypeUpgradeConfigManager.getSameGroupVipTypes(vipType, agreementType);
        List<DutUserNew> dutUserNews = dutUserRepository.listDutUserNew(agreementType, userId, vipTypesInGroup, null);
        if (openingDutType != null) {
            dutUserNews = dutUserNews.stream()
                .filter(dutUserNew -> !Objects.equals(dutUserNew.getType(), openingDutType))
                .collect(Collectors.toList());
        }
        Map<Integer, Long> dutTypeToVipTypeMap = dutUserNews.stream()
            .collect(Collectors.toMap(DutUserNew::getType, DutUserNew::getVipType, (existing, replacement) -> existing));

        List<NeedUnbindInfo> needUnbindInfoList = new ArrayList<>();
        for (Integer payChannel : payChannels) {
            List<Integer> payChannelDutTypes = autoRenewDutTypeManager.listByPayChannelAndAgreementType(payChannel, agreementType).stream()
                .map(AutoRenewDutType::getDutType).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(payChannelDutTypes)) {
                continue;
            }
            List<Integer> needUnbindDutTypes = findNeedUnbindDutTypes(dutUserNews, payChannelDutTypes);
            for (Integer needUnbindDutType : needUnbindDutTypes) {
                needUnbindInfoList.add(new NeedUnbindInfo(payChannel, needUnbindDutType, dutTypeToVipTypeMap.get(needUnbindDutType)));
            }
        }

        return needUnbindInfoList;
    }

    /**
     * 查询苹果需要发起解约的dutType，具体解约哪些dutType由支付决定
     * @param userId
     * @param agreementType
     * @param vipType
     * @param openingDutType
     */
    public List<Integer> needUnbindAppleDutType(Long userId, Integer agreementType, Long vipType, Integer openingDutType, Integer payType) {
        if (openingDutType == null) {
            return Collections.emptyList();
        }
        List<Long> vipTypesInGroup = autoRenewVipTypeUpgradeConfigManager.getSameGroupVipTypes(vipType, agreementType);
        List<DutUserNew> dutUserNews = dutUserRepository.listDutUserNew(agreementType, userId, vipTypesInGroup, DutUserNew.RENEW_AUTO);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return Collections.emptyList();
        }
        Map<Integer, AutoRenewDutType> autoRenewDutTypeMap = autoRenewDutTypeManager.listByPayChannelAndAgreementType(PaymentDutType.PAY_CHANNEL_IAP, agreementType).stream()
            .filter(autoRenewDutType -> vipTypesInGroup.contains(autoRenewDutType.getVipType()))
            .collect(Collectors.toMap(AutoRenewDutType::getDutType, item -> item));
        if (MapUtils.isEmpty(autoRenewDutTypeMap)) {
            return Collections.emptyList();
        }

        boolean appleAcaSignPay = appleAcaPayType.contains(payType);
        Set<Integer> needUnbindDutTypeSet = new HashSet<>();
        for (DutUserNew dutUserNew : dutUserNews) {
            Integer dutType = dutUserNew.getType();
            if (!autoRenewDutTypeMap.containsKey(dutType)) {
                continue;
            }
            AutoRenewDutType autoRenewDutType = autoRenewDutTypeMap.get(dutType);
            if (Objects.equals(openingDutType, dutType) || !autoRenewDutType.isAppleDutType()) {
                continue;
            }
            boolean appleAcaMode = appleAcaPayType.contains(autoRenewDutType.getDutPayType());
            //购买非ACA时，请求支付只传递ACA的dutType
            if (!appleAcaSignPay && !appleAcaMode) {
                continue;
            }
            needUnbindDutTypeSet.add(dutType);
        }

        return new ArrayList<>(needUnbindDutTypeSet);
    }


    /**
     * 找出需要解绑的代扣方式
     * @param dutUserNews
     * @param payChannelDutTypes
     */
    private static List<Integer> findNeedUnbindDutTypes(List<DutUserNew> dutUserNews, List<Integer> payChannelDutTypes) {
        Set<Integer> openedDutTypes = new HashSet<>();
        Set<Integer> closedDutTypes = new HashSet<>();
        for (DutUserNew dutUserNew : dutUserNews) {
            Integer dutType = dutUserNew.getType();
            if (!payChannelDutTypes.contains(dutType) || dutUserNew.unbind()) {
                continue;
            }
            if (dutUserNew.isAutoRenewUser()) {
                openedDutTypes.add(dutType);
            } else {
                closedDutTypes.add(dutType);
            }
        }
        closedDutTypes.removeAll(openedDutTypes);
        return new ArrayList<>(closedDutTypes);
    }

    public static String unbindRedisKeyWhenSignPay(Long userId, Integer payChannel) {
        return String.format("unbind_user_flag_when_sign_pay:%s_%s", userId, payChannel);
    }


}
