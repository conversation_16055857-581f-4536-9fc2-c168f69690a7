package com.qiyi.boss.service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

import com.qiyi.boss.autorenew.dto.AddAutoRenewDto;
import com.qiyi.boss.autorenew.dto.SaveDutUserDto;
import com.qiyi.boss.dto.CancelAutoRenewDto;
import com.qiyi.boss.dto.RtdnCancelDetailDto;
import com.qiyi.boss.dto.UpdateDutTimeReqDto;
import com.qiyi.boss.model.SpecialVipUser;
import com.qiyi.boss.model.VipUser;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewUpgradeConfig;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.SimpleDutUserNew;
import com.qiyi.vip.trade.qiyue.domain.QiYuePlatform;

/**
 * 代扣相关业务逻辑
 *
 * <AUTHOR>
 * @version 11-11-22 - 下午2:57
 */
public interface DutService {
    /**
     * 保存代扣账号
     * @param dutUserNew dutUserNew
     */
    void save(DutUserNew dutUserNew);

    List<DutUserNew> getDutUserAutoRenew(Long userId, Integer agreementType, List<Integer> typeList);

    List<DutUserNew> getShardAutoRenewUsers(Long userId, Integer agreementType, Long vipType, List<Integer> typeList);

    List<DutUserNew> getShardExcludeTypesAutoRenewUsers(Long userId, Integer agreementType, Long vipType, List<Integer> excludeTypeList);

    List<DutUserNew> getShardExcludeTypesUpgradeAutoRenewUsers(Long userId, Integer agreementType, Long sourceVipType, Long targetVipType, List<Integer> excludeTypeList);

    /**
     * 支付宝代扣信息保佑
     * @return true,成功，false，失败
     */
    boolean save(SaveDutUserDto saveDutUserDto) throws Exception;

    DutUserNew addAutoRenewNew(AddAutoRenewDto addAutoRenewDto);

    /**
     * 添加奇异果或台湾用户自动续费，保存平台
     * @param uid
     * @param specialVipUser
     * @param type
     * @param platform
     */
    void addShardAutoRenewPlatform(Long uid, Integer type, QiYuePlatform platform, SpecialVipUser specialVipUser, String fc, String fv, String operateScene);
    /**
     * 添加用户多渠道自动续费
     *
     * @param uid
     * @param types
     */
    List<Integer> addAutoRenews(Long uid, Integer agreementType,Integer[] types, VipUser vipUser, String fc, String fv, String operateScene);

    /**
     * 取消自动续费
     * @param cancelAutoRenewDto {@link CancelAutoRenewDto}
     */
    void cancelAutoRenew(CancelAutoRenewDto cancelAutoRenewDto);

    List<DutUserNew> findByUidAndVipTypeAndAutoRenew(Long userId, Integer agreementType, Long vipType, Integer autoRenewStatus);

    List<DutUserNew> findAllByUserIdAndVipType(Long userId, Integer agreementType, Long vipType);

    List<DutUserNew> findIgnoreAutoRenewStatus(Long userId, Integer agreementType, Long sourceVipType, Long targetVipType);

    void resetUpgradeInfo(DutUserNew dutUserNew, AutoRenewUpgradeConfig autoRenewUpgradeConfig);

    /**
     * 判断指定类型用户最近2天（前2天~当天）是否完成自动续费
     *
     * @param userId 用户ID
     * @param dutTypeList 代扣方式
     * @return true：已自动续费，false:未自动续费
     */
    boolean isAutoRenewRecentDay(Long userId, Integer agreementType, List<Integer> dutTypeList);

    /**
     * 获取指定会员类型用户当天的自动续费日志
     * @param userId 用户ID
     * @param vipType 会员类型
     * @return listPageSelective
     */
    List<DutRenewLog> findTodayDutRenewLog(Long userId, Integer agreementType, Long vipType);

    /**
     * 查询最近4天最后一次代扣的代扣日志.
     *
     * @param userId  用户ID
     * @param dutType 代扣类型
     * @param vipType 会员类型
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return DutRenewLog
     */
    DutRenewLog getLastDutRenewLog(Long userId, Integer dutType, Long vipType, Timestamp beginTime, Timestamp endTime);

    DutRenewLog getLastDutRenewLog(Long userId, Integer dutType, Long vipType);

    DutRenewLog getLastRecentlyNumDaysDutRenewLog(Long userId, int dutType, Long vipType, int num);
    /**
     * 获取用户当前优先执行的自动续费渠道类型
     *
     * @param uid 用户id
     * @param bindTypes 用户绑定的类型
     * @param dutUserNews 用户自动续费列表
     * @return 自动续费类型，1-支付宝，3-百度钱包，4-微信，-1-当前没有开通自动续费
     */
    int getCurrentDutRenewType(Long uid, List<Integer> bindTypes, Long vipType, List<DutUserNew> dutUserNews, Integer agreementType);

    DutRenewLog getDutRenewLogByOrderCode(Long userId, String orderCode);

    /**
     * 按时间查询自动续费日志
     * @param userId
     * @return
     */
    List<DutRenewLog> shardGetDutRenewLog(Long userId, List<Integer> tvDutTypeList);

    /**
     * 获取同步类型的TV奇异果自动续费用户
     *
     * @param startTime
     * @param deadline
     * @return
     */
    List<DutUserNew> getAutoRenewList(Integer agreementType, Timestamp startTime, Timestamp deadline, Long vipType, List<Integer> dutTypeList, Integer autoRenewStatus, String shardTblName);

    /**
     * 根据会员到期时间查询用户.
     * @param advanceHours 代扣提前时间(小时)
     * @param interval 发起代扣时间区间(单位:小时)
     * @param vipType 会员类型
     * @param dutTypeList 代扣类型
     * @param autoRenewStatus 自动续费状态
     * @return 用户列表
     */
    List<DutUserNew> getDutUsersByVipDeadline(Integer advanceHours, Integer interval, Long vipType, List<Integer> dutTypeList, Integer autoRenewStatus, String shardTblName);

    /**
     * 根据下次代扣时间查询用户.
     * @param advanceHours 代扣提前时间(小时)
     * @param interval 发起代扣时间区间(单位:小时)
     * @param vipType 会员类型
     * @param dutTypeList 代扣类型
     * @param autoRenewStatus 自动续费状态
     * @return 用户列表
     */
    List<DutUserNew> getDutUsersByNextDutTime(Integer agreementType ,Integer advanceHours, Integer interval, Long vipType, List<Integer> dutTypeList, Integer autoRenewStatus, String shardTblName);

    DutRenewSetLog shardGetDutRenewSetLog(long uid, Integer type, Integer operator);

    List<DutUserNew> shardGetDutUserNew(Long userId, Integer type);

    DutUserNew shardGetDutUserNew(Long userId, Integer type, Long vipType);

    DutUserNew getInEffectDutUserNewFromMaster(Long userId, Integer type, Long vipType);

    DutUserNew shardGetUpgradeDutUserNew(Long userId, Integer type, Long sourceVipType, Long vipType);

    DutUserNew getInEffectUpgradeDutUserNewFromMaster(Long userId, Integer type, Long sourceVipType, Long vipType);

    void shardSaveRenewLog(DutRenewLog dutRenewLog);

    boolean shardIsAutoRenewUserIncludeType(Long uid, Long vipType, List<Integer> typeList);

    boolean shardIsAutoRenewUser(Long uid, Long vipTyp, Integer agreementType);

    void updateShardDutUserNew(DutUserNew dutUserNew);

    Integer calculateRenewPrice(Integer dutType, AutorenewRequest autorenewRequest);

    Integer getRenewPrice(DutUserNew dutUserNew);

    /**
     * 根据userId、vipType、dutType和autoRenewStatus获取签约关系
     * @param userId
     * @param vipType
     * @param dutType
     * @param autoRenewStatus
     * @return
     */
    List<DutUserNew> findByUidDutTypeVipTypeAutoRenew(Long userId, Long vipType, Integer dutType, Integer autoRenewStatus);

    /**
     * 根据RTDN通知取消自动续费
     * @param dutUserNew 用户自动续费信息
     * @param rtdnCancelDetailDto RTDN取消通知详情
     */
    void cancelAutoRenewByRtdn(DutUserNew dutUserNew, RtdnCancelDetailDto rtdnCancelDetailDto, Integer payChannel);

    /**
     * 根据nextDutTime范围分表查询芝麻GO协议基本信息
     * @param vipType
     * @param startTime
     * @param endTime
     * @param shardTblName
     * @return
     */
    List<SimpleDutUserNew> getZhiMaGoUsersByNextDutTimeRange(Long vipType, Integer agreementType, Timestamp startTime, Timestamp endTime, String shardTblName);

    /**
     * 根据deadline范围查询微信支付分协议用户
     * @param vipType
     * @param startTime
     * @param endTime
     * @param shardTblName
     * @return
     */
    List<SimpleDutUserNew> getWeChatPayScoreUsersByDeadlineRange(Long vipType, Timestamp startTime, Timestamp endTime, String shardTblName);

    /**
     * 根据nextDutTime范围分表查询芝麻GO协议基本信息
     * @param vipType
     * @param startTime
     * @param endTime
     * @param shardTblName
     * @return
     */
    List<SimpleDutUserNew> getWeChatPayScoreUsersByNextDutTimeRange(Long vipType, Integer agreementType, Timestamp startTime, Timestamp endTime, String shardTblName);


    /**
     * 根据nextDutTime范围分表查询芝麻GO协议基本信息
     * @param vipType
     * @param startTime
     * @param endTime
     * @param shardTblName
     * @return
     */
    List<SimpleDutUserNew> getByAgreementTypesAndDutTimeRange(Long vipType, Timestamp startTime, Timestamp endTime, String shardTblName, Set<Integer> agreementTypeList);

    void updateNextDutTime(List<UpdateDutTimeReqDto> updateDutTimeReqDto);
}
