package com.qiyi.boss.service;

import java.util.Map;
import java.util.Optional;
import com.qiyi.boss.dto.CreateOrderDto;
import com.qiyi.boss.dto.OrderCreationRequest;
import com.qiyi.boss.dto.OrderPrepareDto;
import com.qiyi.boss.dto.PayCenterDutReq;
import com.qiyi.boss.dto.PayCenterDutResult;
import com.iqiyi.vip.order.dal.model.Order;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang Date: 2020-10-21 Time: 10:25
 */
public interface OrderService {

    Order createOrder(CreateOrderDto createOrderDto);

    OrderCreationRequest createUnpaidOrder(CreateOrderDto createOrderDto);

    PayCenterDutReq prepare(OrderPrepareDto orderPrepareDto);

    Optional<PayCenterDutResult> processPayments(PayCenterDutReq dutReq);

    PayCenterDutResult processPaymentsWithRetry(PayCenterDutReq dutReq);

    Map<String, Object> transfer(PayCenterDutResult payCenterDutResult, Order order, OrderCreationRequest orderCreationRequest, String signOrderCode);

    /**
     * 根据订单号查询订单信息
     */
    Order queryOrder(String orderCode, Long userId);

    boolean validateOrderConsistency(CreateOrderDto createOrderDto);

}
