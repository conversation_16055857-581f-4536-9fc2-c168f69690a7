package com.qiyi.boss.service.impl;

import com.qiyi.boss.service.AutoRenewLinkedDutConfigService;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewLinkedDutConfig;
import com.qiyi.vip.trade.autorenew.mapper.AutoRenewLinkedDutConfigMapper;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> liuwanqiang
 * Date: 2018-6-24
 * Time: 21:28
 */
@Component(value = "autoRenewLinkedDutConfigService")
public class AutoRenewLinkedDutConfigManager implements AutoRenewLinkedDutConfigService {

	@Resource
	private AutoRenewLinkedDutConfigMapper autoRenewLinkedDutConfigMapper;
	@Resource
	private AutoRenewDutTypeManager autoRenewDutTypeManager;

	private static final int FIRST_INDEX = 1;
	/**
	 * 查询有效代扣配置信息.
	 *
	 * @param vipType 会员类型.
	 * @param payChannel 代扣渠道类型.
	 * @param category 代扣分类.
	 * @return listDutTypes
	 */
    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewLinkedDutConfig_find", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public AutoRenewLinkedDutConfig find(Integer jobType, Long vipType, Long sourceVipType, Integer agreementType, Integer payChannel, String category) {
        List<AutoRenewLinkedDutConfig> linkedDutConfigs = autoRenewLinkedDutConfigMapper.listLinkedDutConfig(jobType, vipType, sourceVipType, agreementType, payChannel, category, AutoRenewLinkedDutConfig.STATUS_VALID);
        if (CollectionUtils.isNotEmpty(linkedDutConfigs)) {
            return linkedDutConfigs.get(0);
        }
        return null;
	}

	/**
	 * 根据id查询代扣配置信息.
	 * @param id 配置id
	 * @return autoRenewLinkedDutConfig
	 */
	@Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewLinkedDutConfig_findById", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public AutoRenewLinkedDutConfig findById(Long id) {
		return autoRenewLinkedDutConfigMapper.selectByPrimaryKey(id);
	}

}
