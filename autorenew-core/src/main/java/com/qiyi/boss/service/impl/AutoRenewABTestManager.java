package com.qiyi.boss.service.impl;

import com.qiyi.vip.trade.autorenew.domain.AutoRenewABTest;
import com.qiyi.vip.trade.autorenew.mapper.AutoRenewABTestMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2020-6-24
 * Time: 11:05
 */
@Service
public class AutoRenewABTestManager {

	@Resource
	private AutoRenewABTestMapper autoRenewABTestMapper;

	public Optional<AutoRenewABTest> query(String category, Long vipType, Integer dutType, Integer amount, Integer index) {
		AutoRenewABTest query = AutoRenewABTest.builder()
				.category(category)
				.vipType(vipType)
				.dutType(dutType)
				.amount(amount)
				.build();
		List<AutoRenewABTest> autoRenewABTestList = autoRenewABTestMapper.list(query);
		return autoRenewABTestList.stream()
				.filter(autoRenewABTest -> autoRenewABTest.getIndexBegin() <= index)
				.filter(autoRenewABTest -> autoRenewABTest.getIndexEnd() >= index)
				.findFirst();
	}

}
