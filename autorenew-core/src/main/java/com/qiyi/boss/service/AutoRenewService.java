package com.qiyi.boss.service;

import java.util.List;

import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.dto.CancelAutoRenewOptDto;
import com.qiyi.boss.dto.OpenAutoRenewOptDto;
import com.qiyi.boss.dto.UserAutoRenewInfo;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.outerinvoke.result.PayCenterAppleDutInfoResult;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * <AUTHOR>
 */
public interface AutoRenewService {

    /**
     * 查询用户自动续签约记录
     * @param findDutUserRequest query condition {@link FindDutUserRequest}
     * @return List
     */
    List<DutUserNew> findDutUsers(FindDutUserRequest findDutUserRequest);

    /**
     * 获取用户第一优先级续费信息
     * @param dutUserNews 同一会员类型的用户用户签约信息
     * @param vipType
     */
    UserAutoRenewInfo getFirstDutRenewInfo(List<DutUserNew> dutUserNews, Long vipType);

    /**
     * 获取可用于用户手动发起代扣的签约关系
     * @param dutUserNews
     * @return
     */
    DutUserNew getFirstDutUserForManualDut(List<DutUserNew> dutUserNews);

    /**
     * 取消自动续费后，无其他签约关系，重置连续续费次数和提醒开关
     * @param userId
     * @param excludeDutTypes 排除的dutType列表
     */
    void resetUserRenewStatusWhenCancel(Long userId, List<Integer> excludeDutTypes);

    /**
     * 取消用户所有的自动续费协议
     */
    void cancelAllAutoRenew(CancelAutoRenewOptDto cancelAutoRenewOptDto);

    void cancelAutoRenew(CancelAutoRenewOptDto cancelAutoRenewOptDto);

    /**
     * 取消自动续费协议
     */
    void cancelAutoRenew(DutUserNew dutUserNew, CancelAutoRenewOptDto cancelAutoRenewOptDto);

    /**
     * 开通自动续费
     */
    DutUserNew openAutoRenew(VipUser vipUser, AgreementNoInfo agreementNoInfo, OpenAutoRenewOptDto openAutoRenewOptDto);

    /**
     * 签约支付方式开通自动续费
     */
    DutUserNew openBySignPay(AutorenewRequest autorenewRequest, AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate);

    /**
     * 根据订单完成消息保存协议信息
     */
    DutUserNew openByAutoRenewRequest(AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate, AutorenewRequest autorenewRequest,
        OperateSceneEnum operateScene, PayCenterAppleDutInfoResult appleSignInfo);

    /**
     * 协议模式使用，处理重复开通的场景：
     * 1. 统一会员体系，仅保留最新开通的会员类型
     * 2. 若代扣方式可以切换，则直接切换，否则取消
     */
    void doRepeatOpening(VipUser vipUser, AgreementNoInfo agreementNoInfo, Integer openingAmount, List<DutUserNew> openedDutUsers, String fc, String fv, AutorenewRequest autorenewRequest);

    /**
     * 非协议模式使用
     */
    void doRepeatOpeningOld(VipUser vipUser, AutoRenewDutType openingAutoRenewDutType, Integer openingAmount, String fc, String fv);

    /**
     * 查询续费价格，兼容协议模式和非协议模式
     */
    Integer getDisplayRenewPrice(DutUserNew dutUserNew);

}
