package com.qiyi.boss.service.impl;

import com.google.common.collect.Lists;
import com.qiyi.boss.autorenew.dto.PasswordFreeRespDto;
import com.qiyi.boss.enums.PasswordFreeStatusEnum;
import com.qiyi.boss.enums.UserSignActionEnum;
import com.qiyi.boss.service.UserPasswordFreeLogService;
import com.qiyi.boss.service.UserPasswordFreeService;
import com.qiyi.vip.trade.autorenew.domain.UserPasswordFree;
import com.qiyi.vip.trade.autorenew.domain.vo.UserPasswordFreeVO;
import com.qiyi.vip.trade.autorenew.domain.UserPasswordFreeLog;
import com.qiyi.vip.trade.autorenew.repository.UserPasswordFreeRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户免密签约状态处理类
 *
 * @Author: <PERSON>
 * @Date: 2020/9/28
 */
@Slf4j
@Service
public class UserPasswordFreeServiceImpl implements UserPasswordFreeService {
    @Resource
    private UserPasswordFreeRepository userPasswordFreeRepository;
    @Resource
    private UserPasswordFreeLogService userPasswordFreeLogService;

    @Override
    public PasswordFreeRespDto getList(Long userId, List<Integer> dutTypeList) {
        PasswordFreeRespDto passwordFreeRespDto = PasswordFreeRespDto.builder()
                .uid(userId).passwordFreeList(Lists.newArrayList()).build();
        List<UserPasswordFree> passwordFreeList = userPasswordFreeRepository.getByUserIdAndDutTypeList(userId, dutTypeList);
        if (CollectionUtils.isEmpty(passwordFreeList)) {
            return passwordFreeRespDto;
        }
        for (UserPasswordFree userPasswordFree : passwordFreeList) {
            PasswordFreeRespDto.PasswordFree passwordFree = PasswordFreeRespDto.PasswordFree.builder()
                    .dutType(userPasswordFree.getDutType())
                    .isPasswordFree(PasswordFreeStatusEnum.isPasswordFree(userPasswordFree.getStatus()))
                    .build();
            passwordFreeRespDto.getPasswordFreeList().add(passwordFree);
        }
        return passwordFreeRespDto;
    }

    @Override
    public void saveOrUpdate(UserPasswordFree userPasswordFree) {
        UserPasswordFree userPasswordFreeRecord = userPasswordFreeRepository.getByUserIdAndDutType(userPasswordFree.getUserId(), userPasswordFree.getDutType());
        if (userPasswordFreeRecord == null) {
            userPasswordFree = UserPasswordFree.builder()
                    .userId(userPasswordFree.getUserId())
                    .dutType(userPasswordFree.getDutType())
                    .payChannel(userPasswordFree.getPayChannel())
                    .status(userPasswordFree.getStatus()).build();
            userPasswordFreeRepository.insert(userPasswordFree);
            return;
        }
        userPasswordFreeRecord.setStatus(userPasswordFree.getStatus());
        userPasswordFreeRepository.update(userPasswordFreeRecord);
    }

    @Override
    public List<UserPasswordFree> getUserPasswordFree(Long userId, Integer payChannel, Integer status) {
        return userPasswordFreeRepository.getByUserIdAndPayChannel(userId, payChannel, status);
    }

    @Override
    public PasswordFreeRespDto getOpenedList(Long userId) {
        PasswordFreeRespDto passwordFreeRespDto = PasswordFreeRespDto.builder()
                .uid(userId).passwordFreeList(Lists.newArrayList()).build();
        List<UserPasswordFree> passwordFreeList = userPasswordFreeRepository.getUserOpenedList(userId);
        if (CollectionUtils.isEmpty(passwordFreeList)) {
            return passwordFreeRespDto;
        }
        for (UserPasswordFree userPasswordFree : passwordFreeList) {
            PasswordFreeRespDto.PasswordFree passwordFree = PasswordFreeRespDto.PasswordFree.builder()
                    .dutType(userPasswordFree.getDutType())
                    .payChannel(userPasswordFree.getPayChannel())
                    .isPasswordFree(true)
                    .build();
            passwordFreeRespDto.getPasswordFreeList().add(passwordFree);
        }
        return passwordFreeRespDto;
    }

    @Override
    public void closePasswordFree(Long userId, Integer dutType, String source) {
        UserPasswordFree userPasswordFree = userPasswordFreeRepository.getByUserIdAndDutType(userId, dutType);
        if (userPasswordFree == null) {
            throw new IllegalArgumentException("找不到对应的免密支付关系");
        }
        userPasswordFree.setStatus(PasswordFreeStatusEnum.CLOSED.getValue());
        userPasswordFreeRepository.update(userPasswordFree);

        UserPasswordFreeLog userPasswordFreeLog = UserPasswordFreeLog.builder()
                .userId(userId).dutType(dutType)
                .payChannel(userPasswordFree.getPayChannel())
                .operation(UserSignActionEnum.CLOSED.getValue())
                .source(source).build();
        userPasswordFreeLogService.save(userPasswordFreeLog);
    }

}
