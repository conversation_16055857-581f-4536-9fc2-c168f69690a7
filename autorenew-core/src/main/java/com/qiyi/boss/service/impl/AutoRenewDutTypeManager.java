package com.qiyi.boss.service.impl;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.qiyi.boss.Constants;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import com.qiyi.vip.trade.autorenew.mapper.AutoRenewDutTypeMapper;

import static com.qiyi.boss.constants.AgreementConstants.EXCLUDE_AGREEMENT_TYPES;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> liuwanqiang
 * Date: 2018-08-02
 * Time: 10:59
 */
@Service
public class AutoRenewDutTypeManager {

	@Resource
	private AutoRenewDutTypeMapper autoRenewDutTypeMapper;

	/**
	 * 根据代扣方式查询
	 *
	 * @param dutType 代扣类型
	 * @return AutoRenewDutType
	 */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewDutType_getByDutType", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public AutoRenewDutType getByDutType(Integer dutType) {
		if (dutType == null) {
			return null;
		}
		return autoRenewDutTypeMapper.selectByDutType(dutType);
	}

	/**
	 * 从数据库获取会员类型对应的自动续费协议代扣方式列表.
	 * @return listDutTypes
	 */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewDutType_getDutTypeListByVipType", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public List<Integer> getDutTypeListByVipType(Long vipType, Integer agreementType) {
		if (vipType == null) {
			return Collections.emptyList();
		}
		List<AutoRenewDutType> autoRenewDutTypes = autoRenewDutTypeMapper.selectByVipType(vipType, agreementType);
		return autoRenewDutTypes.stream().map(AutoRenewDutType::getDutType).distinct().collect(Collectors.toList());
	}

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewDutType_getByVipType", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public List<AutoRenewDutType> getByVipType(Long vipType, Integer agreementType) {
        if (vipType == null) {
            return Collections.emptyList();
        }
        return autoRenewDutTypeMapper.selectByVipType(vipType, agreementType);
//        return autoRenewDutTypeMapper.selectByVipTypeAndExcludeAgreementTypes(vipType, EXCLUDE_AGREEMENT_TYPES);
    }

    public List<AutoRenewDutType> getBySourceVipTypeAndVipType(Long sourceVipType, Long vipType, Integer agreementType) {
        if (vipType == null) {
            return Collections.emptyList();
        }
        AutoRenewDutTypeManager thisObj = (AutoRenewDutTypeManager) AopContext.currentProxy();
        List<AutoRenewDutType> autoRenewDutTypes = thisObj.getByVipType(vipType, agreementType);
        return sourceVipType == null
            ? autoRenewDutTypes
            : autoRenewDutTypes.stream().filter(item -> sourceVipType.equals(item.getSourceVipType())).collect(Collectors.toList());
    }

	/**
	 * 从数据库获取会员类型对应的代扣方式列表.
	 * @return listDutTypes
	 */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewDutType_listDutTypeInfo", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public List<AutoRenewDutType> listDutTypeInfo(Long vipType, Integer agreementType) {
		if (vipType == null) {
			return Collections.emptyList();
		}
		return autoRenewDutTypeMapper.selectByVipType(vipType, agreementType);
	}

	/**
	 * 从数据库获取会员类型对应的代扣方式列表.
	 * @return listDutTypes
	 */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewDutType_listByPayChannelAndAgreementType", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public List<AutoRenewDutType> listByPayChannelAndAgreementType(Integer payChannel, Integer agreementType) {
		AutoRenewDutType query = AutoRenewDutType.builder()
				.payChannel(payChannel)
				.status(Constants.STATUS_VALID)
				.agreementType(agreementType)
				.build();
		return autoRenewDutTypeMapper.list(query);
	}

	/**
	 * 从数据库获取会员类型列表对应的自动续费协议代扣方式列表
	 * @param vipTypes
	 * @return listDutTypes
	 */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewDutType_listDutTypeInfoByVipTypes", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public List<AutoRenewDutType> listDutTypeInfoByVipTypesAndExcludeAgreementTypes(List<Long> vipTypes, Integer agreementType) {
		return autoRenewDutTypeMapper.listAutoRenewDutTypeByVipTypes(vipTypes, EXCLUDE_AGREEMENT_TYPES)
            .stream()
            .filter(
                d -> {
                if (agreementType != null) {
                    return ObjectUtils.equals(d.getAgreementType(), agreementType);
                }
                return true;
            })
            .collect(Collectors.toList());
	}

	/**
	 * 根据支付渠道获取自动续费协议代扣方式列表.
	 * @param payChannel 支付渠道
	 * @return listDutTypes
	 */
	public List<Integer> getDutTypeListByVipTypeAndPayChannel(Long vipType, Integer agreementType, Integer payChannel) {
        AutoRenewDutTypeManager thisObj = (AutoRenewDutTypeManager) AopContext.currentProxy();
        List<AutoRenewDutType> autoRenewDutTypes = thisObj.getByVipTypeAndPayChannel(vipType, agreementType, payChannel);
		return autoRenewDutTypes.stream().map(AutoRenewDutType::getDutType).distinct().collect(Collectors.toList());
	}

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewDutType_getByVipTypeAndPayChannel", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public List<AutoRenewDutType> getByVipTypeAndPayChannel(Long vipType, Integer agreementType, Integer payChannel) {
        AutoRenewDutType query = AutoRenewDutType.builder()
            .vipType(vipType)
            .payChannel(payChannel)
            .status(Constants.STATUS_VALID)
            .agreementType(agreementType)
            .build();
        return autoRenewDutTypeMapper.list(query);
    }

    public List<Integer> getDutTypeListByVipTypeAndPayChannels(Long vipType, Integer agreementType, List<Integer> payChannels) {
		if (vipType == null) {
			return Collections.emptyList();
		}
		AutoRenewDutTypeManager thisObj = (AutoRenewDutTypeManager) AopContext.currentProxy();
		return thisObj.listDutTypeInfo(vipType, null)
                .stream()
                .filter(a -> a != null && ObjectUtils.equals(a.getAgreementType(), agreementType))
				.filter(item -> payChannels.contains(item.getPayChannel()))
				.map(AutoRenewDutType::getDutType)
				.collect(Collectors.toList());
    }

	/**
	 *  根据支付渠道类型获取自动续费协议代扣方式列表.
	 * @param vipType 会员类型
	 * @param payChannelType 支付渠道类型
	 * @return listDutTypes
	 */
	public List<Integer> getDutTypeListByVipTypeAndPayChannelType(Long vipType, Integer agreementType, Integer payChannelType) {
		if (vipType == null) {
			return Collections.emptyList();
		}
		AutoRenewDutTypeManager thisObj = (AutoRenewDutTypeManager) AopContext.currentProxy();
		return thisObj.listDutTypeInfo(vipType, agreementType).stream()
				.filter(item -> Objects.equals(item.getPayChannelType(), payChannelType))
				.map(AutoRenewDutType::getDutType)
				.collect(Collectors.toList());
	}

    public Map<Integer, AutoRenewDutType> getDutTypeInfoListByVipTypeAndPayChannelType(Integer agreementType, Long vipType, Integer payChannelType) {
        if (vipType == null) {
            return Collections.emptyMap();
        }
        AutoRenewDutTypeManager thisObj = (AutoRenewDutTypeManager) AopContext.currentProxy();
        return thisObj.listDutTypeInfo(vipType, agreementType).stream()
            .filter(item -> Objects.equals(item.getPayChannelType(), payChannelType))
            .collect(Collectors.toMap(AutoRenewDutType::getDutType, item -> item));
    }

	/**
	 * 通过代扣类型获取对应会员类型.
	 * @param dutType 代扣类型
	 * @return vipType 会员类型
	 */
	public Long getVipTypeByDutType(Integer dutType) {
		if (dutType == null) {
			return null;
		}
        AutoRenewDutTypeManager thisObj = (AutoRenewDutTypeManager) AopContext.currentProxy();
		AutoRenewDutType autoRenewDutType = thisObj.getByDutType(dutType);
		return autoRenewDutType != null ? autoRenewDutType.getVipType() : null;
	}

	public List<AutoRenewDutType> getByDutTypes(List<Integer> dutTypes) {
		if (CollectionUtils.isEmpty(dutTypes)) {
			return Lists.newArrayList();
		}

		AutoRenewDutTypeManager thisObj = (AutoRenewDutTypeManager) AopContext.currentProxy();
		return dutTypes.stream().map(thisObj::getByDutType).filter(Objects::nonNull).collect(Collectors.toList());
	}

	/**
	 *
	 * @param bindTypes
	 * @param vipType
	 * @return
	 */
	public List<Integer> filterUnSupportDutTypes(List<Integer> bindTypes, Long vipType, Integer agreementType) {
        if (CollectionUtils.isEmpty(bindTypes)) {
            return bindTypes;
        }
		AutoRenewDutTypeManager thisObj = (AutoRenewDutTypeManager) AopContext.currentProxy();
        //过滤partnerId为空的
        List<Integer> dutTypeList = thisObj.getByVipType(vipType, agreementType)
            .stream()
            .filter(AutoRenewDutType::partnerIsNull)
            .map(AutoRenewDutType::getDutType)
            .collect(Collectors.toList());
        List<Integer> mobileDutTypeList = thisObj.getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_MOBILE);
        List<Integer> taiwanDutTypeList = thisObj.getDutTypeListByVipType(Constants.VIP_USER_TW, agreementType);
		List<Integer> thirdDutTypeList = getDutTypeListByVipTypeAndPayChannelType(vipType, agreementType, PaymentDutType.PAY_CHANNEL_TYPE_THIRD_DUT_PAY);

        bindTypes.removeAll(mobileDutTypeList);
        bindTypes.removeAll(taiwanDutTypeList);
        bindTypes.removeAll(thirdDutTypeList);
        bindTypes.retainAll(dutTypeList);

        return bindTypes;
	}

    /**
     * 判断当前dutType是否支持活动
     */
    public boolean dutTypeUnSupportAct(Integer dutType) {
        if (dutType == null) {
            return false;
        }
        AutoRenewDutTypeManager thisObj = (AutoRenewDutTypeManager) AopContext.currentProxy();
        AutoRenewDutType autoRenewDutType = thisObj.getByDutType(dutType);
        if (autoRenewDutType == null || autoRenewDutType.getPayChannel() == null) {
            return false;
        }
        Integer payChannel = autoRenewDutType.getPayChannel();
        return PaymentDutType.PAY_CHANNEL_MOBILE == payChannel || PaymentDutType.PAY_CHANNEL_IAP == payChannel;
    }

	/**
	 * 根据会员类型查找非话费和第三方发起扣费的自动续费协议类型代扣方式
	 * @param vipType
	 */
	public List<Integer> getExcludePassiveDutTypeByVipType(Long vipType, Integer agreementType) {
        AutoRenewDutTypeManager thisObj = (AutoRenewDutTypeManager) AopContext.currentProxy();
		List<Integer> excludeDutTypeList = Lists.newArrayList();
		List<Integer> mobileDutTypeList = thisObj.getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_MOBILE);
		List<Integer> thirdDutTypeList = getDutTypeListByVipTypeAndPayChannelType(vipType, agreementType, PaymentDutType.PAY_CHANNEL_TYPE_THIRD_DUT_PAY);
		excludeDutTypeList.addAll(mobileDutTypeList);
		excludeDutTypeList.addAll(thirdDutTypeList);
		return excludeDutTypeList;
	}

	/**
	 * 查找支持纯签约的自动续费协议代扣方式.
	 * @param vipType 会员类型
	 * @param payChannel 渠道类型
	 * @param priority
	 * @return dutType 代扣方式
	 */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewDutType_findDutTypeList", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public List<AutoRenewDutType> findDutTypeList(Long sourceVipType, Long vipType, Integer payChannel, Short priority) {
		AutoRenewDutType query = AutoRenewDutType.builder()
				.sourceVipType(sourceVipType)
				.vipType(vipType)
				.payChannel(payChannel)
				.agreementType(AgreementTypeEnum.AUTO_RENEW.getValue())
				.status(Constants.STATUS_VALID)
				.build();
		if (priority != null) {
			query.setPriority(priority);
		}
		List<AutoRenewDutType> autoRenewDutTypes = autoRenewDutTypeMapper.list(query);
		if (CollectionUtils.isEmpty(autoRenewDutTypes)) {
			return null;
		}
		return autoRenewDutTypes;
	}

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewDutType_findByVipTypeAndPayChannel", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public List<AutoRenewDutType> findByVipTypeAndPayChannel(Long sourceVipType, Long vipType, Integer agreementType, Integer payChannel) {
        return autoRenewDutTypeMapper.selectByVipTypeAndPayChannel(sourceVipType, vipType, payChannel, null, agreementType);
    }

    public List<AutoRenewDutType> findByVipTypeAndPayChannelAndPriority(Long sourceVipType, Long vipType, Integer agreementType, Integer payChannel, Short priority) {
        AutoRenewDutTypeManager thisObj = (AutoRenewDutTypeManager) AopContext.currentProxy();
        List<AutoRenewDutType> autoRenewDutTypes = thisObj.findByVipTypeAndPayChannel(sourceVipType, vipType, agreementType, payChannel);
        return autoRenewDutTypes.stream()
            .filter(autoRenewDutType -> Objects.equals(priority, autoRenewDutType.getPriority()))
            .collect(Collectors.toList());
    }

	/**
	 * 查询自动续费协议的代扣方式列表
	 * @param sourceVipType 升级源会员类型
	 * @param vipType 会员类型
	 * @param payChannel 支付渠道
	 * @param payChannelType 支付渠道类型
	 * @return list
	 */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewDutType_list", cacheType= CacheType.LOCAL, cacheNullValue=true)
	public List<AutoRenewDutType> list(Long sourceVipType, Long vipType, Integer agreementType, Integer payChannel, Integer payChannelType) {
        AutoRenewDutType query = AutoRenewDutType.builder()
            .sourceVipType(sourceVipType)
            .vipType(vipType)
            .payChannel(payChannel)
            .payChannelType(payChannelType)
            .agreementType(agreementType)
            .status(Constants.STATUS_VALID)
            .build();
        return autoRenewDutTypeMapper.list(query);
	}
}
