package com.qiyi.boss.service.impl;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.qiyi.vip.trade.autorenew.domain.AgreementCancelRestriction;
import com.qiyi.vip.trade.autorenew.mapper.AgreementCancelRestrictionMapper;

/**
 * Created at: 2022-10-14
 *
 * <AUTHOR>
 */
@Service
public class AgreementCancelRestrictionManager {

    @Resource
    AgreementCancelRestrictionMapper cancelRestrictionMapper;

    public AgreementCancelRestriction getByAgreementNo(Integer agreementNo) {
        if (agreementNo == null) {
            return null;
        }
        return cancelRestrictionMapper.selectByAgreementNo(agreementNo);
    }

}
