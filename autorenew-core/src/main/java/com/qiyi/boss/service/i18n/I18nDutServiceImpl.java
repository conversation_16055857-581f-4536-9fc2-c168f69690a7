package com.qiyi.boss.service.i18n;

import com.qiyi.boss.Constants;
import com.qiyi.boss.autorenew.dto.CancelDutSetLogDesp;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.dto.FreePayDto;
import com.qiyi.boss.dto.RtdnCancelDetailDto;
import com.qiyi.boss.enums.BindStatusEnum;
import com.qiyi.boss.processor.FreePayProcessor;
import com.qiyi.boss.service.DutService;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.utils.AppConfig;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.dao.DutUserNewDao;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import com.qiyi.vip.trade.autorenew.domain.vo.DutUserNewVO;
import com.qiyi.vip.trade.autorenew.repository.DutUserRepository;
import com.qiyi.vip.trade.autorenew.service.DutRenewSetLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;

/**
 * @author: zhangtengfei01
 * @date: 2020/7/6 18:40
 * @desc: 批量根据vipTypes查询签约关系
 */
@Service
@Slf4j
public class I18nDutServiceImpl implements I18nDutService {

    private final static Logger LOGGER = LoggerFactory.getLogger(I18nDutServiceImpl.class);

    @Resource
    private DutUserRepository dutUserRepository;
    @Resource
    private FreePayProcessor freePayProcessor;
    @Resource
    private DutService dutService;
    @Resource
    private DutRenewSetLogService dutRenewSetLogService;
    @Resource
    private AutoRenewDutTypeManager autoRenewDutTypeManager;
    @Resource
    private DutUserNewDao dutUserNewDao;

    /**
     * 根据vipType列表获取用户签约关系
     *
     * @param userId
     * @param vipType
     * @param autoRenewStatus
     * @return
     */
    @Override
    public List<DutUserNew> listDutUserNew(Long userId, List<Long> vipType, Integer autoRenewStatus) {
        return dutUserRepository.listDutUserNew(null, userId, vipType, autoRenewStatus);
    }

    /**
     * 批量修改用户权益到期时间
     *
     * @param dutUserNews
     * @param newDeadline 新权益到期时间
     */
    @Override
    public void batchUpdateDutUserDeadline(List<DutUserNew> dutUserNews, Timestamp newDeadline) {
        if (CollectionUtils.isEmpty(dutUserNews) || Objects.isNull(newDeadline)) {
            log.warn("参数无效, dutUserNews:{}, newDeadline:{}", dutUserNews, newDeadline);
            return;
        }
        // 一般用户只有一条签约关系，且更新权益到期时间属于小概率事件，不影响性能
        dutUserNews.forEach(item -> updateShardDutUserDeadline(item, newDeadline));
    }

    /**
     * 更新签约关系的权益到期时间
     *
     * @param dutUserNew
     * @param newDeadline 新权益到期时间
     */
    private void updateShardDutUserDeadline(DutUserNew dutUserNew, Timestamp newDeadline) {
        if (Objects.isNull(dutUserNew)
                || Objects.isNull(dutUserNew.getId())
                || Objects.isNull(dutUserNew.getUserId())) {
            return;
        }

        DutUserNewVO updater = DutUserNewVO.builder()
                .id(dutUserNew.getId())
                .userId(dutUserNew.getUserId())
                .deadline(newDeadline)
                .updateTime(DateHelper.getDateTime())
                .build();

        try {
            dutUserNewDao.updateDutUserByPrimaryKeyAndUserIdSelective(updater);
        } catch (Exception ex) {
            log.error("国际站修改权益到期时间deadline, dutUserNew:{}", dutUserNew);
        }
    }

    @Override
    public boolean rtdnCancel(DutUserNew dutUserNew, RtdnCancelDetailDto rtdnCancelDetailDto, Integer payChannel) {
        if (dutUserNew.isAutoRenewUser() && dutUserNew.getOperateTime()
                .before(rtdnCancelDetailDto.getOperationTime())) {
            //如果数据库中记录的当前用户是自动续费用户且该状态的处理时间早于RTDN通知的触发时间，则进行取消自动续费处理
            dutService.cancelAutoRenewByRtdn(dutUserNew, rtdnCancelDetailDto, payChannel);
        } else if (dutUserNew.isNotAutoRenewUser() && dutUserNew.getOperateTime().getTime() !=
                rtdnCancelDetailDto.getOperationTime().getTime()) {
            //如果数据库中记录的当前用户已取消自动续费且处理事件与RTDN通知的触发时间不一致，则只记录下用户取消相关信息
            DutRenewSetLog lastDutRenewSetLog = dutRenewSetLogService
                    .getRecentlySetLogByVipTypeAndDutType(dutUserNew.getUserId(),
                            dutUserNew.getVipType(), dutUserNew.getType(),
                            DutRenewSetLog.RENEW_CANCEL);
            if (lastDutRenewSetLog != null) {
                lastDutRenewSetLog.setDescription(CancelDutSetLogDesp.buildCancelSetLogDescIncludingExtraInfo(dutUserNew,
                        null, null, rtdnCancelDetailDto, OperateSceneEnum.CANCEL_PAYCENTERNOTIFY.getValue(), payChannel));
                lastDutRenewSetLog.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                dutRenewSetLogService.updateSetLog(lastDutRenewSetLog);
                LOGGER.info("update DutRenewSetLog's description, uid:{}, dutType:{}",
                        dutUserNew.getUserId(), dutUserNew.getType());
            }
        }
        return true;
    }

    /**
     * 宽限期需求
     * 设计文档：http://wiki.qiyi.domain/pages/viewpage.action?pageId=789746921
     * 此处不对用户的deadline进行处理
     *
     * @param dutUserNew
     * @param appId   谷歌的AppId
     * @param oriOrderId 签约时的谷歌订单号
     * @param interval 可再次享受的时间间隔
     * @return
     */
    @Override
    public boolean gracePeriod(DutUserNew dutUserNew, String appId, String oriOrderId,Integer interval) {
        FreePayDto freePayDto = FreePayDto.builder()
                .purpose(Constants.FREE_PAY_FOR_GRACE_PERIOD)
                .userId(dutUserNew.getUserId())
                .vipType(dutUserNew.getVipType().intValue())
                .dutType(dutUserNew.getType())
                .autoRenewProductId(appId)
                .originalTransactionId(oriOrderId)
                .validateInterval(Boolean.TRUE)
                .interval(interval)
                .validateDeadline(Boolean.TRUE)
                .deadline(dutUserNew.getDeadline())
                .freePayPartner(AppConfig.getProperty("free.pay.partner"))
                .signKey(AppConfig.getProperty("vip.freepay.key"))
                .build();
        LOGGER.info("dealGracePeriod dealFreePay params:{}", freePayDto);
        freePayProcessor.dealFreePay(freePayDto);
        return true;
    }

    /**
     * 根据userId获取谷歌支付有效签约关系
     *
     * @param userId 用户userId
     * @return 谷歌签约关系
     */
    public DutUserNew getGoogleDutUser(Long userId) {
        DutUserNewVO query = DutUserNewVO.builder()
                .userId(userId)
                .status(BindStatusEnum.BIND.getValue())
                .autoRenew(DutUserNew.RENEW_AUTO)
                .build();
        List<DutUserNew> dutUserNews = dutUserRepository.queryDutUsers(query);
        log.info("getGoogleDutUser get dutUser, userId:{}, dutUserNews:{}", userId, dutUserNews);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            log.info("未查找到用户有效签约关系, uid:{}", userId);
            return null;
        }
        for (DutUserNew dutUserNew : dutUserNews) {
            AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutUserNew.getType());
            if (Objects.equals(autoRenewDutType.getPayChannel(), PaymentDutType.PAY_CHANNEL_GOOGLE_BILLING)) {
                return dutUserNew;
            }
        }
        log.warn("getGoogleDutUser 未查找到谷歌支付有效签约关系, uid:{}", userId);
        return null;
    }
}
