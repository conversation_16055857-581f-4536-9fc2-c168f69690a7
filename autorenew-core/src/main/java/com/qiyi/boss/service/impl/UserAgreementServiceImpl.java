package com.qiyi.boss.service.impl;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.qiyi.boss.Constants;
import com.qiyi.boss.autorenew.dto.CancelDutSetLogDesp;
import com.qiyi.boss.autorenew.dto.OpenDutSetLogDesp;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.component.AbstractPricingStrategy;
import com.qiyi.boss.component.AgreementHandler;
import com.qiyi.boss.component.AgreementHandlerFactory;
import com.qiyi.boss.component.RenewPriceCalculatorFactory;
import com.qiyi.boss.dto.AgreementDutContext;
import com.qiyi.boss.dto.AgreementDutRemindContext;
import com.qiyi.boss.dto.AgreementOptDto;
import com.qiyi.boss.dto.CancelAutoRenewOptDto;
import com.qiyi.boss.dto.FamilyBindRelation;
import com.qiyi.boss.dto.UserRenewDiscountInfo;
import com.qiyi.boss.enums.AgreementStatusEnum;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.enums.PricingStrategyEnum;
import com.qiyi.boss.exception.BizException;
import com.qiyi.boss.model.RenewPriceDto;
import com.qiyi.boss.model.UserAgreementContext;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.JacksonUtils;
import com.qiyi.vip.trade.autorenew.dao.UserAgreementMapper;
import com.qiyi.vip.trade.autorenew.dao.UserAgreementSetLogMapper;
import com.qiyi.vip.trade.autorenew.domain.AgreementDutMkt;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTempPrice;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

import static com.qiyi.boss.constants.AgreementConstants.EXCLUDE_AGREEMENT_TYPES;
/**
 * Created at: 2021-06-18
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserAgreementServiceImpl implements UserAgreementService {

    @Resource
    UserAgreementMapper userAgreementMapper;
    @Resource
    UserAgreementSetLogMapper userAgreementSetLogMapper;
    @Resource
    UserAgreementLogManager userAgreementLogManager;
    @Resource
    AgreementHandlerFactory agreementHandlerFactory;
    @Resource
    AgreementTemplateManager agreementTemplateManager;
    @Resource
    QiYueProductNewService qiYueProductNewService;
    @Resource
    AutoRenewDutTypeManager autoRenewDutTypeManager;
    @Resource
    AgreementNoInfoManager agreementNoInfoManager;
    @Resource
    DutUserRenewStatusManager dutUserRenewStatusManager;
    @Resource
    private RenewPriceCalculatorFactory renewPriceCalculatorFactory;
    @Resource
    private UserAgreementService userAgreementService;


    /**
     * 计算签约价
     */
    @Override
    public void initRenewPrice(Integer pricingStrategy, AgreementNoInfo agreementNoInfo, DutUserNew dutUserNew) {
        Integer agreementNo = agreementNoInfo.getId();
        PricingStrategyEnum pricingStrategyEnum = PricingStrategyEnum.valueOf(pricingStrategy);
        //不是固定价策略的，代扣时获取续费价格
        if (pricingStrategyEnum != PricingStrategyEnum.FIXED) {
            if (EXCLUDE_AGREEMENT_TYPES.contains(agreementNoInfo.getType()) || agreementNoInfo.defaultNo()) {
                return;
            }
            AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
            Integer templateRenewCount = dutUserNew.getTemplateRenewCount() != null ? dutUserNew.getTemplateRenewCount() : dutUserNew.getSerialRenewCount();
            //自动续费非正式协议计算续费价格
            AbstractPricingStrategy periodPricingStrategy = renewPriceCalculatorFactory.getStrategy(PricingStrategyEnum.PERIOD);

            // 如果协议每次新签约时不限制代扣次数，默认找第2期的价格(第一次是订购价)
            if (!agreementTemplate.needRestrictPeriod()) {
                getAndSetRenewPrice(periodPricingStrategy, agreementNoInfo, Constants.ZERO_PERIOD_NO + 1, agreementNo, dutUserNew);
                return;
            }
            if (templateRenewCount < agreementTemplate.getDiscountPeriods()) {
                getAndSetRenewPrice(periodPricingStrategy, agreementNoInfo, templateRenewCount + 1, agreementNo, dutUserNew);
                return;
            }
        }

        AbstractPricingStrategy fixedPricingStrategy = renewPriceCalculatorFactory.getStrategy(PricingStrategyEnum.FIXED);
        getAndSetRenewPrice(fixedPricingStrategy, agreementNoInfo, Constants.ZERO_PERIOD_NO, agreementNo, dutUserNew);
    }

    private static void getAndSetRenewPrice(AbstractPricingStrategy periodPricingStrategy, AgreementNoInfo agreementNoInfo, int templateRenewCount, Integer agreementNo, DutUserNew dutUserNew) {
        AgreementTempPrice priceInfo = periodPricingStrategy.getPriceInfo(agreementNoInfo.getTemplateCode(), templateRenewCount);
        //兼容逻辑，价格从协议迁移到协议模版
        if (priceInfo == null) {
            priceInfo = periodPricingStrategy.getPriceInfo(agreementNo, templateRenewCount);
        }
        if (priceInfo == null) {
            log.error("未查询到协议对应的价格, agreementNo:{}, payChannel:{}", agreementNo, agreementNoInfo.getPayChannel());
            throw BizException.newSystemException("未查询到协议对应的价格");
        }
        dutUserNew.setRenewPrice(priceInfo.getPrice());
        dutUserNew.setContractPrice(priceInfo.getOriginalPrice());
    }

    @Override
    public Integer getAutoRenewDisplayRenewPrice(DutUserNew dutUserNew) {
        if (dutUserNew.getRenewPrice() != null && dutUserNew.getRenewPrice() > 0) {
            return dutUserNew.getRenewPrice();
        }
        Integer agreementNo = dutUserNew.getAgreementNo();
        if (agreementNo == null) {
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getByDutTypeAndAmount(dutUserNew.getType(), dutUserNew.getAmount());
            AgreementTempPrice defaultPrice = agreementTemplateManager.getDefaultPriceByCode(agreementNoInfo.getTemplateCode());
            if (defaultPrice == null) {
                defaultPrice = agreementTemplateManager.getDefaultPrice(agreementNoInfo.getId());
            }
            return defaultPrice.getPrice();
        }
        AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
        AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
        PricingStrategyEnum pricingStrategy = PricingStrategyEnum.valueOf(agreementTemplate.getPricingStrategy());
        RenewPriceDto renewPrice = agreementHandlerFactory.getHandler(AgreementTypeEnum.AUTO_RENEW)
            .getRenewPrice(dutUserNew, agreementNoInfo, pricingStrategy);
        return renewPrice != null ? renewPrice.getFee() : null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveAutoRenew(DutUserNew dutUserNew, DutRenewSetLog setLog) {
        if (dutUserNew.getOperateTime() == null) {
            dutUserNew.setOperateTime(DateHelper.getDateTime());
        }
        if (setLog != null && setLog.getOperateTime() == null) {
            setLog.setOperateTime(dutUserNew.getOperateTime());
        }
        if (dutUserNew.getId() == null) {
            userAgreementMapper.insert(dutUserNew);
        } else {
            dutUserNew.setUpdateTime(DateHelper.getDateTime());
            userAgreementMapper.save(dutUserNew);
        }
        if (setLog != null) {
            userAgreementSetLogMapper.insert(setLog);
        }
        return true;
    }

    @Override
    public Integer updateNextDutTimeAndExt(DutUserNew dutUserNew) {
        return userAgreementMapper.updateNextDutTimeAndExt(dutUserNew.getId(), dutUserNew.getUserId(), dutUserNew.getNextDutTime(), dutUserNew.getExt());
    }

    @Override
    public Integer updateNextDutTimeAndDeadline(Long id, Long userId, Timestamp nextDutTime, Timestamp deadline, String ext) {
        return userAgreementMapper.updateNextDutTimeAndDeadline(id, userId, nextDutTime, deadline, ext);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateAutoRenewAfterDut(DutUserNew dutUserNew, DutRenewSetLog setLog) {
        userAgreementMapper.updateByPrimaryKeySelective(dutUserNew);
        if (setLog != null) {
            userAgreementSetLogMapper.updateByPrimaryKeySelective(setLog);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DutUserNew openByPureSign(VipUser vipUser, UserAgreementContext userAgreementContext, AgreementOptDto agreementOptDto) {
        Long uid = agreementOptDto.getUid();
        Integer agreementNo = agreementOptDto.getAgreementNo();
        AgreementTemplate agreementTemplate = userAgreementContext.getAgreementTemplate();
        UserAgreementService thisObj = (UserAgreementService) AopContext.currentProxy();
        DutUserNew dutUserNew = thisObj.getEffectiveByAgreementNo(uid, agreementNo);
        if (dutUserNew != null && dutUserNew.agreementValid()) {
            log.error("用户已签约此协议, agreementNo: {}, param: {}", agreementNo, agreementOptDto);
            throw BizException.newParamException("用户已经签约此协议");
        }
        if (dutUserNew != null && dutUserNew.cancelPendingStatus()) {
            log.error("协议当前处于待解约状态，不支持签约, agreementNo: {}, param: {}", agreementNo, agreementOptDto);
            throw BizException.newParamException("协议处于待解约中");
        }
        if (agreementTemplate.needSettle() && dutUserNew != null && dutUserNew.settleInProgress()) {
            log.error("协议正在结算中，不支持签约, agreementNo: {}, param: {}", agreementNo, agreementOptDto);
            throw BizException.newParamException("协议正在结算中");
        }

        Timestamp deadline = vipUser != null ? vipUser.getDeadline() : null;
        if (dutUserNew == null) {
            dutUserNew = DutUserNew.buildOpenRecord(deadline, agreementTemplate, agreementOptDto);
        } else {
            dutUserNew.resetInvalidRecord(deadline, agreementTemplate, agreementOptDto);
        }
        dutUserNew.setOperateSceneExt(agreementOptDto.getOperateScene());
        initRenewPrice(agreementTemplate.getPricingStrategy(), userAgreementContext.getAgreementNoInfo(), dutUserNew);
        try {
            if (dutUserNew.getId() == null) {
                userAgreementMapper.insert(dutUserNew);
            } else {
                dutUserNew.setUpdateTime(DateHelper.getDateTime());
                userAgreementMapper.save(dutUserNew);
            }
            String description = OpenDutSetLogDesp.buildAgreementOpenDesp(dutUserNew, agreementOptDto);
            DutRenewSetLog userOpenSetLog = DutRenewSetLog.buildSetLog(dutUserNew, description, OperateTypeEnum.OPEN, DateHelper.getDateTime());
            userAgreementSetLogMapper.insert(userOpenSetLog);
        } catch (Exception e) {
            log.error("纯签约出现异常，param: {}", JacksonUtils.toJsonString(agreementOptDto), e);
            throw BizException.newSystemException("签约协议时出现异常");
        }
        return dutUserNew;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DutUserNew openBySignPay(AutorenewRequest autoRenewRequest, UserAgreementContext userAgreementContext) {
        Long uid = autoRenewRequest.getUserId();
        AgreementTemplate agreementTemplate = userAgreementContext.getAgreementTemplate();
        AgreementNoInfo agreementNoInfo = userAgreementContext.getAgreementNoInfo();
        UserAgreementService thisObj = (UserAgreementService) AopContext.currentProxy();
        DutUserNew dutUserNew = thisObj.getByAgreementNoAndVipType(uid, agreementNoInfo.getId(), agreementNoInfo.getVipType());
        if (dutUserNew != null) {
            if (dutUserNew.settleInProgress() || dutUserNew.cancelPendingStatus()) {
                log.error("协议当前处于结算中或待解约状态，不支持签约, agreementType: {}, param: {}", AgreementTypeEnum.valueOf(agreementTemplate.getType()), autoRenewRequest);
                return dutUserNew;
            }
            if (dutUserNew.agreementValid()) {
                return dutUserNew;
            }
            dutUserNew.resetInvalidRecord(autoRenewRequest, agreementTemplate);
        } else {
            dutUserNew = DutUserNew.buildOpenRecordBySignPay(autoRenewRequest, agreementTemplate, agreementNoInfo);
        }
        OperateSceneEnum operateSceneEnum = OperateSceneEnum.OPEN_BUY;
        dutUserNew.setOperateSceneExt(operateSceneEnum);
        dutUserNew.setNextDutTime(autoRenewRequest.getExpireTime());
        initRenewPrice(agreementTemplate.getPricingStrategy(), userAgreementContext.getAgreementNoInfo(), dutUserNew);
        try {
            if (dutUserNew.getId() == null) {
                userAgreementMapper.insert(dutUserNew);
            } else {
                dutUserNew.setUpdateTime(DateHelper.getDateTime());
                userAgreementMapper.save(dutUserNew);
            }
            String description = OpenDutSetLogDesp.buildOpenDesp(dutUserNew, autoRenewRequest, operateSceneEnum);
            DutRenewSetLog userOpenSetLog = DutRenewSetLog.buildSetLog(dutUserNew, description, OperateTypeEnum.OPEN, DateHelper.getDateTime());
            userAgreementSetLogMapper.insert(userOpenSetLog);
        } catch (Exception e) {
            log.error("签约协议出现异常，param: {}", JacksonUtils.toJsonString(autoRenewRequest), e);
            throw BizException.newSystemException("签约协议时出现异常");
        }
        return dutUserNew;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateToSettling(DutUserNew dutUserNew, AgreementOptDto agreementOptDto) {
        Long uid = dutUserNew.getUserId();
        if (dutUserNew.settleInProgress()) {
            log.warn("协议正在结算中，dutUserNew: {}, settleOptDto: {}", JacksonUtils.toJsonString(dutUserNew), JacksonUtils.toJsonString(agreementOptDto));
            return true;
        }
        if (dutUserNew.notValidStatus()) {
            log.warn("协议不是生效中，dutUserNew: {}, settleOptDto: {}", JacksonUtils.toJsonString(dutUserNew), JacksonUtils.toJsonString(agreementOptDto));
            return false;
        }

        try {
            userAgreementMapper.updateStatus(dutUserNew.getId(), uid, AgreementStatusEnum.SETTLING.getValue());
            String description = CancelDutSetLogDesp.buildSetLogDesp(agreementOptDto);
            DutRenewSetLog userSettleSetLog = DutRenewSetLog.buildSetLog(dutUserNew, description, OperateTypeEnum.SETTLE, agreementOptDto.getOperateTime());
            userAgreementSetLogMapper.insert(userSettleSetLog);
        } catch (Exception e) {
            log.error("协议置为结算中出现异常，settleOptDto: {}", JacksonUtils.toJsonString(agreementOptDto), e);
            throw BizException.newSystemException("协议结算时出现异常");
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateToCancelPending(DutUserNew dutUserNew, AgreementOptDto agreementOptDto) {
        Long userId = dutUserNew.getUserId();
        if (dutUserNew.cancelPendingStatus()) {
            log.warn("协议已经是待解约，dutUserNew: {}, agreementOptDto: {}", JacksonUtils.toJsonString(dutUserNew), JacksonUtils.toJsonString(agreementOptDto));
            return true;
        }
        if (dutUserNew.notValidStatus()) {
            log.warn("协议不是生效中，dutUserNew: {}, agreementOptDto: {}", JacksonUtils.toJsonString(dutUserNew), JacksonUtils.toJsonString(agreementOptDto));
            return false;
        }

        try {
            userAgreementMapper.updateStatus(dutUserNew.getId(), userId, AgreementStatusEnum.CANCEL_PENDING.getValue());
            String description = CancelDutSetLogDesp.buildSetLogDesp(agreementOptDto);
            DutRenewSetLog cancelPendingSetLog = DutRenewSetLog.buildSetLog(dutUserNew, description, OperateTypeEnum.CANCEL_PENDING, agreementOptDto.getOperateTime());
            userAgreementSetLogMapper.insert(cancelPendingSetLog);
        } catch (Exception e) {
            log.error("协议置待解约出现异常，agreementOptDto: {}", JacksonUtils.toJsonString(agreementOptDto), e);
            throw BizException.newSystemException("协议置为待解约时出现异常");
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean cancelAutoRenew(DutUserNew dutUserNew, CancelAutoRenewOptDto cancelAutoRenewOptDto) {
        Timestamp operateTime = cancelAutoRenewOptDto.getOperateTime();
        if (operateTime == null) {
            cancelAutoRenewOptDto.setOperateTime(DateHelper.getDateTime());
        }
        Long id = dutUserNew.getId();
        Long userId = dutUserNew.getUserId();
        Integer actType = dutUserNew.getActType();
        Boolean unBind = cancelAutoRenewOptDto.isUnBind();
        userAgreementMapper.cancelAutoRenew(id, userId, actType, unBind);
        if (dutUserNew.isAutoRenewUser()) {
            DutRenewSetLog cancelSetLog = DutRenewSetLog.buildCancelSetLogFromDutUserNew(dutUserNew, cancelAutoRenewOptDto);
            userAgreementSetLogMapper.insert(cancelSetLog);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean cancelByAccountUnbindMsg(DutUserNew dutUserNew, AgreementTemplate agreementTemplate, AgreementOptDto agreementOptDto) {
        if (dutUserNew.invalidOrFinished()) {
            log.warn("协议已失效或已履约完成，agreementOptDto: {}, dutUserNew: {}", JacksonUtils.toJsonString(agreementOptDto), JacksonUtils.toJsonString(dutUserNew));
            return false;
        }
        if (dutUserNew.canNotCancelByUnbindMsg()) {
            log.warn("当前协议状态无法操作取消，agreementOptDto: {}, dutUserNew: {}", JacksonUtils.toJsonString(agreementOptDto), JacksonUtils.toJsonString(dutUserNew));
            return false;
        }

        Long userId = dutUserNew.getUserId();
        AgreementStatusEnum agreementStatus = Objects.equals(dutUserNew.getSerialRenewCount(), agreementTemplate.getPeriods())
            ? AgreementStatusEnum.FINISHED : AgreementStatusEnum.INVALID;
        OperateTypeEnum operateType = Objects.equals(dutUserNew.getSerialRenewCount(), agreementTemplate.getPeriods())
            ? OperateTypeEnum.FINISHED : OperateTypeEnum.CANCEL;
        try {
            userAgreementMapper.invalidOrFinishRecord(dutUserNew.getId(), userId, agreementStatus.getValue());
            AgreementTypeEnum agreementType = AgreementTypeEnum.valueOf(dutUserNew.getAgreementType());
            OperateSceneEnum operateScene = getOperateScene(agreementType, agreementOptDto, dutUserNew.getSignKey());
            agreementOptDto.setOperateScene(operateScene);
            String description = CancelDutSetLogDesp.buildSetLogDesp(agreementOptDto);
            DutRenewSetLog cancelSetLog = DutRenewSetLog.buildSetLog(dutUserNew, description, operateType, agreementOptDto.getOperateTime());
            userAgreementSetLogMapper.insert(cancelSetLog);
        } catch (Exception e) {
            log.error("协议取消时出现异常，agreementOptDto: {}", JacksonUtils.toJsonString(agreementOptDto), e);
            throw BizException.newSystemException("取消协议出现异常");
        }
        return true;
    }

    /**
     * 获取解约场景
     * @param agreementType
     * @param agreementOptDto
     * @param signKey
     */
    private OperateSceneEnum getOperateScene(AgreementTypeEnum agreementType, AgreementOptDto agreementOptDto, String signKey) {
        OperateTypeEnum operateType = null;
        //芝麻go的解约成功消息没有取消场景字段，需要去结算中set log对应的值
        if (agreementType == AgreementTypeEnum.ZHIMA_GO) {
            operateType = OperateTypeEnum.SETTLE;
        }
        //用户取消微信支付分时，如果有待完结订单，需要先进行结单，此时协议状态会变为待解约，set log中存取消场景
        if (agreementType == AgreementTypeEnum.WECHAT_PAY_SCORE) {
            operateType = OperateTypeEnum.CANCEL_PENDING;
        }
        if (operateType == null) {
            return agreementOptDto.getOperateScene();
        }
        //从set log中获取芝麻GO和微信支付分的取消场景
        DutRenewSetLog dutRenewSetLog = userAgreementLogManager.getSetLogBySignKey(agreementOptDto.getUid(), signKey, operateType);
        if (dutRenewSetLog == null) {
            return agreementOptDto.getOperateScene();
        }
        OpenDutSetLogDesp setLogDesp = dutRenewSetLog.parseOpenSetLogDescription();
        OperateSceneEnum operateScene = OperateSceneEnum.parseValue(setLogDesp.getScene());
        if (operateScene != null) {
            return operateScene;
        }
        return agreementOptDto.getOperateScene();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DutRenewLog updateDutUserNewAndLog(Long userId, String orderCode) {
        DutRenewLog dutRenewLog = userAgreementLogManager.getRenewLogByOrderCode(userId, orderCode);
        if (dutRenewLog == null) {
            log.error("dutRenewLog is null by uid: {} and orderCode: {}", userId, orderCode);
            return null;
        }
        if (dutRenewLog.isPaySuccess()) {
            return dutRenewLog;
        }

        //续费不成功再次续费的续费更新数据
        dutRenewLog.setErrorCode(null);
        dutRenewLog.setThirdErrorMsg(null);
        dutRenewLog.setStatus(DutRenewLog.PAY_SUCCESS);
        userAgreementLogManager.saveRenewLog(dutRenewLog);
        dutRenewLog.setUpdated(true);
        return dutRenewLog;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDutUserNewAndLogOld(Long userId, String orderCode, Timestamp nextDutTime, Integer agreementNo) {
        DutRenewLog dutRenewLog = userAgreementLogManager.getRenewLogByOrderCode(userId, orderCode);
        if (dutRenewLog == null) {
            log.error("dutRenewLog is null by uid: {} and orderCode: {}", userId, orderCode);
            return;
        }
        if (dutRenewLog.isPaySuccess()) {
            return;
        }

        //续费不成功再次续费的续费更新数据
        dutRenewLog.setErrorCode(null);
        dutRenewLog.setStatus(DutRenewLog.PAY_SUCCESS);
        //代码方式将协议编号刷到数据库
        if (dutRenewLog.getAgreementNo() == null) {
            dutRenewLog.setAgreementNo(agreementNo);
        }
        userAgreementLogManager.saveRenewLog(dutRenewLog);

        DutUserNew dutUserNew = userAgreementMapper.selectBySignKey(userId, dutRenewLog.getSignKey());
        if (dutUserNew == null) {
            return;
        }
        AgreementTypeEnum agreementType = AgreementTypeEnum.valueOf(dutUserNew.getAgreementType());
        if (AgreementTypeEnum.ZHIMA_GO == agreementType) {
            incrementRenewCount(dutUserNew.getId(), dutUserNew.getUserId());
        }
        if (AgreementTypeEnum.WECHAT_PAY_SCORE == agreementType) {
            OperateTypeEnum operateType = OperateTypeEnum.parseValue(dutRenewLog.getOperateType());
            //微信支付分0元创单支付成功后，需要将nextDutTime设置为创单的权益结束时间，以便本次服务到期后发起订单完结。
            if (operateType == OperateTypeEnum.DUT) {
                userAgreementMapper.updateNextDutTime(dutUserNew.getId(), dutUserNew.getUserId(), nextDutTime);
            }
            //结单完成后设置下次续费时间为空
            if (operateType == OperateTypeEnum.COMPLETE_ORDER) {
                incrementRenewCountAndResetNextDutTime(dutUserNew.getId(), dutUserNew.getUserId());
            }
        }
    }


    @Override
    public void incrementRenewCount(Long dutUserNewId, Long userId) {
        userAgreementMapper.incrementRenewCount(dutUserNewId, userId);
        //自动续费状态表中续费总次数和连续续费次数分别加1
        dutUserRenewStatusManager.incrementRenewCount(userId);
    }

    @Override
    public void incrementRenewCountAndResetNextDutTime(Long id, Long userId) {
        userAgreementMapper.incrementRenewCountAndResetNextDutTime(id, userId);
        //自动续费状态表中续费总次数和连续续费次数分别加1
        dutUserRenewStatusManager.incrementRenewCount(userId);
    }

    @Override
    public int batchUpdateDeadline(List<Long> ids, Long userId, Timestamp deadline) {
        return userAgreementMapper.batchUpdateDeadline(ids, userId, deadline);
    }

    @Override
    public int batchUpdateNextDutTime(List<Long> ids, Long userId, Timestamp nextDutTime) {
        return userAgreementMapper.batchUpdateNextDutTime(ids,userId, nextDutTime);
    }

    @Override
    public DutUserNew getByDutTypeAndVipType(Long userId, Integer dutType, Long vipType) {
        List<DutUserNew> dutUserNews = userAgreementMapper.selectByDutTypeAndVipType(userId, dutType, vipType);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return null;
        }
        return dutUserNews.stream().filter(DutUserNew::notInvalidAndFinished).findFirst().orElse(dutUserNews.get(0));
    }

    //@DataSource(DataSourceEnum.SLAVE)
    @Override
    public List<DutUserNew> getByVipType(Long userId, Long vipType) {
        return userAgreementMapper.selectByVipType(userId, null, vipType, null);
    }

    //    @DataSource(DataSourceEnum.SLAVE)
    @Override
    public List<DutUserNew> getByVipType(Long userId, Long sourceVipType, Long vipType) {
        return userAgreementMapper.selectByVipType(userId, sourceVipType, vipType, null);
    }

    //@DataSource(DataSourceEnum.SLAVE)
    @Override
    public List<DutUserNew> getByAgreementTypeAndVipType(Long userId, AgreementTypeEnum agreementType, Long vipType, AgreementStatusEnum agreementStatus) {
        Integer agreementStatusValue = agreementStatus != null ? agreementStatus.getValue() : null;
        return userAgreementMapper.selectByAgreementTypeAndVipType(userId, agreementType.getValue(), vipType, agreementStatusValue);
    }

    @Override
    public List<DutUserNew> getByExcludeAgreementTypesAndVipTypes(Long userId, List<Integer> excludeAgreementTypes, List<Long> vipTypes, AgreementStatusEnum agreementStatus) {
        Integer agreementStatusValue = agreementStatus != null ? agreementStatus.getValue() : null;
        return userAgreementMapper.selectByExcludeAgreementTypesAndVipTypes(userId, excludeAgreementTypes, vipTypes, agreementStatusValue);
    }

    @Override
    public List<DutUserNew> getByAgreementTypeAndVipType(Long userId, AgreementTypeEnum agreementType, Long sourceVipType, Long vipType, AgreementStatusEnum agreementStatus) {
        UserAgreementService thisObj = (UserAgreementService) AopContext.currentProxy();
        List<DutUserNew> dutUserNews = thisObj.getByAgreementTypeAndVipType(userId, agreementType, vipType, agreementStatus);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return Collections.emptyList();
        }
        return dutUserNews.stream()
            .filter(dutUserNew -> Objects.equals(sourceVipType, dutUserNew.getSourceVipType()))
            .collect(Collectors.toList());
    }

    @Override
    public DutUserNew getBySignKey(Long userId, String signKey) {
        return userAgreementMapper.selectBySignKey(userId, signKey);
    }

    @Override
    public List<DutUserNew> getByAgreementNo(Long userId, Integer agreementNo, AgreementStatusEnum agreementStatus) {
        Integer agreementStatusValue = agreementStatus != null ? agreementStatus.getValue() : null;
        return userAgreementMapper.selectByAgreementNoAndVipType(userId, agreementNo, null, agreementStatusValue);
    }

    @Override
    public DutUserNew getByAgreementNoAndVipType(Long userId, Integer agreementNo, Long vipType) {
        List<DutUserNew> dutUserNews = userAgreementMapper.selectByAgreementNoAndVipType(userId, agreementNo, vipType, null);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return null;
        }
        return dutUserNews.stream().filter(DutUserNew::notInvalidAndFinished).findFirst().orElse(dutUserNews.get(0));
    }

    @Override
    public DutUserNew getEffectiveByAgreementNoAndVipType(Long userId, Integer agreementNo, Long vipType) {
        List<DutUserNew> dutUserNews = userAgreementMapper.selectByAgreementNoAndVipType(userId, agreementNo, vipType, AgreementStatusEnum.VALID.getValue());
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return null;
        }
        return dutUserNews.get(0);
    }

    @Override
    public List<DutUserNew> getEffectiveByAgreementType(Long userId, Integer agreementType) {
        List<DutUserNew> dutUserNews = userAgreementMapper.selectByAgreementTypeAndVipType(userId, agreementType, null, null);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return Collections.emptyList();
        }
        return dutUserNews.stream()
            .filter(dutUserNew -> !dutUserNew.invalidOrFinished())
            .collect(Collectors.toList());
    }

    @Override
    public List<DutUserNew> getEffectiveByAgreementTypeAndVipType(Long userId, AgreementTypeEnum agreementType, Long vipType) {
        List<DutUserNew> dutUserNews = userAgreementMapper.selectByAgreementTypeAndVipType(userId, agreementType.getValue(), vipType, null);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return Collections.emptyList();
        }
        return dutUserNews.stream()
            .filter(DutUserNew::notInvalidAndFinished)
            .collect(Collectors.toList());
    }

    @Override
    public DutUserNew getEffectiveByAgreementNo(Long userId, Integer agreementNo) {
        UserAgreementService thisObj = (UserAgreementService) AopContext.currentProxy();
        List<DutUserNew> dutUserNews = thisObj.getByAgreementNo(userId, agreementNo, null);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return null;
        }
        return dutUserNews.stream()
            .filter(dutUserNew -> !dutUserNew.invalidOrFinished())
            .max(Comparator.comparing(DutUserNew::getSignTime)).orElse(null);
    }

    @Override
    public List<DutUserNew> getEffectiveByUserId(Long userId) {
        List<DutUserNew> dutUserNews = userAgreementMapper.selectEffectByUid(userId);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return Lists.newArrayList();
        }
        return dutUserNews.stream()
            .filter(dutUserNew -> !dutUserNew.invalidOrFinished())
            .collect(Collectors.toList());
    }

    @Override
    public Optional<DutUserNew> getUserLatestAgreement(Long userId, Integer agreementNo) {
        UserAgreementService thisObj = (UserAgreementService) AopContext.currentProxy();
        List<DutUserNew> dutUserNews = thisObj.getByAgreementNo(userId, agreementNo, null);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return Optional.empty();
        }
        // 查找有效的协议
        Optional<DutUserNew> validAgreementOptional = dutUserNews.stream()
            .filter(dutUserNew -> !dutUserNew.invalidOrFinished())
            .max(Comparator.comparing(DutUserNew::getSignTime));
        if (validAgreementOptional.isPresent()) {
            return validAgreementOptional;
        }
        // 查找已过期的协议中最近的一个
        return dutUserNews.stream().max(Comparator.comparing(DutUserNew::getSignTime));
    }

    @Override
    public List<DutUserNew> getUserWithAllAgreements(Long userId, Integer status) {
        if (userId == null)  {
            return Collections.emptyList();
        }
        return userAgreementMapper.getUserByAllAgreements(userId, status);
    }

    @Override
    public DutUserNew getByAgreementNoAndDutType(Long userId, Integer agreementNo, Integer dutType) {
        UserAgreementService thisObj = (UserAgreementService) AopContext.currentProxy();
        return thisObj.getByAgreementNoAndDutType(userId, agreementNo, dutType, null);
    }

    @Override
    public DutUserNew getByAgreementNoAndDutType(Long userId, Integer agreementNo, Integer dutType, Long vipType) {
        UserAgreementService thisObj = (UserAgreementService) AopContext.currentProxy();
        return thisObj.getByAgreementNoAndDutType(userId, agreementNo, dutType, null, vipType);
    }

    @Override
    public DutUserNew getByAgreementNoAndDutType(Long userId, Integer agreementNo, Integer dutType, Long sourceVipType, Long vipType) {
        if (agreementNo == null && dutType == null) {
            return null;
        }
        List<DutUserNew> dutUserNews = userAgreementMapper.selectByAgreementNoAndDutType(userId, agreementNo, dutType, sourceVipType, vipType);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return null;
        }
        return dutUserNews.stream().filter(DutUserNew::notInvalidAndFinished).findFirst().orElse(dutUserNews.get(0));
    }

    @Override
    public DutUserNew getByAgreementNoOrDutType(Long userId, Integer agreementNo, Integer dutType, Long vipType) {
        List<DutUserNew> dutUserNews = userAgreementMapper.selectByDutTypeAndVipType(userId, dutType, vipType);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return null;
        }
        dutUserNews.sort(Comparator.comparing(DutUserNew::getAutoRenew).reversed());
        DutUserNew sameAgreementNoRecord = dutUserNews.stream()
            .filter(dutUserNew -> Objects.equals(agreementNo, dutUserNew.getAgreementNo()))
            .findFirst().orElse(null);
        if (sameAgreementNoRecord != null) {
            return sameAgreementNoRecord;
        }
        return dutUserNews.stream()
            .filter(dutUserNew -> dutUserNew.getAgreementNo() == null)
            .findFirst().orElse(null);
    }

    @Override
    public DutUserNew getByAgreementNoOrDutTypeForManagement(Long userId, Integer agreementNo, Integer dutType, Long vipType) {
        List<DutUserNew> dutUserNews = userAgreementMapper.selectByDutTypeAndVipType(userId, dutType, vipType);
        if (CollectionUtils.isEmpty(dutUserNews)) {
            return null;
        }
        dutUserNews.sort(Comparator.comparing(DutUserNew::getAutoRenew).reversed());
        DutUserNew sameAgreementNoRecord = dutUserNews.stream()
            .filter(dutUserNew -> Objects.equals(agreementNo, dutUserNew.getAgreementNo()))
            .findFirst().orElse(null);
        if (sameAgreementNoRecord != null) {
            return sameAgreementNoRecord;
        }
        DutUserNew matchedRecord = dutUserNews.stream()
            .filter(dutUserNew -> dutUserNew.getAgreementNo() == null)
            .findFirst().orElse(null);
        if (matchedRecord != null) {
            return matchedRecord;
        }
        return dutUserNews.get(0);
    }

    @Override
    public AgreementDutContext buildDutContext(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate, AgreementDutMkt dutMktAct) {
        PricingStrategyEnum pricingStrategyEnum = PricingStrategyEnum.valueOf(agreementTemplate.getPricingStrategy());
        AgreementHandler agreementHandler = agreementHandlerFactory.getHandler(AgreementTypeEnum.valueOf(dutUserNew.getAgreementType()));
        RenewPriceDto renewPriceDto = agreementHandler.getRenewPrice(dutUserNew, agreementNoInfo, pricingStrategyEnum);

        Integer serialRenewCount = dutUserNew.getSerialRenewCount();
        Integer amount = dutUserNew.getAmount();
        String pid = agreementTemplate.getPid();
        String skuId = agreementTemplate.getSkuId();
         //TODO 此处需要增加非会员商品sku非包月的的映射
        if (Constants.AMOUNT_OF_AUTORENEW_BY_QUARTER_YEAR.equals(amount) && Constants.getQuarterSkuIdMap().containsKey(skuId)) {
            skuId = Constants.getQuarterSkuIdMap().get(skuId);
        }
        if (Constants.AMOUNT_OF_AUTORENEW_BY_YEAR.equals(amount) && Constants.getYearSkuIdMap().containsKey(skuId)) {
            skuId = Constants.getYearSkuIdMap().get(skuId);
        }
        Integer skuAmount = Constants.AMOUNT_OF_COMMON_AUTORENEW;
        if (dutMktAct != null && serialRenewCount < dutMktAct.getPeriods()) {
            pid = dutMktAct.getPid() != null ? dutMktAct.getPid() : pid;
            amount = dutMktAct.getAmount();
            if (dutMktAct.getSkuId() != null) {
                skuId = dutMktAct.getSkuId();
                skuAmount = dutMktAct.getAmount();
            }
        }
        com.qiyi.boss.model.Product qiyueProduct = null;
        if (StringUtils.isBlank(skuId)) {
            qiyueProduct = qiYueProductNewService.getProductFromQiYueProduct(pid);
        }
        return AgreementDutContext.builder()
            .amount(amount)
            .pid(pid)
            .skuId(skuId)
            .skuAmount(skuId != null ? skuAmount : null)
            .renewPrice(renewPriceDto.getFee())
            .qiyueProduct(qiyueProduct)
            .payChannel(agreementNoInfo.getPayChannel())
            .payType(agreementNoInfo.getDutPayType())
            .partnerId(agreementNoInfo.getPartnerId())
            .agreementType(agreementNoInfo.getType())
            .build();
    }

    @Override
    public AgreementDutRemindContext buildDutRemindContext(DutUserNew dutUserNew) {
        Integer amount = null == dutUserNew.getAmount() ? Constants.AMOUNT_OF_COMMON_AUTORENEW : dutUserNew.getAmount();
        Integer agreementNo = dutUserNew.getAgreementNo();
        Integer dutType = dutUserNew.getType();
        Integer payChannel;
        Integer renewPrice;

        if (agreementNo != null) {
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
            AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
            AgreementDutMkt dutMktAct = agreementTemplateManager.getDutMktAct(agreementNo);
            PricingStrategyEnum pricingStrategyEnum = PricingStrategyEnum.valueOf(agreementTemplate.getPricingStrategy());
            AgreementHandler agreementHandler = agreementHandlerFactory.getHandler(AgreementTypeEnum.valueOf(dutUserNew.getAgreementType()));
            RenewPriceDto renewPriceDto = agreementHandler.getRenewPrice(dutUserNew, agreementNoInfo, pricingStrategyEnum);

            dutType = agreementNoInfo.getDutType();
            payChannel = agreementNoInfo.getPayChannel();
            renewPrice = renewPriceDto != null ? renewPriceDto.getFee() : null;
            if (dutMktAct != null && dutUserNew.getSerialRenewCount() < dutMktAct.getPeriods()) {
                amount = dutMktAct.getAmount();
            }
        } else {
            AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutUserNew.getType());
            payChannel = autoRenewDutType.getPayChannel();
            renewPrice = dutUserNew.getRenewPrice() != null ? dutUserNew.getRenewPrice() : null;
        }
        Timestamp nextDutTime = dutUserNew.getNextDutTime();
        return AgreementDutRemindContext.builder()
            .agreementNo(dutUserNew.getAgreementNo())
            .dutType(dutType)
            .payChannel(payChannel)
            .amount(amount)
            .renewPrice(renewPrice)
            .sourceVipType(dutUserNew.getSourceVipType())
            .vipType(dutUserNew.getVipType())
            .nextDutTime(nextDutTime)
            .build();
    }

    @Override
    public void updateDutUserNewBindStatus(Long userId, Integer dutType, Integer status) {
        userAgreementMapper.updateUserAccountBindStatus(userId, dutType, status);
    }

    @Override
    public UserRenewDiscountInfo canEnjoyDiscountPrice(DutUserNew dutUserNew) {
        UserAgreementService thisObj = (UserAgreementService) AopContext.currentProxy();
        return thisObj.canEnjoyDiscountPrice(dutUserNew, null, null);
    }

    @Override
    public UserRenewDiscountInfo canEnjoyDiscountPrice(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate) {
        if (dutUserNew == null || dutUserNew.getAgreementNo() == null) {
            return null;
        }
        Integer agreementNo = dutUserNew.getAgreementNo();
        if (agreementNoInfo == null) {
            agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
        }
        if (agreementNoInfo == null || agreementNoInfo.defaultNo()) {
            return null;
        }
        String templateCode = agreementNoInfo.getTemplateCode();
        if (agreementTemplate == null) {
            agreementTemplate = agreementTemplateManager.getByCode(templateCode);
        }
        if (agreementTemplate == null || agreementTemplate.notDiscountTmp()) {
            return null;
        }
        //折扣协议无法纯签约，需要算上签约支付那一笔
        Integer usedPeriods = agreementTemplate.needRestrictPeriod() ? dutUserNew.getSerialRenewCount() : userAgreementLogManager.getSuccessDutLogCount(dutUserNew, agreementNoInfo, agreementTemplate);
        boolean canEnjoyDiscount = agreementTemplate.getDiscountPeriods() > usedPeriods + 1;
        if (!canEnjoyDiscount) {
            return null;
        }
        Integer templateRenewCount = agreementTemplate.needRestrictPeriod() ? dutUserNew.getTemplateRenewCount() : usedPeriods;
        if (templateRenewCount == null) {
            templateRenewCount = agreementNoInfo.thirdDut()
                ? dutUserNew.getSerialRenewCount() : userAgreementLogManager.successDutLogCount(dutUserNew.getUserId(), agreementTemplate.getCode(), null);
        }
        usedPeriods = templateRenewCount + 1;
        canEnjoyDiscount = agreementTemplate.getDiscountPeriods() > usedPeriods;
        if (!canEnjoyDiscount) {
            return null;
        }
        AbstractPricingStrategy periodPricingStrategy = renewPriceCalculatorFactory.getStrategy(PricingStrategyEnum.PERIOD);
        RenewPriceDto renewPrice = periodPricingStrategy.getDiscountPrice(agreementNoInfo.getTemplateCode(), templateRenewCount);
        if (renewPrice == null) {
            renewPrice = periodPricingStrategy.getDiscountPrice(agreementNo, templateRenewCount);
        }
        return UserRenewDiscountInfo.builder()
            .renewPrice(renewPrice.getFee())
            .contractPrice(renewPrice.getOriginalPrice())
            .amount(dutUserNew.getAmount())
            .totalPeriods(agreementTemplate.getDiscountPeriods())
            .usedPeriods(usedPeriods)
            .remainPeriods(agreementTemplate.getDiscountPeriods() - usedPeriods)
            .build();
    }

    @Override
    @Transactional
    public void updateFamilyTypeInfo(Long userId, List<DutUserNew> familyTypeUserNews, FamilyBindRelation familyBindRelation, Timestamp smallerDeadlineNonNullValue, Timestamp sourceUidDeadline, Timestamp targetUidDeadline) {
        log.info("updateFamilyTypeInfo, userId:{}, smallerDeadlineNonNullValue:{}, sourceUidDeadline:{}, targetUidDeadline:{}, familyBindRelation:{}", userId, smallerDeadlineNonNullValue, sourceUidDeadline, targetUidDeadline, familyBindRelation);
        updateNoAppleFamilyTypeDutUserNews(familyTypeUserNews, familyBindRelation, smallerDeadlineNonNullValue, sourceUidDeadline, targetUidDeadline);
        updateAppleFamilyTypeDutUserNews(familyTypeUserNews, familyBindRelation, sourceUidDeadline, targetUidDeadline);
    }

    private void updateNoAppleFamilyTypeDutUserNews(List<DutUserNew> familyTypeUserNews, FamilyBindRelation familyBindRelation, Timestamp smallerDeadlineNonNullValue, Timestamp sourceUidDeadline, Timestamp targetUidDeadline) {
        List<DutUserNew> noAppleFamilyTypeUserNews = familyTypeUserNews
            .stream()
            .filter(d -> agreementNoInfoManager.getById(d.getAgreementNo()) != null && !agreementNoInfoManager.getById(d.getAgreementNo()).thirdDut())
            .collect(Collectors.toList());
        for (DutUserNew userNew : noAppleFamilyTypeUserNews) {
            Long id = userNew.getId();
            Long userId = userNew.getUserId();
            String targetUidDeadlineStr = targetUidDeadline != null ? DateUtil.date(targetUidDeadline).toString("yyyy-MM-dd HH:mm:ss") : null;
            userNew.setBindUidInfo(null, familyBindRelation, targetUidDeadlineStr);
            String ext = userNew.getExt();
            userAgreementService.updateNextDutTimeAndDeadline(id, userId, smallerDeadlineNonNullValue, sourceUidDeadline, ext);
        }
    }

    private void updateAppleFamilyTypeDutUserNews(List<DutUserNew> familyTypeUserNews, FamilyBindRelation familyBindRelation, Timestamp sourceUidDeadline, Timestamp targetUidDeadline) {
        List<DutUserNew> appleFamilyTypeUserNews = familyTypeUserNews
            .stream()
            .filter(d -> agreementNoInfoManager.getById(d.getAgreementNo()) != null && agreementNoInfoManager.getById(d.getAgreementNo()).thirdDut())
            .collect(Collectors.toList());
        for (DutUserNew userNew : appleFamilyTypeUserNews) {
            Long id = userNew.getId();
            Long userId = userNew.getUserId();
            String targetUidDeadlineStr = targetUidDeadline != null ? DateUtil.date(targetUidDeadline).toString("yyyy-MM-dd HH:mm:ss") : null;
            userNew.setBindUidInfo(null, familyBindRelation, targetUidDeadlineStr);
            String ext = userNew.getExt();
            userAgreementService.updateNextDutTimeAndDeadline(id, userId, null, sourceUidDeadline, ext);
        }
    }
}
