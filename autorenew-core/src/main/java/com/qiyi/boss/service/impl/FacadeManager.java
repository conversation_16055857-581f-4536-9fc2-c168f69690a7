package com.qiyi.boss.service.impl;

import com.qiyi.boss.processor.AutoRenewManagementProcessor;
import com.qiyi.boss.processor.DutProcessor;
import org.springframework.stereotype.Component;

import org.springframework.context.annotation.DependsOn;
import javax.annotation.Resource;


/**
 * 后台管理类统一入口
 *
 * <AUTHOR>
 */
@Component
@DependsOn("cloudConfigUtil")
public class FacadeManager {

	@Resource
	private AsyncTaskManager asyncTaskManager;

    @Resource
    private QiYuePlatformManager qiYuePlatformManager;

	@Resource
	private UserManager userManager;

	@Resource
	private PaymentDutTypeManager paymentDutTypeManager;

	@Resource
	private DutManager dutManager;

	@Resource
	private DutProcessor dutProcessor;

	@Resource
	private AutorenewDutConfigManager autorenewDutConfigManager;

	@Resource
	private CommonAutorenewDutConfigManager commonAutorenewDutConfigManager;

	@Resource(name = "autoRenewLinkedDutConfigService")
	private AutoRenewLinkedDutConfigManager autoRenewLinkedDutConfigManager;

	@Resource
	private AutoRenewDutTypeManager autoRenewDutTypeManager;

	@Resource
	private AutoRenewUpgradeConfigManager autoRenewUpgradeConfigManager;

	@Resource
	private AutoRenewManagementProcessor autoRenewManagementProcessor;

	@Resource
	private AutoRenewVipTypeUpgradeConfigManager autoRenewVipTypeUpgradeConfigManager;

	public AsyncTaskManager getAsyncTaskManager() {
		return asyncTaskManager;
	}

    public QiYuePlatformManager getQiYuePlatformManager() {
        return qiYuePlatformManager;
    }

    public UserManager getUserManager() {
		return userManager;
	}

	public PaymentDutTypeManager getPaymentDutTypeManager() {
		return paymentDutTypeManager;
	}

	public DutManager getDutManager() {
		return dutManager;
	}

	public DutProcessor getDutProcessor() {
		return dutProcessor;
	}

	public AutorenewDutConfigManager getAutorenewDutConfigManager() {
		return autorenewDutConfigManager;
	}

	public CommonAutorenewDutConfigManager getCommonAutorenewDutConfigManager() {
		return commonAutorenewDutConfigManager;
	}

	public AutoRenewLinkedDutConfigManager getAutoRenewLinkedDutConfigManager() {
		return autoRenewLinkedDutConfigManager;
	}

	public AutoRenewDutTypeManager getAutoRenewDutTypeService() {
		return autoRenewDutTypeManager;
	}

	public AutoRenewUpgradeConfigManager getAutoRenewUpgradeConfigManager() {
		return autoRenewUpgradeConfigManager;
	}

	public AutoRenewManagementProcessor getAutoRenewManagementProcessor() {
		return autoRenewManagementProcessor;
	}

	public AutoRenewVipTypeUpgradeConfigManager getAutoRenewVipTypeUpgradeConfigManager() {
		return autoRenewVipTypeUpgradeConfigManager;
	}

	public void setAutoRenewVipTypeUpgradeConfigManager(AutoRenewVipTypeUpgradeConfigManager autoRenewVipTypeUpgradeConfigManager) {
		this.autoRenewVipTypeUpgradeConfigManager = autoRenewVipTypeUpgradeConfigManager;
	}
}
