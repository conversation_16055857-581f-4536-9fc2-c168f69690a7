package com.qiyi.boss.service.impl;

import com.qiyi.boss.db.DataSource;
import com.qiyi.boss.db.DataSourceEnum;
import com.qiyi.vip.trade.autorenew.domain.DutUserRenewStatus;
import com.qiyi.vip.trade.autorenew.mapper.DutUserRenewStatusMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * 自动续费状态管理类
 *
 * <AUTHOR>
 * @version 16-9-2 - 下午8:31
 */
@Component
public class DutUserRenewStatusManager {

    @Resource
    private DutUserRenewStatusMapper dutUserRenewStatusMapper;

    /**
     * 增加或更改自动续费用户信息，如果有开通记录，则更改，否则新增
     */
    //@DataSource(DataSourceEnum.MASTER)
    public DutUserRenewStatus addUserRenewStatusIfNotExisted(Long userId) {
        //更新DutUserRenewStatus状态
        DutUserRenewStatus dutUserRenewStatus = dutUserRenewStatusMapper.getDutUserRenewStatus(userId);
        if (dutUserRenewStatus == null) {
            //添加DutUserRenewStatus用户
            dutUserRenewStatus = new DutUserRenewStatus();
            dutUserRenewStatus.setUserId(userId);
            dutUserRenewStatus.setRenewCount(0);
            dutUserRenewStatus.setSerialRenewCount(0);
            dutUserRenewStatus.setSmsRemind(DutUserRenewStatus.REMIND_OFF);
            dutUserRenewStatus.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            dutUserRenewStatusMapper.insertSelective(dutUserRenewStatus);
        }
        return dutUserRenewStatus;
    }

    //@DataSource(DataSourceEnum.SLAVE)
    public DutUserRenewStatus getDutUserRenewStatus(Long userId) {
        return dutUserRenewStatusMapper.getDutUserRenewStatus(userId);
    }

    //@DataSource(DataSourceEnum.MASTER)
    public void saveNew(DutUserRenewStatus dutUserRenewStatus) {
        Objects.requireNonNull(dutUserRenewStatus, "保存DutUserRenewStatus不能为null!");

        dutUserRenewStatus.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        if (dutUserRenewStatus.getId() != null) {
            dutUserRenewStatusMapper.updateByPrimaryKeySelective(dutUserRenewStatus);
        } else {
            dutUserRenewStatusMapper.insertSelective(dutUserRenewStatus);
        }
    }

    /**
     * 续费次数自增一次
     * @param userId
     */
    //@DataSource(DataSourceEnum.MASTER)
    public void incrementRenewCount(Long userId) {
        dutUserRenewStatusMapper.incrementRenewCount(userId);
    }

    //@DataSource(DataSourceEnum.MASTER)
    public void resetAutoRenewStatus(Long userId) {
        dutUserRenewStatusMapper.resetAutoRenewStatus(userId);
    }

    //@DataSource(DataSourceEnum.MASTER)
    public void initOrIncrement(Long userId) {
        DutUserRenewStatus dutUserRenewStatus = dutUserRenewStatusMapper.getDutUserRenewStatus(userId);
        if (dutUserRenewStatus == null) {
            dutUserRenewStatus = DutUserRenewStatus.builder().userId(userId).serialRenewCount(1).renewCount(1).build();
            dutUserRenewStatusMapper.insertSelective(dutUserRenewStatus);
        } else {
            dutUserRenewStatusMapper.incrementRenewCount(userId);
        }
    }

}
