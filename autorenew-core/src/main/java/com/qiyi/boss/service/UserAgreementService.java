package com.qiyi.boss.service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

import com.qiyi.boss.dto.AgreementDutContext;
import com.qiyi.boss.dto.AgreementDutRemindContext;
import com.qiyi.boss.dto.AgreementOptDto;
import com.qiyi.boss.dto.CancelAutoRenewOptDto;
import com.qiyi.boss.dto.FamilyBindRelation;
import com.qiyi.boss.dto.UserRenewDiscountInfo;
import com.qiyi.boss.enums.AgreementStatusEnum;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.model.UserAgreementContext;
import com.qiyi.boss.model.VipUser;
import com.qiyi.vip.trade.autorenew.domain.AgreementDutMkt;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * 用户协议信息Service Created at: 2021-06-18
 *
 * <AUTHOR>
 */
public interface UserAgreementService {

    /**
     * 初始化续费金额
     */
    void initRenewPrice(Integer pricingStrategy, AgreementNoInfo agreementNoInfo, DutUserNew dutUserNew);

    /**
     * 获取自动续费协议的展示续费价格
     */
    Integer getAutoRenewDisplayRenewPrice(DutUserNew dutUserNew);

    boolean saveAutoRenew(DutUserNew dutUserNew, DutRenewSetLog setLog);

    Integer updateNextDutTimeAndExt(DutUserNew dutUserNew);

    Integer updateNextDutTimeAndDeadline(Long id, Long userId, Timestamp nextDutTime, Timestamp deadline, String ext);

    boolean updateAutoRenewAfterDut(DutUserNew dutUserNew, DutRenewSetLog setLog);

    /**
     * 纯签约方式开通协议
     */
    DutUserNew openByPureSign(VipUser vipUser, UserAgreementContext userAgreementContext, AgreementOptDto agreementOptDto);

    /**
     * 签约支付方式开通协议
     */
    DutUserNew openBySignPay(AutorenewRequest autoRenewRequest, UserAgreementContext userAgreementContext);

    /**
     * 协议置为结算中
     */
    boolean updateToSettling(DutUserNew dutUserNew, AgreementOptDto agreementOptDto);

    /**
     * 协议更新为待解约 微信支付分协议如果有待完结订单，需要先置为待解约状态，等完结后或取消0元单才能真正发起解约
     */
    boolean updateToCancelPending(DutUserNew dutUserNew, AgreementOptDto agreementOptDto);

    /**
     * 取消自动续费
     */
    boolean cancelAutoRenew(DutUserNew dutUserNew, CancelAutoRenewOptDto cancelAutoRenewOptDto);

    /**
     * 根据解绑消息解约协议
     */
    boolean cancelByAccountUnbindMsg(DutUserNew dutUserNew, AgreementTemplate agreementTemplate, AgreementOptDto agreementOptDto);

    /**
     * 续费成功后，更新DutRenewLog和 DutUserNew
     */
    DutRenewLog updateDutUserNewAndLog(Long userId, String orderCode);


    void updateDutUserNewAndLogOld(Long userId, String orderCode, Timestamp nextDutTime, Integer agreementNo);

    /**
     * 续费此时自增1
     */
    void incrementRenewCount(Long dutUserNewId, Long userId);

    /**
     * 续费次数自增1，同时重置nextDutTime
     */
    void incrementRenewCountAndResetNextDutTime(Long id, Long userId);

    /**
     * 批量更新deadline
     * @param ids 主键id列表
     * @param userId
     * @param deadline
     */
    int batchUpdateDeadline(List<Long> ids, Long userId, Timestamp deadline);

    int batchUpdateNextDutTime(List<Long> ids, Long userId, Timestamp nextDutTime);

    /**
     * 查询用户协议信息
     */
    DutUserNew getByDutTypeAndVipType(Long userId, Integer dutType, Long vipType);

    List<DutUserNew> getByVipType(Long userId, Long vipType);

    List<DutUserNew> getByVipType(Long userId, Long sourceVipType, Long vipType);

    /**
     * 根据协议类型和会员类型查询用户签约关系
     */
    List<DutUserNew> getByAgreementTypeAndVipType(Long userId, AgreementTypeEnum agreementType, Long vipType, AgreementStatusEnum agreementStatus);
    List<DutUserNew> getByExcludeAgreementTypesAndVipTypes(Long userId, List<Integer> excludeAgreementTypes, List<Long> vipTypes, AgreementStatusEnum agreementStatus);

    List<DutUserNew> getByAgreementTypeAndVipType(Long userId, AgreementTypeEnum agreementType, Long sourceVipType, Long vipType, AgreementStatusEnum agreementStatus);

    DutUserNew getBySignKey(Long userId, String signKey);

    /**
     * 查询用户协议信息
     */
    List<DutUserNew> getByAgreementNo(Long userId, Integer agreementNo, AgreementStatusEnum agreementStatus);

    DutUserNew getByAgreementNoAndVipType(Long userId, Integer agreementNo, Long vipType);

    DutUserNew getEffectiveByAgreementNoAndVipType(Long userId, Integer agreementNo, Long vipType);

    List<DutUserNew> getEffectiveByAgreementType(Long userId, Integer agreementType);

    List<DutUserNew> getEffectiveByAgreementTypeAndVipType(Long userId, AgreementTypeEnum agreementType, Long vipType);

    DutUserNew getEffectiveByAgreementNo(Long userId, Integer agreementNo);

    List<DutUserNew> getEffectiveByUserId(Long userId);

    /**
     * 查找用户最新的用户协议 优先查找生效中的协议，如果没有，则查找最近一份状态为已完结或失效的协议
     */
    Optional<DutUserNew> getUserLatestAgreement(Long userId, Integer agreementNo);

    /**
     * 根据状态返回用户所有的签约关系
     * @param userId
     * @param status
     */
    List<DutUserNew> getUserWithAllAgreements(Long userId, Integer status);

    DutUserNew getByAgreementNoAndDutType(Long userId, Integer agreementNo, Integer dutType);

    DutUserNew getByAgreementNoAndDutType(Long userId, Integer agreementNo, Integer dutType, Long vipType);

    DutUserNew getByAgreementNoAndDutType(Long userId, Integer agreementNo, Integer dutType, Long sourceVipType, Long vipType);

    DutUserNew getByAgreementNoOrDutType(Long userId, Integer agreementNo, Integer dutType, Long vipType);

    DutUserNew getByAgreementNoOrDutTypeForManagement(Long userId, Integer agreementNo, Integer dutType, Long vipType);

    /**
     * 构建代扣时所需的产品和价格信息
     */
    AgreementDutContext buildDutContext(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate, AgreementDutMkt dutMktAct);

    /**
     * 构建代扣提醒消息所需信息
     */
    AgreementDutRemindContext buildDutRemindContext(DutUserNew dutUserNew);

    /**
     *  更新用户在支付中心的签约状态
     * @param userId
     * @param dutType
     * @param status
     */
    void updateDutUserNewBindStatus(Long userId, Integer dutType, Integer status);

    /**
     * 是否可继续享受折扣优惠
     */
    UserRenewDiscountInfo canEnjoyDiscountPrice(DutUserNew dutUserNew);

    UserRenewDiscountInfo canEnjoyDiscountPrice(DutUserNew dutUserNew, AgreementNoInfo agreementNoInfo, AgreementTemplate agreementTemplate);


    void updateFamilyTypeInfo(Long userId, List<DutUserNew> familyTypeUserNews, FamilyBindRelation familyBindRelation, Timestamp smallerDeadlineNonNullValue, Timestamp sourceUidDeadline, Timestamp targetUidDeadline);

}
