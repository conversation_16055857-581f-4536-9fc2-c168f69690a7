package com.qiyi.boss.service;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.qiyi.boss.Constants;
import com.qiyi.boss.autorenew.dto.AddAutoRenewDto;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.component.FixedPriceStrategy;
import com.qiyi.boss.db.DataSource;
import com.qiyi.boss.db.DataSourceEnum;
import com.qiyi.boss.dto.OpenAutoRenewOptDto;
import com.qiyi.boss.dto.PriorityItem;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.exception.BizException;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.processor.DutProcessor;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.AgreementTemplateManager;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.service.impl.DutManager;
import com.qiyi.boss.service.impl.DutUserRenewStatusManager;
import com.qiyi.boss.service.impl.PaymentDutTypeManager;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTempPrice;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import com.qiyi.vip.trade.autorenew.service.DutRenewSetLogService;
import com.qiyi.vip.trade.qiyue.domain.QiYuePlatform;

/**
 * @auther: guojing
 * @date: 2023/4/25 6:09 PM
 * @description:
 */
@Slf4j
@Component
public class PureSignManager {

    public static final int MORE_THAN_ONE = 2;

    @Resource
    private AgreementNoInfoManager agreementNoInfoManager;
    @Resource
    private AutoRenewDutTypeManager autoRenewDutTypeManager;
    @Resource
    private DutProcessor dutProcessor;
    @Resource
    private UserAgreementService userAgreementService;
    @Resource
    private PaymentDutTypeManager paymentDutTypeManager;
    @Resource
    private DutManager dutManager;
    @Resource
    private DutRenewSetLogService dutRenewSetLogService;
    @Resource
    private DutUserRenewStatusManager dutUserRenewStatusManager;
    @Resource
    private AgreementTemplateManager agreementTemplateManager;
    @Resource
    private AutoRenewService autoRenewService;
    @Resource
    private FixedPriceStrategy fixedPriceStrategy;

    /**
     * 寻找已绑定的最高优先级的dutType
     * 1.从已绑定的列表过滤出可纯签约的
     * 2.找到优先级最高的多个代扣方式
     * 3.若未查到签约记录，则返回2中的代扣方式列表
     * 4.否则，从签约记录中找开通过的最高优先级。
     * 5.若签约过的最高优先级大于已绑定代扣方式的最大优先级，则返回空，否则返回已绑定的最高优先级代扣方式
     */
    public List<AutoRenewDutType> findMaxPriorityBindTypes(Long uid, Long vipType, Integer agreementType, List<Integer> bindTypes, String partner) {
        if (CollectionUtils.isEmpty(bindTypes)) {
            return Collections.emptyList();
        }
        List<AutoRenewDutType> validBoundPureSignDutTypes = Lists.newArrayList();
        List<Short> boundDutTypePriorityList = Lists.newArrayList();
        for (Integer bindType : bindTypes) {
            AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(bindType);
            if (validPureSignDutType(autoRenewDutType) && vipType.equals(autoRenewDutType.getVipType()) && agreementType.equals(autoRenewDutType.getAgreementType())) {
                validBoundPureSignDutTypes.add(autoRenewDutType);
                boundDutTypePriorityList.add(autoRenewDutType.getPriority());
            }
        }
        if (CollectionUtils.isEmpty(boundDutTypePriorityList)) {
            return Collections.emptyList();
        }

        boundDutTypePriorityList.sort(Comparator.reverseOrder());
        Short boundMaxPriority = boundDutTypePriorityList.get(0);
        List<AutoRenewDutType> maxPriorityBindTypes = validBoundPureSignDutTypes.stream()
            .filter(autoRenewDutType -> Objects.equals(autoRenewDutType.getPriority(), boundMaxPriority))
            .collect(Collectors.toList());
        List<DutUserNew> dutUserNewList = userAgreementService.getByAgreementTypeAndVipType(uid, AgreementTypeEnum.AUTO_RENEW, vipType, null);
        if (CollectionUtils.isEmpty(dutUserNewList)) {
            return maxPriorityBindTypes;
        }

        Optional<Short> hadOpenedDutTypeMaxPriority = dutUserNewList.stream()
            .map(dutUserNew -> autoRenewDutTypeManager.getByDutType(dutUserNew.getType()))
            .filter(this::validPureSignDutType)
            .map(AutoRenewDutType::getPriority)
            .max(Comparator.comparing(Short::intValue));
        if (boundMaxPriority < hadOpenedDutTypeMaxPriority.orElse(boundMaxPriority)) {
            return Collections.emptyList();
        }
        return maxPriorityBindTypes;
    }

    private boolean validPureSignDutType(AutoRenewDutType autoRenewDutType) {
        return autoRenewDutType != null
            && autoRenewDutType.isValid()
            && !autoRenewDutType.expired()
            && autoRenewDutType.isAutoRenewAgreementType()
            && !autoRenewDutType.thirdDut()
            && !autoRenewDutType.isMobileDutType()
            && autoRenewDutType.getSourceVipType() == null;
    }

    public List<DutUserNew> openAutoRenewUnderBinding(String fc, String fv, QiYuePlatform qiYuePlatform, VipUser vipUser, List<AutoRenewDutType> bindTypes, Integer agreementType) {
        List<AutoRenewDutType> wechatBindDutTypes = bindTypes.stream()
            .filter(AutoRenewDutType::isMainlandWechatDutType)
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(wechatBindDutTypes)) {
            return doIfWechatBinded(bindTypes, wechatBindDutTypes, qiYuePlatform, vipUser, fc, fv, agreementType);
        } else {
            return doIfWechatNotBinded(bindTypes, qiYuePlatform, vipUser, fc, fv, agreementType);
        }
    }

    private List<DutUserNew> doIfWechatBinded(List<AutoRenewDutType> bindTypesList, List<AutoRenewDutType> wechatBindTypesList,
        QiYuePlatform qiYuePlatform, VipUser vipUser, String fc, String fv, Integer agreementType
    ) {
        Long uid = vipUser.getId();
        List<Integer> supportDirectOpenDutTypes = dutManager.getSupportDirectOpenDutTypes(uid, vipUser.getTypeId(), agreementType);
        List<DutUserNew> dutUserNews = new ArrayList<>();
        if (wechatBindTypesList.size() == 1) {
            Integer amount = paymentDutTypeManager.getWechatAmount(wechatBindTypesList.get(0).getDutType());
            for (AutoRenewDutType item : bindTypesList) {
                Integer dutType = item.getDutType();
                if (CollectionUtils.isNotEmpty(supportDirectOpenDutTypes) && !supportDirectOpenDutTypes.contains(dutType)) {
                    continue;
                }
                DutUserNew dutUserNew = openAutoRenewByTypeAndAmount(item, amount, qiYuePlatform, vipUser, fc, fv, OperateSceneEnum.OPEN_DIRECT.getValue());
                if (dutUserNew != null) {
                    dutUserNews.add(dutUserNew);
                }
            }
        } else {
            Integer wechatLatestType = wechatBindTypesList.get(wechatBindTypesList.size() - 1).getDutType();
            Integer amount = paymentDutTypeManager.getWechatAmount(wechatLatestType);
            List<Integer> wechatDutTypeList = autoRenewDutTypeManager.getDutTypeListByVipTypeAndPayChannel(vipUser.getTypeId(), agreementType, PaymentDutType.PAY_CHANNEL_WECHAT);
            for (AutoRenewDutType item : bindTypesList) {
                Integer dutType = item.getDutType();
                if (wechatDutTypeList.contains(dutType) && !dutType.equals(wechatLatestType)) {
                    continue;
                }
                if (CollectionUtils.isNotEmpty(supportDirectOpenDutTypes) && !supportDirectOpenDutTypes.contains(dutType)) {
                    continue;
                }
                DutUserNew dutUserNew = openAutoRenewByTypeAndAmount(item, amount, qiYuePlatform, vipUser, fc, fv, OperateSceneEnum.OPEN_DIRECT.getValue());
                if (dutUserNew != null) {
                    dutUserNews.add(dutUserNew);
                }
            }
        }
        return dutUserNews;
    }

    private List<DutUserNew> doIfWechatNotBinded(List<AutoRenewDutType> bindTypesList, QiYuePlatform qiYuePlatform, VipUser vipUser, String fc, String fv, Integer agreementType) {
        Long uid = vipUser.getId();
        Integer amount = dutRenewSetLogService.getLastRecentOpenAmountFromSetLog(uid, agreementType, vipUser.getTypeId());
        List<Integer> supportDirectOpenDutTypes = dutManager.getSupportDirectOpenDutTypes(uid, vipUser.getTypeId(), agreementType);
        List<DutUserNew> dutUserNews = new ArrayList<>();
        for (AutoRenewDutType item : bindTypesList) {
            Integer dutType = item.getDutType();
            if (CollectionUtils.isNotEmpty(supportDirectOpenDutTypes) && !supportDirectOpenDutTypes.contains(dutType)) {
                continue;
            }
            DutUserNew dutUserNew = openAutoRenewByTypeAndAmount(item, amount, qiYuePlatform, vipUser, fc, fv, OperateSceneEnum.OPEN_DIRECT.getValue());
            if (dutUserNew != null) {
                dutUserNews.add(dutUserNew);
            }
        }
        return dutUserNews;
    }

    public DutUserNew openAutoRenewByTypeAndAmount(AutoRenewDutType autoRenewDutType, Integer amount, QiYuePlatform qiYuePlatform,
        VipUser vipUser, String fc, String fv, String operateScene) {
        Long uid = vipUser.getId();
        if (autoRenewDutType.getPayChannelType() != PaymentDutType.PAY_CHANNEL_TYPE_VIP_DUT_PAY) {
            return null;
        }
        Integer dutType = autoRenewDutType.getDutType();
        Integer renewPrice = paymentDutTypeManager.getRenewPrice(vipUser.getTypeId(), amount, null, dutType, uid);
        if (renewPrice == null) {
            return null;
        }

        AddAutoRenewDto addAutoRenewDto = AddAutoRenewDto.builder()
            .userId(uid)
            .dutType(dutType)
            .renewPrice(renewPrice)
            .platformId(qiYuePlatform.getId())
            .platformCode(qiYuePlatform.getCode())
            .vipUser(vipUser)
            .amount(amount)
            .fc(fc)
            .fv(fv)
            .operateScene(operateScene)
            .payChannel(autoRenewDutType.getPayChannel())
            .agreementType(autoRenewDutType.getAgreementType())
            .build();
        DutUserNew dutUserNew = dutManager.addAutoRenewNew(addAutoRenewDto);
        dutUserRenewStatusManager.addUserRenewStatusIfNotExisted(uid);
        return dutUserNew;
    }

    /**
     * 寻找已绑定的最高优先级的协议编号，如果partner不为null，还要判断partner是否相等
     * 返回的List<AgreementNoInfo>协议号amount一定是一致的
     */
    public List<AgreementNoInfo> findMaxPriorityBindTypesNew(Long uid, Long vipType, Integer agreementType, List<Integer> bindTypes, String partner, Integer amount, Integer payChannel) {
        if (CollectionUtils.isEmpty(bindTypes)) {
            return Collections.emptyList();
        }
        List<AgreementNoInfo> validPureSignDutTypes = Lists.newArrayList();
        List<Short> bindedDutTypePriorityList = Lists.newArrayList();
        for (Integer bindType : bindTypes) {
            List<AgreementNoInfo> agreementNoInfos = agreementNoInfoManager.getByDutType(bindType)
                .stream()
                .filter(d -> agreementType.equals(d.getType()))
                .filter(a -> a.getAmount().equals(amount))
                .filter(a -> payChannel == null || a.getPayChannel().equals(payChannel))
                .collect(Collectors.toList());
            for (AgreementNoInfo agreementNoInfo : agreementNoInfos) {
                if (validPureSignAgreementNo(agreementNoInfo) && vipType.equals(agreementNoInfo.getVipType())) {
                    validPureSignDutTypes.add(agreementNoInfo);
                    bindedDutTypePriorityList.add(agreementNoInfo.getPriority());
                }
            }
        }
        if (CollectionUtils.isEmpty(bindedDutTypePriorityList)) {
            return Collections.emptyList();
        }

        bindedDutTypePriorityList.sort(Comparator.reverseOrder());
        Short boundMaxPriority = bindedDutTypePriorityList.get(0);
        List<AgreementNoInfo> maxPriorityBindTypes = validPureSignDutTypes.stream()
            .filter(agreementNoInfo -> Objects.equals(agreementNoInfo.getPriority(), boundMaxPriority))
            .filter(agreementNoInfo -> Objects.equals(partner, agreementNoInfo.getPartnerId()))
            .collect(Collectors.toList());
        List<DutUserNew> dutUserNewList = userAgreementService.getByAgreementTypeAndVipType(uid, AgreementTypeEnum.valueOf(agreementType), vipType, null);
        if (CollectionUtils.isEmpty(dutUserNewList)) {
            return maxPriorityBindTypes;
        }

        List<PriorityItem> priorityItems = dutUserNewList.stream()
            .map(dutUserNew -> dutUserNew.getAgreementNo() != null
                ? agreementNoInfoManager.getById(dutUserNew.getAgreementNo())
                : autoRenewDutTypeManager.getByDutType(dutUserNew.getType())
            )
            .filter(item ->
                item instanceof AgreementNoInfo
                    ? validPureSignAgreementNo((AgreementNoInfo) item)
                    : validPureSignDutType((AutoRenewDutType) item)
            )
            .map(item -> {
                if (item instanceof AgreementNoInfo) {
                    AgreementNoInfo agreementNoInfo = (AgreementNoInfo) item;
                    return new PriorityItem(agreementNoInfo, agreementNoInfo.getPriority(), agreementNoInfo.getPartnerId());
                } else {
                    AutoRenewDutType dutType = (AutoRenewDutType) item;
                    return new PriorityItem(dutType, dutType.getPriority(), dutType.getPartnerId());
                }
            })
            .filter(ObjectUtil::isNotNull)
            .sorted(Comparator.comparing(PriorityItem::getPriority, Comparator.reverseOrder())) // 使用Comparator进行倒序排序
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(priorityItems)) {
            return Collections.emptyList();
        }
        Short maxpriority = priorityItems.get(0).getPriority();
        if (maxpriority == null) {
            return Collections.emptyList();
        }
        boolean noneMatchPartner = priorityItems.stream()
            .filter(item -> item.getPriority().equals(maxpriority))
            .noneMatch(item -> Objects.equals(item.getParterId(), partner));
        if (!bindedDutTypePriorityList.contains(maxpriority) || noneMatchPartner) {
            return Collections.emptyList();
        }
        return validPureSignDutTypes.stream()
            .filter(item -> item.getPriority().equals(maxpriority))
            .filter(item -> Objects.equals(item.getPartnerId(), partner))
            .collect(Collectors.toList());
    }

    private boolean validPureSignAgreementNo(AgreementNoInfo agreementNoInfo) {
        return agreementNoInfo != null
            && agreementNoInfo.online()
            && agreementNoInfo.isAutoRenewType()
            && !agreementNoInfo.thirdDut()
            && !agreementNoInfo.isMobileChannel()
            && agreementNoInfo.defaultNo()
            && agreementNoInfo.getSourceVipType() == null;
    }

    public List<DutUserNew> openAutoRenewUnderBindingNew(String fc, String fv, QiYuePlatform qiYuePlatform, VipUser vipUser, List<AgreementNoInfo> bindTypes, Integer agreementType, Integer amount) {
        List<AgreementNoInfo> wechatBindDutTypes = bindTypes.stream()
            .filter(AgreementNoInfo::isMainlandWechatChannel)
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(wechatBindDutTypes)) {
            return doIfWechatBindedNew(bindTypes, wechatBindDutTypes, qiYuePlatform, vipUser, fc, fv, agreementType, amount);
        } else {
            return doIfWechatNotBindedNew(bindTypes, qiYuePlatform, vipUser, fc, fv, agreementType, amount);
        }
    }

    private List<DutUserNew> doIfWechatBindedNew(List<AgreementNoInfo> bindTypesList, List<AgreementNoInfo> wechatBindTypesList,
        QiYuePlatform qiYuePlatform, VipUser vipUser, String fc, String fv, Integer agreementType, Integer amount) {
        List<AgreementNoInfo> supportDirectOpenDutTypes = getSupportDirectOpenDutTypes(vipUser.getId(), vipUser.getTypeId(), agreementType);
        Short maxPriority = CollectionUtils.isNotEmpty(supportDirectOpenDutTypes) ? supportDirectOpenDutTypes.get(0).getPriority() : null;
        Map<Integer, List<AgreementNoInfo>> supportDirectOpenDutTypeMap = supportDirectOpenDutTypes.stream()
            .collect(Collectors.groupingBy(AgreementNoInfo::getDutType));
        List<DutUserNew> dutUserNews = new ArrayList<>();
        // 只有1个微信签约关系
        if (wechatBindTypesList.size() == 1) {
            for (AgreementNoInfo item : bindTypesList) {
                Integer dutType = item.getDutType();
                if (CollectionUtils.isNotEmpty(supportDirectOpenDutTypes) && !supportDirectOpenDutTypeMap.containsKey(dutType)) {
                    continue;
                }
                if (!Objects.equals(amount, item.getAmount())) {
                    continue;
                }
                if (maxPriority != null && !Objects.equals(item.getPriority(), maxPriority)) {
                    continue;
                }
                DutUserNew dutUserNew = openAutoRenewByTypeAndAmountNew(item, qiYuePlatform, vipUser, fc, fv, OperateSceneEnum.OPEN_DIRECT);
                if (dutUserNew != null) {
                    dutUserNews.add(dutUserNew);
                }
            }
        } else {
            for (AgreementNoInfo item : bindTypesList) {
                Integer dutType = item.getDutType();
                if (CollectionUtils.isNotEmpty(supportDirectOpenDutTypes) && !supportDirectOpenDutTypeMap.containsKey(dutType)) {
                    continue;
                }
                if (!Objects.equals(amount, item.getAmount())) {
                    continue;
                }
                if (maxPriority != null && !Objects.equals(item.getPriority(), maxPriority)) {
                    continue;
                }
                DutUserNew dutUserNew = openAutoRenewByTypeAndAmountNew(item, qiYuePlatform, vipUser, fc, fv, OperateSceneEnum.OPEN_DIRECT);
                if (dutUserNew != null) {
                    dutUserNews.add(dutUserNew);
                }
            }
        }
        return dutUserNews;
    }

    private List<DutUserNew> doIfWechatNotBindedNew(List<AgreementNoInfo> bindTypesList, QiYuePlatform qiYuePlatform,
        VipUser vipUser, String fc, String fv, Integer agreementType, Integer amount) {
        Long uid = vipUser.getId();
        List<AgreementNoInfo> supportDirectOpenDutTypes = getSupportDirectOpenDutTypes(uid, vipUser.getTypeId(), agreementType);
        Short maxPriority = CollectionUtils.isNotEmpty(supportDirectOpenDutTypes) ? supportDirectOpenDutTypes.get(0).getPriority() : null;
        Map<Integer, List<AgreementNoInfo>> supportDirectOpenDutTypeMap = supportDirectOpenDutTypes.stream()
            .collect(Collectors.groupingBy(AgreementNoInfo::getDutType));
        List<DutUserNew> dutUserNews = new ArrayList<>();
        for (AgreementNoInfo item : bindTypesList) {
            Integer dutType = item.getDutType();
            if (CollectionUtils.isNotEmpty(supportDirectOpenDutTypes) && !supportDirectOpenDutTypeMap.containsKey(dutType)) {
                continue;
            }
            if (!Objects.equals(amount, item.getAmount())) {
                continue;
            }
            if (maxPriority != null && !Objects.equals(item.getPriority(), maxPriority)) {
                continue;
            }
            DutUserNew dutUserNew = openAutoRenewByTypeAndAmountNew(item, qiYuePlatform, vipUser, fc, fv, OperateSceneEnum.OPEN_DIRECT);
            if (dutUserNew != null) {
                dutUserNews.add(dutUserNew);
            }
        }
        return dutUserNews;
    }

    private List<AgreementNoInfo> getSupportDirectOpenDutTypes(Long uid, Long vipType, Integer agreementType) {
        List<DutUserNew> dutUserNewList = userAgreementService.getByAgreementTypeAndVipType(uid, AgreementTypeEnum.valueOf(agreementType), vipType, null);
        if (CollectionUtils.isEmpty(dutUserNewList)) {
            return Collections.emptyList();
        }

        List<AgreementNoInfo> agreementNoInfos = Lists.newArrayList();
        for (DutUserNew dutUserNew : dutUserNewList) {
            AgreementNoInfo agreementNoInfo = null;
            if (dutUserNew.getAgreementNo() != null) {
                agreementNoInfo = agreementNoInfoManager.getById(dutUserNew.getAgreementNo());
            } else {
                agreementNoInfo = agreementNoInfoManager.getByDutTypeAndAmount(dutUserNew.getType(), dutUserNew.getAmount());
            }
            if (agreementNoInfo != null && !agreementNoInfo.thirdDut() && agreementNoInfo.online()) {
                agreementNoInfos.add(agreementNoInfo);
            }
        }
        boolean hasDiffDutTypePriority = agreementNoInfos.stream().map(AgreementNoInfo::getPriority).distinct().count() >= MORE_THAN_ONE;
        if (!hasDiffDutTypePriority) {
            return Collections.emptyList();
        }

        return getMaxPriorityBindTypes(agreementNoInfos);
    }

    private List<AgreementNoInfo> getMaxPriorityBindTypes(List<AgreementNoInfo> agreementNoInfos) {
        short maxPriority = agreementNoInfos.stream()
            .max(Comparator.comparing(AgreementNoInfo::getPriority))
            .map(AgreementNoInfo::getPriority).orElse((short) 0);
        if (maxPriority == 0) {
            return Collections.emptyList();
        }
        return agreementNoInfos.stream()
            .filter(autoRenewDutType -> autoRenewDutType.getPriority() == maxPriority)
            .collect(Collectors.toList());
    }

    public DutUserNew openAutoRenewByTypeAndAmountNew(AgreementNoInfo agreementNoInfo, QiYuePlatform qiYuePlatform,
        VipUser vipUser, String fc, String fv, OperateSceneEnum operateScene) {
        if (agreementNoInfo.getPayChannelType() != PaymentDutType.PAY_CHANNEL_TYPE_VIP_DUT_PAY) {
            return null;
        }
        return openAutoRenewByPureSign(agreementNoInfo, qiYuePlatform, vipUser, fc, fv, null, null, operateScene);
    }

    public DutUserNew openAutoRenewByPureSign(AgreementNoInfo agreementNoInfo, QiYuePlatform qiYuePlatform, VipUser vipUser,
        String fc, String fv, String thirdUid, Integer signScene, OperateSceneEnum operateScene) {
        Long uid = vipUser.getId();

        AgreementTempPrice priceInfo = fixedPriceStrategy.getPriceInfo(agreementNoInfo.getTemplateCode(), Constants.ZERO_PERIOD_NO);
        if (priceInfo == null) {
            priceInfo = fixedPriceStrategy.getPriceInfo(agreementNoInfo.getId(), Constants.ZERO_PERIOD_NO);
        }

        if (priceInfo == null) {
            log.error("未查询到协议对应的价格, agreementNo:{}, payChannel:{}", agreementNoInfo.getId(), agreementNoInfo.getPayChannel());
            throw BizException.newSystemException("未查询到协议对应的价格");
        }

        OpenAutoRenewOptDto openAutoRenewOptDto = OpenAutoRenewOptDto.builder()
            .userId(uid)
            .dutType(agreementNoInfo.getDutType())
            .agreementNo(agreementNoInfo.getId())
            .renewPrice(priceInfo.getPrice())
            .platformId(qiYuePlatform.getId())
            .platformCode(qiYuePlatform.getCode())
            .amount(agreementNoInfo.getAmount())
            .fc(fc)
            .fv(fv)
            .thirdUid(thirdUid)
            .signScene(signScene)
            .operateScene(operateScene)
            .payChannel(agreementNoInfo.getPayChannel())
            .agreementType(agreementNoInfo.getType())
            .build();
        DutUserNew dutUserNew = autoRenewService.openAutoRenew(vipUser, agreementNoInfo, openAutoRenewOptDto);
        dutUserRenewStatusManager.addUserRenewStatusIfNotExisted(uid);
        return dutUserNew;
    }

    @DataSource(DataSourceEnum.SLAVE)
    public AgreementNoInfo findPureSignDutType(Long userId, Long sourceVipType, Long vipType, Integer agreementType, Integer payChannel, Integer amount, String partner) {
        Short priority = getPriority(userId, sourceVipType, vipType, agreementType, payChannel, amount, partner);
        return findPureSignAgreementNoWithPriority(sourceVipType, vipType, agreementType, payChannel, amount, priority, partner);
    }

    public Short getPriority(Long userId, Long sourceVipType, Long vipType, Integer agreementType, Integer payChannel, Integer amount, String partner) {
        List<AgreementNoInfo> autoRenewAgreementInfos = agreementNoInfoManager.getAutoRenewAgreement(sourceVipType, vipType, agreementType, amount, null, null)
            .stream()
            .filter(a -> Objects.equals(a.getPartnerId(), partner))
            .collect(Collectors.toList());

        List<AgreementNoInfo> payChannelAutoRenewAgreementInfos = autoRenewAgreementInfos.stream()
            .filter(agreementNoInfo -> Objects.equals(agreementNoInfo.getPayChannel(), payChannel))
            .collect(Collectors.toList());
        Short existMaxPriority = getMaxPriority(autoRenewAgreementInfos);
        Short existMaxPriorityOfPayChannel = getMaxPriority(payChannelAutoRenewAgreementInfos);

        // 非自动续费用户签约当前最高优先级代扣方式
        List<DutUserNew> dutUserNewList = userAgreementService.getByAgreementTypeAndVipType(userId, AgreementTypeEnum.valueOf(agreementType), vipType, null);
        boolean autoRenewStatus = dutUserNewList.stream().anyMatch(DutUserNew::isAutoRenewUser);
        if (!autoRenewStatus) {
            return existMaxPriorityOfPayChannel;
        }
        boolean sameAmount = dutUserNewList.stream()
            .filter(DutUserNew::isAutoRenewUser)
            .allMatch(dutUserNew -> Objects.equals(amount, dutUserNew.getAmount()));
        if (!sameAmount) {
            return existMaxPriorityOfPayChannel;
        }

        List<AgreementNoInfo> dutUserAgreementNoInfoList = dutUserNewList.stream()
            .filter(DutUserNew::isAutoRenewUser)
            .map(dutUserNew -> {
                if (dutUserNew.getAgreementNo() != null) {
                    return agreementNoInfoManager.getById(dutUserNew.getAgreementNo());
                } else {
                    return agreementNoInfoManager.getByDutTypeAndAmount(dutUserNew.getType(), dutUserNew.getAmount());
                }
            })
            .collect(Collectors.toList());

        Short currentMaxPriority = getMaxPriority(dutUserAgreementNoInfoList);
        boolean isPriceInsured = existMaxPriority != null && currentMaxPriority != null && existMaxPriority > currentMaxPriority;;
        // 非保价用户签约最高优先级代扣方式
        if (!isPriceInsured) {
            return existMaxPriorityOfPayChannel;
        }
        boolean isBeingPriceInsuredPeriod = dutUserAgreementNoInfoList.stream().noneMatch(AgreementNoInfo::offline);
        // 非保价期内用户签约最高优先级代扣方式
        if (!isBeingPriceInsuredPeriod) {
            return existMaxPriorityOfPayChannel;
        }

        List<AgreementNoInfo> samePriorityAgreementNoInfoList = payChannelAutoRenewAgreementInfos.stream()
            .filter(agreementNoInfo -> Objects.equals(currentMaxPriority, agreementNoInfo.getPriority()))
            .collect(Collectors.toList());
        // 当前渠道存在与已开通渠道优先级一致的代扣方式，签约当前优先级代扣方式
        // 否则签约目标渠道下最高优先级代扣方式
        if (CollectionUtils.isNotEmpty(samePriorityAgreementNoInfoList)) {
            return currentMaxPriority;
        } else {
            return existMaxPriorityOfPayChannel;
        }
    }

    private Short getMaxPriority(List<AgreementNoInfo> agreementNoInfos) {
        Optional<AgreementNoInfo> existMaxPriorityOptional = agreementNoInfos.stream()
            .max(Comparator.comparing(AgreementNoInfo::getPriority));
        return existMaxPriorityOptional.map(AgreementNoInfo::getPriority).orElse(null);
    }

    /**
     * 查找支持纯签约的协议号.
     * @param vipType 会员类型
     * @param payChannel 渠道类型
     * @param amount 签约时长
     */
    @DataSource(DataSourceEnum.SLAVE)
    public AgreementNoInfo findPureSignAgreementNoWithPriority(Long sourceVipType, Long vipType, Integer agreementType, Integer payChannel, Integer amount, Short priority, String partner) {
        List<AgreementNoInfo> autoRenewAgreementInfos = agreementNoInfoManager.getAutoRenewAgreement(sourceVipType, vipType, agreementType, amount, payChannel, priority);
        if (CollectionUtils.isEmpty(autoRenewAgreementInfos)) {
            return null;
        }
        if (sourceVipType == null) {
            autoRenewAgreementInfos = autoRenewAgreementInfos.stream()
                .filter(agreementNoInfo -> agreementNoInfo.getSourceVipType() == null)
                .collect(Collectors.toList());
        }
        List<AgreementNoInfo> agreementNoInfoList = autoRenewAgreementInfos.stream()
            .filter(AgreementNoInfo::supportPureSign)
            .filter(AgreementNoInfo::online)
            .filter(AgreementNoInfo::defaultNo)
            .filter(a -> Objects.equals(a.getPartnerId(), partner))
            .collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(agreementNoInfoList) ? agreementNoInfoList.get(0) : null;
    }


}
