package com.qiyi.boss.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import com.qiyi.boss.utils.RespResultUtils;

import static com.qiyi.boss.Constants.SUCCESS_CODE;

@NoArgsConstructor
@Setter
@Getter
@ToString
public class BaseResponse<T> {

    private String code;
    private String msg;
    private String message;
    private T data;
    private T dataList;

    public BaseResponse setData(T data) {
        this.data = data;
        return this;
    }

    public T getData() {
        if (data != null) {
            return data;
        }
        return dataList;
    }

    public BaseResponse(CodeEnum responseCode) {
        this.code = responseCode.getCode();
        this.msg = responseCode.getMsg();
        this.message = responseCode.getMsg();
    }

    public BaseResponse(CodeEnum responseCode, String msg) {
        this(responseCode);
        this.msg = msg;
        this.message = msg;
    }

    public BaseResponse(String code, String msg) {
        this.code = code;
        this.msg = msg;
        this.message = msg;
    }

    public static <T> BaseResponse<T> create(CodeEnum responseCode) {
        return new BaseResponse<>(responseCode);
    }

    public static <T> BaseResponse<T> create(String code, String msg) {
        return new BaseResponse<>(code, msg);
    }

    public static <T> BaseResponse<T> createSuccess(T data) {
        BaseResponse<T> response = new BaseResponse<>(CodeEnum.SUCCESS);
        response.setData(data);
        return response;
    }

    public void responseJsonOrJsonp(final String callback, final String... headers) {
        RespResultUtils.responseJsonOrJsonp(callback, this);
    }

    public static <T> BaseResponse<T> success() {
        return new BaseResponse<T>(CodeEnum.SUCCESS);
    }

    public boolean isFailure() {
        return !CodeEnum.isSuccess(code);
    }

    public boolean isSuccess() {
        return CodeEnum.isSuccess(code);
    }

    public boolean isSystemError() {
        return CodeEnum.ERROR_SYSTEM.getCode().equals(code);
    }

    public boolean outerInvokeError() {
        return CodeEnum.OUTER_SERVICE_ERROR.getCode().equals(code);
    }

    public static <T> BaseResponse<T> notLoggedIn() {
        return new BaseResponse<T>(CodeEnum.ERROR_LOGIN);
    }

    public static <T> BaseResponse<T> notAutoRenewUser() {
        return new BaseResponse<T>(CodeEnum.ERROR_USER_NOT_AUTORENEW);
    }

    public static <T> BaseResponse<T> paramError() {
        return new BaseResponse<T>(CodeEnum.ERROR_PARAM);
    }

    public static <T> BaseResponse<T> systemError() {
        return new BaseResponse<T>(CodeEnum.ERROR_SYSTEM);
    }

    public enum CodeEnum {

        SUCCESS("A00000", "处理成功"),
        DUPLICATION("A00001","重复请求"),
        PART_SUCCESS("A00010", "部分处理成功"),
        ERROR_SYSTEM("Q00332", "系统错误"),
        ERROR_PARAM("Q00301", "参数错误"),
        INVALID_SIGN("Q00302", "签名错误"),
        ERROR_LOGIN("Q00304", "用户未登陆"),
        ERROR_USER_NOT_AUTORENEW("Q00305","用户未开通自动续费"),
        ALREADY_SIGNED("Q00307","已签约，无需重复签约"),
        ACCESS_DENY("Q00347", "无权访问"),
        ERROR_NO_BIND("Q00362", "未绑定,不能操作自动续费"),
        NON_VIP_NOT_OPT_AUTO_RENEW("Q00363", "非会员不能操作自动续费"),
        ERROR_RESTRICT_CANCELLATION("Q00364", "限制取消自动续费"),
        FAILED_QUERY_USER_DUT_DISCOUNTS("Q00365", "查询用户立减优惠信息失败"),
        OUTER_SERVICE_ERROR("Q00366", "请求外部服务失败，请稍后重试"),
        MANUAL_DUT_TIMES_LIMIT("Q00345","主动代扣次数超限"),
        NO_VALID_PAY_TYPE("Q00341","未找到合适的支付方式"),
        WAIT_MANUAL_DUT_RESULT("Q00342","主动代扣已发起，等待支付结果中"),
        QUERY_BIND_INFO_FAILED("Q00343","查询绑定用户权益失败"),


        ACCOUNT_UNBIND_EXCEPTION("Q00416", "账户中心解绑超时"),
        DUT_BIND_EXCEPTION("Q00417", "账户中心签约出现异常"),
        REFUND_EXCEPTION("Q00419", "退款时出现异常"),
        CLOSE_ORDER_EXCEPTION("Q00420", "调用支付中心取消订单时出现异常"),
        WECHAT_PAY_SCORE_ORDER_UN_COMPLETE("Q00423", "您有未支付订单，请支付完成后使用该服务"),

        PASSWORD_FREE_REGISTER_ERROR("Q00424", "请求自动续费营销登记免密活动出现异常"),
        ;

        private String code;

        private String msg;

        CodeEnum(String code, String desc) {
            this.code = code;
            this.msg = desc;
        }

        public String getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }


        public static boolean isSuccess(String code) {
            return SUCCESS.getCode().equals(code) || DUPLICATION.getCode().equals(code) || SUCCESS_CODE.equals(code);
        }
    }
}
