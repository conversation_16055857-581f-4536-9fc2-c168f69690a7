package com.qiyi.boss.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.MapUtils;
import java.sql.Timestamp;
import java.util.Map;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.utils.DateHelper;
/**
 * Created at: 2021-07-01
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AccountChangeMsg {

    /**
     * 用户渠道解绑
     */
    public static final String SOURCE_UNBIND_OUTSIDE = "1";
    /**
     * 客服和业务后台解绑
     */
    public static final String SOURCE_UNBIND_KEFU = "2";
    /**
     * 取消续费后解约
     */
    public static final String SOURCE_UNBIND_AFTER_CANCELED = "3";

    //签约
    public static final String ACCOUNT_DUT_BIND = "account_dut_bind";
    //解约
    public static final String ACCOUNT_DUT_UNBIND = "account_dut_unbind";
    //解约中
    public static final String ACCOUNT_DUT_UNBINDING = "account_dut_unbinding";

    /**
     * 芝麻go协议退出方式 用户在支付宝侧主动退出
     */
    private static final String ZHIMA_GO_QUIT_TYPE_USER_CANCEL_QUIT = "USER_CANCEL_QUIT";
    /**
     * 芝麻go协议退出方式 协议到期退出
     */
    private static final String ZHIMA_GO_QUIT_TYPE_EXPIRE_QUIT = "EXPIRE_QUIT";
    /**
     * 芝麻go协议退出方式 商户结算退出，或支付宝扣款失败，支付宝发起解约
     */
    private static final String ZHIMA_GO_QUIT_TYPE_SETTLE_APPLY_QUIT = "SETTLE_APPLY_QUIT";
    /**
     * 芝麻go协议退出方式 扣款超时退出
     */
    private static final String ZHIMA_GO_QUIT_TYPE_SETTLE_PAY_TIMEOUT_QUIT = "SETTLE_PAY_TIMEOUT_QUIT";

    private Long uid;
    /**
     * 代扣方式
     */
    private Integer type;
    private Integer agreementType;
    private String source;
    private Timestamp operateTime;
    private String msgType;
    private String signType;
    private String quitType;
    private String thirdUserId;
    private String thirdAccount;
    private String signCode;
    /**
     * 业务方订单号
     */
    private String partnerOrderNo;
    public static AccountChangeMsg buildFromMsg(Map<String, String> content, Integer agreementType) {
        Long uid = Long.parseLong(MapUtils.getString(content, "uid"));
        Integer type = Integer.parseInt(MapUtils.getString(content, "type"));
        String source = MapUtils.getString(content, "source");
        String operateTime = MapUtils.getString(content, "operateTime");
        return AccountChangeMsg.builder()
                .uid(uid)
                .type(type)
                .agreementType(agreementType)
                .source(source)
                .operateTime(operateTime != null ? DateHelper.getTimestamp(operateTime) : null)
                .msgType(MapUtils.getString(content, "msgtype"))
                .signType(MapUtils.getString(content, "signType"))
                .quitType(MapUtils.getString(content, "quit_type"))
                .thirdUserId(MapUtils.getString(content, "thirdUserId"))
                .thirdAccount(MapUtils.getString(content, "thirdAccount"))
                .signCode(MapUtils.getString(content, "signCode"))
                .partnerOrderNo(MapUtils.getString(content, "partnerOrderNo", null))
                .build();
    }

    /**
     * 解绑消息
     */
    public boolean isUnbindMsg() {
        return ACCOUNT_DUT_UNBIND.equals(msgType);
    }

    /**
     * 绑定消息
     */
    public boolean isBindMsg() {
        return ACCOUNT_DUT_BIND.equals(msgType);
    }

    /**
     * 解约中消息
     */
    public boolean isUnbindingMsg() {
        return ACCOUNT_DUT_UNBINDING.equals(msgType);
    }

    /**
     * 自动续费协议消息
     */
    public boolean isAutoRenewMsg() {
        return AgreementTypeEnum.AUTO_RENEW.getValue() == agreementType
            || AgreementTypeEnum.FAMILY_CARD_PACKAGE.getValue() == agreementType
            || AgreementTypeEnum.jointType(agreementType);
    }

    /**
     * 芝麻GO消息
     */
    public boolean isZhiMaGoMsg() {
        return AgreementTypeEnum.ZHIMA_GO.getValue() == agreementType;
    }

    /**
     * 微信支付分消息
     */
    public boolean isWeChatPayScoreMsg() {
        return AgreementTypeEnum.WECHAT_PAY_SCORE.getValue() == agreementType;
    }

    /**
     * 免密消息
     */
    public boolean isPasswordFreeMsg() {
        return AgreementTypeEnum.PASSWORD_FREE.getValue() == agreementType;
    }

    public OperateSceneEnum getOperateScene() {
        if (isWeChatPayScoreMsg()) {
            return OperateSceneEnum.CANCEL_UNBIND_OUTSIDE;
        }
        OperateSceneEnum operateScene = OperateSceneEnum.CANCEL_INSIDE;
        if (isZhiMaGoMsg()) {
            if (ZHIMA_GO_QUIT_TYPE_EXPIRE_QUIT.equals(quitType)) {
                operateScene = OperateSceneEnum.CANCEL_AGREEMENT_PERIODS_REACHED;
            }
            if (ZHIMA_GO_QUIT_TYPE_USER_CANCEL_QUIT.equals(quitType)) {
                operateScene = OperateSceneEnum.CANCEL_UNBIND_OUTSIDE;
            }
            if (ZHIMA_GO_QUIT_TYPE_SETTLE_PAY_TIMEOUT_QUIT.equals(quitType)) {
                operateScene = OperateSceneEnum.CANCEL_PAY_TIMEOUT;
            }
            if (ZHIMA_GO_QUIT_TYPE_SETTLE_APPLY_QUIT.equals(quitType)) {
                operateScene = OperateSceneEnum.CANCEL_INSIDE;
            }
        }
        return operateScene;
    }

    /**
     * 自动续费解绑消息获取操作场景
     */
    public OperateSceneEnum getAutoRenewOperateScene() {
        if (SOURCE_UNBIND_OUTSIDE.equals(source)) {
            return OperateSceneEnum.CANCEL_UNBIND_OUTSIDE;
        }
        if (SOURCE_UNBIND_KEFU.equals(source)) {
            return OperateSceneEnum.CANCEL_UNBIND_KEFU;
        }
        if (SOURCE_UNBIND_AFTER_CANCELED.equals(source)) {
            return OperateSceneEnum.CANCEL_UNBIND_AFTER_CANCELED;
        }
        return OperateSceneEnum.CANCEL_UNBIND;
    }

}
