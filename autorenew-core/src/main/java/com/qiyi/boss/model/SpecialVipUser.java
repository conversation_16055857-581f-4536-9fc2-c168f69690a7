package com.qiyi.boss.model;

import com.qiyi.vip.commons.component.dto.VipInfo;
import lombok.Data;

import java.sql.Timestamp;

/**
 * Created by IntelliJ IDEA.
 * <AUTHOR>
 * Date: 15-9-10
 * Time: 下午4:41
 * To change this template use File | Settings | File Templates.
 */
@Data
public class SpecialVipUser {

    private Long id;
    /**
     * 对应USER的id
     */
    private Long userId;

    /**
     * 会员类型(1 )
     */
    private Integer type;
    /**
     * 会员加入时间
     */
    private Timestamp createTime;

    /**
     * 会员到期日期
     */
    private Timestamp deadline;

    /**
     * 状态：有效，无效...
     */
    private Integer status;

    /**自动续费状态*/

    private Integer autoRenew;

    /**
     * 过期标识: 0:未过期, 1:已过期
     */
    private Integer expire;

    public VipUser toVipUser() {
        VipUser vipUser = new VipUser();
        vipUser.setId(userId);
        vipUser.setTypeId(getType().longValue());
        vipUser.setDeadline(getDeadline());
        vipUser.setStatus(getStatus());
        vipUser.setCreateTime(getCreateTime());
        return vipUser;
    }

    public static SpecialVipUser ofVipInfo(final Long userId, final VipInfo vipInfo) {
        if (vipInfo != null) {
            SpecialVipUser specialVipUser = new SpecialVipUser();
            Timestamp deadline = new Timestamp(vipInfo.getDeadline().getT());
            specialVipUser.setUserId(userId);
            specialVipUser.setStatus(vipInfo.getStatus());
            specialVipUser.setExpire(vipInfo.getExpire());
            specialVipUser.setType(vipInfo.getVipType());
            specialVipUser.setDeadline(deadline);
            return specialVipUser;
        }
        return null;
    }
}
