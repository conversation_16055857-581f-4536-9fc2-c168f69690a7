package com.qiyi.boss.model;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.time.LocalDate;
/**
 * 代金券
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PayCenterCoupon {

    /**
     * 卡券码
     */
    private String code;

    /**
     * 卡券状态
     */
    private int status;

    /**
     * 卡券批次号
     */
    private String batchNo;

    private Timestamp startTime;

    private String startTimeStr;

    private Timestamp endTime;

    private String endTimeStr;

    //是否是站外
    private Boolean offSite;

    //金额门槛。单位：分
    private Integer transactionMinimum;

    //第三方渠道。1：微信
    Integer thirdChannel;

    private int fee;

    private Long userId;

    private Long consumerUserId;
    /**
     * 消费订单号
     */
    private String consumeOrderCode;
    /**
     * 产品描述 卡券使用限制描述说明
     */
    private String productDesc;
    /**
     * 批次名称，用于显示卡券名称
     */
    private String batchDesc;
    /**
     * 系统时间戳,用于判断卡券是否到期
     */
    private String time;
    /**
     * 代金券的结算价
     */
    private Integer settlementFee;

    public boolean timeValid(LocalDate dutDay) {
        boolean startTimeBlank = StringUtils.isBlank(this.getStartTimeStr());
        boolean endTimeBlank = StringUtils.isBlank(this.getEndTimeStr());
        if (startTimeBlank && endTimeBlank) {
            return false;
        }
        LocalDate couponStartTime = startTimeBlank ? null : LocalDate.parse(this.getStartTimeStr().substring(0, 10));
        LocalDate couponEndTime = endTimeBlank ? null : LocalDate.parse(this.getEndTimeStr().substring(0, 10));
        boolean startValid = couponStartTime == null || !dutDay.isBefore(couponStartTime);
        boolean endValid = couponEndTime == null || dutDay.isBefore(couponEndTime);
        return startValid && endValid;
    }



}
