package com.qiyi.boss.async.queue;

import com.qiyi.boss.async.task.Task;
import org.apache.commons.logging.Log;
import org.slf4j.Logger;

import java.util.concurrent.locks.Condition;


/**
 * 优先级队列 single
 * 特性：1、移出不阻塞
 *      2、插入阻塞
 */
public class AsyncTaskPriorityQueue {

	private final Task[] items;
	
	private int takeIndex;// 下一个 take 位置
	
	private int putIndex;// 下一个 put 位置
	
	private int count;// 队列长度
	
	private final Condition notFull;// 唤醒和等待：队列是否已满
	
	private AsyncTaskPriorityQueueCollection collection;
	
	/**
	 * @param capacity 队列容量
	 */
	public AsyncTaskPriorityQueue(int capacity, AsyncTaskPriorityQueueCollection collection) {
		this.items = new Task[capacity];
		this.collection = collection;
		notFull =  this.collection.newCondition();
    }
	
	private Task extract() {
		Task item = items[takeIndex];
		items[takeIndex] = null;
		// 下一个 take 位置
		takeIndex = inc(takeIndex);
		count--;
		return item;
	}
	
	/**
	 * 移出头
	 * @return
	 */
	public Task take() {
		Task item = extract();
		notFull.signal();// 队列未满唤醒
		return item;
    }
	
    private void insert(Task item) {
    	items[putIndex] = item;
        // 下一个 put 位置
    	putIndex = inc(putIndex);
    	count++;
    }
    
    /**
	 * 插入尾
	 * @param item
	 */
    public void put(Task item) throws InterruptedException {
    	collection.lockInterruptibly();
    	try {
			while (count == items.length) {
				notFull.await();
			}
        } catch (InterruptedException e) {
        	notFull.signal();
        	throw e;
        }
        insert(item);
        collection.increaseCount();// 队列集计数器增1
        collection.notEmpty();// 队列集非空唤醒
        collection.unlock();
	}
    
    /**
     * 队列长度
     * @return
     */
    public int size() {
    	return count;
    }
    
    /**
     * 队列是否已满
     * @return
     */
    public boolean isFull() {
    	return (count == items.length);
    }
    
    /**
     * 清空队列
     */
    public void clear() {
    	int i = takeIndex;// 下一个 take 位置
    	int k = count;// 队列长度
    	while (k-- > 0) {
    		items[i] = null;
    		i = inc(i);
    	}
    	count = 0;
    	putIndex = 0;
    	takeIndex = 0;
    }
    
    /**
     * 循环增1
     */
    private int inc(int i) {
        return (++i == items.length)? 0 : i;
    }
    
    public void printQueue(Logger log) {
    	int i = takeIndex;// 下一个 take 位置
    	int k = count;// 队列长度
    	while (k-- > 0) {
    		log.info("printing item:" + items[i]);
    	}
    }
}
