package com.qiyi.boss.async.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import io.micrometer.core.instrument.ImmutableTag;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tag;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import com.qiyi.boss.Constants;
import com.qiyi.boss.async.SimpleThreadPoolExecutor;
import com.qiyi.boss.async.task.i18n.I18nDutAutoRenewTask;
import com.qiyi.boss.autorenew.dto.DutRenewLogDesp;
import com.qiyi.boss.autorenew.dto.OpenDutSetLogDesp;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.component.SmartJedisClient;
import com.qiyi.boss.dto.AccountBindInfo;
import com.qiyi.boss.dto.AccountResponse;
import com.qiyi.boss.dto.AgreementDutContext;
import com.qiyi.boss.dto.AvailableDutCouponInfo;
import com.qiyi.boss.dto.CancelAutoRenewOptDto;
import com.qiyi.boss.dto.CreateOrderDto;
import com.qiyi.boss.dto.DutParamsDto;
import com.qiyi.boss.dto.FamilyBindRelation;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.DutFailedEnum;
import com.qiyi.boss.enums.OperateTypeEnum;
import com.qiyi.boss.exception.BizException;
import com.qiyi.boss.model.BaseResponse.CodeEnum;
import com.qiyi.boss.model.DutSkuInfo;
import com.qiyi.boss.model.PayCenterCoupon;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.outerinvoke.CommodityProxy;
import com.qiyi.boss.outerinvoke.CustomVipInfoClient;
import com.qiyi.boss.outerinvoke.VipChargeApi;
import com.qiyi.boss.outerinvoke.VipTradeCouponProxy;
import com.qiyi.boss.outerinvoke.result.CommodityInfo;
import com.qiyi.boss.outerinvoke.result.CouponContext;
import com.qiyi.boss.processor.DutProcessor;
import com.qiyi.boss.service.AutoRenewLinkedDutConfigService;
import com.qiyi.boss.service.AutoRenewService;
import com.qiyi.boss.service.AutorenewDutConfigService;
import com.qiyi.boss.service.AutorenewPreDutRecordService;
import com.qiyi.boss.service.DutService;
import com.qiyi.boss.service.DutUserService;
import com.qiyi.boss.service.OrderService;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.service.UserDutDiscountService;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.AgreementTemplateManager;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.service.impl.AutoRenewUpgradeConfigManager;
import com.qiyi.boss.service.impl.DutUserRenewStatusManager;
import com.qiyi.boss.service.impl.PaymentDutTypeManager;
import com.qiyi.boss.service.impl.QiYuePlatformManager;
import com.qiyi.boss.service.impl.UserManager;
import com.qiyi.boss.utils.AppConfig;
import com.qiyi.boss.utils.ApplicationContextUtil;
import com.qiyi.boss.utils.AutoRenewConfig;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.DutCouponUtils;
import com.qiyi.boss.utils.EncodeUtils;
import com.qiyi.boss.utils.JacksonUtils;
import com.qiyi.boss.utils.PassportApi;
import com.qiyi.boss.utils.PayUtils;
import com.qiyi.boss.utils.PromotionProcessApi;
import com.qiyi.vip.commons.constant.QueryConstants;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewLinkedDutConfig;
import com.qiyi.vip.trade.autorenew.domain.AutorenewDutConfig;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.DutUserRenewStatus;
import com.qiyi.vip.trade.autorenew.service.DutRenewLogService;
import com.qiyi.vip.trade.autorenew.service.DutRenewSetLogService;
import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.uitls.model.MailHeader;

import static com.qiyi.boss.Constants.RENEW_LOG_REAL_DUT_FEE;
import static com.qiyi.boss.constants.CacheConstants.bulidAliPayAsyncDutTaskOrderCacheKey;

/**
 * <AUTHOR>
 * @date 2021/9/13 下午 04:35
 */
@Data
public abstract class AbstractDutAutoRenewTask extends AbstractTask {

    private final static Logger log = LoggerFactory.getLogger(AbstractDutAutoRenewTask.class);

    public static final int POOLTYPE_OFFSET = 60;
    public static final long MAX_NOTIFY_COUNT = 6;
    public static final String RESPONSE_DATA = "data";
    public static final String RESPONSE_CODE = "code";
    public static final String RESPONSE_ORDER_CODE = "orderCode";
    public static final String RESPONSE_FEE = "fee";
    public static final String RESPONSE_THIRD_ERROR_CODE = "third_error_code";
    public static final String RESPONSE_THIRD_ERROR_MSG = "third_error_msg";
    public static final String RESPONSE_REQ_ERROR_TYPE = "req_error_type";
    public static final String CODE_A00000 = "A00000";
    public static final String CODE_A00003 = "A00003";
    public static final String PARAM_FC = "fc";
    public static final String PARAM_PRODUCT = "product";
    public static final String PARAM_PRODUCT_CODE = "productCode";

    public static final String PARAM_SKU_ID = "skuId";
    public static final String PARAM_USER = "user";
    public static final String PARAM_DUT_BIND_TYPE = "dutBindType";
    public static final String PARAM_AGREEMENT_NO = "agreementNo";
    public static final String PARAM_AGREEMENT_TYPE = "agreementType";
    public static final String PARAM_EXECUTED_TYPES = "executedTypes";
    public static final String PARAM_AUTO_RENEW_LINKED_DUT_CONFIG = "autoRenewLinkedDutConfig";
    public static final String PARAM_AUTORENEW_DUT_CONFIG = "autorenewDutConfig";
    public static final String PARAM_TASK_TYPE = "taskType";
    public static final String PARAM_NOTIFY_COUNT = "notifyCount";
    public static final String DUT_PAY_NEW_URL = AppConfig.getProperty("dutPayNew.url");

    public DutService dutService;
    public DutUserRenewStatusManager dutUserRenewStatusManager;
    public QiYuePlatformManager qiYuePlatformManager;
    public DutProcessor dutProcessor;
    public PassportApi passportApi;
    public AutoRenewLinkedDutConfigService autoRenewLinkedDutConfigService;
    public AutorenewDutConfigService autorenewDutConfigService;
    public PaymentDutTypeManager paymentDutTypeManager;
    public PromotionProcessApi promotionProcessApi;
    public UserManager userManager;
    public CustomVipInfoClient customVipInfoClient;
    public AutoRenewLinkedDutConfig autoRenewLinkedDutConfig;
    public AutorenewDutConfig autorenewDutConfig;
    public AutoRenewDutTypeManager autoRenewDutTypeManager;
    public CloudConfig cloudConfig;
    public DutRenewSetLogService dutRenewSetLogService;
    public DutUserService dutUserService;
    public DutRenewLogService dutRenewLogService;
    public AutorenewPreDutRecordService autorenewPreDutRecordService;
    public OrderService orderService;
    public AutoRenewUpgradeConfigManager autoRenewUpgradeConfigManager;
    public AutoRenewService autoRenewService;
    public AgreementNoInfoManager agreementNoInfoManager;
    public UserAgreementService userAgreementService;
    public AgreementTemplateManager agreementTemplateManager;
    public CommodityProxy commodityProxy;
    public UserDutDiscountService userDutDiscountService;
    public VipTradeCouponProxy vipTradeCouponProxy;
    public VipChargeApi vipChargeApi;

    public String productCode;
    public Long userId;
    public Long vipType;
    public Long sourceVipType = -1L;
    public Integer amount = 1;
    public String fc = "";
    public Integer dutBindType;
    public Integer agreementNo;
    public String taskType = Constants.TASK_TYPE_NORMAL;
    public Integer notifyCount = 0;
    public Set<Integer> executedTypes;
    public AutoRenewDutType autoRenewDutType;
    public DutParamsDto dutParamsDto;
    public SmartJedisClient smartJedisClient;
    public AgreementNoInfo agreementNoInfo;
    public AgreementTemplate agreementTemplate;
    public Integer agreementType;
    /**
     * 通过什么方式生成的
     */
    public String generatedBy;

    @Override
    protected abstract void execute();

    public abstract void createNextDutTask(VipUser vipUser, Long uid);


    public boolean nextCategoryDutOnSameDay(String exeTimeStr, Integer payChannel, String category, VipUser vipUser, AutoRenewLinkedDutConfig currentConfig) {
        if (payChannel == null) {
            return false;
        }
        Long sourceVipType = currentConfig.getSourceVipType();
        AutoRenewLinkedDutConfig nextLinkedDutConfig = getAutoRenewLinkedDutConfigByDutTypeAndCategory(payChannel, category, vipUser, sourceVipType, currentConfig.getJobType(), currentConfig.getAgreementType());
        if (nextLinkedDutConfig == null) {
            return false;
        }
        Timestamp exeTime = getExeTime(exeTimeStr, currentConfig, nextLinkedDutConfig);
        return DateHelper.getTomorrowDate().after(new Date(exeTime.getTime()));
    }


    public Timestamp getExeTime(String exeTimeStr, AutoRenewLinkedDutConfig currentConfig, AutoRenewLinkedDutConfig nextConfig) {
        Timestamp exeTime = DateHelper.getTimestamp(exeTimeStr);
        boolean exeTimeNotOnToday = exeTimeNotOnToday(exeTime);
        Integer retryIntervalMin = currentConfig.getRetryIntervalMin();
        exeTime = DateHelper.caculateTime(exeTime, retryIntervalMin, Constants.PRODUCT_PERIODUNIT_MINUTE);

        Integer offset = exeTimeNotOnToday ? nextConfig.getOffset() : retryIntervalMin;
        int second = (int) ((Math.random()) * offset * Constants.ONE_MINUTE_IN_SECOND);
        exeTime = DateHelper.caculateTime(exeTime, second, Constants.PRODUCT_PERIODUNIT_SECOND);
        return exeTime;
    }

    private boolean exeTimeNotOnToday(Timestamp exeTime) {
        if (exeTime == null) {
            return false;
        }
        return exeTime.after(DateHelper.getTomorrowDate());
    }

    public AutoRenewLinkedDutConfig getAutoRenewLinkedDutConfigByDutTypeAndCategory(Integer payChannel, String category, VipUser vipUser, Long sourceVipType, Integer jobType, Integer agreementType) {
        return autoRenewLinkedDutConfigService.find(jobType, vipUser.getTypeId(), sourceVipType, agreementType, payChannel, category);
    }

    public DutRenewSetLog getBindedAliPayDutType(List<DutRenewSetLog> dutRenewSetLogs, List<Integer> aliPayDutTypes) {
        return dutRenewSetLogs.stream()
            .filter(dutRenewSetLog -> aliPayDutTypes.contains(dutRenewSetLog.getType()))
            .findFirst()
            .orElse(null);
    }

    public boolean isAliPayChannelAutoRenewUser(List<DutRenewSetLog> dutRenewSetLogs, List<Integer> aliPayDutTypes) {
        return dutRenewSetLogs.stream().anyMatch(dutRenewSetLog -> aliPayDutTypes.contains(dutRenewSetLog.getType()));
    }

    public boolean validateVipUserStatus(VipUser vipUser, DutRenewLog dutRenewLog, Long uid) {
        if (vipUser.hasBeenFrozenForever()) {
            log.error("[validateVipUserStatus] [uid:{}] [Vipuser has been frozen.] [taskType:{}]", uid, taskType);

            dutRenewLog.setStatus(DutRenewLog.PAY_FAILURE);
            dutRenewLog.setErrorCode(DutFailedEnum.USER_HAS_BEEN_FROZEN_FOREVER.getCode());
            dutRenewLog.setThirdErrorMsg(DutFailedEnum.USER_HAS_BEEN_FROZEN_FOREVER.getDesc());
            dutService.shardSaveRenewLog(dutRenewLog);

            return false;
        }
        return true;
    }

    public void saveRenewLogBasicInfo(DutRenewLog dutRenewLog, DutUserNew dutUserNew, String fc) {
        Integer amount = null == dutUserNew.getAmount() ? Constants.AMOUNT_OF_COMMON_AUTORENEW : dutUserNew.getAmount();
        dutRenewLog.setUserId(userId);
        dutRenewLog.setSignKey(dutUserNew.getSignKey());
        dutRenewLog.setAgreementNo(dutUserNew.getAgreementNo());
        dutRenewLog.setAgreementType(dutUserNew.getAgreementType());
        dutRenewLog.setType(dutBindType);
        dutRenewLog.setCreateTime(DateHelper.getDateTime());
        dutRenewLog.setPlatform(dutUserNew.getPlatform());
        dutRenewLog.setPlatformCode(dutUserNew.getPlatformCode());
        dutRenewLog.setVipType(dutUserNew.getVipType());
        dutRenewLog.setAmount(amount);
        String finalFc = fc;
        if (StringUtils.isBlank(fc)) {
            OpenDutSetLogDesp setLogDesp = dutRenewSetLogService.getLastRecentOpenSetLogDesp(userId, dutUserNew.getVipType(), dutBindType, DutRenewSetLog.RENEW_SET);
            if (setLogDesp != null && setLogDesp.getFc() != null) {
                finalFc = setLogDesp.getFc();
            }
        }

        DutRenewLogDesp desp = DutRenewLogDesp.builder()
            .actCode(StringUtils.defaultIfEmpty(dutUserNew.getActCode(), ""))
            .orderFc(finalFc == null ? "" : finalFc)
            .actPeriods(dutUserNew.getRemainPeriods() == null ? 0 : dutUserNew.getRemainPeriods())
            .actType(dutUserNew.getActType())
            .taskType(taskType)
            .build();
        if (this instanceof I18nDutAutoRenewTask) {
            desp.setTimeZone(dutUserNew.getTimeZone());
            desp.setSignTime(dutUserNew.getSignTime() == null ? "" : DateHelper.getDateStringByPattern(dutUserNew.getSignTime(), DateHelper.MOST_COMMON_PATTERN));
        }
        dutRenewLog.setDescription(desp.toJSONString());
        dutRenewLog.setVipCategory(dutUserNew.getVipCategory());
    }


    public boolean validateDutUser(DutRenewLog dutRenewLog, Long uid, DutUserNew dutUserNew) {
        if (dutUserNew == null || dutUserNew.getAutoRenew() == null || dutUserNew.getAutoRenew() != DutUserNew.RENEW_AUTO) {
            log.warn("[validateDutUser] [user:{}] [dutType:{}] [taskType:{}] [dutUser is null]", uid, dutBindType, taskType);
            dutRenewLog.setFailedInfo(DutFailedEnum.DUT_USER_IS_NULL_OR_INVALID);
            dutService.shardSaveRenewLog(dutRenewLog);

            return false;
        }
        return true;
    }


    public boolean validateDutUser(DutUserNew dutUserNew) {
        if (dutUserNew != null && dutUserNew.isAutoRenewUser()) {
            return true;
        }
        DutRenewLog dutRenewLog = new DutRenewLog();
        dutRenewLog.setUserId(userId);
        dutRenewLog.setType(dutBindType);
        dutRenewLog.setAgreementNo(agreementNo);
        dutRenewLog.setCreateTime(DateHelper.getDateTime());
        dutRenewLog.setVipType(autorenewDutConfig.getVipType());
        dutRenewLog.setFailedInfo(DutFailedEnum.DUT_USER_IS_NULL_OR_INVALID);
        dutRenewLog.setAgreementType(agreementType);
        dutRenewLog.setDescription(JacksonUtils.toJsonString(DutRenewLogDesp.builder().taskType(taskType).build()));
        if (dutUserNew != null) {
            dutRenewLog.setSignKey(dutUserNew.getSignKey());
        }
        dutService.shardSaveRenewLog(dutRenewLog);
        log.warn("[user:{}] [agreementNo:{}] [dutType:{}] [taskType:{}] [dutUser is null]", userId, agreementNo, dutBindType, taskType);
        return false;
    }

    public boolean recordCommodityError(DutUserNew dutUserNew, String skuId) {
        DutRenewLog dutRenewLog = new DutRenewLog();
        dutRenewLog.setUserId(dutUserNew.getUserId());
        dutRenewLog.setType(dutUserNew.getType());
        dutRenewLog.setAgreementNo(dutUserNew.getAgreementNo());
        dutRenewLog.setSignKey(dutUserNew.getSignKey());
        dutRenewLog.setCreateTime(DateHelper.getDateTime());
        dutRenewLog.setVipType(dutUserNew.getVipType());
        dutRenewLog.setFailedInfo(DutFailedEnum.COMMODITY_NOT_EXISTS);
        dutRenewLog.setDescription(JacksonUtils.toJsonString(DutRenewLogDesp.builder().taskType(taskType).build()));
        dutRenewLog.setAgreementType(agreementType);
        dutService.shardSaveRenewLog(dutRenewLog);
        log.error("Commodity not exists, [user:{}] [agreementNo:{}] [dutType:{}] [skuId:{}] [taskType:{}]",
            dutUserNew.getUserId(), dutUserNew.getAgreementNo(), dutUserNew.getType(), skuId, taskType);
        return false;
    }

    public boolean validateAccountStatus(DutRenewLog dutRenewLog, Long uid) {
        boolean isAccountClosed = passportApi.isAccountClosed(uid);
        if (isAccountClosed) {
            dutRenewLog.setReqErrorType("THIRD_ERROR");
            dutRenewLog.setFailedInfo(DutFailedEnum.PASSPORT_ACCOUNT_NOT_EXISTS);
            dutService.shardSaveRenewLog(dutRenewLog);

            log.info("[uid:{}] [taskType:{}] [msg:passport账户不存在!]", uid, taskType);
            return false;
        }
        return true;
    }

    public int getPoolType(Long userId) {
        return (int) (userId % ApplicationContextUtil.getBean(AutoRenewConfig.class).getAutoRenewTaskServerNum()) + POOLTYPE_OFFSET;
    }

    public String getFv(OpenDutSetLogDesp setLogDesp, Long vipType, String skuId) {
        String fv = null;
        if (setLogDesp != null && StringUtils.isNotBlank(setLogDesp.getFv()) && !"null".equals(setLogDesp.getFv())) {
            fv = setLogDesp.getFv();
        }
        if (Constants.DEFAULT_FV.equals(fv) || fv == null) {
            fv = CloudConfigUtil.getDefaultFvByVipType(vipType, Constants.DEFAULT_FV);
        }
        return CloudConfigUtil.transferFvWithSku(fv, skuId);
    }

    public boolean validateAutoRenewDutTypeExpired(DutRenewLog dutRenewLog, VipUser vipUser, DutUserNew dutUserNew) {
        if (dutUserNew.getAgreementNo() != null) {
            if (agreementNoInfo == null || agreementNoInfo.online()) {
                return true;
            }
        } else {
            if (autoRenewDutType == null || !autoRenewDutType.expired()) {
                return true;
            }
        }

        CancelAutoRenewOptDto cancelAutoRenewOptDto = CancelAutoRenewOptDto.buildFromDutUserNew(dutUserNew, OperateSceneEnum.CANCEL_DUT_TYPE_EXPIRED, false);
        autoRenewService.cancelAutoRenew(dutUserNew, cancelAutoRenewOptDto);

        // 保存代扣日志记录
        dutRenewLog.setFailedInfo(DutFailedEnum.DUT_TYPE_EXPIRED);
        dutService.shardSaveRenewLog(dutRenewLog);

        if (this instanceof DutAutoRenewTaskNew || this instanceof UpgradeAutoRenewTask) {
            // 生成下次代扣异步任务
            createNextDutTask(vipUser, dutUserNew.getUserId());
        }
        return false;
    }

    public boolean validateDeadline(VipUser vipUser, Long uid) {
        if (vipUser.getDeadline().after(DateHelper.caculateTime(DateHelper.getCurrentTime(),
            autorenewDutConfig.getAdvanceDays() + 1, Constants.PRODUCT_PERIODUNIT_DAY))) {
            log.info("[validateDeadline] [userId:{}] [deadline:{}] [taskType:{}] [msg:已经自动续费或者用户主动续费]",
                uid, DateHelper.getFormatDate(vipUser.getDeadline(), DateHelper.SIMPLE_PATTERN), taskType);
            return false;
        }
        return true;
    }
    public boolean validateNextDutTime(DutUserNew dutUserNew) {
        Timestamp nextDutTime = dutUserNew.getNextDutTime();
        if (nextDutTime.after(DateHelper.caculateTime(DateHelper.getCurrentTime(), autorenewDutConfig.getAdvanceDays() + 1, Constants.PRODUCT_PERIODUNIT_DAY))) {
            log.info("[validateNextDutTime] [userId:{}] [nextDutTime:{}] [taskType:{}] [agreementType:{}] [msg:下次代扣时间校验不通过]",
                dutUserNew.getUserId(), DateHelper.getFormatDate(nextDutTime, DateHelper.SIMPLE_PATTERN), agreementType, taskType);
            return false;
        }
        return true;
    }

    public boolean validateFamilyTypeDeadline(VipUser vipUser, DutUserNew dutUserNew) {
        Long userId = dutUserNew.getUserId();
        Timestamp sourceUserDeadline = vipUser.getDeadline();
        FamilyBindRelation bindRelation = vipChargeApi.queryBindRelationBySourceUid(userId);
        Timestamp latestAllowDutTime = DateHelper.caculateTime(DateHelper.getCurrentTime(), autorenewDutConfig.getAdvanceDays() + 1, Constants.PRODUCT_PERIODUNIT_DAY);
        if (bindRelation == null || bindRelation.unregistered()) {
            log.info("[validateFamilyTypeDeadline] [userId:{}] bindRelation is null or unregistered", userId);
            if (sourceUserDeadline.after(latestAllowDutTime)) {
                log.info("(sourceUserDeadline is after latestAllowDutTime, uid:{}, sourceUserDeadline:{}, latestAllowDutTime:{}", userId, sourceUserDeadline, latestAllowDutTime);
                return false;
            }
            return true;
        } else {
            Long targetUid = bindRelation.getTargetUid();
            VipUser targetVipUser = customVipInfoClient.getVipInfoNew(targetUid, vipType);
            if (targetVipUser == null) {
                log.info("targetVipUser is null, userId :{}", userId);
                // 绑定关系存在时，一定开过权益，targetVipUser为空进行重试
                throw new BizException(CodeEnum.OUTER_SERVICE_ERROR);
            }
            Timestamp targetDeadline = targetVipUser.getDeadline();
            Timestamp smallerDeadlineNonNullValue = FamilyBindRelation.getSmallerDeadlineNonNullValue(sourceUserDeadline.getTime(), targetDeadline.getTime());
            if (smallerDeadlineNonNullValue == null) {
                log.warn("smallerDeadlineNonNullValue is null, userId:{}", userId);
                return false;
            }
            // 绑定关系存在时，最小的权益时间需要小于最晚允许的时间
            Timestamp nextDutTime = dutUserNew.getNextDutTime();
            return nextDutTime != null && nextDutTime.before(latestAllowDutTime) && smallerDeadlineNonNullValue.before(latestAllowDutTime);
        }
    }


    public void validateDeadlineIsConsistent(VipUser vipUser, Long uid, DutUserNew dutUserNew) {
        if (!vipUser.getDeadline().equals(dutUserNew.getDeadline())
            && DateHelper.getTimeInterval(dutUserNew.getDeadline(), vipUser.getDeadline()) > 1000) {
            log.warn("boss vip deadline unequals dutUserNew deadline! uid:{} boss deadline {} dutUserNew deadlne:{} taskType:{}",
                uid, vipUser.getDeadline(), dutUserNew.getDeadline(), taskType);
            sendErrorMail(uid, vipUser.getDeadline(), dutUserNew.getDeadline(),
                "[Warning][boss vip deadline unequals dutUserNew deadline][task excute normally,check uids for more info]");
        }
    }



    public void sendErrorMail(Long uid, Timestamp vipDeadline, Timestamp dutDeadline, String title) {
        MailHeader header = new MailHeader();
        String contact = AppConfig.getProperty("mail.autorenew.error.contact");
        if (StringUtils.isNotBlank(contact)) {
            try {
                header.setTos(contact.split(QueryConstants.COMMA));
                header.setTitle(title);
                AutoRenewErrorSendEmailWorker.AutoRenewErrorSendEmailContent content =
                    new AutoRenewErrorSendEmailWorker.AutoRenewErrorSendEmailContent();
                content.setUid(uid);
                content.setVipDeadline(vipDeadline);
                content.setDutDeadline(dutDeadline);
                content.setDutType(dutBindType);
                SimpleThreadPoolExecutor.execute(new AutoRenewErrorSendEmailWorker(header, content));
            } catch (Exception e) {
                log.error("[AbstractDutAutoRenewTask] [sendErrorMail] [taskType:{}] [paras:{}]", taskType, this.toString());
            }
        }
    }


    public String getQiyueSign(Map<String, String> params) {
        //把数组所有元素，按照"参数=参数值"的模式用"&"字符拼接成字符串
        String prestr = PayUtils.createLinkString(params);
        //把拼接后的字符串再与安全校验码直接连接起来
        String key = AppConfig.getProperty("boss.pay.key");
        prestr = prestr + key;
        return EncodeUtils.MD5(prestr, "UTF-8");
    }

    String getPartnerOrderNo(DutUserNew dutUserNew) {
        String partnerOrderNo = dutUserNew.getOrderCode();

        if (StringUtils.isEmpty(partnerOrderNo)) {
            return null;
        }
        if (StringUtils.length(partnerOrderNo) == Constants.DEFAULT_LENGTH_OF_VIP_PLUS_ORDER_CODE) {
            return partnerOrderNo.substring(0, Constants.DEFAULT_LENGTH_OF_VIP_ORDER_CODE);
        }
        return partnerOrderNo;
    }


    public boolean validateHasRenewToday(DutUserNew dutUserNew, Long vipType) {
        // 只要续费了一种方式就不再续费
        Long userId = dutUserNew.getUserId();
        boolean isAutoRenew = dutRenewLogService.userHadSuccessDutLogToday(userId, vipType, dutUserNew.getAgreementType());
        if (isAutoRenew) {
            log.info("用户今日已经成功代扣! userId:{} vipType:{} taskType:{}", userId, vipType, taskType);
            return false;
        }
        return true;
    }

    public boolean validateVipUserType(VipUser vipUser, DutRenewLog dutRenewLog, Long uid) {
        if (vipUser.getTypeId() == Constants.VIP_USER_FUNCTION) {
            log.info("[validateVipUserType] [userId:{}] [vipType:{}] [deadline:{}] [taskType:{}] [msg:白银会员停止自动续费]",
                uid, vipUser.getTypeId(), DateHelper.getFormatDate(vipUser.getDeadline(), DateHelper.SIMPLE_PATTERN), taskType);

            dutRenewLog.setFailedInfo(DutFailedEnum.VIP_TYPE_NOT_SUPPORT_DUT);
            dutService.shardSaveRenewLog(dutRenewLog);

            return false;
        }
        return true;
    }

    public void doIfPaySuccess(DutRenewLog dutRenewLog, Long uid, DutUserNew dutUserNew, Integer amount, String fc) {
        dutRenewLog.setStatus(DutRenewLog.PAY_SUCCESS);
        dutRenewLog.setErrorCode(null);
        //判断是否已经插入了，支付那边异步调用，有可能失败一次，保存了失败的记录，又成功了，这时候只需要
        //更新下数据就行了
        DutRenewLog dutRenewLog1 = dutService.getDutRenewLogByOrderCode(userId, dutRenewLog.getOrderCode());
        if (dutRenewLog1 != null) {
            if (dutRenewLog1.getStatus() == null || dutRenewLog1.getStatus() != DutRenewLog.PAY_SUCCESS) {
                dutRenewLog1.setStatus(DutRenewLog.PAY_SUCCESS);
                dutRenewLog1.setErrorCode(null);
                dutRenewLog1.setFee(dutRenewLog.getFee());
                dutRenewLog1.setPlatform(dutUserNew.getPlatform());
                dutRenewLog1.setPlatformCode(dutUserNew.getPlatformCode());
                dutRenewLog1.setAmount(amount);
                dutRenewLog1.setVipType(dutUserNew.getVipType());

                DutRenewLogDesp desp = DutRenewLogDesp.builder()
                    .actCode(StringUtils.defaultIfEmpty(dutUserNew.getActCode(), ""))
                    .orderFc(fc)
                    .actPeriods(dutUserNew.getRemainPeriods() == null ? 0 : dutUserNew.getRemainPeriods())
                    .actType(dutUserNew.getActType())
                    .taskType(taskType)
                    .build();
                if (this instanceof I18nDutAutoRenewTask) {
                    desp.setTimeZone(dutUserNew.getTimeZone());
                    desp.setSignTime(dutUserNew.getSignTime() == null ? "" : DateHelper.getDateStringByPattern(dutUserNew.getSignTime(), DateHelper.MOST_COMMON_PATTERN));
                }
                dutRenewLog1.setDescription(desp.toJSONString());
                dutRenewLog1.setVipCategory(dutUserNew.getVipCategory());
                dutService.shardSaveRenewLog(dutRenewLog1);
            }
        } else {
            dutService.shardSaveRenewLog(dutRenewLog);
        }
        dutUserService.processDutSuccess(dutUserNew);
        //自动续费状态表中续费总次数和连续续费次数分别加1
        DutUserRenewStatus dutUserRenewStatus = dutUserRenewStatusManager.getDutUserRenewStatus(uid);
        if (dutUserRenewStatus != null) {
            dutUserRenewStatus.setRenewCount(dutUserRenewStatus.getRenewCount() + 1);
            dutUserRenewStatus.setSerialRenewCount(dutUserRenewStatus.getSerialRenewCount() + 1);
            dutUserRenewStatusManager.saveNew(dutUserRenewStatus);
        }

        log.info("DutAutoRenewTask success dutRenewLog:{}, taskType:{}", dutRenewLog, taskType);
    }


    public void doIfPayFailed(DutRenewLog dutRenewLog, Long uid, DutUserNew dutUserNew, JSONObject reData) {
        log.info("DutAutoRenewTask fail! uid:{}, taskType:{}, dutRenewLog:{}, reData:{}", uid, taskType, dutRenewLog, reData);

        if ((reData != null) && (reData.get(RESPONSE_THIRD_ERROR_CODE) != null)
            && StringUtils.isNotBlank(reData.getString(RESPONSE_THIRD_ERROR_CODE))) {
            dutRenewLog.setThirdErrorCode(reData.getString(RESPONSE_THIRD_ERROR_CODE));
        }
        if ((reData != null) && (reData.get(RESPONSE_THIRD_ERROR_MSG) != null)
            && StringUtils.isNotBlank(reData.getString(RESPONSE_THIRD_ERROR_MSG))) {
            dutRenewLog.setThirdErrorMsg(reData.getString(RESPONSE_THIRD_ERROR_MSG));
        }
        if ((reData != null) && (reData.get(RESPONSE_REQ_ERROR_TYPE) != null)
            && StringUtils.isNotBlank(reData.getString(RESPONSE_REQ_ERROR_TYPE))) {
            dutRenewLog.setReqErrorType(reData.getString(RESPONSE_REQ_ERROR_TYPE));
        }

        dutRenewLog.setStatus(DutRenewLog.PAY_FAILURE);
        dutService.shardSaveRenewLog(dutRenewLog);

        dutUserService.processDutFail(dutUserNew);
    }

    public void doIfRequestSuccess(DutRenewLog dutRenewLog, DutUserNew dutUserNew, Integer amount, String fc, String response) {
        log.info("[module:DutAutoRenewTask] [uid:{}] [taskType:{}] [result:{}]", dutRenewLog.getUserId(), taskType, response);
        JSONObject jb = JSONObject.parseObject(response);
        String code = "";
        if (jb.containsKey(RESPONSE_CODE)) {
            code = jb.getString(RESPONSE_CODE);
        }
        JSONObject reData = null;
        if (jb.get(RESPONSE_DATA) != null) {
            reData = jb.getJSONObject(RESPONSE_DATA);
            String orderCode = reData.getString(RESPONSE_ORDER_CODE);
            if (StringUtils.isNotBlank(orderCode) && StringUtils.isBlank(dutRenewLog.getOrderCode())) {
                dutRenewLog.setOrderCode(orderCode);
            }
            if (reData.containsKey(RESPONSE_FEE)) {
                int fee = reData.getIntValue(RESPONSE_FEE);
                dutRenewLog.setFee(fee);
            }
        }
        if (!CODE_A00000.equals(code)) {
            dutRenewLog.setErrorCode(code);
        }
        if (CODE_A00000.equals(code)) {
            doIfPaySuccess(dutRenewLog, dutRenewLog.getUserId(), dutUserNew, amount, fc);
        } else if (CODE_A00003.equals(code)) {
            dutRenewLogService.processDutNeedAsyncConfirm(dutRenewLog);
        } else {
            doIfPayFailed(dutRenewLog, dutRenewLog.getUserId(), dutUserNew, reData);
        }
    }

    public boolean validateBindStatus(DutRenewLog dutRenewLog, DutUserNew dutUserNew, VipUser vipUser, Long uid) {
        Integer dutType = agreementNo != null ? agreementNoInfo.getDutType() : dutUserNew.getType();
        List<Integer> coverageDutTypes = CloudConfigUtil.coverageDutTypes(dutType);
        // 微信自动续费用户可能由于支付账号已为其他爱奇艺账号签约，导致账户中心未成功建立签约关系的情况；
        // 为使其能正常发起代扣，账户中心在会员发起代扣前查询签约关系时根据支付账号信息查找对应其他uid的签约关系并返回，需要会员加传业务方及签约订单号信息。
        Optional<AccountResponse> accountResponse = Optional.empty();
        if (this instanceof I18nDutAutoRenewTask) {
            accountResponse = dutProcessor.queryAccountBindInfos(userId, dutType, null, null);
        } else {
            String partnerId = agreementNoInfo != null ? agreementNoInfo.getPartnerId() : autoRenewDutType.getPartnerId();
            String partner = StringUtils.isNotBlank(partnerId)
                ? partnerId
                : CloudConfigUtil.getPartnerByVipType(dutUserNew.getVipType());
            String partnerOrderNo = getPartnerOrderNo(dutUserNew);
            Integer userDutType = CollectionUtils.isEmpty(coverageDutTypes) ? dutType : null;
            accountResponse = dutProcessor.queryAccountBindInfos(userId, userDutType, partner, partnerOrderNo);
        }
        if (!accountResponse.isPresent()) {
            log.info("[AbstractDutAutoRenewTask fail] [bind query timeout] [uid:{}] [type:{}] [taskType:{}]", userId, dutType, taskType);
            dutRenewLog.setFailedInfo(DutFailedEnum.BIND_QUERY_TIMEOUT);
        } else {
            AccountBindInfo accountBindInfo = dutProcessor.getAccountSignCode(accountResponse.get(), dutType, coverageDutTypes);
            dutParamsDto.setDutType(accountBindInfo != null ? accountBindInfo.getType() : dutType);
            dutParamsDto.setAgreementNo(agreementNo);
            if (accountBindInfo == null) {
                log.info("[AbstractDutAutoRenewTask fail] [no Bind] [uid:{}] [type:{}] [taskType:{}]", userId, dutType, taskType);
                dutRenewLog.setFailedInfo(DutFailedEnum.PAY_CENTER_NO_BIND);
            } else {
                dutParamsDto.setAccountSignCode(accountBindInfo.getSignCode());
                // 记录支付中心补充微信签约关系标识.
                String wechatSignFlag = accountBindInfo.getSignFlag();
                if (StringUtils.isNotEmpty(wechatSignFlag)) {
                    Map<String, Object> map = JSON.parseObject(dutRenewLog.getDescription());
                    map.put("signFlag", wechatSignFlag);
                    dutRenewLog.setDescription(JSON.toJSONString(map));
                }
                return true;
            }
        }
        dutRenewLog.setStatus(DutRenewLog.PAY_FAILURE);
        dutService.shardSaveRenewLog(dutRenewLog);
        if (this instanceof DutAutoRenewTaskNew || this instanceof UpgradeAutoRenewTask || this instanceof I18nDutAutoRenewTask) {
            // 生成下次代扣异步任务
            log.info("createNextDutTask for validateBindStatus fail, dutUserNew:{}", dutUserNew);
            createNextDutTask(vipUser, uid);
        }

        return false;
    }

    public void reportRenewLogMetrics(DutUserNew dutUserNew, String errorCode) {
        Tag agreementNoTag = new ImmutableTag("agreementNo", Objects.toString(dutUserNew.getAgreementNo(), "null"));
        Tag agreementTypeTag = new ImmutableTag("agreementType", Objects.toString(dutUserNew.getAgreementType(), String.valueOf(AgreementTypeEnum.AUTO_RENEW.getValue())));
        Tag operateTypeTag = new ImmutableTag("operateType", String.valueOf(OperateTypeEnum.DUT.getValue()));
        Tag vipTypeTag = new ImmutableTag("vipType", String.valueOf(dutUserNew.getVipType()));
        Tag dutTypeTag = new ImmutableTag("dutType", String.valueOf(dutUserNew.getType()));
        Tag payChannelTag = new ImmutableTag("payChannel", Objects.toString(autoRenewLinkedDutConfig.getPayChannel(), "0"));
        Tag dutStatusTag = new ImmutableTag("dutStatus", String.valueOf(DutRenewLog.PAY_FAILURE));
        Tag errorCodeTag = new ImmutableTag("errorCode", errorCode);
        List<Tag> tags = Arrays.asList(agreementTypeTag, agreementNoTag, operateTypeTag, vipTypeTag, dutTypeTag, payChannelTag, dutStatusTag, errorCodeTag);
        Metrics.counter("autorenew_renew_log_insert_total", tags).increment();
    }

    public boolean validate(VipUser vipUser, DutRenewLog dutRenewLog, Long uid, DutUserNew dutUserNew) {

        if (isNotInWhiteList(userId)) {
            // 校验用户是否已主动续费
            if (needValidDeadline() && !validateDeadline(vipUser, uid)) {
                reportRenewLogMetrics(dutUserNew, DutFailedEnum.DEADLINE_EXTEND_OTHER_WAY.getCode());
                log.warn("[validateDeadline] [uid:{}] [need not renew reason:{}]", uid, DutFailedEnum.DEADLINE_EXTEND_OTHER_WAY.getCode());
                dutRenewLog.setFailedInfo(DutFailedEnum.DEADLINE_EXTEND_OTHER_WAY);
                dutService.shardSaveRenewLog(dutRenewLog);
                return true;
            }

            if (needValidNextDutTime() && !validateNextDutTime(dutUserNew)) {
                reportRenewLogMetrics(dutUserNew, DutFailedEnum.DEADLINE_EXTEND_OTHER_WAY.getCode());
                log.warn("[validateNextDutTime] [uid:{}] [need not renew reason:{}]", uid, DutFailedEnum.DEADLINE_EXTEND_OTHER_WAY.getCode());
                dutRenewLog.setFailedInfo(DutFailedEnum.DEADLINE_EXTEND_OTHER_WAY);
                dutService.shardSaveRenewLog(dutRenewLog);
                return true;
            }

            if (needValidBothDeadline() && !validateFamilyTypeDeadline(vipUser, dutUserNew)) {
                reportRenewLogMetrics(dutUserNew, DutFailedEnum.FAMILY_TYPE_DUT_TIME_INVALID.getCode());
                log.warn("[validateFamilyTypeNextDutTime] [uid:{}] [need not renew reason:{}]", uid, DutFailedEnum.FAMILY_TYPE_DUT_TIME_INVALID.getCode());
                dutRenewLog.setFailedInfo(DutFailedEnum.FAMILY_TYPE_DUT_TIME_INVALID);
                dutService.shardSaveRenewLog(dutRenewLog);
                return true;
            }


            // 校验该用户当天是否已经进行了自动续费
            if (!validateHasRenewToday(dutUserNew, vipUser.getTypeId())) {
                reportRenewLogMetrics(dutUserNew, DutFailedEnum.USER_ALREADY_RENEWED_TODAY.getCode());
                log.warn("[validateDeadline] [uid:{}] [need not renew reason:{}]", uid, DutFailedEnum.USER_ALREADY_RENEWED_TODAY.getCode());
                dutRenewLog.setFailedInfo(DutFailedEnum.USER_ALREADY_RENEWED_TODAY);
                dutService.shardSaveRenewLog(dutRenewLog);
                return true;
            }
        }

        // 校验dutUser是否为空
        if (!validateDutUser(dutRenewLog, userId, dutUserNew)) {
            return true;
        }

        // 校验passport账户是否被注销
        if (!validateAccountStatus(dutRenewLog, uid)) {
            return true;
        }

        // 校验VipUser是否被封停
        if (!validateVipUserStatus(vipUser, dutRenewLog, uid)) {
            return true;
        }

        // 校验用户身份是否是白银会员
        if (!validateVipUserType(vipUser, dutRenewLog, uid)) {
            return true;
        }

        // 校验绑定关系是否正常
        if (!validateBindStatus(dutRenewLog, dutUserNew, vipUser, uid)) {
            return true;
        }

        // 校验vipUser与dutUser到期时间是否一致
        validateDeadlineIsConsistent(vipUser, uid, dutUserNew);

        // 代扣方式已过期
        if (!validateAutoRenewDutTypeExpired(dutRenewLog, vipUser, dutUserNew)) {
            return true;
        }

        return false;
    }

    private boolean needValidDeadline() {
        return AgreementTypeEnum.commonAutoRenew(agreementType) || agreementType == null;
    }

    private boolean needValidNextDutTime() {
        return AgreementTypeEnum.jointType(agreementType);
    }

    private boolean needValidBothDeadline() {
        return AgreementTypeEnum.familyType(agreementType);
    }


    public boolean validateManualDut(DutRenewLog dutRenewLog, Long uid, DutUserNew dutUserNew) {

        // 校验dutUser是否为空
        if (!validateDutUser(dutRenewLog, userId, dutUserNew)) {
            return true;
        }

        // 校验passport账户是否被注销
        if (!validateAccountStatus(dutRenewLog, uid)) {
            return true;
        }

        // 校验绑定关系是否正常
        if (!validateBindStatus(dutRenewLog, dutUserNew, null, uid)) {
            return true;
        }

        // 代扣方式已过期
        if (!validateAutoRenewDutTypeExpired(dutRenewLog, null, dutUserNew)) {
            return true;
        }

        return false;
    }

    public boolean needValidStudentAutoRenew() {
        return AgreementTypeEnum.commonAutoRenew(agreementType);
    }

    public String buildAliPayAsyncDutTaskType(String taskType) {
        return taskType + "_aliPayAsync";
    }

    public void putAliPayAsyncDutOrderIntoCache(Long userId, Long vipType, String orderCode, Integer timeToLive) {
        String orderCacheKey = bulidAliPayAsyncDutTaskOrderCacheKey(userId, vipType);
        smartJedisClient.setex(orderCacheKey, orderCode, timeToLive);
    }

    public void cancelAutoRenew(VipUser vipUser, Long uid, OperateSceneEnum operateScene) {
        List<DutUserNew> dutUserNewList = dutService.findByUidAndVipTypeAndAutoRenew(userId, agreementType, vipUser.getTypeId(), DutUserNew.RENEW_AUTO);
        OpenDutSetLogDesp setLogDesp;
        String cancelFc = fc;
        for (DutUserNew dutUser : dutUserNewList) {
            setLogDesp = dutRenewSetLogService.getLastRecentOpenSetLogDesp(uid, vipUser.getTypeId(), dutUser.getType(), DutRenewSetLog.RENEW_SET);
            if (setLogDesp != null && setLogDesp.getFc() != null) {
                cancelFc = setLogDesp.getFc();
            }
            CancelAutoRenewOptDto cancelAutoRenewOptDto = CancelAutoRenewOptDto.buildFromDutUserNew(dutUser, operateScene, cancelFc, false);
            autoRenewService.cancelAutoRenew(dutUser, cancelAutoRenewOptDto);
        }
    }

    public void appendRealDutFeeToRenewLog(Map<String, String> configs, DutRenewLog dutRenewLog) {
        Integer realDutFee = Integer.valueOf(configs.get("fee"));
        if (StringUtils.isNotBlank(dutRenewLog.getDescription())) {
            Map<String, Object> descMap = JacksonUtils.parseMap(dutRenewLog.getDescription());
            descMap.put(RENEW_LOG_REAL_DUT_FEE, realDutFee);
            dutRenewLog.setDescription(JacksonUtils.toJsonString(descMap));
        } else {
            DutRenewLogDesp desp = DutRenewLogDesp.builder()
                .realDutFee(realDutFee)
                .build();
            dutRenewLog.setDescription(desp.toJSONString());
        }
    }

    public boolean couldContinueOnCurrentCategory(Set<Integer> executedTypes, Integer payChannel, Integer dutType, boolean enoughToday, AutoRenewLinkedDutConfig autoRenewLinkedDutConfig) {
        Integer jobType = autoRenewLinkedDutConfig.getJobType();
        Long vipType = autoRenewLinkedDutConfig.getVipType();
        String category = autoRenewLinkedDutConfig.getCategory();
        Integer agreementType = autoRenewLinkedDutConfig.getAgreementType();
        Long sourceVipType = autoRenewLinkedDutConfig.getSourceVipType();
        return !executedTypes.contains(dutType)
            && !enoughToday
            && autoRenewLinkedDutConfigService.find(jobType, vipType, sourceVipType, agreementType, payChannel, category) != null;
    }

    public static Integer getNextDutPayChannel(AgreementNoInfo nextDutAgreementNoInfo, AutoRenewDutType nextDutAutoRenewDutType) {
        if (nextDutAgreementNoInfo == null && nextDutAutoRenewDutType == null) {
            return null;
        }
        return nextDutAgreementNoInfo != null ? nextDutAgreementNoInfo.getPayChannel() : nextDutAutoRenewDutType.getPayChannel();
    }

    public AvailableDutCouponInfo getAvailableDutCouponInfoByAgreementDutContext(AgreementDutContext dutContext) {
        if (dutContext == null) {
            return null;
        }
        String partner = CloudConfigUtil.getPartnerByVipType(vipType);
        DutSkuInfo dutSkuInfo = DutSkuInfo.builder()
            .skuId(dutContext.getSkuId())
            .price(dutContext.getRenewPrice())
            .build();
        List<CouponContext> couponContexts = vipTradeCouponProxy.queryAvailableDutCoupon(userId, partner, Lists.newArrayList(dutSkuInfo));
        // 使用当前日期作为代扣日期
        PayCenterCoupon maxValuePayCenterCoupon = getMaxValuePayCenterCoupon(couponContexts, dutContext, LocalDate.now());
        if (maxValuePayCenterCoupon == null) {
            log.info("not find available dut coupon, uid : {}", userId);
            return null;
        } else {
            log.info("selected coupon, uid:{}, coupon:{}", userId, maxValuePayCenterCoupon);
            return AvailableDutCouponInfo.buildFromPayCenterCoupon(maxValuePayCenterCoupon);
        }
    }


    private static PayCenterCoupon getMaxValuePayCenterCoupon(List<CouponContext> couponContexts, AgreementDutContext dutContext, LocalDate dutDay) {
        Integer agreementType = dutContext.getAgreementType();
        Integer payChannel = dutContext.getPayChannel();
        Integer renewPrice = dutContext.getRenewPrice();
        return DutCouponUtils.selectBestCoupon(couponContexts, renewPrice, dutDay, agreementType, payChannel);
    }


    public boolean commonDutUseOrderStateMachine(CreateOrderDto createOrderDto) {
        if (createOrderDto == null || createOrderDto.getCommodityInfo() == null || createOrderDto.getDutParamsDto() == null) {
            return false;
        }
        // 开关开启 且 (UID在允许范围内 或 UID在白名单中)
        boolean useStateMachineSwitch = CloudConfigUtil.enableOrderStateMachine() &&
                                  CloudConfigUtil.enableOrderStateMachineByTaskType(createOrderDto.getDutParamsDto().getTaskType()) &&
                                 (CloudConfigUtil.isUidInAllowedRange(userId) || 
                                  CloudConfigUtil.isUidInWhiteList(userId));
        

        CommodityInfo commodityInfo = createOrderDto.getCommodityInfo();
        boolean useStateMachine = StringUtils.isNotBlank(commodityInfo.getSkuId())
            && orderService.validateOrderConsistency(createOrderDto)
            && useStateMachineSwitch;
        log.info("[commonDutUseOrderStateMachine] [useStateMachine:{}] [uid:{}]", useStateMachine, userId);
        return useStateMachine;
    }
}