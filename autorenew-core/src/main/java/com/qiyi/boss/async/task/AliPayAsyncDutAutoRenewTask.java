package com.qiyi.boss.async.task;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Date;
import java.util.Map;
import java.util.Optional;

import com.qiyi.boss.Constants;
import com.qiyi.boss.autorenew.dto.OpenDutSetLogDesp;
import com.qiyi.boss.autorenew.enumerate.ActTypeEnum;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.component.SmartJedisClient;
import com.qiyi.boss.constants.ParamKeyConstants;
import com.qiyi.boss.dto.AgreementDutContext;
import com.qiyi.boss.dto.AvailableDutCouponInfo;
import com.qiyi.boss.dto.CreateOrderDto;
import com.qiyi.boss.dto.DutParamsDto;
import com.qiyi.boss.dto.OrderCreationRequest;
import com.qiyi.boss.dto.OrderPrepareDto;
import com.qiyi.boss.dto.PayCenterDutReq;
import com.qiyi.boss.dto.PayCenterDutResult;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.DutFailedEnum;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.outerinvoke.CommodityProxy;
import com.qiyi.boss.outerinvoke.CustomVipInfoClient;
import com.qiyi.boss.outerinvoke.VipChargeApi;
import com.qiyi.boss.outerinvoke.VipTradeCouponProxy;
import com.qiyi.boss.outerinvoke.result.CommodityInfo;
import com.qiyi.boss.service.AutoRenewLinkedDutConfigService;
import com.qiyi.boss.service.AutoRenewService;
import com.qiyi.boss.service.AutorenewDutConfigService;
import com.qiyi.boss.service.AutorenewPreDutRecordService;
import com.qiyi.boss.service.DutService;
import com.qiyi.boss.service.DutUserService;
import com.qiyi.boss.service.OrderService;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.service.UserDutDiscountService;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.AgreementTemplateManager;
import com.qiyi.boss.service.impl.DutUserRenewStatusManager;
import com.qiyi.boss.service.impl.QiYueProductNewService;
import com.qiyi.boss.utils.ApplicationContextUtil;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.PassportApi;
import com.qiyi.boss.utils.PromotionProcessApi;
import com.qiyi.vip.commons.constant.QueryConstants;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.domain.AgreementDutMkt;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AgreementTemplate;
import com.qiyi.vip.trade.autorenew.domain.AutorenewPreDutRecord;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.Platform;
import com.qiyi.vip.trade.autorenew.service.DutRenewLogService;
import com.qiyi.vip.trade.autorenew.service.DutRenewSetLogService;
import com.iqiyi.vip.order.dal.model.Order;

import static com.qiyi.boss.Constants.TRADE_TYPE_KEY;
import static com.qiyi.boss.Constants.TRADE_TYPE_VALUE;


/**
 * 支付宝代扣会员自动续费
 *
 * <AUTHOR>
 * @version 12-1-17 - 下午4:25
 */
@Slf4j
public class AliPayAsyncDutAutoRenewTask extends AbstractDutAutoRenewTask {

    @Override
    protected void execute() {
        long start = System.currentTimeMillis();
        if (userId == null) {
            log.warn("AliPayAsyncDutAutoRenewTask execute user is null ! ");
            return;
        }
        log.info("[AliPayAsyncDutAutoRenewTask start] [uid:{}] [dutType:{}] [taskType:{}]", userId, dutBindType, taskType);

        AgreementDutContext dutContext;
        DutUserNew dutUserNew;
        if (agreementNo != null) {
            dutUserNew = userAgreementService.getByAgreementNoAndDutType(userId, agreementNo, dutBindType, autorenewDutConfig.getVipType());
            if (!validateDutUser(dutUserNew) || agreementNoInfo.upgrade()) {
                return;
            }
            AgreementDutMkt dutMktAct = agreementTemplateManager.getDutMktAct(agreementNo);
            dutContext = userAgreementService.buildDutContext(dutUserNew, agreementNoInfo, agreementTemplate, dutMktAct);
            dutBindType = agreementNoInfo.getDutType();
        } else {
            dutUserNew = dutService.getInEffectDutUserNewFromMaster(userId, dutBindType, autorenewDutConfig.getVipType());
            if (!validateDutUser(dutUserNew) || autoRenewDutType.isUpgrade()) {
                return;
            }
            Integer amount = null == dutUserNew.getAmount() ? Constants.AMOUNT_OF_COMMON_AUTORENEW : dutUserNew.getAmount();
            Integer renewPrice = dutUserNew.getRenewPrice() != null ? dutUserNew.getRenewPrice() : 0;
            String skuId = null;
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getByDutTypeAndAmount(dutUserNew.getType(), amount);
            if (agreementNoInfo != null) {
                AgreementTemplate agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
                skuId = agreementTemplate != null ? agreementTemplate.getSkuId() : null;
            }
            com.qiyi.boss.model.Product qiyueProduct = null;
            if (StringUtils.isBlank(skuId)) {
                QiYueProductNewService qiYueProductNewService = ApplicationContextUtil.getBean(QiYueProductNewService.class);
                qiyueProduct = qiYueProductNewService.getProductFromQiYueProduct(productCode);
            }
            dutContext = AgreementDutContext.buildFrom(skuId, productCode, qiyueProduct, amount, renewPrice, autoRenewDutType);
        }
        if (StringUtils.isNotBlank(dutContext.getSkuId())) {
            CommodityInfo commodityInfo = commodityProxy.queryCommodity(dutContext.getSkuId());
            if (commodityInfo == null) {
                recordCommodityError(dutUserNew, dutContext.getSkuId());
                return;
            }
            dutContext.setCommodityInfo(commodityInfo);
            dutContext.setAmount(commodityInfo.getAmount() * dutContext.getSkuAmount());
        }

        OpenDutSetLogDesp openSetLogDesp = dutRenewSetLogService.getLastRecentOpenSetLogDesp(userId, dutUserNew.getVipType(), dutBindType, DutRenewSetLog.RENEW_SET);
        if (StringUtils.isBlank(fc) && openSetLogDesp != null && openSetLogDesp.getFc() != null) {
            fc = openSetLogDesp.getFc();
        }
        DutRenewLog dutRenewLog = DutRenewLog.initBeforeDutOfAutoRenew(dutUserNew, dutContext.getAmount(), fc, taskType);

        VipUser vipUser = customVipInfoClient.getVipInfoNew(userId, autorenewDutConfig.getVipType());
        if (vipUser == null) {
            log.warn("[AliPayAsyncDutAutoRenewTask] [vipUser null] [saveRetryTask] [paras:{}] [taskType:{}]", this.toString(), taskType);
            return;
        }
        try {
            autoRenew(dutContext, dutUserNew, vipUser, dutRenewLog, openSetLogDesp);
        } catch (Exception e) {
            log.error("pid=" + dutContext.getPid() + ",uid=" + userId, e);
        }
        log.info("[AliPayAsyncDutAutoRenewTask] [time cost:{}]", System.currentTimeMillis() - start);
    }


    @Override
    public void createNextDutTask(VipUser vipUser, Long uid) {
        return;
    }

    /**
     * 执行自动续费
     */
    private void autoRenew(AgreementDutContext dutContext, DutUserNew dutUserNew, VipUser vipUser, DutRenewLog dutRenewLog, OpenDutSetLogDesp openSetLogDesp) {
        if (validate(vipUser, dutRenewLog, dutUserNew)) {
            return;
        }

        Map<String, String> params = organizeParams(dutContext, vipUser, userId, dutUserNew, dutRenewLog, openSetLogDesp);
        log.info("Dut by new process. Params:{}.", params);

        AvailableDutCouponInfo availableDutCouponInfo = getAvailableDutCouponInfoByAgreementDutContext(dutContext);
        dutParamsDto.setAvailableDutCouponInfo(availableDutCouponInfo);
        CreateOrderDto createOrderDto = CreateOrderDto.builder()
                .userId(userId)
                .dutParamsDto(dutParamsDto)
                .amount(dutContext.getAmount())
                .skuAmount(dutContext.getSkuAmount())
                .dutUserNew(dutUserNew)
                .params(params)
                .qiyueProduct(dutContext.getQiyueProduct())
                .commodityInfo(dutContext.getCommodityInfo())
                .agreementType(dutContext.getAgreementType())
                .build();
        Order order = null;
        OrderCreationRequest orderCreationRequest = null;
        if (AgreementTypeEnum.commonAutoRenew(agreementType)) {
            String orderCode = null;
            if (commonDutUseOrderStateMachine(createOrderDto)) {
                log.info("common dut new way, uid:{}, skuId:{}", userId, dutContext.getSkuId());
                orderCreationRequest = orderService.createUnpaidOrder(createOrderDto);
                orderCode = orderCreationRequest.getOrderCode();
            } else {
                log.info("common dut old way, uid:{}, skuId:{}", userId, dutContext.getSkuId());
                order = orderService.createOrder(createOrderDto);
                orderCode = order.getOrderCode();
            }
            dutRenewLog.setOrderCode(orderCode);
            dutRenewLog.setFee(dutParamsDto.getFee());
        } else {
            orderCreationRequest = orderService.createUnpaidOrder(createOrderDto);
            dutRenewLog.setOrderCode(orderCreationRequest.getOrderCode());
            dutRenewLog.setFee(dutParamsDto.getFee());
        }

        OrderPrepareDto orderPrepareDto = OrderPrepareDto.builder()
                .dutParamsDto(dutParamsDto)
                .order(order)
                .orderCreationRequest(orderCreationRequest)
                .qiyueProduct(dutContext.getQiyueProduct())
                .commodityInfo(dutContext.getCommodityInfo())
                .userId(userId)
                .enableAliPayAsyncDut(true)
                .build();

        PayCenterDutReq payCenterDutReq = orderService.prepare(orderPrepareDto);
        Optional<PayCenterDutResult> payCenterDutResponse = orderService.processPayments(payCenterDutReq);
        appendRealDutFeeToRenewLog(payCenterDutReq.getConfigs(), dutRenewLog);
        log.info("Uid:{}, Dut result: {}.", userId, payCenterDutResponse);

        if (payCenterDutResponse.isPresent() && payCenterDutResponse.get().isReqSuccess()) {
            String resultString = JSON.toJSONString(orderService.transfer(payCenterDutResponse.get(), order, orderCreationRequest, dutParamsDto.getSignOrderCode()));
            doIfRequestSuccess(dutRenewLog, dutUserNew, dutContext.getAmount(), params.get(PARAM_FC), resultString);
        } else {
            if (payCenterDutResponse.isPresent()) {
                PayCenterDutResult failedResult = payCenterDutResponse.get();
                if (StringUtils.isNotBlank(failedResult.getCode())) {
                    dutRenewLog.setErrorCode(failedResult.getCode());
                }
            }
            dutRenewLogService.processDutRequestFailure(dutRenewLog);
        }

        if (payCenterDutReq.aliPayAsyncDutReq()) {
            Date currentDate = DateHelper.getCurrentDate();
            Date tomorrowDate = DateHelper.getTomorrowDate();
            Long timeInterval = DateHelper.getTimeInterval(currentDate, tomorrowDate) / 1000;
            String orderCode = order != null ? order.getOrderCode() : orderCreationRequest.getOrderCode();
            putAliPayAsyncDutOrderIntoCache(userId, dutUserNew.getVipType(), orderCode, timeInterval.intValue());
        }

    }

    private boolean validate(VipUser vipUser, DutRenewLog dutRenewLog, DutUserNew dutUserNew) {
        if (super.validate(vipUser, dutRenewLog, userId, dutUserNew)) {
            return true;
        }
        if (needValidStudentAutoRenew() && !validateStudentAutoRenew(vipUser, dutRenewLog, userId, dutUserNew)) {
            return true;
        }
        return false;
    }


    private Map<String, String> organizeParams(AgreementDutContext dutContext, VipUser vipUser, Long uid,
        DutUserNew dutUserNew, DutRenewLog dutRenewLog, OpenDutSetLogDesp openSetLogDesp) {

        Integer renewPrice = dutContext.getRenewPrice();
        Map<String, String> params = Maps.newHashMap();
        dutParamsDto.setAgreementType(dutContext.getAgreementType());
        dutParamsDto.setVipType(dutUserNew.getVipType());
        params.put("pid", dutContext.getPid());
        dutParamsDto.setProductName(dutContext.getProductName());
        params.put("dutType", String.valueOf(dutBindType));
        dutParamsDto.setDutType(dutBindType);
        dutParamsDto.setPayChannel(dutContext.getPayChannel());
        params.put("amount", String.valueOf(dutContext.getAmount()));
        dutParamsDto.setAmount(dutContext.getAmount());
        String skuId = dutContext.getSkuId();
        if (dutContext.getCommodityInfo() != null) {
            params.put("skuId", skuId);
            params.put("skuAmount", String.valueOf(dutContext.getSkuAmount()));
        }
        params.put("serviceCode", autorenewDutConfig.getServiceCode());
        dutParamsDto.setServiceCode(autorenewDutConfig.getServiceCode());
        dutParamsDto.setPartnerId(dutContext.getPartnerId());
        log.info("[DutAutoRenewTask] [user:{}] [product:{}] [dutType:{}] [renewPrice:{}] [taskType:{}]",
            uid, dutContext.getPid(), dutBindType, renewPrice, taskType);
        if (renewPrice > 0) {
            params.put(RESPONSE_FEE, String.valueOf(renewPrice));
            dutParamsDto.setFee(renewPrice);
        }
        params.put("BACKEND-AUTO-RENEW", "1");
        params.put("uid", String.valueOf(uid));
        dutParamsDto.setUid(uid);
        params.put("payType", String.valueOf(dutContext.getPayType()));
        dutParamsDto.setPayType(dutContext.getPayType());
        dutParamsDto.setPlatform(dutUserNew.getPlatformCode());
        if (StringUtils.isBlank(dutUserNew.getPlatformCode()) && dutUserNew.getPlatform() != null) {
            Platform platform = qiYuePlatformManager.getBossPlatformById(dutUserNew.getPlatform());
            if (platform != null) {
                params.put("platform", platform.getCode());
                dutParamsDto.setPlatform(platform.getCode());
            }
        }

        params.put("fc", fc);
        dutParamsDto.setFc(fc);
        String fv = getFv(openSetLogDesp, dutUserNew.getVipType(), skuId);
        params.put("fv", fv);
        dutParamsDto.setFv(fv);

        if (openSetLogDesp != null && StringUtils.isNotBlank(openSetLogDesp.getPartnerNo())) {
            params.put("partnerNo", openSetLogDesp.getPartnerNo());
            dutParamsDto.setPartnerNo(openSetLogDesp.getPartnerNo());
        }

        if (StringUtils.isNotBlank(dutUserNew.getOrderCode())) {
            params.put("signOrderCode", dutUserNew.getOrderCode());
            dutParamsDto.setSignOrderCode(dutUserNew.getOrderCode());
        }

        // 供支付中心标记重试订单
        if (!Constants.TASK_TYPE_NORMAL.equals(taskType)) {
            params.put("orderType", "autoRenewRetry");
            dutParamsDto.setOrderType("autoRenewRetry");
        }

        params.put("taskType", taskType);
        dutParamsDto.setTaskType(taskType);

        if (dutRenewLog.getDescription().contains("signFlag")) {
            Map<String, Object> map = JSON.parseObject(dutRenewLog.getDescription());
            params.put("signFlag", String.valueOf(map.get("signFlag")));
            dutParamsDto.setSignFlag(String.valueOf(map.get("signFlag")));
        }

        Map<String, Object> businessProperty = Maps.newHashMap();
        businessProperty.put(ParamKeyConstants.SERIAL_RENEW_COUNT, dutUserNew.getCurrentSerialRenewCount());
        AutorenewPreDutRecord autorenewPreDutRecord = autorenewPreDutRecordService
                .getAutorenewPreDutRecord(uid, vipUser.getTypeId(), dutBindType, dutContext.getAmount());
        if (autorenewPreDutRecord != null) {
            businessProperty.put("ABTestGroup", autorenewPreDutRecord.getGroupKey());
        }
        if (dutParamsDto.getPartnerId() != null) {
            businessProperty.put("partnerId", dutParamsDto.getPartnerId());
        }
        if (StringUtils.isNotBlank(dutUserNew.getSignKey())) {
            businessProperty.put(ParamKeyConstants.SIGN_KEY, dutUserNew.getSignKey());
        }
        if (!isNotInWhiteList(uid)) {
            businessProperty.put(ParamKeyConstants.WHITE_USER_DUT_KEY, true);
        }
        businessProperty.put(TRADE_TYPE_KEY, TRADE_TYPE_VALUE);
        params.put("businessProperty", JSON.toJSONString(businessProperty));
        dutParamsDto.setBusinessProperty(businessProperty);

        String sign = getQiyueSign(params);
        params.put("sign", sign);
        return params;
    }


    private boolean validateStudentAutoRenew(VipUser vipUser, DutRenewLog dutRenewLog, Long uid, DutUserNew dutUserNew) {
        if (!studentRemainsZeroPeriod(vipUser, dutUserNew)) {
            return true;
        }
        log.warn("student vip user remains 0 period, stop to autorenew. [uid:{}] [boss deadline {}] [dutUserNew deadline:{}] [taskType:{}]",
            uid, vipUser.getDeadline(), dutUserNew.getDeadline(), taskType);
        // 保存续费日志
        saveStudentRenewLog(dutRenewLog);

        // 取消自动续费
        cancelAutoRenew(vipUser, uid, OperateSceneEnum.CANCEL_STUDENTAUTORENEW24TIMES);

        return false;
    }

    private void saveStudentRenewLog(DutRenewLog dutRenewLog) {
        // 保存代扣日志记录
        dutRenewLog.setFailedInfo(DutFailedEnum.STUDENT_AUTO_RENEW_24_TIMES);
        dutService.shardSaveRenewLog(dutRenewLog);
    }

    private boolean studentRemainsZeroPeriod(VipUser vipUser, DutUserNew dutUserNew) {
        return validVipType(vipUser)
            && ActTypeEnum.STOP_AFTER_X.getValue().equals(dutUserNew.getActType())
            && !promotionProcessApi.canBuy(userId);
    }

    private boolean validVipType(VipUser vipUser) {
        return Constants.VIP_USER_STUDENT == vipUser.getTypeId()
            || Constants.VIP_USER_SUPER == vipUser.getTypeId();
    }

    @Override
    public void deserialize(String data) throws IllegalArgumentException {
        Map<String, String> paramsMap = Splitter.on("&").withKeyValueSeparator("=").split(data);
        if (MapUtils.isEmpty(paramsMap)) {
            throw new IllegalArgumentException("参数解析失败");
        }
        dutParamsDto = new DutParamsDto();
        String userParam = paramsMap.get(PARAM_USER);
        String dutBindTypeParam = paramsMap.get(PARAM_DUT_BIND_TYPE);
        if (paramsMap.containsKey(PARAM_EXECUTED_TYPES)) {
            String[] executedTypesParam = paramsMap.get(PARAM_EXECUTED_TYPES).split(QueryConstants.COMMA);
            this.executedTypes = Sets.newHashSet();
            for (String elem : executedTypesParam) {
                this.executedTypes.add(Integer.valueOf(elem));
            }
        }
        if (paramsMap.containsKey(PARAM_AUTO_RENEW_LINKED_DUT_CONFIG)) {
            String autoRenewLinkedDutConfigParam = paramsMap.get(PARAM_AUTO_RENEW_LINKED_DUT_CONFIG);
            this.autoRenewLinkedDutConfig = getFacadeManager().getAutoRenewLinkedDutConfigManager().findById(Long.valueOf(autoRenewLinkedDutConfigParam));
        }
        if (paramsMap.containsKey(PARAM_AUTORENEW_DUT_CONFIG)) {
            String autorenewDutConfigParam = paramsMap.get(PARAM_AUTORENEW_DUT_CONFIG);
            this.autorenewDutConfig = getFacadeManager().getAutorenewDutConfigManager().findById(Long.valueOf(autorenewDutConfigParam));
        }
        if (paramsMap.containsKey(PARAM_FC)) {
            this.fc = paramsMap.get(PARAM_FC);
        }
        this.agreementType = MapUtils.getInteger(paramsMap, PARAM_AGREEMENT_TYPE, AgreementTypeEnum.AUTO_RENEW.getValue());

        userId = Long.parseLong(userParam);
        this.dutBindType = Integer.parseInt(dutBindTypeParam);
        autoRenewDutType = getFacadeManager().getAutoRenewDutTypeService().getByDutType(dutBindType);
        if (autoRenewDutType != null) {
            vipType = autoRenewDutType.getVipType();
            agreementType = autoRenewDutType.getAgreementType();
        }
        this.taskType = paramsMap.get(PARAM_TASK_TYPE);
        if (paramsMap.containsKey(PARAM_NOTIFY_COUNT)) {
            try {
                this.notifyCount = Integer.valueOf(paramsMap.get(PARAM_NOTIFY_COUNT));
            } catch (Exception e) {
                log.error("[AliPayAsyncDutAutoRenewTask error] [deserialize] [uid:{}] [notifyCount:{}] " +
                    "[taskType:{}] [paras:{}] ", userId, notifyCount, taskType, this.toString());
            }
        }

        dutService = (DutService) ApplicationContextUtil.getBean("dutService");
        qiYuePlatformManager = getFacadeManager().getQiYuePlatformManager();
        dutProcessor = getFacadeManager().getDutProcessor();
        passportApi = ApplicationContextUtil.getBean(PassportApi.class);
        dutUserRenewStatusManager = ApplicationContextUtil.getBean(DutUserRenewStatusManager.class);
        userManager = getFacadeManager().getUserManager();
        autoRenewLinkedDutConfigService = (AutoRenewLinkedDutConfigService) ApplicationContextUtil.getBean("autoRenewLinkedDutConfigService");
        autorenewDutConfigService = (AutorenewDutConfigService) ApplicationContextUtil.getBean("autorenewDutConfigService");
        paymentDutTypeManager = getFacadeManager().getPaymentDutTypeManager();
        autoRenewDutTypeManager = getFacadeManager().getAutoRenewDutTypeService();
        cloudConfig = CloudConfigUtil.getCloudConfig();
        promotionProcessApi = ApplicationContextUtil.getBean(PromotionProcessApi.class);
        dutRenewSetLogService = ApplicationContextUtil.getBean(DutRenewSetLogService.class);
        dutUserService = ApplicationContextUtil.getBean(DutUserService.class);
        dutRenewLogService = ApplicationContextUtil.getBean(DutRenewLogService.class);
        autorenewPreDutRecordService = ApplicationContextUtil.getBean(AutorenewPreDutRecordService.class);
        orderService = ApplicationContextUtil.getBean(OrderService.class);
        smartJedisClient = ApplicationContextUtil.getBean(SmartJedisClient.class);
        autoRenewService = ApplicationContextUtil.getBean(AutoRenewService.class);
        agreementNoInfoManager = ApplicationContextUtil.getBean(AgreementNoInfoManager.class);
        agreementTemplateManager = ApplicationContextUtil.getBean(AgreementTemplateManager.class);
        userAgreementService = ApplicationContextUtil.getBean(UserAgreementService.class);
        commodityProxy = ApplicationContextUtil.getBean(CommodityProxy.class);

        agreementNo = MapUtils.getInteger(paramsMap, PARAM_AGREEMENT_NO, null);
        if (agreementNo != null) {
            agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
            agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
            if (agreementNoInfo != null) {
                agreementType = agreementNoInfo.getType();
            }
        }

        productCode = MapUtils.getString(paramsMap, PARAM_PRODUCT_CODE, null);
        userDutDiscountService = ApplicationContextUtil.getBean(UserDutDiscountService.class);
        vipTradeCouponProxy = ApplicationContextUtil.getBean(VipTradeCouponProxy.class);
        vipChargeApi = ApplicationContextUtil.getBean(VipChargeApi.class);
        customVipInfoClient = ApplicationContextUtil.getBean(CustomVipInfoClient.class);
    }

    @Override
    public String serialize() {
        StringBuilder buffer = new StringBuilder();
        long autoRenewLinkedDutConfigId = (autoRenewLinkedDutConfig == null) ? 0L : autoRenewLinkedDutConfig.getId();
        long autorenewDutConfigId = (autorenewDutConfig == null) ? 0L : autorenewDutConfig.getId();
        buffer.append("user=").append(userId)
            .append("&dutBindType=").append(dutBindType)
            .append("&taskType=").append(taskType)
            .append("&notifyCount=").append(notifyCount)
            .append("&executedTypes=").append(Joiner.on(QueryConstants.COMMA).skipNulls().join(executedTypes))
            .append("&autoRenewLinkedDutConfig=").append(autoRenewLinkedDutConfigId)
            .append("&autorenewDutConfig=").append(autorenewDutConfigId)
            .append("&fc=").append(fc);
        if (agreementNo != null) {
            buffer.append("&agreementNo=").append(agreementNo);
        }
        if (StringUtils.isNotBlank(productCode)) {
            buffer.append("&productCode=").append(productCode);
        }
        if (agreementType != null) {
            buffer.append("&agreementType=").append(agreementType);
        }
        return buffer.toString();
    }

    @Override
    public boolean isSupportDelay() {
        return true;
    }

    @Override
    public String toString() {
        return "AliPayAsyncDutAutoRenewTask{" +
            "product=" + productCode +
            "&user=" + userId +
            "&dutBindType=" + dutBindType +
            "&agreementNo=" + agreementNo +
            "&taskType=" + taskType +
            "&notifyCount=" + notifyCount +
            "&fc=" + this.fc +
            "&executedTypes=" + Joiner.on(QueryConstants.COMMA).skipNulls().join(executedTypes) +
            "&autoRenewLinkedDutConfig=" + this.autoRenewLinkedDutConfig.getName() +
            "&autorenewDutConfig=" + this.autorenewDutConfig.toString() +
            "&agreementType=" + this.agreementType +
            '}';
    }
    @Override
    public void setFc(String fc) {
        if (StringUtils.isEmpty(fc)) {
            this.fc = "";
            return;
        }
        this.fc = fc;
    }


}
