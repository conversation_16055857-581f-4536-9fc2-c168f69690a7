package com.qiyi.boss.async.task;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Charsets;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.Set;

import com.qiyi.boss.Constants;
import com.qiyi.boss.async.queue.AsyncTaskFactory;
import com.qiyi.boss.autorenew.dto.MessageSenderDto;
import com.qiyi.boss.autorenew.dto.OpenDutSetLogDesp;
import com.qiyi.boss.constants.ParamKeyConstants;
import com.qiyi.boss.dto.AgreementDutContext;
import com.qiyi.boss.dto.AvailableDutCouponInfo;
import com.qiyi.boss.dto.CreateOrderDto;
import com.qiyi.boss.dto.DutParamsDto;
import com.qiyi.boss.dto.OrderCreationRequest;
import com.qiyi.boss.dto.OrderPrepareDto;
import com.qiyi.boss.dto.PayCenterDutReq;
import com.qiyi.boss.dto.PayCenterDutResult;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.DutFailedEnum;
import com.qiyi.boss.exception.OrderSystemException;
import com.qiyi.boss.model.Product;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.outerinvoke.CommodityProxy;
import com.qiyi.boss.outerinvoke.CustomVipInfoClient;
import com.qiyi.boss.outerinvoke.VipTradeCouponProxy;
import com.qiyi.boss.outerinvoke.result.CommodityInfo;
import com.qiyi.boss.processor.DutProcessor;
import com.qiyi.boss.service.AutoRenewLinkedDutConfigService;
import com.qiyi.boss.service.AutoRenewService;
import com.qiyi.boss.service.AutorenewDutConfigService;
import com.qiyi.boss.service.AutorenewPreDutRecordService;
import com.qiyi.boss.service.DutService;
import com.qiyi.boss.service.DutUserService;
import com.qiyi.boss.service.OrderService;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.service.UserDutDiscountService;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.AgreementTemplateManager;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.service.impl.AutoRenewLinkedDutConfigManager;
import com.qiyi.boss.service.impl.AutoRenewUpgradeConfigManager;
import com.qiyi.boss.service.impl.DutUserRenewStatusManager;
import com.qiyi.boss.service.impl.PaymentDutTypeManager;
import com.qiyi.boss.service.impl.QiYueProductNewService;
import com.qiyi.boss.utils.ApplicationContextUtil;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.EncodeUtils;
import com.qiyi.boss.utils.MessageMQUtils;
import com.qiyi.boss.utils.PassportApi;
import com.qiyi.vip.commons.constant.QueryConstants;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.constants.TaskConstants;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AsyncTask;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewLinkedDutConfig;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewUpgradeConfig;
import com.qiyi.vip.trade.autorenew.domain.AutorenewDutConfig;
import com.qiyi.vip.trade.autorenew.domain.AutorenewPreDutRecord;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import com.qiyi.vip.trade.autorenew.domain.Platform;
import com.qiyi.vip.trade.autorenew.service.DutRenewLogService;
import com.qiyi.vip.trade.autorenew.service.DutRenewSetLogService;
import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.order.dal.model.Order;

import static com.qiyi.boss.Constants.ONE_MINUTE_IN_SECOND;
import static com.qiyi.boss.Constants.TRADE_TYPE_KEY;
import static com.qiyi.boss.Constants.TRADE_TYPE_VALUE;

/**
 * Created by IntelliJ IDEA.
 * 升级自动续费代扣任务
 *
 * <AUTHOR> liuwanqiang
 * Date: 2018-9-6
 * Time: 23:27
 */
public class UpgradeAutoRenewTask extends AbstractDutAutoRenewTask {

    private final static Logger log = LoggerFactory.getLogger(UpgradeAutoRenewTask.class);
    public static final int OFFSET_ONE = 1;


    @Override
    protected void execute() {
        long start = System.currentTimeMillis();
        if (userId == null || agreementType == null) {
            log.warn("UpgradeAutoRenewTask execute user is null or agreementType is null ! ");
            return;
        }
        log.info("[UpgradeAutoRenewTask start] [uid:{}] [agreementNo:{}] [dutType:{}] [taskType:{}]", userId, agreementNo, dutBindType, taskType);

        Long sourceVipType = autorenewDutConfig.getSourceVipType();
        Long vipType = autorenewDutConfig.getVipType();
        DutUserNew dutUserNew;
        Short priority;
        if (agreementNo != null) {
            dutUserNew = userAgreementService.getByAgreementNoAndDutType(userId, agreementNo, dutBindType, sourceVipType, vipType);
            priority = agreementNoInfo.getPriority();
            dutBindType = agreementNoInfo.getDutType();
        } else {
            dutUserNew = dutService.getInEffectUpgradeDutUserNewFromMaster(userId, dutBindType, sourceVipType, vipType);
            priority = autoRenewDutType.getPriority();
        }
        if (!validateDutUser(dutUserNew)) {
            return;
        }
        AutoRenewUpgradeConfig autoRenewUpgradeConfig = autoRenewUpgradeConfigManager.getByVipType(sourceVipType, vipType, priority);
        if (autoRenewUpgradeConfig == null) {
            log.warn("[UpgradeAutoRenewTask] [can not find autoRenewUpgradeConfig.] [{}]", this);
            return;
        }

        OpenDutSetLogDesp openSetLogDesp = dutRenewSetLogService.getLastRecentOpenSetLogDesp(userId, dutUserNew.getVipType(), dutBindType, DutRenewSetLog.RENEW_SET);
        if (StringUtils.isBlank(fc) && openSetLogDesp != null && openSetLogDesp.getFc() != null) {
            fc = openSetLogDesp.getFc();
        }
        Integer amount = null == dutUserNew.getAmount() ? Constants.AMOUNT_OF_COMMON_AUTORENEW : dutUserNew.getAmount();
        DutRenewLog dutRenewLog = DutRenewLog.initBeforeDutOfAutoRenew(dutUserNew, amount, fc, taskType);

        VipUser toVipUser = customVipInfoClient.getVipInfoNew(userId, vipType);
        if (toVipUser == null) {
            try {
                saveRetryTask(dutRenewLog, DutFailedEnum.VIP_USER_IS_NULL);
            } catch (Exception e) {
                log.error("[UpgradeAutoRenewTask] [saveRetryTask] [paras:{}] [taskType:{}]", this, taskType, e);
            }
            log.warn("[UpgradeAutoRenewTask] [vipUser null] [saveRetryTask] [paras:{}] [taskType:{}]", this, taskType);
            return;
        }
        Integer upgradeDays = customVipInfoClient.getUpgradeDays(userId, sourceVipType, vipType);
        if (upgradeDays == null) {
            try {
                saveRetryTask(dutRenewLog, DutFailedEnum.UPGRADE_DAYS_IS_NULL);
            } catch (Exception e) {
                log.error("[UpgradeAutoRenewTask] [saveRetryTask] [paras:{}] [taskType:{}]", this, taskType, e);
            }
            log.warn("[UpgradeAutoRenewTask] [user upgradeDays is null] [saveRetryTask] [paras:{}] [taskType:{}]", this, taskType);
            return;
        }

        AgreementDutContext dutContext = AgreementDutContext.buildUpgradeDutContext(dutUserNew, upgradeDays, autoRenewUpgradeConfig, agreementNoInfo, autoRenewDutType);
        if (StringUtils.isNotBlank(dutContext.getSkuId())) {
            CommodityInfo commodityInfo = commodityProxy.queryCommodity(dutContext.getSkuId());
            if (commodityInfo == null) {
                recordCommodityError(dutUserNew, dutContext.getSkuId());
                return;
            }
            dutContext.setCommodityInfo(commodityInfo);
        } else {
            QiYueProductNewService qiYueProductNewService = ApplicationContextUtil.getBean(QiYueProductNewService.class);
            Product qiyueProduct = qiYueProductNewService.getProductFromQiYueProduct(dutContext.getPid());
            dutContext.setQiyueProduct(qiyueProduct);
        }

        try {
            autoRenew(dutContext, toVipUser, dutRenewLog, dutUserNew, openSetLogDesp);
        } catch (Exception e) {
            log.error("userId={}, agreementNo={}, dutBindType={}, autorenewDutConfig={}, autoRenewLinkedDutConfig={}",
                    userId, agreementNo, dutBindType, autorenewDutConfig, autoRenewLinkedDutConfig, e);
            if (e instanceof OrderSystemException) {
                saveRetryTask(dutRenewLog, DutFailedEnum.ORDER_SAVE_FAILED);
            }
        }
        log.info("[UpgradeAutoRenewTask] [time cost:{}]", System.currentTimeMillis() - start);
    }

    /**
     * 执行自动续费
     */
    private void autoRenew(AgreementDutContext dutContext, VipUser vipUser, DutRenewLog dutRenewLog,
        DutUserNew dutUserNew, OpenDutSetLogDesp openSetLogDesp) {
        if (validate(vipUser, dutRenewLog, userId, dutUserNew)) {
            return;
        }

        Map<String, String> params = organizeParams(dutContext, vipUser.getTypeId(), userId, dutUserNew, dutRenewLog, openSetLogDesp);
        AvailableDutCouponInfo availableDutCouponInfo = getAvailableDutCouponInfoByAgreementDutContext(dutContext);
        dutParamsDto.setAvailableDutCouponInfo(availableDutCouponInfo);
        log.info("Dut by new process. Params:{}.", params);
        CreateOrderDto createOrderDto = CreateOrderDto.builder()
                .userId(userId)
                .dutParamsDto(dutParamsDto)
                .amount(dutContext.getAmount())
                .skuAmount(dutContext.getSkuAmount())
                .dutUserNew(dutUserNew)
                .params(params)
                .qiyueProduct(dutContext.getQiyueProduct())
                .commodityInfo(dutContext.getCommodityInfo())
                .build();

        Order order = null;
        OrderCreationRequest orderCreationRequest = null;
        if (AgreementTypeEnum.commonAutoRenew(agreementType)) {
            String orderCode = null;
            if (commonDutUseOrderStateMachine(createOrderDto)) {
                log.info("common dut new way, uid:{}, skuId:{}", userId, dutContext.getSkuId());
                orderCreationRequest = orderService.createUnpaidOrder(createOrderDto);
                orderCode = orderCreationRequest.getOrderCode();
            } else {
                log.info("common dut old way, uid:{}, skuId:{}", userId, dutContext.getSkuId());
                order = orderService.createOrder(createOrderDto);
                orderCode = order.getOrderCode();
            }
            dutRenewLog.setOrderCode(orderCode);
            dutRenewLog.setFee(dutParamsDto.getFee());
        } else {
            orderCreationRequest = orderService.createUnpaidOrder(createOrderDto);
            dutRenewLog.setOrderCode(orderCreationRequest.getOrderCode());
            dutRenewLog.setFee(dutParamsDto.getFee());
        }

        OrderPrepareDto orderPrepareDto = OrderPrepareDto.builder()
                .dutParamsDto(dutParamsDto)
                .order(order)
                .orderCreationRequest(orderCreationRequest)
                .qiyueProduct(dutContext.getQiyueProduct())
                .commodityInfo(dutContext.getCommodityInfo())
                .userId(userId)
                .build();

        PayCenterDutReq payCenterDutReq = orderService.prepare(orderPrepareDto);
        Optional<PayCenterDutResult> payCenterDutResponse = orderService.processPayments(payCenterDutReq);
        appendRealDutFeeToRenewLog(payCenterDutReq.getConfigs(), dutRenewLog);
        log.info("Uid:{}, Dut result: {}.", userId, payCenterDutResponse);
        if (payCenterDutResponse.isPresent() && payCenterDutResponse.get().isReqSuccess()) {
            String resultString = JSON.toJSONString(orderService.transfer(payCenterDutResponse.get(), order, orderCreationRequest, dutParamsDto.getSignOrderCode()));
            doIfRequestSuccess(dutRenewLog, dutUserNew, dutContext.getAmount(), params.get(PARAM_FC), resultString);
        } else {
            dutRenewLogService.processDutRequestFailure(dutRenewLog);
        }

        if (!dutRenewLog.isPaySuccess()) {
            // 生成下次代扣异步任务
            createNextDutTask(vipUser, userId);
        }
    }

    public void createNextDutTask(VipUser vipUser, Long uid) {
        List<DutRenewSetLog> dutRenewSetLogs = dutUserService.getDutRenewSetLogs(uid, autorenewDutConfig.getSourceVipType(), vipUser.getTypeId(), agreementType);
        if (CollectionUtils.isEmpty(dutRenewSetLogs)) {
            return;
        }

        String category = null;
        Integer dutType = null;
        Integer nextDutAgreementNo = null;
        AgreementNoInfo nextDutAgreementNoInfo = null;
        AutoRenewDutType nextDutAutoRenewDutType = null;
        String exeTimeStr = null;
        Set<Integer> currentCategoryExecutedTypes = this.executedTypes;
        for (DutRenewSetLog setLog : dutRenewSetLogs) {
            // 若未遍历完就取下一个
            Integer setLogType = setLog.getType();
            Integer payChannel = null;
            Integer setLogAgreementNo = setLog.getAgreementNo();
            if (setLogAgreementNo != null) {
                AgreementNoInfo noInfo = agreementNoInfoManager.getById(setLogAgreementNo);
                setLogType = noInfo.getDutType();
                payChannel = noInfo.getPayChannel();
            } else {
                AutoRenewDutType autoRenewDutTypeBySetLog = autoRenewDutTypeManager.getByDutType(setLogType);
                payChannel = autoRenewDutTypeBySetLog.getPayChannel();
            }
            // 判断当前的签约关系是否有链式代扣配置
            if (couldContinueOnCurrentCategory(executedTypes, payChannel, setLogType,false, autoRenewLinkedDutConfig)) {
                dutType = setLogType;
                nextDutAgreementNo = setLog.getAgreementNo();
                this.executedTypes.add(dutType);
                category = autoRenewLinkedDutConfig.getCategory();
                exeTimeStr = DateHelper.getFormatDate(DateHelper.getDateTime(), DateHelper.MOST_COMMON_PATTERN);
                break;
            }
        }
        // 若已遍历完则取第一个
        Long sourceVipType = autoRenewLinkedDutConfig.getSourceVipType();
        if (dutType == null) {
            exeTimeStr = DateHelper.getFormatDate(DateHelper.caculateTime(DateHelper.getDateTime(),
                autoRenewLinkedDutConfig.getRetryIntervalDay(), Constants.PRODUCT_PERIODUNIT_DAY), DateHelper.SIMPLE_PATTERN);
            String suffix;
            if (AutoRenewLinkedDutConfig.CATEGORY_NORMAL.equals(autoRenewLinkedDutConfig.getCategory())) {
                suffix = "1";
                category = AutoRenewLinkedDutConfig.CATEGORY_RETRY_PREFIX + AutoRenewLinkedDutConfig.CATEGORY_RETRY_DELIMITER + suffix;
                exeTimeStr += " " + DateHelper.getFormatDate(DateHelper.getDateTime(), "HH:mm:ss");
            } else {
                suffix = Integer.toString(Integer.valueOf(autoRenewLinkedDutConfig.getCategory().split(AutoRenewLinkedDutConfig.CATEGORY_RETRY_DELIMITER)[OFFSET_ONE]) + OFFSET_ONE);
                category = AutoRenewLinkedDutConfig.CATEGORY_RETRY_PREFIX + AutoRenewLinkedDutConfig.CATEGORY_RETRY_DELIMITER + suffix;
                exeTimeStr += " " + CloudConfigUtil.getAutorenewTaskRetryExecTime();
            }

            Integer jobType = autoRenewLinkedDutConfig.getJobType();
            Long configVipType = autoRenewLinkedDutConfig.getVipType();
            for (DutRenewSetLog dutRenewSetLog : dutRenewSetLogs) {
                Integer setLogType = dutRenewSetLog.getType();
                Integer payChannel = null;
                Integer setLogAgreementNo = dutRenewSetLog.getAgreementNo();
                if (setLogAgreementNo != null) {
                    AgreementNoInfo noInfo = agreementNoInfoManager.getById(setLogAgreementNo);
                    setLogType = noInfo.getDutType();
                    payChannel = noInfo.getPayChannel();
                } else {
                    AutoRenewDutType autoRenewDutTypeBySetLog = autoRenewDutTypeManager.getByDutType(setLogType);
                    payChannel = autoRenewDutTypeBySetLog.getPayChannel();
                }

                AutoRenewLinkedDutConfig linkedDutConfig = autoRenewLinkedDutConfigService.find(jobType, configVipType, sourceVipType, agreementType, payChannel, category);
                if (linkedDutConfig != null) {
                    dutType = setLogType;
                    nextDutAgreementNo = dutRenewSetLog.getAgreementNo();
                    break;
                }
            }
            if (nextDutAgreementNo != null) {
                nextDutAgreementNoInfo = agreementNoInfoManager.getById(nextDutAgreementNo);
                dutType = nextDutAgreementNoInfo.getDutType();
            } else {
                nextDutAutoRenewDutType = autoRenewDutTypeManager.getByDutType(dutType);
            }
            Integer nextDutPayChannel = getNextDutPayChannel(nextDutAgreementNoInfo, nextDutAutoRenewDutType);
            // 此处在每轮链式代扣当天增加一次支付宝异步代扣
            if (CloudConfigUtil.allowAliPayAsyncDutPay() && !nextCategoryDutOnSameDay(exeTimeStr, nextDutPayChannel, category, vipUser, this.autoRenewLinkedDutConfig)) {
                addAliPayAsyncDutTask(dutRenewSetLogs, currentCategoryExecutedTypes);
            }
            if (dutType == null) {
                log.info("UpgradeAutoRenewTask last time to retry for uid:{}, dutType:{}, autoRenewLinkedDutConfigId:{}", userId, dutBindType, autoRenewLinkedDutConfig.getId());
                return;
            }

            this.executedTypes = Sets.newHashSet(dutType);

            // 发送扣费失败提醒
            if (autoRenewLinkedDutConfig.getNeedNotify() == AutoRenewLinkedDutConfig.NEED_NOTIFY) {
                Integer payChannel = agreementNoInfo != null ? agreementNoInfo.getPayChannel() : autoRenewDutType.getPayChannel();
                String payChannelName = agreementNoInfo != null ? agreementNoInfo.getPayChannelName() : autoRenewDutType.getPayChannelName();
                MessageSenderDto senderDto = MessageSenderDto.builder()
                        .dutType(dutBindType)
                        .agreementNo(agreementNo)
                        .agreementType(agreementType)
                        .payChannel(payChannel)
                        .payChannelName(payChannelName)
                        .userId(userId)
                        .vipType(vipUser.getTypeId())
                        .failedType(AutoRenewLinkedDutConfig.FAILED_TYPE_RENEW_RETRY_FAILED).build();
                MessageMQUtils.sendMessage(senderDto);
            }


        } else {
            if (nextDutAgreementNo != null) {
                nextDutAgreementNoInfo = agreementNoInfoManager.getById(nextDutAgreementNo);
                dutType = nextDutAgreementNoInfo.getDutType();
            } else {
                nextDutAutoRenewDutType = autoRenewDutTypeManager.getByDutType(dutType);
            }
        }
        Integer nextDutPayChannel = getNextDutPayChannel(nextDutAgreementNoInfo, nextDutAutoRenewDutType);
        AutoRenewLinkedDutConfig linkedDutConfig = getAutoRenewLinkedDutConfigByDutTypeAndCategory(nextDutPayChannel, category, vipUser, sourceVipType, AutoRenewLinkedDutConfig.JOB_TYPE_UPGRADE, agreementType);
        if (linkedDutConfig == null) {
            // 已经是最后一轮重试
            log.info("UpgradeAutoRenewTask last time to retry for uid:{}, dutType:{}, config:{}", userId, dutBindType, this.autoRenewLinkedDutConfig);
            return;

        }

        if (isDutByThird(vipUser.getTypeId(), userId, agreementType)) {
            //若开通由第三方扣费的自动续费类型(如苹果),则不发起代扣
            log.info("[用户已开通由第三方扣费自动续费类型(如苹果),故不发起扣费] [uid:{}]", userId);
            return;
        }

        saveAsyncTask(uid, dutType, nextDutAgreementNo, exeTimeStr, linkedDutConfig);
    }


    private void addAliPayAsyncDutTask(List<DutRenewSetLog> dutRenewSetLogs, Set<Integer> currentCategoryExecutedTypes) {
        String currentCategory = autoRenewLinkedDutConfig.getCategory();
        if (!CloudConfigUtil.allowAliPayAsyncDutThisCategory(currentCategory)) {
            return;
        }

        List<Integer> aliPayDutTypes = autoRenewDutTypeManager.getDutTypeListByVipTypeAndPayChannel(autoRenewLinkedDutConfig.getVipType(), agreementType, PaymentDutType.PAY_CHANNEL_ALIPAY);
        if (!isAliPayChannelAutoRenewUser(dutRenewSetLogs, aliPayDutTypes)) {
            return;
        }

        DutRenewSetLog aliSetLog = getBindedAliPayDutType(dutRenewSetLogs, aliPayDutTypes);
        if (aliSetLog == null) {
            return;
        }

        Date beginDate = getBeginDateWithAutoRenewLinkedDutConfig();
        int hourInterval = DateHelper.getHourInterval(beginDate, DateHelper.getTomorrowDate());
        if (hourInterval < CloudConfigUtil.getAliPayAsyncDutMinExpireTime()) {
            log.info("addAliPayAsyncDutTask not match min time limit: uid:{}, dutType:{}, agreementNo:{}, category:{}",
                this.userId, aliSetLog.getType(), aliSetLog.getAgreementNo(), currentCategory);
            return;
        }
        Timestamp execTime = new Timestamp(beginDate.getTime());
        saveAliPayAsyncTask(userId, aliSetLog, autoRenewLinkedDutConfig, execTime, currentCategoryExecutedTypes);
    }

    private Date getBeginDateWithAutoRenewLinkedDutConfig() {
        String nowDateTimeStr = DateHelper.getNowDateTime();
        Timestamp beginTimestamp = getExeTime(nowDateTimeStr, autoRenewLinkedDutConfig, autoRenewLinkedDutConfig);
        return DateHelper.getDate(beginTimestamp);
    }

    private void saveAliPayAsyncTask(Long uid, DutRenewSetLog aliSetLog, AutoRenewLinkedDutConfig linkedDutConfig, Timestamp exeTime, Set<Integer> currentCategoryExecutedTypes) {
        AliPayAsyncUpgradeDutAutoRenewTask task = new AliPayAsyncUpgradeDutAutoRenewTask();
        task.setUserId(uid);
        task.setDutBindType(aliSetLog.getType());
        task.setAgreementNo(aliSetLog.getAgreementNo());
        task.setAutoRenewLinkedDutConfig(linkedDutConfig);
        task.setAutorenewDutConfig(autorenewDutConfig);
        task.setExecutedTypes(currentCategoryExecutedTypes);
        Integer agreementType = aliSetLog.getAgreementType();
        task.setAgreementType(agreementType);
        task.setTaskType(buildAliPayAsyncDutTaskType(linkedDutConfig.getCategory()));
        String className = task.getClass().getName();
        task.setTaskId(genTaskId(userId, autorenewDutConfig.getVipType(), autorenewDutConfig.getSourceVipType(), agreementType, className));
        AsyncTaskFactory.getInstance().delayInsertIntoDB(task, getPoolType(userId), AsyncTask.Task_PRIORITY_1, exeTime);
    }

    private Timestamp getExeTime(String exeTimeStr, AutoRenewLinkedDutConfig config) {
        Timestamp executeTime = DateHelper.getTimestamp(exeTimeStr);
        executeTime = DateHelper.caculateTime(executeTime, autoRenewLinkedDutConfig.getRetryIntervalMin(), Constants.PRODUCT_PERIODUNIT_MINUTE);
        int second = new Random().nextInt(config.getOffset() * ONE_MINUTE_IN_SECOND);
        executeTime = DateHelper.caculateTime(executeTime, second, Constants.PRODUCT_PERIODUNIT_SECOND);
        return executeTime;
    }


    private boolean isDutByThird(Long vipType, Long userId, Integer agreementType) {
        List<Integer> thirdDutTypeList = autoRenewDutTypeManager.getDutTypeListByVipTypeAndPayChannelType(vipType, agreementType, PaymentDutType.PAY_CHANNEL_TYPE_THIRD_DUT_PAY);

        return dutService.shardIsAutoRenewUserIncludeType(userId, vipType, thirdDutTypeList);
    }


    private Map<String, String> organizeParams(AgreementDutContext dutContext, Long vipType, Long uid,
        DutUserNew dutUserNew, DutRenewLog dutRenewLog, OpenDutSetLogDesp openSetLogDesp) {
        Integer renewPrice = dutContext.getRenewPrice();
        Map<String, String> params = Maps.newHashMap();
        params.put("pid", dutContext.getPid());
        dutParamsDto.setAgreementType(dutContext.getAgreementType());
        dutParamsDto.setProductName(dutContext.getProductName());
        dutParamsDto.setVipType(dutUserNew.getVipType());
        dutParamsDto.setSourceVipType(autorenewDutConfig.getSourceVipType());
        params.put("dutType", String.valueOf(dutBindType));
        dutParamsDto.setDutType(dutBindType);
        dutParamsDto.setPayChannel(dutContext.getPayChannel());
        params.put("amount", String.valueOf(dutContext.getAmount()));
        dutParamsDto.setAmount(dutContext.getAmount());
        String skuId = dutContext.getSkuId();
        if (dutContext.getCommodityInfo() != null) {
            params.put("skuId", skuId);
            params.put("skuAmount", String.valueOf(dutContext.getSkuAmount()));
        }
        params.put("serviceCode", autorenewDutConfig.getServiceCode());
        dutParamsDto.setServiceCode(autorenewDutConfig.getServiceCode());
        dutParamsDto.setPartnerId(dutContext.getPartnerId());
        log.info("[UpgradeAutoRenewTask] [user:{}] [product:{}] [agreementNo:{}] [dutType:{}] [renewPrice:{}] [taskType:{}]",
                uid, dutContext.getPid(), agreementNo, dutBindType, renewPrice, taskType);
        if (renewPrice > 0) {
            params.put(RESPONSE_FEE, String.valueOf(renewPrice));
            dutParamsDto.setFee(renewPrice);
        }
        params.put("BACKEND-AUTO-RENEW", "1");
        params.put("uid", String.valueOf(uid));
        dutParamsDto.setUid(uid);
        params.put("payType", String.valueOf(dutContext.getPayType()));
        dutParamsDto.setPayType(dutContext.getPayType());
        dutParamsDto.setPlatform(dutUserNew.getPlatformCode());
        if (StringUtils.isBlank(dutUserNew.getPlatformCode()) && dutUserNew.getPlatform() != null) {
            Platform platform = qiYuePlatformManager.getBossPlatformById(dutUserNew.getPlatform());
            if (platform != null) {
                String platformCode = platform.getCode();
                params.put("platform", platformCode);
                dutParamsDto.setPlatform(platformCode);
            }
        }
        params.put("fc", fc);
        dutParamsDto.setFc(fc);
        String fv = getFv(openSetLogDesp, dutUserNew.getVipType(), skuId);
        params.put("fv", fv);
        dutParamsDto.setFv(fv);

        if (StringUtils.isNotBlank(dutUserNew.getOrderCode())) {
            params.put("signOrderCode", dutUserNew.getOrderCode());
            dutParamsDto.setSignOrderCode(dutUserNew.getOrderCode());
        }

        // 供支付中心标记重试订单
        if (!Constants.TASK_TYPE_NORMAL.equals(taskType)) {
            params.put("orderType", "autoRenewRetry");
            dutParamsDto.setOrderType("autoRenewRetry");
        }

        if (dutRenewLog.getDescription().contains("signFlag")) {
            Map<String, Object> map = JSON.parseObject(dutRenewLog.getDescription());
            params.put("signFlag", String.valueOf(map.get("signFlag")));
            dutParamsDto.setSignFlag(String.valueOf(map.get("signFlag")));
        }

        params.put("taskType", taskType);
        dutParamsDto.setTaskType(taskType);

        Map<String, Object> businessProperty = Maps.newHashMap();
        businessProperty.put(ParamKeyConstants.SERIAL_RENEW_COUNT, dutUserNew.getCurrentSerialRenewCount());
        AutorenewPreDutRecord autorenewPreDutRecord = autorenewPreDutRecordService
                .getAutorenewPreDutRecord(uid, vipType, dutBindType, dutContext.getAmount());
        if (autorenewPreDutRecord != null) {
            businessProperty.put("ABTestGroup", autorenewPreDutRecord.getGroupKey());
        }
        if (dutParamsDto.getPartnerId() != null) {
            businessProperty.put("partnerId", dutParamsDto.getPartnerId());
        }
        if (StringUtils.isNotBlank(dutUserNew.getSignKey())) {
            businessProperty.put(ParamKeyConstants.SIGN_KEY, dutUserNew.getSignKey());
        }
        if (!isNotInWhiteList(uid)) {
            businessProperty.put(ParamKeyConstants.WHITE_USER_DUT_KEY, true);
        }
        businessProperty.put(TRADE_TYPE_KEY, TRADE_TYPE_VALUE);
        params.put("businessProperty", JSON.toJSONString(businessProperty));
        dutParamsDto.setBusinessProperty(businessProperty);

        String sign = getQiyueSign(params);
        params.put("sign", sign);
        return params;
    }

    @Override
    public void deserialize(String data) {
        Map<String, String> paramsMap = Splitter.on("&").withKeyValueSeparator("=").split(data);
        if (MapUtils.isEmpty(paramsMap)) {
            throw new IllegalArgumentException("参数解析失败");
        }
        dutParamsDto = new DutParamsDto();
        String userParam = paramsMap.get(PARAM_USER);
        String dutBindTypeParam = paramsMap.get(PARAM_DUT_BIND_TYPE);
        if (paramsMap.containsKey(PARAM_EXECUTED_TYPES)) {
            String[] executedTypesParam = paramsMap.get(PARAM_EXECUTED_TYPES).split(QueryConstants.COMMA);
            this.executedTypes = Sets.newHashSet();
            for (String elem : executedTypesParam) {
                this.executedTypes.add(Integer.valueOf(elem));
            }
        }
        if (paramsMap.containsKey(PARAM_AUTO_RENEW_LINKED_DUT_CONFIG)) {
            String autoRenewLinkedDutConfigParam = paramsMap.get(PARAM_AUTO_RENEW_LINKED_DUT_CONFIG);
            this.autoRenewLinkedDutConfig = getFacadeManager().getAutoRenewLinkedDutConfigManager().findById(Long.valueOf(autoRenewLinkedDutConfigParam));
        }
        if (paramsMap.containsKey(PARAM_AUTORENEW_DUT_CONFIG)) {
            String autorenewDutConfigParam = paramsMap.get(PARAM_AUTORENEW_DUT_CONFIG);
            this.autorenewDutConfig = getFacadeManager().getAutorenewDutConfigManager().findById(Long.valueOf(autorenewDutConfigParam));
        }
        if (paramsMap.containsKey(PARAM_FC)) {
            this.fc = paramsMap.get(PARAM_FC);
        }
        if (paramsMap.containsKey(PARAM_AGREEMENT_TYPE)) {
            this.agreementType = MapUtils.getInteger(paramsMap, PARAM_AGREEMENT_TYPE, null);
        }

        userId = Long.parseLong(userParam);
        this.dutBindType = Integer.parseInt(dutBindTypeParam);
        autoRenewDutType = getFacadeManager().getAutoRenewDutTypeService().getByDutType(dutBindType);
        if (autoRenewDutType != null) {
            vipType = autoRenewDutType.getVipType();
            agreementType = autoRenewDutType.getAgreementType();
        }
        this.taskType = paramsMap.get(PARAM_TASK_TYPE);
        this.generatedBy = MapUtils.getString(paramsMap, "generatedBy");

        if (paramsMap.containsKey(PARAM_NOTIFY_COUNT)) {
            try {
                this.notifyCount = Integer.valueOf(paramsMap.get(PARAM_NOTIFY_COUNT));
            } catch (Exception e) {
                log.error("[UpgradeAutoRenewTask error] [deserialize] [uid:{}] [notifyCount:{}] " +
                        "[taskType:{}] [paras:{}] ", userId, notifyCount, taskType, this.toString());
            }
        }

        dutService = (DutService) ApplicationContextUtil.getBean("dutService");
        qiYuePlatformManager = getFacadeManager().getQiYuePlatformManager();
        dutProcessor = getFacadeManager().getDutProcessor();
        passportApi = ApplicationContextUtil.getBean(PassportApi.class);
        dutUserRenewStatusManager = ApplicationContextUtil.getBean(DutUserRenewStatusManager.class);
        userManager = getFacadeManager().getUserManager();
        customVipInfoClient = ApplicationContextUtil.getBean(CustomVipInfoClient.class);
        autoRenewLinkedDutConfigService = (AutoRenewLinkedDutConfigService) ApplicationContextUtil.getBean("autoRenewLinkedDutConfigService");
        autorenewDutConfigService = (AutorenewDutConfigService) ApplicationContextUtil.getBean("autorenewDutConfigService");
        paymentDutTypeManager = getFacadeManager().getPaymentDutTypeManager();
        autoRenewDutTypeManager = getFacadeManager().getAutoRenewDutTypeService();
        autoRenewUpgradeConfigManager = getFacadeManager().getAutoRenewUpgradeConfigManager();
        cloudConfig = CloudConfigUtil.getCloudConfig();
        dutRenewSetLogService = ApplicationContextUtil.getBean(DutRenewSetLogService.class);
        dutRenewLogService = ApplicationContextUtil.getBean(DutRenewLogService.class);
        dutUserService = ApplicationContextUtil.getBean(DutUserService.class);
        autorenewPreDutRecordService = ApplicationContextUtil.getBean(AutorenewPreDutRecordService.class);
        orderService = ApplicationContextUtil.getBean(OrderService.class);
        autoRenewService = ApplicationContextUtil.getBean(AutoRenewService.class);
        agreementNoInfoManager = ApplicationContextUtil.getBean(AgreementNoInfoManager.class);
        agreementTemplateManager = ApplicationContextUtil.getBean(AgreementTemplateManager.class);
        userAgreementService = ApplicationContextUtil.getBean(UserAgreementService.class);
        commodityProxy = ApplicationContextUtil.getBean(CommodityProxy.class);

        agreementNo = MapUtils.getInteger(paramsMap, PARAM_AGREEMENT_NO, null);
        if (agreementNo != null) {
            agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
            agreementTemplate = agreementTemplateManager.getByCode(agreementNoInfo.getTemplateCode());
            if (agreementNoInfo != null) {
                agreementType = agreementNoInfo.getType();
            }
        }
        userDutDiscountService = ApplicationContextUtil.getBean(UserDutDiscountService.class);
        vipTradeCouponProxy = ApplicationContextUtil.getBean(VipTradeCouponProxy.class);
    }

    @Override
    public String serialize() {
        StringBuilder buffer = new StringBuilder();
        long autoRenewLinkedDutConfigId = (autoRenewLinkedDutConfig == null) ? 0L : autoRenewLinkedDutConfig.getId();
        long autorenewDutConfigId = (autorenewDutConfig == null) ? 0L : autorenewDutConfig.getId();
        buffer.append("user=").append(userId)
                .append("&dutBindType=").append(dutBindType)
                .append("&taskType=").append(taskType)
                .append("&notifyCount=").append(notifyCount)
                .append("&executedTypes=").append(Joiner.on(QueryConstants.COMMA).skipNulls().join(executedTypes))
                .append("&autoRenewLinkedDutConfig=").append(autoRenewLinkedDutConfigId)
                .append("&autorenewDutConfig=").append(autorenewDutConfigId)
                .append("&fc=").append(fc);
        if (agreementNo != null) {
            buffer.append("&agreementNo=").append(agreementNo);
        }
        if (agreementType != null) {
            buffer.append("&agreementType=").append(agreementType);
        }
        if (StringUtils.isNotBlank(generatedBy)) {
            buffer.append("&generatedBy=").append(generatedBy);
        }
        return buffer.toString();
    }

    @Override
    public boolean isSupportDelay() {
        return true;
    }

    @Override
    public String toString() {
        return "UpgradeAutoRenewTask{" +
                "&user=" + userId +
                "&dutBindType=" + dutBindType +
                "&agreementNo=" + agreementNo +
                "&taskType=" + taskType +
                "&notifyCount=" + notifyCount +
                "&fc=" + this.fc +
                "&executedTypes=" + Joiner.on(QueryConstants.COMMA).skipNulls().join(executedTypes) +
                "&autoRenewLinkedDutConfig=" + this.autoRenewLinkedDutConfig.getName() +
                "&autorenewDutConfig=" + this.autorenewDutConfig.toString() +
                "&agreementType=" + this.agreementType +
                "&generatedBy=" + this.generatedBy +
                '}';
    }

    private void saveRetryTask(DutRenewLog dutRenewLog, DutFailedEnum dutFailedEnum) {
        if (notifyCount > MAX_NOTIFY_COUNT) {
            log.error("[UpgradeAutoRenewTask] [saveRetryTask] [taskType:{}] " +
                    "[notifyCount greater than 6] [paras:{}]", taskType, this);
            sendErrorMail(userId, null, null, "UpgradeAutoRenewTask notifyCount greater than 6");

            if (dutFailedEnum != null) {
                dutRenewLog.setFailedInfo(dutFailedEnum);
            }
            dutRenewLog.setStatus(DutRenewLog.PAY_FAILURE);
            dutService.shardSaveRenewLog(dutRenewLog);
            return;
        }

        log.warn("[{} retry at {} time] [taskType:{}]", getClass().getSimpleName(), notifyCount, taskType);
        String times = Constants.NOTIFY_TIME[notifyCount];
        UpgradeAutoRenewTask task = new UpgradeAutoRenewTask();
        task.setUserId(userId);
        task.setDutBindType(dutBindType);
        task.setAgreementNo(agreementNo);
        task.setTaskType(taskType);
        task.setNotifyCount(++notifyCount);
        task.setFc(fc);
        task.setExecutedTypes(Sets.newHashSet(dutBindType));
        task.setAutoRenewLinkedDutConfig(autoRenewLinkedDutConfig);
        task.setAutorenewDutConfig(autorenewDutConfig);
        task.setAgreementType(agreementType);
        String className = task.getClass().getName();
        task.setTaskId(genTaskId(userId, autorenewDutConfig.getVipType(), autorenewDutConfig.getSourceVipType(), agreementType, className));

        Timestamp notifyTime = DateHelper.caculateTime(DateHelper.getDateTime(), times);

        AsyncTaskFactory.getInstance().delayInsertIntoDB(task, getPoolType(userId), TaskConstants.PRIORITY_HIGH, notifyTime);

    }

    private void saveAsyncTask(Long uid, Integer dutType, Integer nextDutAgreementNo, String exeTimeStr,
                               AutoRenewLinkedDutConfig linkedDutConfig) {
        UpgradeAutoRenewTask task = new UpgradeAutoRenewTask();
        task.setUserId(uid);
        task.setDutBindType(dutType);
        task.setAgreementNo(nextDutAgreementNo);
        task.setAgreementType(agreementType);
        task.setAutoRenewLinkedDutConfig(linkedDutConfig);
        task.setAutorenewDutConfig(autorenewDutConfig);
        task.setExecutedTypes(executedTypes);
        task.setTaskType(linkedDutConfig.getCategory());
        task.setAgreementType(agreementType);
        String className = task.getClass().getName();
        task.setTaskId(genTaskId(userId, autorenewDutConfig.getVipType(), autorenewDutConfig.getSourceVipType(), agreementType, className));
        if (Constants.TASK_TYPE_NORMAL.equals(linkedDutConfig.getCategory())) {
            task.setGeneratedBy(Constants.TASK_GENERATE_BY_MULTI_SIGN);
        }

        Timestamp exeTime = getExeTime(exeTimeStr, linkedDutConfig);
        AsyncTaskFactory.getInstance().delayInsertIntoDB(task, getPoolType(userId), AsyncTask.Task_PRIORITY_1, exeTime);

    }

    public String genTaskId(Long userId, Long vipType, Long sourceVipType, Integer agreementType, String className) {
        //需要保证正常任务和重试任务不能同时存在
        String taskIdStr = new StringBuilder()
            .append(className)
            .append(userId)
            .append(vipType)
            .append(sourceVipType)
            .append(agreementType)
            .toString();
        return EncodeUtils.MD5(taskIdStr, Charsets.UTF_8.name());
    }


    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getDutBindType() {
        return dutBindType;
    }

    public void setDutBindType(Integer dutBindType) {
        this.dutBindType = dutBindType;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public DutProcessor getDutProcessor() {
        return dutProcessor;
    }

    public void setDutProcessor(DutProcessor dutProcessor) {
        this.dutProcessor = dutProcessor;
    }

    public Integer getNotifyCount() {
        return notifyCount;
    }

    public void setNotifyCount(Integer notifyCount) {
        this.notifyCount = notifyCount;
    }

    public Set<Integer> getExecutedTypes() {
        return executedTypes;
    }

    public void setExecutedTypes(Set<Integer> executedTypes) {
        this.executedTypes = executedTypes;
    }

    public AutoRenewLinkedDutConfigService getAutoRenewLinkedDutConfigService() {
        return autoRenewLinkedDutConfigService;
    }

    public AutorenewDutConfig getAutorenewDutConfig() {
        return autorenewDutConfig;
    }

    public void setAutorenewDutConfig(AutorenewDutConfig autorenewDutConfig) {
        this.autorenewDutConfig = autorenewDutConfig;
    }

    public String getFc() {
        return fc;
    }

    public void setFc(String fc) {
        this.fc = fc;
    }

    public AutoRenewLinkedDutConfig getAutoRenewLinkedDutConfig() {
        return autoRenewLinkedDutConfig;
    }

    public void setAutoRenewLinkedDutConfig(AutoRenewLinkedDutConfig autoRenewLinkedDutConfig) {
        this.autoRenewLinkedDutConfig = autoRenewLinkedDutConfig;
    }

    public void setAutoRenewLinkedDutConfigService(AutoRenewLinkedDutConfigManager autoRenewLinkedDutConfigService) {
        this.autoRenewLinkedDutConfigService = autoRenewLinkedDutConfigService;
    }

    public AutorenewDutConfigService getAutorenewDutConfigService() {
        return autorenewDutConfigService;
    }

    public void setAutorenewDutConfigService(AutorenewDutConfigService autorenewDutConfigService) {
        this.autorenewDutConfigService = autorenewDutConfigService;
    }

    public PaymentDutTypeManager getPaymentDutTypeManager() {
        return paymentDutTypeManager;
    }

    public void setPaymentDutTypeManager(PaymentDutTypeManager paymentDutTypeManager) {
        this.paymentDutTypeManager = paymentDutTypeManager;
    }

    public AutoRenewDutTypeManager getAutoRenewDutTypeManager() {
        return autoRenewDutTypeManager;
    }

    public void setAutoRenewDutTypeManager(AutoRenewDutTypeManager autoRenewDutTypeManager) {
        this.autoRenewDutTypeManager = autoRenewDutTypeManager;
    }

    public AutoRenewUpgradeConfigManager getAutoRenewUpgradeConfigManager() {
        return autoRenewUpgradeConfigManager;
    }

    public void setAutoRenewUpgradeConfigManager(AutoRenewUpgradeConfigManager autoRenewUpgradeConfigManager) {
        this.autoRenewUpgradeConfigManager = autoRenewUpgradeConfigManager;
    }

    public CloudConfig getCloudConfig() {
        return cloudConfig;
    }

    public void setCloudConfig(CloudConfig cloudConfig) {
        this.cloudConfig = cloudConfig;
    }
}
