package com.qiyi.boss.async.task;

import com.google.common.base.Splitter;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.Map;
import java.util.StringJoiner;

import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.boss.service.impl.DutManager;
import com.qiyi.boss.utils.DateHelper;

/**
 *  监听order_finish消息开通自动续费失败，保存的异步任务
 *  <AUTHOR>
 */
public class MonthlyRenewTask_Dut extends AbstractTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(MonthlyRenewTask_Dut.class);

    protected Long userId;
    protected Integer dutType = 1;

    private AutorenewRequest autorenewRequest;
    protected Integer productType;
    protected Long platformId;
    protected Integer payType;
    protected Integer payChannel;
    protected Integer amount;
    protected String orderCode;
    protected Long sourceVipType;
	protected Long vipType;
    protected String autoRenew;
    protected String platformCode;
    protected Timestamp payTime;
    protected Timestamp deadline;
    protected String actCode;

    public MonthlyRenewTask_Dut() {
    }

    public MonthlyRenewTask_Dut(AutorenewRequest autorenewRequest) {
        try {
            this.userId = autorenewRequest.getUserId();
            this.dutType = autorenewRequest.getDutType();
            this.productType = autorenewRequest.getProductType();
            this.platformId = autorenewRequest.getPlatformId();
            this.payType = autorenewRequest.getPayType();
            this.payChannel = autorenewRequest.getPayChannel();
            this.amount = autorenewRequest.getAmount();
            this.orderCode = autorenewRequest.getOrderCode();
            this.sourceVipType = autorenewRequest.getSourceVipType();
            this.vipType = autorenewRequest.getVipType();
            this.autoRenew = autorenewRequest.getAutoRenew();
            this.platformCode = autorenewRequest.getPlatformCode();
            this.payTime = autorenewRequest.getPayTime();
            this.deadline = autorenewRequest.getDeadline();
            this.actCode = autorenewRequest.getActCode();

            this.autorenewRequest = autorenewRequest;
        } catch (Exception e) {
            LOGGER.warn("Error happen when build MainlandMonthlyRenewTask", e);
        }
    }

    @Override
    protected void execute() {
        LOGGER.info("Start to enable TW auto renew. UserID: {}, ProdType: {}, " +
                        "PlatformID: {}, PayType: {}, Amount: {}, OrderCode: {} vipType: {}",
                userId, productType, platformId, payType, amount, orderCode, vipType);
        DutManager dutManager = getFacadeManager().getDutManager();
        try {
            dutManager.reProcessMonthlyRenew(autorenewRequest);
        } catch (Exception e) {
            LOGGER.warn("[Error happens when do execute MonthlyRenewTask_Dut.] [userId:{}] [dutType:{}]", userId, dutType, e);
        }
        LOGGER.info("[module:order] [action:execute] [userId:{}] [not sales promotion]", userId);
    }

    @Override
    public void deserialize(String data) throws IllegalArgumentException {
        try {
            Map<String, String> paramsMap = Splitter.on("&").withKeyValueSeparator("=").split(data);
            AutorenewRequest autorenewRequest = new AutorenewRequest();
            autorenewRequest.setUserId(Long.parseLong(paramsMap.get("userId")));
            autorenewRequest.setDutType(Integer.parseInt(paramsMap.get("dutType")));
            autorenewRequest.setProductType(Integer.parseInt(paramsMap.get("productType")));
            autorenewRequest.setPlatformId(Long.parseLong(paramsMap.get("platformId")));
            autorenewRequest.setPayType(Integer.parseInt(paramsMap.get("payType")));
            autorenewRequest.setAmount(Integer.parseInt(paramsMap.get("amount")));
            autorenewRequest.setOrderCode(paramsMap.get("orderCode"));
            if (paramsMap.containsKey("sourceVipType")) {
                autorenewRequest.setSourceVipType(Long.parseLong(paramsMap.get("sourceVipType")));
            }
            autorenewRequest.setVipType(Long.parseLong(paramsMap.get("vipType")));
            autorenewRequest.setAutoRenew(paramsMap.get("autoRenew"));
            autorenewRequest.setPlatformCode(paramsMap.get("platformCode"));
            if (paramsMap.containsKey("payTime")) {
                autorenewRequest.setPayTime(DateHelper.getTimestamp(MapUtils.getString(paramsMap, "payTime")));
            }
            String deadline = MapUtils.getString(paramsMap, "deadline");
            if (StringUtils.isNotBlank(deadline)) {
                autorenewRequest.setDeadline(DateHelper.getTimestamp(deadline));
            }
            autorenewRequest.setPayChannel(MapUtils.getInteger(paramsMap, "payChannel"));
            if (paramsMap.containsKey("actCode")) {
                autorenewRequest.setActCode(MapUtils.getString(paramsMap, "actCode"));
            }

			this.autorenewRequest = autorenewRequest;
        } catch (Exception e) {
            LOGGER.warn("Error happen when deserialize {}", this.getClass().getSimpleName(), e);
        }
    }

    @Override
    public String serialize() {
        StringBuilder buffer = new StringBuilder();
        buffer.append("userId=").append(userId);
        buffer.append("&dutType=").append(dutType);
        buffer.append("&payChannel=").append(payChannel);
        buffer.append("&productType=").append(productType);
        buffer.append("&platformId=").append(platformId);
        buffer.append("&payType=").append(payType);
        buffer.append("&amount=").append(amount);
        buffer.append("&orderCode=").append(orderCode);
        if (sourceVipType != null) {
            buffer.append("&sourceVipType=").append(sourceVipType);
        }
        buffer.append("&vipType=").append(vipType);
		buffer.append("&autoRenew=").append(autoRenew);
        buffer.append("&platformCode=").append(platformCode);
        buffer.append("&payTime=").append(DateHelper.getMostCommonPatternStr(payTime));
        if (deadline != null) {
            buffer.append("&deadline=").append(DateHelper.getMostCommonPatternStr(deadline));
        }
        if (StringUtils.isNotBlank(actCode)) {
            buffer.append("&actCode=").append(actCode);
        }
		return buffer.toString();
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", MonthlyRenewTask_Dut.class.getSimpleName() + "[", "]")
            .add("userId=" + userId)
            .add("dutType=" + dutType)
            .add("autorenewRequest=" + autorenewRequest)
            .add("productType=" + productType)
            .add("platformId=" + platformId)
            .add("payType=" + payType)
            .add("amount=" + amount)
            .add("orderCode='" + orderCode + "'")
            .add("sourceVipType=" + sourceVipType)
            .add("vipType=" + vipType)
            .add("autoRenew='" + autoRenew + "'")
            .add("platformCode='" + platformCode + "'")
            .add("payTime=" + payTime)
            .add("deadline=" + deadline)
            .toString();
    }
}
