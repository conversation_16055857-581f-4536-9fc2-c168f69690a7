package com.qiyi.boss.outerinvoke.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Set;
import com.qiyi.boss.dto.CouponSkuInfo;
import com.qiyi.boss.model.CouponBatch;
import com.qiyi.boss.model.PayCenterCoupon;


/**
 * createTime:  2023/07/26
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CouponContext {

    /**
     * 可用的券批次信息
     */
    private CouponBatch couponBatch;
    /**
     * sku信息
     */
    private Set<CouponSkuInfo> skuInfoSet;
    /**
     * 支付中心券基础信息
     */
    private PayCenterCoupon coupon;
}
