package com.qiyi.boss.outerinvoke.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/9/26 上午 10:26
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class GlobalCouponQueryParam {

    /**
     * 用券前付费金额
     */
    private Integer needPayFee;
    /**
     * 支付渠道
     */
    private Integer payChannel;
    /**
     * 会员类型：2:黄金；1：钻石
     */
    private Integer vipTag;
    /**
     * 地区
     */
    private String region;
    /**
     * 用户uid
     */
    private Long userId;
    /**
     * 语言
     */
    private String lang;
    /**
     * 地区，与region保持一致
     */
    private String app_lm;
    /**
     * 时区
     */
    private String timeZone;


}
