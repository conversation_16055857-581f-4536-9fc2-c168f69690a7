package com.qiyi.boss.outerinvoke.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

import com.qiyi.boss.utils.JacksonUtils;

/**
 * <AUTHOR>
 * @date 2021/9/27 下午 05:25
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class CounsumeCouponReqParm {

    private String couponCode;
    private String orderCode;

    public Map<String, String> toStringMap() {
        return JacksonUtils.beanToStringMap(this);
    }
}
