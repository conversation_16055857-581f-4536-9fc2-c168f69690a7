package com.qiyi.boss.outerinvoke;

import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.outerinvoke.result.VcqContentBaseInfo;
import com.qiyi.boss.outerinvoke.result.VcqContentCustomQueryResp;
import com.qiyi.boss.utils.CommonHystrixCommand;
import com.qiyi.boss.utils.SignatureUtil;
import com.qiyi.vip.commons.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;

/**
 * 内容查询系统代理层
 * Created at: 2021-11-15
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ContentQueryServerProxy extends BaseApi {

    @Value("${content.query.server.domain}")
    String contentQueryServerDomain;
    @Value("${content.query.server.sign.key}")
    String contentQueryServerSignKey;
    @Resource
    RestTemplate contentQueryServerClient;

    /**
     * 内容付费信息自定义查询接口
     * http://wiki.qiyi.domain/pages/viewpage.action?pageId=737282456
     */
    private static final String CONTENT_CUSTOM_QUERY_URL = "/content/custom/query";

    private Function<Map<String, Object>, BaseResponse<VcqContentCustomQueryResp>> queryContentBaseInfoRunFunc = param -> doGet(contentQueryServerClient,
            contentQueryServerDomain + CONTENT_CUSTOM_QUERY_URL, param, "内容付费信息自定义查询接口",
            new ParameterizedTypeReference<BaseResponse<VcqContentCustomQueryResp>>() {});
    private Function<Map<String, Object>, BaseResponse<VcqContentCustomQueryResp>> queryContentBaseInfoFallbackFunc = param -> BaseResponse.create(BaseResponse.CodeEnum.OUTER_SERVICE_ERROR);

    /**
     * 根据aid查询内容基本信息
     * @param aid
     */
    public VcqContentBaseInfo queryContentBaseInfo(Long aid) {
        if (aid == null || aid == 0L) {
            return null;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("bizSource", Constants.BIZ_SOURCE);
        params.put("messageId", UUID.randomUUID().toString().replaceAll("-", ""));
        params.put("platform", com.qiyi.boss.Constants.PLATFORM_DEFAULT_CODE);
        params.put("columns", "BASE");
        params.put("aid", aid);
        params.put("sign", SignatureUtil.genContentQueryServerSign(params, contentQueryServerSignKey));
        BaseResponse<VcqContentCustomQueryResp> response = new CommonHystrixCommand<>(HystrixCommandPropsEnum.CONTENT_CUSTOM_QUERY,
                queryContentBaseInfoRunFunc,
                queryContentBaseInfoFallbackFunc,
                params).execute();
        if (response.isFailure()) {
            return null;
        }
        return response.getData().getBase();
    }

}
