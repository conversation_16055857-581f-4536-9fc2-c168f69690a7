package com.qiyi.boss.outerinvoke;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.dto.SingleResponse;

/**
 * <AUTHOR>
 * @date 2021/12/27 下午 02:36
 */
@Slf4j
public class PayInfoApi {
    /**
     *
     * @param callable
     * @param <T>
     * @return
     */
    public static  <T> SingleResponse<T> invokePayInfoForSingleResponse(Callable callable) {
        Retryer<SingleResponse<T>> retryer = RetryerBuilder
            .<SingleResponse<T>>newBuilder()
            //抛出runtime异常、checked异常时都会重试，但是抛出error不会重试。
            .retryIfException()
            //返回false也需要重试
            .retryIfResult(Response::isFailed)
            //重调策略
            .withWaitStrategy(WaitStrategies.fixedWait(100, TimeUnit.MILLISECONDS))
            //尝试次数
            .withStopStrategy(StopStrategies.stopAfterAttempt(2))
            .build();
        try {
            SingleResponse<T> response = retryer.call(callable);
            log.info("invokePayInfoForSingleResponse, response:{}, data:{}", response, response.getData());
            return response;
        } catch (Exception e) {
            log.error("invokePayInfoForSingleResponse error:", e);
            return null;
        }
    }

    /**
     *
     * @param callable
     * @param <T>
     * @return
     */
    public static <T> MultiResponse<T> invokePayInfoForMultiResponse(Callable callable) {
        Retryer<MultiResponse<T>> retryer = RetryerBuilder
            .<MultiResponse<T>>newBuilder()
            //抛出runtime异常、checked异常时都会重试，但是抛出error不会重试。
            .retryIfException()
            //返回false也需要重试
            .retryIfResult(Response::isFailed)
            //重调策略
            .withWaitStrategy(WaitStrategies.fixedWait(100, TimeUnit.MILLISECONDS))
            //尝试次数
            .withStopStrategy(StopStrategies.stopAfterAttempt(2))
            .build();
        try {
            MultiResponse<T> response = retryer.call(callable);
            log.info("invokePayInfoForMultiResponse, response:{} , data:{}", response, response.getData());
            if (response.isSuccess() && response.getData() != null) {
                return response;
            }
            return null;
        } catch (Exception e) {
            log.error("invokePayInfoForMultiResponse error:", e);
            return null;
        }
    }

}
