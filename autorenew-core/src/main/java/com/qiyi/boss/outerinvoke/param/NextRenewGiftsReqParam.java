package com.qiyi.boss.outerinvoke.param;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created at: 2020-10-26
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class NextRenewGiftsReqParam {

    private String P00001;
    private Integer[] vipTypes;

    public JSONObject toJSONObject() {
        JSONObject jsonObj = new JSONObject();
        jsonObj.put("P00001", P00001);
        jsonObj.put("vipTypes", vipTypes);
        return jsonObj;
    }

}
