package com.qiyi.boss.outerinvoke.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @auther: guojing
 * @date: 2023/8/11 2:39 PM
 */
@Slf4j
public class OrderCoreSignatureInterceptor implements ClientHttpRequestInterceptor {

    private static final String SIGNATURE_HEADER = "X-Signature";
    private static final String SOURCE_HEADER = "X-Source";

    private final String source;
    private final String signatureKey;

    public OrderCoreSignatureInterceptor(String source, String signatureKey) {
        this.source = source;
        this.signatureKey = signatureKey;
    }

    @Override
    public ClientHttpResponse intercept(final HttpRequest request, final byte[] body, final ClientHttpRequestExecution execution) throws IOException {

        // 获取请求参数
        Map<String, String[]> requestParams = new LinkedHashMap<>();
        UriComponentsBuilder.fromUri(request.getURI())
            .build()
            .getQueryParams()
            .forEach((key, value) -> requestParams.put(key, value.toArray(new String[0])));

        // 获取请求体
        String requestBody = new String(body, StandardCharsets.UTF_8);

        // 生成签名
        String signature;
        try {
            signature = generateSignature(requestParams, requestBody, signatureKey);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }

        // 添加签名到请求头
        request.getHeaders().add(SIGNATURE_HEADER, signature);
        request.getHeaders().add(SOURCE_HEADER, source);

        return execution.execute(request, body);
    }

    private String generateSignature(Map<String, String[]> requestParams, String requestBody, String signatureKey)
        throws NoSuchAlgorithmException {

        // 将请求参数和请求体按照一定规则排序和拼接
        // 可根据具体需求调整排序和拼接逻辑
        StringBuilder sb = new StringBuilder();
        requestParams.keySet().stream()
            .sorted()
            .forEach(requestKey -> sb.append(requestKey).append("=").append(
                String.join(",", requestParams.get(requestKey))).append("&"));
        sb.append(requestBody);
        sb.append(signatureKey);

        // 使用指定的签名算法计算签名
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        md.update(sb.toString().getBytes());
        byte[] digest = md.digest();

        // 将签名转换为十六进制
        return byte2Hex(digest);
    }

    private static String byte2Hex(byte[] digest) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : digest) {
            hexString.append(String.format("%02x", b));
        }
        return hexString.toString();
    }

}
