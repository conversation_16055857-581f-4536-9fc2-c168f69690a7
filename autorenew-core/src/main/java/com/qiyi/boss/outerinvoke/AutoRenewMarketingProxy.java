package com.qiyi.boss.outerinvoke;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.qiyi.boss.dto.DutDiscountTemplate;
import com.qiyi.boss.dto.GiftCard;
import com.qiyi.boss.dto.UnusedGiftInfo;
import com.qiyi.boss.dto.UnusedGiftListResp;
import com.qiyi.boss.dto.UserDutDiscountInfo;
import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.boss.exception.BizException;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.model.BaseResponse.CodeEnum;
import com.qiyi.boss.outerinvoke.param.DutDiscountConsumeParam;
import com.qiyi.boss.outerinvoke.param.NextRenewGiftsReqParam;
import com.qiyi.boss.outerinvoke.param.UnusedGiftListReqParam;
import com.qiyi.boss.outerinvoke.result.DutDiscountConsumeResp;
import com.qiyi.boss.utils.AppConfig;
import com.qiyi.boss.utils.CommonHystrixCommand;
import com.qiyi.boss.utils.SignatureUtil;
import com.iqiyi.kit.http.client.util.HttpClients;

@Slf4j
@Component
public class AutoRenewMarketingProxy {

    @Value("${autorenew.marketing.invoke.channel}")
    String channel;
    @Value("${autorenew.marketing.invoke.sign.key}")
    String signKey;

    private static final String AUTO_RENEW_MARKETING_DOMAIN = AppConfig.getProperty("autorenew.marketing.domain");
    /**
     * 查询下次续费可领福利列表
     */
    private static final String NEXT_RENEW_GIFTS_URL = AUTO_RENEW_MARKETING_DOMAIN + "/autorenew-marketing/userGifts/nextRenew";
    /**
     * 立减优惠核销url
     */
    private static final String DUT_DISCOUNT_CONSUME_URL = AUTO_RENEW_MARKETING_DOMAIN + "/autorenew-marketing/dutDiscount/consume";
    /**
     * 根据立减优惠批次号查询模板信息
     */
    private static final String GET_DUT_DISCOUNT_TEMPLATE_INFO_URL = AUTO_RENEW_MARKETING_DOMAIN + "/autorenew-marketing/dutDiscount/getTemplateInfoByBatchNo";
    /**
     * 查询用户立减优惠列表
     */
    private static final String GET_USER_DUT_DISCOUNTS_URL = AUTO_RENEW_MARKETING_DOMAIN + "/autorenew-marketing/dutDiscount/getUserDutDiscounts";

    private static final String GET_UNUSED_GIFT_LIST_URL = AUTO_RENEW_MARKETING_DOMAIN + "/autorenew-marketing/userGifts/unusedList";
    /**
     * 自动续费营销活动登记
     */
    private static final String ACT_REGISTER_URL = AUTO_RENEW_MARKETING_DOMAIN + "/autorenew-marketing/activity/register";
    /**
     * 自动续费营销免密活动类型
     */
    private static final int PASSWORD_FREE_ACT_TYPE = 5;

    private Function<NextRenewGiftsReqParam, Map<Integer, List<GiftCard>>> nextRenewGiftsRunFunc = param -> doGetRequest(
            param.toJSONObject(), NEXT_RENEW_GIFTS_URL, "查询下次续费福利列表",
            new ParameterizedTypeReference<BaseResponse<Map<Integer, List<GiftCard>>>>() {});
    private Function<NextRenewGiftsReqParam, Map<Integer, List<GiftCard>>> nextRenewGiftsFallbackFunc = t -> Collections.emptyMap();
    private Function<DutDiscountConsumeParam, DutDiscountConsumeResp> consumeDutDiscountRunFunc = this::doConsumeDutDiscount;
    private Function<DutDiscountConsumeParam, DutDiscountConsumeResp> consumeDutDiscountFallbackFunc = t -> null;
    private Function<JSONObject, DutDiscountTemplate> getDutDiscountTemplateRunFunc = param -> doGetRequest(
            param, GET_DUT_DISCOUNT_TEMPLATE_INFO_URL, "查询立减优惠模板信息",
            new ParameterizedTypeReference<BaseResponse<DutDiscountTemplate>>() {});
    private Function<JSONObject, DutDiscountTemplate> getDutDiscountTemplateFallbackFunc = param -> null;
    private Function<JSONObject, List<UserDutDiscountInfo>> getUserDutDiscountsRunFunc = param -> doGetRequest(
            param, GET_USER_DUT_DISCOUNTS_URL, "查询用户立减优惠列表",
            new ParameterizedTypeReference<BaseResponse<List<UserDutDiscountInfo>>>() {});
    private Function<JSONObject, List<UserDutDiscountInfo>> getUserDutDiscountsFallbackFunc = param -> null;
    private Function<UnusedGiftListReqParam, UnusedGiftListResp> queryUnusedGiftRunFunc = reqParam -> doGetRequest(
        reqParam.toParamMap(), GET_UNUSED_GIFT_LIST_URL, "查询未使用福利列表", new ParameterizedTypeReference<BaseResponse<UnusedGiftListResp>>() {});
    private Function<UnusedGiftListReqParam, UnusedGiftListResp> queryUnusedGiftFallbackFunc = t -> null;

    private Function<Map<String, Object>, BaseResponse<Map<String, Object>>> actRegisterRunFunc = reqParam ->
        doGetResp(reqParam, ACT_REGISTER_URL, "自动续费营销免密活动登记", new ParameterizedTypeReference<BaseResponse<Map<String, Object>>>() {});
    private Function<Map<String, Object>, BaseResponse<Map<String, Object>>> actRegisterFallbackFunc = t -> BaseResponse.create(CodeEnum.OUTER_SERVICE_ERROR);


    @Resource(name = "autoRenewMarketingHttpClient")
    private RestTemplate autoRenewMarketingClient;
    @Resource
    RestTemplate dutDiscountConsumeClient;

    public List<UnusedGiftInfo> getUnusedGiftList(String authCookie, Integer vipType) {
        if (StringUtils.isBlank(authCookie) || vipType == null) {
            return Collections.emptyList();
        }
        UnusedGiftListReqParam reqParam = UnusedGiftListReqParam.builder()
            .P00001(authCookie)
            .vipType(vipType.longValue())
            .build();
       UnusedGiftListResp unusedGiftListResp = new CommonHystrixCommand<>(
            HystrixCommandPropsEnum.QUERY_UNUSED_GIFT_LIST,
            queryUnusedGiftRunFunc,
            queryUnusedGiftFallbackFunc,
            reqParam
        ).execute();
        return unusedGiftListResp != null ? unusedGiftListResp.getUnusedGiftList() : Collections.emptyList();
    }

    /**
     * 查询下次续费后可领福利列表
     *
     * @param P00001
     * @param vipTypes
     */
    public Map<Integer, List<GiftCard>> nextRenewGifts(String P00001, List<Integer> vipTypes) {
        if (CollectionUtils.isEmpty(vipTypes)) {
            return Collections.emptyMap();
        }
        NextRenewGiftsReqParam reqParam = NextRenewGiftsReqParam.builder()
                .P00001(P00001)
                .vipTypes(vipTypes.toArray(new Integer[]{}))
                .build();
        Map<Integer, List<GiftCard>> result = new CommonHystrixCommand<>(
                HystrixCommandPropsEnum.NEXT_RENEW_GIFTS,
                nextRenewGiftsRunFunc,
                nextRenewGiftsFallbackFunc,
                reqParam
        ).execute();
        return result != null ? result : Collections.emptyMap();
    }

    /**
     * 核销立减优惠
     */
    public DutDiscountConsumeResp consumeDutDiscount(DutDiscountConsumeParam consumeParam) {
        return new CommonHystrixCommand<>(
                HystrixCommandPropsEnum.DUT_DISCOUNT_CONSUME,
                consumeDutDiscountRunFunc,
                consumeDutDiscountFallbackFunc,
                consumeParam,
                true
        ).execute();
    }

    private DutDiscountConsumeResp doConsumeDutDiscount(DutDiscountConsumeParam consumeParam) {
        JSONObject consumeParamJson = consumeParam.toJSONObject();
        consumeParamJson.put("_channel", channel);
        String sign = SignatureUtil.generateSign(consumeParamJson, signKey);
        consumeParamJson.put("sign", sign);
        ParameterizedTypeReference<BaseResponse<DutDiscountConsumeResp>> typeReference = new ParameterizedTypeReference<BaseResponse<DutDiscountConsumeResp>>() {};
        BaseResponse<DutDiscountConsumeResp> baseResponse = doGet(dutDiscountConsumeClient, consumeParamJson, DUT_DISCOUNT_CONSUME_URL, "核销立减优惠", typeReference);
        return baseResponse != null ? baseResponse.getData() : null;
    }

    /**
     * 查询立减优惠模板信息
     */
    public DutDiscountTemplate getTemplateInfoByBatchNo(String batchNo) {
        JSONObject param = new JSONObject();
        param.put("batchNo", batchNo);
        param.put("_channel", channel);
        String sign = SignatureUtil.generateSign(param, signKey);
        param.put("sign", sign);
        return new CommonHystrixCommand<>(
                HystrixCommandPropsEnum.GET_DUT_DISCOUNT_TEMPLATE_INFO,
                getDutDiscountTemplateRunFunc,
                getDutDiscountTemplateFallbackFunc,
                param,
                true
        ).execute();
    }

    /**
     * 查询用户立减优惠列表
     */
    public Map<Integer, List<UserDutDiscountInfo>> getUserDutDiscounts(Long userId) {
        JSONObject param = new JSONObject();
        param.put("userId", userId);
        param.put("_channel", channel);
        String sign = SignatureUtil.generateSign(param, signKey);
        param.put("sign", sign);
        List<UserDutDiscountInfo> userDutDiscounts = new CommonHystrixCommand<>(
                HystrixCommandPropsEnum.GET_USER_DUT_DISCOUNTS,
                getUserDutDiscountsRunFunc,
                getUserDutDiscountsFallbackFunc,
                param,
                true
        ).execute();
        if (CollectionUtils.isEmpty(userDutDiscounts)) {
            return Collections.emptyMap();
        }
        return userDutDiscounts.stream().collect(Collectors.groupingBy(UserDutDiscountInfo::getVipType));
    }

    /**
     * 免密签约成功后，登记免密激励活动
     */
    public void passwordFreeActRegister(Long userId, String fc) {
        Map<String, Object> params = new HashMap<>();
        params.put("uid", userId);
        params.put("type", PASSWORD_FREE_ACT_TYPE);
        params.put("_channel", channel);
        if (StringUtils.isNotBlank(fc)) {
            params.put("fc", fc);
        }
        String sign = SignatureUtil.generateSign(params, signKey);
        params.put("sign", sign);
        BaseResponse<Map<String, Object>> baseResponse = new CommonHystrixCommand<>(HystrixCommandPropsEnum.AUTORENEW_MARKETING_PROXY,
            actRegisterRunFunc, actRegisterFallbackFunc, params).execute();
        if (baseResponse.outerInvokeError() || baseResponse.isSystemError()) {
            log.error("免密签约成功后，请求登记免密激励活动失败，userId: {}, fc: {}, response:{}", userId, fc, baseResponse);
            throw BizException.newException(CodeEnum.PASSWORD_FREE_REGISTER_ERROR);
        }
    }

    private <T> T doGetRequest(Map<String, Object> requestParams, String requestUrl, String logHeader, ParameterizedTypeReference<BaseResponse<T>> typeReference) {
        BaseResponse<T> baseResponse = doGet(autoRenewMarketingClient, requestParams, requestUrl, logHeader, typeReference);
        return baseResponse != null ? baseResponse.getData() : null;
    }

    private <T> BaseResponse<T> doGetResp(Map<String, Object> requestParams, String requestUrl, String logHeader, ParameterizedTypeReference<BaseResponse<T>> typeReference) {
        return doGet(autoRenewMarketingClient, requestParams, requestUrl, logHeader, typeReference);
    }

    private <T> BaseResponse<T> doGet(RestTemplate restTemplate, Map<String, Object> requestParams, String requestUrl, String logHeader, ParameterizedTypeReference<BaseResponse<T>> typeReference) {
        String urlWithParam = HttpClients.buildQueryUrl(requestUrl, requestParams);
        ResponseEntity<BaseResponse<T>> responseEntity = null;
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            responseEntity = restTemplate.exchange(urlWithParam, HttpMethod.GET, null, typeReference);
        } catch (Exception e) {
            log.error(logHeader + "失败，url: {}, cost: {}", urlWithParam, stopWatch.getTime(), e);
            return BaseResponse.create(CodeEnum.OUTER_SERVICE_ERROR);
        }
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            int statusCode = responseEntity.getStatusCodeValue();
            log.error(logHeader + "失败，url: {}, httpStatusCode: {}, cost: {}", urlWithParam, statusCode, stopWatch.getTime());
            return BaseResponse.create(CodeEnum.OUTER_SERVICE_ERROR);
        }
        BaseResponse<T> responseBody = responseEntity.getBody();
        if (responseBody == null || responseBody.isFailure()) {
            log.info(logHeader + "失败，url: {}, cost: {}, result: {}", urlWithParam, stopWatch.getTime(), JSON.toJSONString(responseBody));
        } else {
            log.info(logHeader + "成功，url: {}, cost: {}, result: {}", urlWithParam, stopWatch.getTime(), JSON.toJSONString(responseBody));
        }
        return responseBody;
    }
}
