package com.qiyi.boss.outerinvoke;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import javax.annotation.Resource;
import java.util.function.Function;
import com.qiyi.boss.dto.BindRelationReqParam;
import com.qiyi.boss.dto.FamilyBindRelation;
import com.qiyi.boss.enums.CodeEnum;
import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.boss.exception.BizException;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.utils.CommonHystrixCommand;
import com.qiyi.boss.utils.JacksonUtils;
import static com.qiyi.boss.model.BaseResponse.CodeEnum.OUTER_SERVICE_ERROR;

/**
 * <AUTHOR>
 * @className VipChargeApi
 * @description
 * @date 2024/8/30
 **/
@Slf4j
@Component
public class VipChargeApi extends BaseApi {

    @Resource
    private RestTemplate vipChargeClient;

    @Value("${vip.charge.domain}")
    private String vipChargeDomain;

    @Value("${vip.charge.signKey}")
    private String vipChargeSignKey;

    private static final String SOURCE = "autorenew";

    public static final String BIND_RELATION_SOURCE_UID_URL = "/charge/api/relation/user/bindRelationBySourceUid";
    public static final String BIND_RELATION_TARGET_UID_URL = "/charge/api/relation/user/bindRelationByTargetUid";

    private Function<BindRelationReqParam, BaseResponse<FamilyBindRelation>> getBindRelationBySourceUid = param -> doGet(
        vipChargeClient, vipChargeDomain + BIND_RELATION_SOURCE_UID_URL, JacksonUtils.beanToMap(param), "根据亲情卡发起用户查询绑定关系",
        new ParameterizedTypeReference<BaseResponse<FamilyBindRelation>>() {});
    private Function<BindRelationReqParam, BaseResponse<FamilyBindRelation>> getBindRelationBySourceUidFallbackFunc = param -> null;

    private Function<BindRelationReqParam, BaseResponse<FamilyBindRelation>> getBindRelationByTargetUid = param -> doGet(
        vipChargeClient, vipChargeDomain + BIND_RELATION_TARGET_UID_URL, JacksonUtils.beanToMap(param), "根据亲情卡被绑定用户查询绑定关系",
        new ParameterizedTypeReference<BaseResponse<FamilyBindRelation>>() {});
    private Function<BindRelationReqParam, BaseResponse<FamilyBindRelation>> getBindRelationByTargetUidFallbackFunc = t -> null;


    public FamilyBindRelation queryBindRelationBySourceUid(Long uid) {
        BindRelationReqParam reqParam = new BindRelationReqParam();
        reqParam.setUid(uid);
        reqParam.setSource(SOURCE);
        reqParam.setSign(reqParam.getSign(vipChargeSignKey));
        BaseResponse<FamilyBindRelation> response = new CommonHystrixCommand<>(
            HystrixCommandPropsEnum.FAMILY_BIND_QUERY,
            getBindRelationBySourceUid,
            getBindRelationBySourceUidFallbackFunc,
            reqParam,
            true
        ).execute();
        if (response != null && response.isSuccess()) {
            return response.getData();
        }
        if (response != null && OUTER_SERVICE_ERROR.getCode().equals(response.getCode())) {
            log.error("queryBindRelationBySourceUid error, uid:{}", uid);
            throw new BizException(CodeEnum.OUTER_SERVICE_ERROR);
        }
        return null;
    }

    public FamilyBindRelation queryBindRelationByTargetUid(Long uid) {
        BindRelationReqParam reqParam = new BindRelationReqParam();
        reqParam.setUid(uid);
        reqParam.setSource(SOURCE);
        reqParam.setSign(reqParam.getSign(vipChargeSignKey));
        BaseResponse<FamilyBindRelation> response = new CommonHystrixCommand<>(
            HystrixCommandPropsEnum.FAMILY_BIND_QUERY,
            getBindRelationByTargetUid,
            getBindRelationByTargetUidFallbackFunc,
            reqParam,
            true
        ).execute();
        if (response != null && response.isSuccess()) {
            return response.getData();
        }
        if (response != null && OUTER_SERVICE_ERROR.getCode().equals(response.getCode())) {
            log.error("queryBindRelationBySourceUid error, uid:{}", uid);
            throw new BizException(CodeEnum.OUTER_SERVICE_ERROR);
        }
        return null;
    }
}
