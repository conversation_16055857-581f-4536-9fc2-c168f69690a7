package com.qiyi.boss.outerinvoke.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className MultiVipInfoReqParam
 * @description
 * @date 2024/9/5
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class MultiVipInfoReqParam {

    private Map<String, Object> urlParam;

    private Map<String, Object> bodyParam;

    private List<Long> uid;

    private List<Long> vipTypes;
}
