package com.qiyi.boss.outerinvoke.param;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @className UnusedGiftListReqParam
 * @description
 * @date 2022/7/29
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class UnusedGiftListReqParam {

    private Long uid;

    @ApiModelProperty(value = "P00001", required = true)
    private String P00001;

    @ApiModelProperty(value = "会员类型", required = true)
    private Long vipType;

    public Map<String, Object> toParamMap() {
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("P00001", P00001);
        paramMap.put("vipType", vipType);
        return paramMap;
    }
}
