package com.qiyi.boss.outerinvoke.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * Created at: 2020-12-01
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class DutDiscountConsumeResp {

    private Long uid;
    /**
     * 会员类型
     */
    private Integer vipType;
    /**
     * 被核销的立减优惠id
     */
    private Long dutDiscountId;
    /**
     * 优惠金额，单位：分
     */
    private Integer discountAmount;
    /**
     * 优惠核销时间
     */
    private Timestamp consumeTime;

}
