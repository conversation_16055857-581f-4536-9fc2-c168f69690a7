package com.qiyi.boss.outerinvoke.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 国际站代金券接口：http://wiki.qiyi.domain/pages/viewpage.action?pageId=1122700441
 * <AUTHOR>
 * @date 2021/9/26 上午 10:15
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class GlobalCoupon {

    /**
     * 券名称
     */
    private String name;
    /**
     * 券状态：Integer 1 待使用 2 已冻结 3 已使用 4 已过期
     */
    private Integer couponStatus;
    /**
     * 券码
     */
    private String couponCode;
    /**
     * 券批次号
     */
    private String couponBatchNo;
    /**
     * 优惠券类型:1：立减；2：折扣
     */
    private Integer couponType;
    /**
     * (1：立减）对应立减金额（分）
     */
    private Integer discountPrice;
    /**
     * (2：折扣）对应折扣（五折：50）
     */
    private Integer allowance;
    /**
     * 优惠券等价金额
     */
    private Integer couponEquivalencePrice;


}
