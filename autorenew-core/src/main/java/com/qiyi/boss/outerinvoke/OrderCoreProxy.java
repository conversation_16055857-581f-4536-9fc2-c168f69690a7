package com.qiyi.boss.outerinvoke;

import com.alibaba.csp.sentinel.slots.block.SentinelRpcException;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.qiyi.boss.dto.OrderCreationRequest;
import com.qiyi.boss.exception.OrderSystemException;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.outerinvoke.param.OrderFulfillRequest;
import com.qiyi.boss.utils.JacksonUtils;
import com.iqiyi.kit.http.client.util.HttpClients;
import com.iqiyi.vip.order.dal.model.Order;
import static com.qiyi.boss.Constants.QIYUE_ORDER_AUTORENEW_FIRST;
import static com.qiyi.boss.Constants.QIYUE_ORDER_AUTORENEW_FIRST_LARGE;

/**
 * @auther: guojing
 * @date: 2023/8/11 10:38 AM
 */
@Slf4j
@Component
public class OrderCoreProxy {

    private static final int RETRY_TIMES = 2;

    //保存或更新订单
    private static final String ORDER_SAVE_URL = "/api/orders/repo/save";
    private static final String CREATE_UNPAID_ORDER_V2 = "/api/order/createUnpaidV2";
    //根据订单号查询订单
    private static final String ORDER_QUERY_BY_ORDER_CODE_URL = "/api/orders/repo/byOrderCode";
    //支付并履约订单
    private static final String ORDER_PAY_AND_FULFILL_URL = "/api/order/payAndFulfill";

    @Resource
    private RestTemplate orderCoreClient;
    @Resource
    private RestTemplate orderCoreSlowClient;

    @Value("${order.core.domain:http://ORDER-SYSTEM}")
    private String orderCoreDomain;



    public boolean createUnpaidOrder(OrderCreationRequest request) {
        String requestUrl = orderCoreDomain + CREATE_UNPAID_ORDER_V2;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<OrderCreationRequest> requestEntity = new HttpEntity<>(request, headers);
        boolean result = false;
        try {
            result = doSaveUnpaidOrder(requestUrl, requestEntity);
            if (!result) {
                result = retryUnpaidSaveOrder(requestUrl, requestEntity);
            }
        } catch (Exception e) {
            if (e instanceof SentinelRpcException && e.getCause() instanceof DegradeException) {
                SentinelRpcException rpcException = (SentinelRpcException) e;
                DegradeException degradeException = (DegradeException) rpcException.getCause();
                log.error("CircuitBreaker Opened, resourceName: {}", degradeException.getRule().getResource(), e);
                return false;
            }
            log.error("saveOrder occurred exception, order: {}", JacksonUtils.toJsonString(request), e);
            result = retryUnpaidSaveOrder(requestUrl, requestEntity);
        }
        return result;
    }


    private boolean doSaveUnpaidOrder(String requestUrl, HttpEntity<OrderCreationRequest> requestEntity) {
        StopWatch stopWatch = StopWatch.createStarted();
        OrderCreationRequest creationRequest = requestEntity.getBody();
        log.info("请求订单核心服务保存订单V2接口-start，url: {}, body: {}", requestUrl, JacksonUtils.toJsonString(creationRequest));
        ResponseEntity<BaseResponse> responseEntity = orderCoreSlowClient.exchange(requestUrl, HttpMethod.POST,
            requestEntity, new ParameterizedTypeReference<BaseResponse>() {});
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error("请求订单核心服务保存订单V2接口-响应码非200，url: {}, httpStatusCode: {}, cost: {}", requestUrl,
                responseEntity.getStatusCodeValue(), stopWatch.getTime());
            return false;
        }
        BaseResponse responseBody = responseEntity.getBody();
        log.info("请求订单核心服务保存订单V2接口-end，url: {}, cost: {}, result: {}", requestUrl, stopWatch.getTime(), JacksonUtils.toJsonString(responseBody));
        return responseBody != null && responseBody.isSuccess();
    }

    private boolean retryUnpaidSaveOrder(String requestUrl, HttpEntity<OrderCreationRequest> requestEntity) {
        boolean result = false;
        for (int i = 0; i < RETRY_TIMES; i++) {
            try {
                result = doSaveUnpaidOrder(requestUrl, requestEntity);
                if (result) {
                    break;
                }
            } catch (Exception retryException) {
                if (retryException instanceof SentinelRpcException && retryException.getCause() instanceof DegradeException) {
                    SentinelRpcException rpcException = (SentinelRpcException) retryException;
                    DegradeException degradeException = (DegradeException) rpcException.getCause();
                    log.error("CircuitBreaker Opened, resourceName: {}", degradeException.getRule().getResource(), retryException);
                    break;
                }
                log.error("Retry saveOrder occurred exception, order: {}", JacksonUtils.toJsonString(requestEntity.getBody()), retryException);
            }
        }
        return result;
    }

    /**
     * 保存订单
     */
    public boolean saveOrder(Order order) {
        String requestUrl = orderCoreDomain + ORDER_SAVE_URL;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        List<Order> orders = Lists.newArrayList(order);
        if (CollectionUtils.isNotEmpty(order.goodsOrder())) {
            orders.addAll(order.goodsOrder());
        }
        HttpEntity<List<Order>> requestEntity = new HttpEntity<>(orders, headers);
        boolean result = false;
        try {
            result = doSaveOrder(requestUrl, requestEntity);
            if (!result) {
                result = retrySaveOrder(requestUrl, requestEntity);
            }
        } catch (Exception e) {
            if (e instanceof SentinelRpcException && e.getCause() instanceof DegradeException) {
                SentinelRpcException rpcException = (SentinelRpcException) e;
                DegradeException degradeException = (DegradeException) rpcException.getCause();
                log.error("CircuitBreaker Opened, resourceName: {}", degradeException.getRule().getResource(), e);
                return false;
            }
            log.error("saveOrder occurred exception, order: {}", JacksonUtils.toJsonString(order), e);
            result = retrySaveOrder(requestUrl, requestEntity);
        }
        return result;
    }

    private boolean retrySaveOrder(String requestUrl, HttpEntity<List<Order>> requestEntity) {
        boolean result = false;
        for (int i = 0; i < RETRY_TIMES; i++) {
            try {
                result = doSaveOrder(requestUrl, requestEntity);
                if (result) {
                    break;
                }
            } catch (Exception retryException) {
                if (retryException instanceof SentinelRpcException && retryException.getCause() instanceof DegradeException) {
                    SentinelRpcException rpcException = (SentinelRpcException) retryException;
                    DegradeException degradeException = (DegradeException) rpcException.getCause();
                    log.error("CircuitBreaker Opened, resourceName: {}", degradeException.getRule().getResource(), retryException);
                    break;
                }
                log.error("Retry saveOrder occurred exception, order: {}", JacksonUtils.toJsonString(requestEntity.getBody()), retryException);
            }
        }
        return result;
    }

    private boolean doSaveOrder(String requestUrl, HttpEntity<List<Order>> requestEntity) {
        StopWatch stopWatch = StopWatch.createStarted();
        List<Order> body = requestEntity.getBody();
        log.info("请求订单核心服务保存订单接口-start，url: {}, body: {}", requestUrl, JacksonUtils.toJsonString(body));
        ResponseEntity<BaseResponse<String>> responseEntity = orderCoreSlowClient.exchange(requestUrl, HttpMethod.POST,
            requestEntity, new ParameterizedTypeReference<BaseResponse<String>>() {});
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error("请求订单核心服务保存订单接口-响应码非200，url: {}, httpStatusCode: {}, cost: {}", requestUrl,
                responseEntity.getStatusCodeValue(), stopWatch.getTime());
            return false;
        }
        BaseResponse<String> responseBody = responseEntity.getBody();
        log.info("请求订单核心服务保存订单接口-end，url: {}, cost: {}, result: {}", requestUrl, stopWatch.getTime(), JacksonUtils.toJsonString(responseBody));
        return responseBody != null && responseBody.isSuccess();
    }



    /**
     * 根据订单号查询订单信息
     */
    public Order getByOrderCode(String orderCode) {
        String requestUrl = orderCoreDomain + ORDER_QUERY_BY_ORDER_CODE_URL;
        Map<String, Object> params = new HashMap<>();
        params.put("orderCode", orderCode);
        String urlWithParam = MapUtils.isNotEmpty(params) ? HttpClients.buildQueryUrl(requestUrl, params) : requestUrl;
        Order result = null;
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            result = doGetByOrderCode(urlWithParam);
        } catch (Exception e) {
            if (e instanceof SentinelRpcException && e.getCause() instanceof DegradeException) {
                SentinelRpcException rpcException = (SentinelRpcException) e;
                DegradeException degradeException = (DegradeException) rpcException.getCause();
                log.error("CircuitBreaker Opened, resourceName: {}", degradeException.getRule().getResource(), e);
                return null;
            }
            log.error("请求订单核心服务查询订单接口-出现异常，url: {}, cost: {}", urlWithParam, stopWatch.getTime(), e);
            if (!(e instanceof OrderSystemException)) {
                result = retryGetOrderCode(urlWithParam);
            }
        }
        return result;
    }

    /**
     * 根据订单号查询自动续费订单信息
     * @param orderCode
     * @return Order
     */
    public Order getAutoRenewOrderByOrderCode(String orderCode) {
        Order order = getByOrderCode(orderCode);
        if (order == null) {
            return null;
        }
        Integer autoRenew = order.getAutoRenew();
        if (isAutoRenew(autoRenew)) {
            return order;
        }
        return order.goodsOrder().stream().filter(o -> isAutoRenew(o.getAutoRenew())).findFirst().orElse(null);
    }

    private static boolean isAutoRenew(Integer autoRenew) {
        return autoRenew != null
            && (QIYUE_ORDER_AUTORENEW_FIRST.equals(String.valueOf(autoRenew)) || QIYUE_ORDER_AUTORENEW_FIRST_LARGE.equals(String.valueOf(autoRenew)));
    }

    private Order retryGetOrderCode(String urlWithParam) {
        Order result = null;
        for (int i = 0; i < RETRY_TIMES; i++) {
            try {
                result = doGetByOrderCode(urlWithParam);
                break;
            } catch (Exception retryException) {
                if (retryException instanceof SentinelRpcException && retryException.getCause() instanceof DegradeException) {
                    SentinelRpcException rpcException = (SentinelRpcException) retryException;
                    DegradeException degradeException = (DegradeException) rpcException.getCause();
                    log.error("CircuitBreaker Opened, resourceName: {}", degradeException.getRule().getResource(), retryException);
                    break;
                }
                log.error("Retry getByOrderCode occurred exception, url: {}", urlWithParam, retryException);
                if (!(retryException instanceof OrderSystemException)) {
                    result = retryGetOrderCode(urlWithParam);
                }
            }
        }
        return result;
    }

    private Order doGetByOrderCode(String urlWithParam) {
        StopWatch stopWatch = StopWatch.createStarted();
        log.info("请求订单核心服务查询订单接口-start，url: {}", urlWithParam);
        ResponseEntity<BaseResponse<Order>> responseEntity = orderCoreClient.exchange(urlWithParam, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResponse<Order>>() {
        });
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error("请求订单核心服务查询订单接口-响应码非200，url: {}, httpStatusCode: {}, cost: {}", urlWithParam, responseEntity.getStatusCodeValue(), stopWatch.getTime());
            return null;
        }
        BaseResponse<Order> responseBody = responseEntity.getBody();
        log.info("请求订单核心服务查询订单接口-end，url: {}, cost: {}, result: {}", urlWithParam, stopWatch.getTime(), JacksonUtils.toJsonString(responseBody));
        if (responseBody == null || responseBody.isFailure()) {
            throw OrderSystemException.newQueryOrderException(responseBody);
        }
        return responseBody.getData();
    }

    /**
     * 支付并履约订单
     * @param orderCode 订单号
     * @param request 订单履约请求
     * @return 是否成功
     */
    public boolean payAndFulfillOrder(String orderCode, OrderFulfillRequest request) {
        String requestUrl = orderCoreDomain + ORDER_PAY_AND_FULFILL_URL;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        // 直接使用OrderFulfillRequest作为请求体
        HttpEntity<OrderFulfillRequest> requestEntity = new HttpEntity<>(request, headers);
        boolean result = false;
        
        try {
            result = doPayAndFulfillRequest(requestUrl, requestEntity);
            if (!result) {
                result = retryPayAndFulfillRequest(requestUrl, requestEntity);
            }
        } catch (Exception e) {
            if (e instanceof SentinelRpcException && e.getCause() instanceof DegradeException) {
                SentinelRpcException rpcException = (SentinelRpcException) e;
                DegradeException degradeException = (DegradeException) rpcException.getCause();
                log.error("CircuitBreaker Opened, resourceName: {}", degradeException.getRule().getResource(), e);
                return false;
            }
            log.error("payAndFulfill occurred exception, orderCode: {}, request: {}", orderCode, JacksonUtils.toJsonString(request), e);
            result = retryPayAndFulfillRequest(requestUrl, requestEntity);
        }
        
        return result;
    }
    
    private boolean doPayAndFulfillRequest(String requestUrl, HttpEntity<OrderFulfillRequest> requestEntity) {
        StopWatch stopWatch = StopWatch.createStarted();
        OrderFulfillRequest body = requestEntity.getBody();
        log.info("请求订单核心服务支付并履约订单接口-start，url: {}, body: {}", requestUrl, JacksonUtils.toJsonString(body));
        ResponseEntity<BaseResponse<String>> responseEntity = orderCoreSlowClient.exchange(requestUrl, HttpMethod.POST,
            requestEntity, new ParameterizedTypeReference<BaseResponse<String>>() {});
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error("请求订单核心服务支付并履约订单接口-响应码非200，url: {}, httpStatusCode: {}, cost: {}", requestUrl,
                responseEntity.getStatusCodeValue(), stopWatch.getTime());
            return false;
        }
        BaseResponse<String> responseBody = responseEntity.getBody();
        log.info("请求订单核心服务支付并履约订单接口-end，url: {}, cost: {}, result: {}", requestUrl, stopWatch.getTime(), JacksonUtils.toJsonString(responseBody));
        return responseBody != null && responseBody.isSuccess();
    }
    
    private boolean retryPayAndFulfillRequest(String requestUrl, HttpEntity<OrderFulfillRequest> requestEntity) {
        boolean result = false;
        for (int i = 0; i < RETRY_TIMES; i++) {
            try {
                result = doPayAndFulfillRequest(requestUrl, requestEntity);
                if (result) {
                    break;
                }
            } catch (Exception e) {
                log.error("retry payAndFulfill occurred exception, retry times: {}", i + 1, e);
            }
        }
        return result;
    }

}
