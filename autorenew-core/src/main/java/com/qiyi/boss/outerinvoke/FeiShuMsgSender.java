package com.qiyi.boss.outerinvoke;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.qiyi.boss.model.BaseResponse;

/**
 * @author: guojing
 * @date: 2025/3/13 20:48
 */
@Slf4j
@Component
public class FeiShuMsgSender extends BaseApi {

    private static final String HEADER_PARTNER = "vip-trade";

    private static final String MSG_SEND_URL = "/textMsg";

    @Value("${feishu.msg.api.domain:http://feishu.qiyi.domain}")
    private String feiShuMsgApiDomain;
    @Value("${feishu.msg.receiver.list:zhouguojing,felixsun,guoyang04,lidan04}")
    private String feiShuMsgReciverList;
    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Resource
    private RestTemplate feiShuMsgSenderClient;

    public BaseResponse<String> sendMsg(String content) {
        if (!Objects.equals(activeProfile, "prod")) {
            content = "[" + activeProfile + "]" + content;
        }
        String requestUrl = feiShuMsgApiDomain + MSG_SEND_URL;
        Map<String, String> customHeaders = new HashMap<>();
        customHeaders.put("X-Partner", HEADER_PARTNER);

        Map<String, Object> body = new HashMap<>();
        body.put("email", feiShuMsgReciverList);
        body.put("content", content);
        return doPostByJson(feiShuMsgSenderClient, requestUrl,
            customHeaders, body, "请求飞书发送消息", new ParameterizedTypeReference<BaseResponse<String>>() {});
    }

}
