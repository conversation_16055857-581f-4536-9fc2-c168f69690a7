package com.qiyi.boss.outerinvoke.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * Created at: 2021-09-17
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class PartnerUserSignRecordQueryResult {

    /**
     * 签约协议号
     */
    private String signCode;
    /**
     * 之前是否签约过，0无，1有
     */
    private Integer onceSigned;
    /**
     * 签约账号如： 手机号,爱奇艺uid
     */
    private String account;
    /**
     * 签约账号类型 0爱奇艺uid, 1手机号
     */
    private Integer accountType;
    /**
     * 会员类型
     */
    private Long vipType;
    /**
     * 签约产品code
     */
    private String productCode;
    /**
     * 签约时长
     */
    private Integer amount;
    /**
     * 续费价格
     */
    private Integer renewPrice;
    /**
     * 支付渠道
     */
    private Integer payChannel;
    /**
     * 平台code
     */
    private String platformCode;
    /**
     * 签约时间
     */
    private Timestamp signTime;
    /**
     * 签约状态
     * def(-1, "初始化"),
     * fail(0,"签约失败"),
     * sucess(1, "签约成功"),
     * cancel(2, "签约取消")
     */
    private Integer signStatus;
    private String fc;
    private String fv;

    public boolean autoRenewUser() {
        return signStatus != null && signStatus == 1;
    }

    public boolean notAutoRenewUser() {
        return !autoRenewUser();
    }


}
