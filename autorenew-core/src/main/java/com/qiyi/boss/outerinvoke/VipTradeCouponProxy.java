package com.qiyi.boss.outerinvoke;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.function.Function;

import com.qiyi.boss.enums.CouponTypeEnum;
import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.model.DutSkuInfo;
import com.qiyi.boss.outerinvoke.result.CouponContext;
import com.qiyi.boss.utils.CommonHystrixCommand;
import com.qiyi.boss.utils.SimpleHystrixCommand;
import com.qiyi.vip.commons.component.logging.HystrixFallbackLog;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;

/**
 * <AUTHOR>
 * @className VipTradeCouponProxy
 * @description
 * @date 2024/2/22
 **/
@Slf4j
@Component
public class VipTradeCouponProxy extends BaseApi {

    @Resource(name = "couponProxyClient")
    private RestTemplate couponProxyClient;

    @Value("${coupon.proxy.domain}")
    private String couponProxyDomain;

    public static final String QUERY_USER_COUPON_URL = "/api/coupon/queryUserCoupons";

    private Function<Map<String, Object>, BaseResponse<List<CouponContext>>> queryUserCouponFunc = params ->
        doPostByJson(couponProxyClient, couponProxyDomain + QUERY_USER_COUPON_URL, params, "请求券代理系统查询用户可用券接口",
            new ParameterizedTypeReference<BaseResponse<List<CouponContext>>>() {
            });
    private Function<Map<String, Object>, BaseResponse<List<CouponContext>>> queryUserCouponFallbackFunc = params ->
        BaseResponse.create(BaseResponse.CodeEnum.OUTER_SERVICE_ERROR);

    public List<CouponContext> queryAvailableDutCoupon(Long userId, String partner, List<DutSkuInfo> dutSkuInfo) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("partner", partner);
        params.put("userId", String.valueOf(userId));
        params.put("flag", "1");
        params.put("status", "1");
        if (CollectionUtils.isNotEmpty(dutSkuInfo)) {
            params.put("saleInfoList", dutSkuInfo);
        }
        params.put("couponType", CouponTypeEnum.DISCOUNT_COUPON.getType());
        params.put("withOffsite", true);
        BaseResponse<List<CouponContext>> response = new CommonHystrixCommand<>(
            HystrixCommandPropsEnum.COUPON_PROXY_QUERY,
            queryUserCouponFunc,
            queryUserCouponFallbackFunc,
            params,
            true).execute();
        if (response.isFailure() || CollectionUtils.isEmpty(response.getData())) {
            return Collections.emptyList();
        }
        return response.getData();
    }

    public Future<List<CouponContext>> queryAvailableDutCouponAsync(Long userId, List<DutSkuInfo> dutSkuInfo, List<String> partners) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("userId", String.valueOf(userId));
        params.put("flag", "1");
        params.put("status", "1");
        if (CollectionUtils.isNotEmpty(dutSkuInfo)) {
            params.put("saleInfoList", dutSkuInfo);
        }
        params.put("withOffsite", true);
        params.put("couponType", CouponTypeEnum.DISCOUNT_COUPON.getType());
        params.put("queryPartners", Joiner.on(",").join(partners));
        return new QueryUserCouponCommand(params).queue();
    }

    private class QueryUserCouponCommand extends SimpleHystrixCommand<List<CouponContext>> {

        private static final String GROUP_KEY = "QueryUserCouponGroupKey";
        private static final String COMMAND_KEY = "QueryUserCouponCmdKey";
        private Map<String, Object> params;

        QueryUserCouponCommand(Map<String, Object> params) {
            super(GROUP_KEY, COMMAND_KEY, CloudConfigUtil.getHystrixTimeout(COMMAND_KEY));
            this.params = params;
        }

        @Override
        protected List<CouponContext> run() {
            BaseResponse<List<CouponContext>> response = doPostByJson(couponProxyClient,
                couponProxyDomain + QUERY_USER_COUPON_URL, params, "请求券代理系统查询用户可用券接口",
                new ParameterizedTypeReference<BaseResponse<List<CouponContext>>>() {
                });
            if (response.isFailure() || CollectionUtils.isEmpty(response.getData())) {
                return Collections.emptyList();
            }

            return response.getData();
        }

        @Override
        protected List<CouponContext> getFallback() {
            HystrixFallbackLog.logFallback(this, params, getExecutionException());
            return Collections.emptyList();
        }
    }

}
