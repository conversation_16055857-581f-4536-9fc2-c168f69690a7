package com.qiyi.boss.outerinvoke;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.Map;

import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.utils.JacksonUtils;
import com.iqiyi.kit.http.client.util.HttpClients;

/**
 * Created at: 2020-12-28
 *
 * <AUTHOR>
 */
@Slf4j
public class BaseApi {

    protected  <T> BaseResponse<T> doGet(RestTemplate restTemplate, String requestUrl, Map<String, Object> param, String logHeader, ParameterizedTypeReference<BaseResponse<T>> typeReference) {
        String urlWithParam = MapUtils.isNotEmpty(param) ? HttpClients.buildQueryUrl(requestUrl, param) : requestUrl;
        ResponseEntity<BaseResponse<T>> responseEntity = null;
        StopWatch stopWatch = StopWatch.createStarted();
        log.info(logHeader + "-start，url: {}", urlWithParam);
        try {
            responseEntity = restTemplate.exchange(urlWithParam, HttpMethod.GET, null, typeReference);
        } catch (Exception e) {
            log.error(logHeader + "-出现异常，url: {}, cost: {}", urlWithParam, stopWatch.getTime(), e);
            return BaseResponse.create(BaseResponse.CodeEnum.OUTER_SERVICE_ERROR);
        }
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error(logHeader + "-响应码非200，url: {}, httpStatusCode: {}, cost: {}", urlWithParam, responseEntity.getStatusCodeValue(), stopWatch.getTime());
            return BaseResponse.create(BaseResponse.CodeEnum.OUTER_SERVICE_ERROR);
        }
        BaseResponse<T> responseBody = responseEntity.getBody();
        log.info(logHeader + "-end，url: {}, cost: {}, result: {}", urlWithParam, stopWatch.getTime(), JacksonUtils.toJsonString(responseBody));
        return responseBody;
    }

    protected  <T> BaseResponse<T> doGet(RestTemplate restTemplate, URI uri, String logHeader, ParameterizedTypeReference<BaseResponse<T>> typeReference) {
        String uriStr = uri.toString();
        ResponseEntity<BaseResponse<T>> responseEntity = null;
        StopWatch stopWatch = StopWatch.createStarted();
        log.info(logHeader + "-start，url: {}", uriStr);
        try {
            responseEntity = restTemplate.exchange(uri, HttpMethod.GET, null, typeReference);
        } catch (Exception e) {
            log.error(logHeader + "-出现异常，url: {}, cost: {}", uriStr, stopWatch.getTime(), e);
            return BaseResponse.create(BaseResponse.CodeEnum.OUTER_SERVICE_ERROR);
        }
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error(logHeader + "-响应码非200，url: {}, httpStatusCode: {}, cost: {}", uriStr, responseEntity.getStatusCodeValue(), stopWatch.getTime());
            return BaseResponse.create(BaseResponse.CodeEnum.OUTER_SERVICE_ERROR);
        }
        BaseResponse<T> responseBody = responseEntity.getBody();
        log.info(logHeader + "-end，url: {}, cost: {}, result: {}", uriStr, stopWatch.getTime(), JacksonUtils.toJsonString(responseBody));
        return responseBody;
    }

    protected  <T> T doGet(RestTemplate restTemplate, URI uri, String logHeader, Class<T> clazz) {
        String uriStr = uri.toString();
        ResponseEntity<T> responseEntity = null;
        StopWatch stopWatch = StopWatch.createStarted();
        log.info(logHeader + "-start，url: {}", uriStr);
        try {
            responseEntity = restTemplate.getForEntity(uri, clazz);
        } catch (Exception e) {
            log.error(logHeader + "-出现异常，url: {}, cost: {}", uriStr, stopWatch.getTime(), e);
            throw e;
        }
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            int statusCode = responseEntity.getStatusCodeValue();
            log.error(logHeader + "-响应码非200，url: {}, httpStatusCode: {}, cost: {}", uriStr, statusCode, stopWatch.getTime());
            return null;
        }
        T responseBody = responseEntity.getBody();
        log.info(logHeader + "-end，url: {}, cost: {}", uriStr, stopWatch.getTime());
        return responseBody;
    }

    protected <P, R> BaseResponse<R> doPostByJson(RestTemplate restTemplate, String requestUrl, P body, String logHeader, ParameterizedTypeReference<BaseResponse<R>> typeReference) {
        return doPostByJson(restTemplate, requestUrl, null, body, logHeader, typeReference);
    }

    protected <P, R> BaseResponse<R> doPostByJson(RestTemplate restTemplate, String requestUrl, Map<String, String> customHeaders, P body, String logHeader, ParameterizedTypeReference<BaseResponse<R>> typeReference) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        if (MapUtils.isNotEmpty(customHeaders)) {
            customHeaders.forEach(headers::add);
        }
        HttpEntity<P> requestEntity = new HttpEntity<>(body, headers);
        ResponseEntity<BaseResponse<R>> responseEntity = null;
        StopWatch stopWatch = StopWatch.createStarted();
        log.info(logHeader + "-start，url: {}", requestUrl);
        try {
            responseEntity = restTemplate.exchange(requestUrl, HttpMethod.POST, requestEntity, typeReference);
        } catch (Exception e) {
            log.error(logHeader + "-出现异常，url: {}, cost: {}, param: {}", requestUrl, stopWatch.getTime(), JacksonUtils.toJsonString(body), e);
            return BaseResponse.create(BaseResponse.CodeEnum.OUTER_SERVICE_ERROR);
        }
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error(logHeader + "-响应码非200，url: {}, httpStatusCode: {}, cost: {}, param: {}", requestUrl, responseEntity.getStatusCodeValue(), stopWatch.getTime(), JacksonUtils.toJsonString(body));
            return BaseResponse.create(BaseResponse.CodeEnum.OUTER_SERVICE_ERROR);
        }
        BaseResponse<R> responseBody = responseEntity.getBody();
        log.info(logHeader + "-end，url: {}, cost: {}, param: {}, result: {}", requestUrl, stopWatch.getTime(), JacksonUtils.toJsonString(body), JacksonUtils.toJsonString(responseBody));
        return responseBody;
    }

    protected <T> BaseResponse<T> doPostByFormData(RestTemplate restTemplate, String requestUrl, Map<String, String> param, String logHeader, ParameterizedTypeReference<BaseResponse<T>> typeReference) {
        return doPostByFormDataWithUrlVariables(restTemplate, requestUrl, param, logHeader, typeReference);
    }

    protected <T> BaseResponse<T> doPostByFormDataWithUrlVariables(RestTemplate restTemplate, String requestUrl, Map<String, String> param, String logHeader, ParameterizedTypeReference<BaseResponse<T>> typeReference, Object... uriVariables) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.setAll(param);
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(formData, headers);
        ResponseEntity<BaseResponse<T>> responseEntity = null;
        StopWatch stopWatch = StopWatch.createStarted();
        log.info(logHeader + "-start，url: {}", requestUrl);
        try {
            responseEntity = restTemplate.exchange(requestUrl, HttpMethod.POST, requestEntity, typeReference, uriVariables);
        } catch (Exception e) {
            log.error(logHeader + "-出现异常，url: {}, cost: {}, param: {}", requestUrl, stopWatch.getTime(), JacksonUtils.toJsonString(param), e);
            return BaseResponse.create(BaseResponse.CodeEnum.OUTER_SERVICE_ERROR);
        }
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error(logHeader + "-响应码非200，url: {}, httpStatusCode: {}, cost: {}, param: {}", requestUrl, responseEntity.getStatusCodeValue(), stopWatch.getTime(), JacksonUtils.toJsonString(param));
            return BaseResponse.create(BaseResponse.CodeEnum.OUTER_SERVICE_ERROR);
        }
        BaseResponse<T> responseBody = responseEntity.getBody();
        log.info(logHeader + "-end，url: {}, cost: {}, param: {}, result: {}", requestUrl, stopWatch.getTime(), JacksonUtils.toJsonString(param), JacksonUtils.toJsonString(responseBody));
        return responseBody;
    }
}
