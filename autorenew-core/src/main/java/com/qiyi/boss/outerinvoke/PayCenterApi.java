package com.qiyi.boss.outerinvoke;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.net.URI;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import com.qiyi.boss.Constants;
import com.qiyi.boss.async.queue.AsyncTaskFactory;
import com.qiyi.boss.async.task.AbstractTask;
import com.qiyi.boss.async.task.WechatPreDutRemindTask;
import com.qiyi.boss.dto.CloseOrderRequest;
import com.qiyi.boss.dto.PayCenterDutReq;
import com.qiyi.boss.dto.PayCenterDutResult;
import com.qiyi.boss.dto.PayCenterOrderCheckResult;
import com.qiyi.boss.dto.PayCenterPureSignResult;
import com.qiyi.boss.dto.WechatPreDutRemindReqDto;
import com.qiyi.boss.dto.WechatPreDutRemindResponse;
import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.boss.exception.BizException;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.model.BaseResponse.CodeEnum;
import com.qiyi.boss.outerinvoke.result.PayCenterAppleDutInfoResult;
import com.qiyi.boss.param.QiyiParam;
import com.qiyi.boss.utils.AppConfig;
import com.qiyi.boss.utils.CommonHystrixCommand;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.PayUtils;
import com.qiyi.boss.utils.dut.OrderUtil;
import com.qiyi.vip.commons.component.SimpleHystrixCommand;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.constants.TaskConstants;
import com.qiyi.vip.trade.autorenew.domain.AsyncTask;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang Date: 2020-6-24 Time: 16:23
 */
@Component
public class PayCenterApi extends BaseApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(PayCenterApi.class);
    public static final String WECHAT_PRE_DUT_REMIND_URL = AppConfig.getProperty("wechat.pre.dut.remind.url");
    public static final String GATEWAY = "gateway";
    //订单不存在或者已支付或者已关闭
    public static final String ORDER_ALREADY_CLOSED = "ORD000004";

    @Value("${sendWechatPreDutRemind.fallbackTimeOut:5000}")
    private int fallbackTimeOut = 5000;
    @Value("${sendWechatPreDutRemind.queryThreads:50}")
    private int queryThreads;
    @Value("${pay.centerpay.close.order.url}")
    private String payCenterCloseOrderUrl;
    @Value("${pay.center.check.order.url:http://inter.pay.qiyi.domain/order/user/checkOrder}")
    private String payCenterCheckOrderUrl;
    /**
     * 接口wiki: http://wiki.qiyi.domain/pages/viewpage.action?pageId=1508344592
     */
    @Value("${pay.center.apple.dut.info.url:http://inter.pay.qiyi.domain/pay-product-iap/apple/dut-info}")
    private String userAppleDutInfoUrl;

    @Resource(name = "payCenterClient")
    private RestTemplate payCenterClient;
    @Resource(name = "slowPayCenterClient")
    private RestTemplate slowPayCenterClient;
    @Resource(name = "payCenterAppleClient")
    private RestTemplate payCenterAppleClient;

    public Optional<WechatPreDutRemindResponse> sendWechatPreDutRemind(WechatPreDutRemindReqDto wechatPreDutRemindReqDto) {
        return new WechatPreDutRemindCommand(wechatPreDutRemindReqDto).execute();
    }

    class WechatPreDutRemindCommand extends SimpleHystrixCommand<Optional<WechatPreDutRemindResponse>> {

        private WechatPreDutRemindReqDto wechatPreDutRemindReqDto;

        WechatPreDutRemindCommand(WechatPreDutRemindReqDto wechatPreDutRemindReqDto) {
            super("PayCenterApi", queryThreads, fallbackTimeOut);
            this.wechatPreDutRemindReqDto = wechatPreDutRemindReqDto;
        }

        @Override
        protected Optional<WechatPreDutRemindResponse> run() {
            return doSendWechatPreDutRemind(wechatPreDutRemindReqDto);
        }

        @Override
        protected Optional<WechatPreDutRemindResponse> getFallback() {
            LOGGER.warn("Send wechat pre dut remind fallback. params:{}", wechatPreDutRemindReqDto);
            return new WechatPreDutRemindRetryCommand(wechatPreDutRemindReqDto).execute();
        }
    }

    class WechatPreDutRemindRetryCommand extends SimpleHystrixCommand<Optional<WechatPreDutRemindResponse>> {

        private WechatPreDutRemindReqDto wechatPreDutRemindReqDto;

        WechatPreDutRemindRetryCommand(WechatPreDutRemindReqDto wechatPreDutRemindReqDto) {
            super("PayCenterApi", queryThreads, fallbackTimeOut);
            this.wechatPreDutRemindReqDto = wechatPreDutRemindReqDto;
        }

        @Override
        protected Optional<WechatPreDutRemindResponse> run() {
            return doSendWechatPreDutRemind(wechatPreDutRemindReqDto);
        }

        @Override
        protected Optional<WechatPreDutRemindResponse> getFallback() {
            LOGGER.info("Send wechat pre dut remind fail. params:{}", wechatPreDutRemindReqDto);

            AbstractTask task = new WechatPreDutRemindTask(wechatPreDutRemindReqDto);
            Timestamp execTime = DateHelper.caculateTime(DateHelper.getDateTime(), 5, Constants.PRODUCT_PERIODUNIT_MINUTE);
            AsyncTaskFactory.getInstance()
                .insertIntoDB(task, AsyncTask.POOL_TYPE_DUT_AUTO_RENEW_SMS_REMIND_NEW, TaskConstants.PRIORITY_NORMAL, execTime);

            return Optional.empty();
        }
    }

    public Optional<WechatPreDutRemindResponse> doSendWechatPreDutRemind(WechatPreDutRemindReqDto wechatPreDutRemindReqDto) {

        LOGGER.info("Do send wechat pre dut remind start. url:{}, params:{}.",
            WECHAT_PRE_DUT_REMIND_URL, wechatPreDutRemindReqDto.toString());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String url = WECHAT_PRE_DUT_REMIND_URL + "?" + Joiner.on("&").withKeyValueSeparator("=").join(wechatPreDutRemindReqDto.buildMap());
        ResponseEntity<WechatPreDutRemindResponse> responseEntity = payCenterClient.exchange(
            url,
            HttpMethod.POST,
            null,
            new ParameterizedTypeReference<WechatPreDutRemindResponse>() {
            }
        );
        if (responseEntity.getBody() != null) {
            LOGGER.info("Do send wechat pre dut remind finished. url:{}, result:{}, costTime:{}ms.",
                url, responseEntity.getBody(), stopWatch.getTime());
            return Optional.ofNullable(responseEntity.getBody());
        }
        return Optional.empty();
    }

    public Optional<PayCenterDutResult> processPayments(PayCenterDutReq dutReq) {
        Integer payChannel = dutReq.getPayChannel();
        Map<String, String> params = dutReq.getConfigs();
        String signKey = params.containsKey("service_provider")
            ? PayUtils.getPayCenterServiceProviderKey()
            : OrderUtil.getPayCenterSignKeyByPartner(params.get("partner"));
        params.put("sign", OrderUtil.getSignMsg(params, signKey));
        HystrixCommandPropsEnum cmdPropsEnum = CloudConfigUtil.useSlowPayCenterClient(payChannel)
                ? HystrixCommandPropsEnum.SLOW_PAY_CENTER_GATEWAY
                : HystrixCommandPropsEnum.PAY_CENTER_GATEWAY;
        return new CommonHystrixCommand<>(cmdPropsEnum,
                payCenterGatewayRunFunc,
                payCenterGatewayFallbackFunc,
                dutReq, queryThreads, fallbackTimeOut).execute();
    }

    private Function<PayCenterDutReq, Optional<PayCenterDutResult>> payCenterGatewayRunFunc = this::doPayCenterDut;
    private Function<PayCenterDutReq, Optional<PayCenterDutResult>> payCenterGatewayFallbackFunc = param -> Optional.empty();

    public Optional<PayCenterDutResult> doPayCenterDut(PayCenterDutReq dutReq) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Map<String, String> params = dutReq.getConfigs();
        String gateway = dutReq.getGateway() != null ? dutReq.getGateway() : QiyiParam.gatewayDomain;
        RestTemplate restTemplate = CloudConfigUtil.useSlowPayCenterClient(dutReq.getPayChannel()) ? slowPayCenterClient : payCenterClient;
        PayCenterDutResult payCenterDutResult = null;
        try {
            LOGGER.info("Request payCenter start. url:{}, params:{}.", gateway, params.toString());
            Map<String, Object> resultMap = restTemplate.postForObject(gateway, toForm(params), Map.class);
            if (MapUtils.isEmpty(resultMap)) {
                LOGGER.info("Request payCenter error. url:{}, result:null, costTime:{}ms.", gateway, stopWatch.getTime());
                return Optional.empty();
            }
            LOGGER.info("Request payCenter finished. url:{}, result:{}, costTime:{}ms.",
                gateway, resultMap, stopWatch.getTime());
            payCenterDutResult = JSON.parseObject(JSON.toJSONString(resultMap), PayCenterDutResult.class);
        } catch (RestClientException e) {
            LOGGER.error("Request payCenter error. url: {}, params:{}, costTime:{}ms.", gateway, params, stopWatch.getTime(), e);
        }
        return Optional.ofNullable(payCenterDutResult);
    }

    public Optional<PayCenterDutResult> processPaymentsWithRetry(PayCenterDutReq dutReq) {
        Map<String, String> params = dutReq.getConfigs();
        String signKey = params.containsKey("service_provider")
            ? PayUtils.getPayCenterServiceProviderKey()
            : OrderUtil.getPayCenterSignKeyByPartner(params.get("partner"));
        params.put("sign", OrderUtil.getSignMsg(params, signKey));
        return new CommonHystrixCommand<>(
                HystrixCommandPropsEnum.PAY_CENTER_GATEWAY,
                payCenterGatewayRunFunc,
                payCenterGatewayFallbackFunc,
                dutReq, true).execute();
    }

    private MultiValueMap<String, String> toForm(Map<String, String> parameters) {
        MultiValueMap<String, String> result = new LinkedMultiValueMap<>();
        for (Map.Entry<String, String> entry : parameters.entrySet()) {
            result.add(entry.getKey(), entry.getValue());
        }
        return result;
    }


    private Function<String, BaseResponse<PayCenterPureSignResult>> dutBindRunFunc = url ->
        doGet(payCenterClient, URI.create(url), "请求支付中心签约接口",
            new ParameterizedTypeReference<BaseResponse<PayCenterPureSignResult>>() {
            });
    private Function<String, BaseResponse<PayCenterPureSignResult>> dutBindFallbackFunc = url ->
        BaseResponse.create(BaseResponse.CodeEnum.OUTER_SERVICE_ERROR);

    /**
     * 请求支付中心签约接口
     */
    public PayCenterPureSignResult doDutBind(String url) {
        BaseResponse<PayCenterPureSignResult> response = new CommonHystrixCommand<>(
            HystrixCommandPropsEnum.DUT_BIND,
            dutBindRunFunc,
            dutBindFallbackFunc,
            url).execute();
        if (response.isFailure()) {
            throw BizException.newException(CodeEnum.DUT_BIND_EXCEPTION);
        }
        return response.getData();
    }

    private Function<CloseOrderRequest, BaseResponse<String>> closeOrderRunFunc = this::closeOrderInternal;
    private Function<CloseOrderRequest, BaseResponse<String>> closeOrderFallbackFunc = closeOrderRequest ->
        BaseResponse.create(BaseResponse.CodeEnum.OUTER_SERVICE_ERROR);

    private BaseResponse<String> closeOrderInternal(CloseOrderRequest closeOrderRequest) {
        String requestParams = Joiner.on("&").withKeyValueSeparator("=").join(closeOrderRequest.buildRequestMap());
        String url = payCenterCloseOrderUrl + "?" + requestParams;
        return doGet(payCenterClient, URI.create(url), "请求支付中心取消订单接口",
            new ParameterizedTypeReference<BaseResponse<String>>() {
            });
    }

    /**
     * 请求支付中心关闭订单 接口wiki: http://wiki.qiyi.domain/pages/viewpage.action?pageId=79922160
     */
    public void closeOrder(CloseOrderRequest closeOrderRequest) {
        BaseResponse<String> response = new CommonHystrixCommand<>(
            HystrixCommandPropsEnum.CLOSE_ORDER,
            closeOrderRunFunc,
            closeOrderFallbackFunc,
            closeOrderRequest).execute();
        if (response.isSuccess()) {
            LOGGER.info("close order success! closeOrderRequest:{}", closeOrderRequest);
            return;
        }
        if (ORDER_ALREADY_CLOSED.equals(response.getCode())) {
            LOGGER.warn("orderCode:{}, {}", closeOrderRequest.getPartner_order_no(), response.getMsg());
            return;
        }
        throw new BizException(CodeEnum.CLOSE_ORDER_EXCEPTION);
    }

    /**
     * 请求支付中心查询订单接口
     * wiki: http://wiki.qiyi.domain/pages/viewpage.action?pageId=22839495
     */
    public PayCenterOrderCheckResult checkOrder(String orderCode, String partner, boolean historyOrder) {
        Map<String, Object> params = new HashMap<>();
        params.put("partner", partner);
        params.put("partner_order_no", orderCode);
        params.put("version", "1.0");
        params.put("charset", "UTF-8");
        params.put("history", historyOrder ? "1" : "0");
        params.put("sign", PayUtils.generateSignWithObjValue(params, PayUtils.getPayCenterSignKey()));
        BaseResponse<PayCenterOrderCheckResult> response = doGet(slowPayCenterClient, payCenterCheckOrderUrl, params
            , "请求支付中心查单接口", new ParameterizedTypeReference<BaseResponse<PayCenterOrderCheckResult>>() {});
        if (response.isFailure()) {
            throw BizException.newException(response.getCode(), response.getMsg());
        }
        return response.getData();
    }

    private Function<Map<String, String>, BaseResponse<List<PayCenterAppleDutInfoResult>>> appleSignInfoRunFunc = params ->
        doPostByFormDataWithUrlVariables(payCenterAppleClient, userAppleDutInfoUrl, params, "请求支付中心签约接口",
            new ParameterizedTypeReference<BaseResponse<List<PayCenterAppleDutInfoResult>>>() {
            });
    private Function<Map<String, String>, BaseResponse<List<PayCenterAppleDutInfoResult>>> appleSignInfoFallbackFunc = params ->
        BaseResponse.create(BaseResponse.CodeEnum.OUTER_SERVICE_ERROR);

    /**
     * 查询用户的苹果签约关系
     */
    public List<PayCenterAppleDutInfoResult> getUserAppleSignInfo(Long userId) {
        Map<String, String> params = new HashMap<>();
        //仅用于接口签名
        params.put("partner", Constants.DEFAULT_PARTNER);
        params.put("userId", userId.toString());
        params.put("sign", OrderUtil.getSignMsg(params, PayUtils.getPayCenterSignKey()));
        BaseResponse<List<PayCenterAppleDutInfoResult>> response = new CommonHystrixCommand<>(
            HystrixCommandPropsEnum.APPLE_SIGN_INFO,
            appleSignInfoRunFunc,
            appleSignInfoFallbackFunc,
            params).execute();
        if (response.isFailure() || CollectionUtils.isEmpty(response.getData())) {
            return Collections.emptyList();
        }
        return response.getData();
    }

}
