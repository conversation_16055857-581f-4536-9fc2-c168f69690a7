package com.qiyi.boss.outerinvoke.param;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created at: 2020-12-01
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class DutDiscountConsumeParam {

    private Long uid;
    /**
     * 立减优惠id
     */
    private Long dutDiscountId;
    /**
     * 订单号
     */
    private String orderCode;
    /**
     * 会员类型
     */
    private Integer vipType;
    /**
     * 签约时长
     */
    private Integer amount;
    /**
     * 续费金额，单位：分
     */
    private Integer renewPrice;

    public JSONObject toJSONObject() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("uid", uid);
        jsonObject.put("vipType", vipType);
        jsonObject.put("orderCode", orderCode);
        jsonObject.put("amount", amount);
        jsonObject.put("renewPrice", renewPrice);
        jsonObject.put("dutDiscountId", dutDiscountId);
        return jsonObject;
    }

    public static DutDiscountConsumeParam buildFrom(Long uid, Long dutDiscountId, Integer vipType, Integer amount, Integer renewPrice, String orderCode) {
        return DutDiscountConsumeParam.builder()
                .uid(uid)
                .dutDiscountId(dutDiscountId)
                .orderCode(orderCode)
                .vipType(vipType)
                .amount(amount)
                .renewPrice(renewPrice)
                .build();
    }

}
