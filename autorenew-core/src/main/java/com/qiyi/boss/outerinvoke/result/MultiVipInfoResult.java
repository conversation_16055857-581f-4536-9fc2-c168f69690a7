package com.qiyi.boss.outerinvoke.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * <AUTHOR>
 * @className MultiVipInfoResult
 * @description
 * @date 2024/9/1
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MultiVipInfoResult {

    private Long uid;

    private Integer concurrentRuleVersion;

    private List<UsersVipVipInfoResult> detail;

}
