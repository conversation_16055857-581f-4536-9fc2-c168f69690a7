package com.qiyi.boss.outerinvoke.param;

import com.google.common.base.Joiner;
import com.qiyi.boss.utils.JacksonUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

/**
 * Created at: 2021-09-17
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class PartnerUserSignRecordQueryParam {

    private static final int ACCOUNT_TYPE_UID = 0;
    private static final int ACCOUNT_TYPE_PHONE = 1;

    /**
     * 签约用户uid.
     * signUserId 和 signAccount 至少传一个， 尽量传signAccount
     * (如果是按手机好签约，参数为signUserId那么还要去查下passport)
     */
    private Long signUserId;
    /**OrderUtil
     * 签约账号如： 手机号
     */
    private String signAccount;
    /**
     * 如签约账号类型 1手机号，目前仅支持传1
     */
    private Integer signAccountType;
    /**
     * 多个用逗号分隔
     */
    private String vipType;
    /**
     * 支付渠道
     * 联通：44
     * 移动：45
     * 电信：46
     */
    private Integer payChannel;
    /**
     * 签约状态 默认 1 处于签约成功状态
     * def(-1, "初始化")
     * fail(0,"签约失败")
     * sucess(1, "签约成功")
     * cancel(2, "签约取消")
     */
    private Integer signStatus;
    /**
     * 调用方系统名称
     */
    private String sys;
    private Long timestamp;
    private String sign;

    public PartnerUserSignRecordQueryParam(String sys, Long signUserId, String vipType) {
        this.sys = sys;
        this.signUserId = signUserId;
        this.signAccountType = ACCOUNT_TYPE_PHONE;
        this.vipType = vipType;
        this.timestamp = System.currentTimeMillis();
    }

    public Map<String, Object> toMap() {
        return JacksonUtils.beanToMap(this);
    }

    public String generateSign(String signKey) {
        Map<String, String> params = JacksonUtils.beanToStringMap(this);
        params.remove("sign");
        SortedMap<String, String> sortedParams = new TreeMap<>(params);
        String strForSign = Joiner.on("&").useForNull("").withKeyValueSeparator("=").join(sortedParams).concat(signKey);
        return DigestUtils.md5Hex(strForSign);
    }

}
