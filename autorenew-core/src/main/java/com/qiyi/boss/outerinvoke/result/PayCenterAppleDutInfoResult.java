package com.qiyi.boss.outerinvoke.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * Created at: 2022-12-09
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayCenterAppleDutInfoResult {

    /**
     * 苹果签约号，即otId
     */
    private String signCode;
    /**
     * 产品id，即appId
     */
    private String productId;
    /**
     * 包id
     */
    private String bundleId;
    /**
     * 最近订阅开始日期
     */
    private Timestamp recentSubscriptionStartDate;
    /**
     * 过期日期，即下次续费时间
     */
    private Timestamp expiresDate;
    /**
     * 代扣方式
     */
    private Integer dutType;
    /**
     * 价格
     */
    private Integer fee;
    /**
     * 币种
     */
    private String currency;

}
