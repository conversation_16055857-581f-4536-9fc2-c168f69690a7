package com.qiyi.boss.outerinvoke.result;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @className SendCouponResp
 * @description
 * @date 2024/2/23
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SendCouponResp {

    @JsonProperty("coupon_code")
    private String couponCode;

    @JsonProperty("start_time")
    private String startTime;

    @JsonProperty("end_time")
    private String endTime;

    @JsonProperty("amount")
    private String amount;

    @JsonProperty("settlement")
    private String settlement;
}
