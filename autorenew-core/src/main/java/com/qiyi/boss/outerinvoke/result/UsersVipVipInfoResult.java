package com.qiyi.boss.outerinvoke.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 会员信息接口：http://vinfo.vip.qiyi.domain/internal/users/vip/vip_infos 返回值
 * <AUTHOR>
 * @date 2021/7/31 15:53
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class UsersVipVipInfoResult {

    private Long uid;
    /**
     * 封停状态：0：已过期 1：正常用户 2：临时封停 3：永久封停
     */
    private Integer status;
    /**
     * 会员类型
     */
    private Long vipType;
    /**
     * 用户成长值等级，台湾和奇异果统一为：0 ，无等级->显示null
     */
    private Integer level;
    /**
     * 用户付费标识： 0：从来没有付费； 1：至少付费一次
     */
    private Integer paidSign;
    /**
     * 用户是否是自动续费用户：0：不自动续费 1：自动续费
     */
    private Integer autoRenew;
    /**
     * 会员到期时间
     */
    private String deadline;
    /**
     * 过期标识 0:未过期 ;1:已过期
     */
    private Integer expire;
    /**
     * 年费会员过期标识  0：未过期，是有效年费会员  1：已过期，不是有效年费会员
     */
    private Integer yearExpire;

    private String createTime;
    /**
     * 会员卡号，16位数字，每4位增加一个空格隔开："0013 **************"
     */
    private String cardNumber;
    /**
     * 会员角标，内容为图片URL
     */
    private String superscript;

    public UsersVipVipInfoResult(Long uid, Long vipType) {
        this.uid = uid;
        this.vipType = vipType;
    }
}
