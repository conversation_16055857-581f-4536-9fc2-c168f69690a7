package com.qiyi.boss.outerinvoke.param;

import com.google.common.base.Joiner;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import com.qiyi.boss.utils.JacksonUtils;

/**
 * Created at: 2022-11-24
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class PartnerUserSignCancelParam {

    /**
     * 用户uid
     */
    private Long uid;
    /**
     * 多个用逗号分隔
     */
    private String vipType;
    /**
     * 支付渠道
     * 联通：44
     * 移动：45
     * 电信：46
     */
    private Integer payChannel;
    /**
     * 调用方系统名称
     */
    private String sys;
    private Long timestamp;
    private String sign;

    public PartnerUserSignCancelParam(String sys, Long uid, String vipType, Integer payChannel) {
        this.sys = sys;
        this.uid = uid;
        this.vipType = vipType;
        this.payChannel = payChannel;
        this.timestamp = System.currentTimeMillis();
    }

    public Map<String, String> toStringMap() {
        return JacksonUtils.beanToStringMap(this);
    }

    public String generateSign(String signKey) {
        Map<String, String> params = JacksonUtils.beanToStringMap(this);
        params.remove("sign");
        SortedMap<String, String> sortedParams = new TreeMap<>(params);
        String strForSign = Joiner.on("&").useForNull("").withKeyValueSeparator("=").join(sortedParams).concat(signKey);
        return DigestUtils.md5Hex(strForSign);
    }

}
