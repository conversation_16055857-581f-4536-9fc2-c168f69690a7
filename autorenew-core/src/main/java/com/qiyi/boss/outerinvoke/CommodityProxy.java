package com.qiyi.boss.outerinvoke;

import com.alibaba.fastjson.JSON;
import com.alicp.jetcache.anno.CachePenetrationProtect;
import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.qiyi.boss.outerinvoke.result.CommodityInfo;
import com.qiyi.vip.commons.constant.CodeConstants;
import com.iqiyi.vip.v.commodity.model.DataResult;
import com.iqiyi.vip.v.commodity.request.QuerySkuRequest;
import com.iqiyi.vip.v.commodity.response.QuerySkuResponse;
import com.iqiyi.vip.v.commodity.services.SkuService;

/**
 * @auther: guojing
 * @date: 2023/6/2 4:46 PM
 * 商品中心接口wiki: http://wiki.qiyi.domain/pages/viewpage.action?pageId=1004443327
 */
@Slf4j
@Component
public class CommodityProxy {

    @Value("${commodity.config.app.caller}")
    private String caller;
    @Value("${commodity.config.app.signKey}")
    private String signKey;

    @Autowired
    private SkuService skuService;

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "CommodityInfo_bySkuId", cacheType= CacheType.LOCAL)
    @CachePenetrationProtect
    public CommodityInfo queryCommodity(String skuId) {
        QuerySkuRequest querySkuRequest = new QuerySkuRequest();
        querySkuRequest.setSkuId(skuId);
        querySkuRequest.setCaller(caller);
        querySkuRequest.setSignKey(signKey);
        querySkuRequest.setRetryCount(1);

        StopWatch stopWatch = StopWatch.createStarted();
        DataResult<QuerySkuResponse> result = skuService.queryWithOutProduct(querySkuRequest);
        log.info("queryCommodity params:{} cost:{}ms result:{}", skuId, stopWatch.getTime(), JSON.toJSON(result));
        if (isFailed(result)) {
            log.error("queryCommodity responseEntity_body_null skuId:{}, result:{}", skuId, result);
            return null;
        }
        QuerySkuResponse skuResponse = result.getData();
        CommodityInfo commodityInfo = buildFromSkuResp(skuResponse);
        if (CollectionUtils.isNotEmpty(skuResponse.getSubSkuList())) {
            List<CommodityInfo> subSkuList = new ArrayList<>();
            for (QuerySkuResponse subSkuResponse : skuResponse.getSubSkuList()) {
                subSkuList.add(buildFromSkuResp(subSkuResponse));
            }
            commodityInfo.setSubSkuList(subSkuList);
        }

        return commodityInfo;

    }

    private boolean isFailed(DataResult<?> result) {
        return Objects.isNull(result) || !CodeConstants.COMMON_SUC.equals(result.getCode());
    }

    private CommodityInfo buildFromSkuResp(QuerySkuResponse skuResponse) {
        if (skuResponse == null) {
            return null;
        }

        CommodityInfo simpleInfo = CommodityInfo.builder()
            .skuId(skuResponse.getSkuId())
            .spuId(skuResponse.getSpuId())
            .skuName(skuResponse.getSkuName())
            .skuDesc(skuResponse.getSkuDesc())
            .catalogId(skuResponse.getCatalogId())
            .price(skuResponse.getPrice().intValue())
            .status(skuResponse.getStatus())
            .deadline(skuResponse.getEndTime() != null ? new Timestamp(skuResponse.getEndTime().getTime()) : null)
            .build();

        Map<String, Object> specAttributes = skuResponse.getSpecAttributes();
        simpleInfo.setDisplayName(MapUtils.getString(specAttributes, "displayName"));
        simpleInfo.setProductId(MapUtils.getLong(specAttributes, "productId"));
        simpleInfo.setProductCode(MapUtils.getString(specAttributes, "productCode"));
        simpleInfo.setAmount(MapUtils.getInteger(specAttributes, "amount"));
        simpleInfo.setPeriod(MapUtils.getInteger(specAttributes, "period"));
        simpleInfo.setPeriodUnit(MapUtils.getInteger(specAttributes, "periodUnit"));
        simpleInfo.setType(MapUtils.getInteger(specAttributes, "type"));
        simpleInfo.setSubType(MapUtils.getLong(specAttributes, "subType"));
        simpleInfo.setSourceSubType(MapUtils.getLong(specAttributes, "sourceSubType"));
        simpleInfo.setServiceType(MapUtils.getInteger(specAttributes, "serviceType"));
        simpleInfo.setArea(MapUtils.getString(specAttributes, "area"));
        simpleInfo.setBusinessCode(MapUtils.getString(specAttributes, "businessCode"));
        simpleInfo.setChargeType(MapUtils.getInteger(specAttributes, "chargeType"));
        simpleInfo.setOriginalPrice(MapUtils.getInteger(specAttributes, "originalPrice"));
        simpleInfo.setCurrencyUnit(MapUtils.getString(specAttributes, "currencyUnit"));
        simpleInfo.setVipTypeCode(MapUtils.getString(specAttributes, "vipTypeCode"));
        String sourceVipTypeCode = MapUtils.getString(specAttributes, "sourceVipTypeCode");
        Integer deductionModel = MapUtils.getInteger(specAttributes, "deductionModel", null);
        simpleInfo.setDeductionModel(deductionModel);
        simpleInfo.setSourceVipTypeCode(StringUtils.isNotBlank(sourceVipTypeCode) ? sourceVipTypeCode : null);
        simpleInfo.setSkuIdentifier(MapUtils.getInteger(specAttributes, "skuIdentifier", null));
        simpleInfo.setRightsTimeLength(MapUtils.getInteger(specAttributes, "rightsTimeLength", null));
        simpleInfo.setSettleType(MapUtils.getInteger(specAttributes, "settleType"));
        simpleInfo.setSettleValue(MapUtils.getInteger(specAttributes, "settleValue"));
        return simpleInfo;
    }

}
