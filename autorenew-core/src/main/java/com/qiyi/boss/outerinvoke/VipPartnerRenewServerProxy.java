package com.qiyi.boss.outerinvoke;

import com.google.common.base.Joiner;
import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.model.BaseResponse.CodeEnum;
import com.qiyi.boss.outerinvoke.param.PartnerUserSignCancelParam;
import com.qiyi.boss.outerinvoke.param.PartnerUserSignRecordQueryParam;
import com.qiyi.boss.outerinvoke.result.PartnerUserSignRecordQueryResult;
import com.qiyi.boss.utils.CommonHystrixCommand;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.function.Function;

/**
 * Created at: 2021-09-17
 * <p>
 * 对外合作续费服务代理
 * 接口wiki: http://wiki.qiyi.domain/pages/viewpage.action?pageId=1112086478
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class VipPartnerRenewServerProxy extends BaseApi {

    private static final String SYS = "VIP_TRADE";
    //部分取消成功状态码
    private static final String PART_CANCEL_SUCCESS_RESULT_CODE = "A00010";
    //没有签约关系
    private static final String NO_SIGN_RECORD_RESULT_CODE = "Q00405";

    /**
     * 对外合作用户签约关系查询
     */
    private static final String QUERY_USER_SIGN_RECORD_URL = "/user/sign/record/all";
    /**
     * 对外合作解约
     */
    private static final String CANCEL_SIGN_URL = "/user/cancelSign";

    @Value("${partner.renew.server.domain}")
    String partnerRenewServerDomain;
    @Value("${partner.renew.server.sign.key}")
    String partnerRenewServerSignKey;

    @Resource
    RestTemplate partnerRenewServerClient;
    @Resource
    RestTemplate partnerRenewServerCancelClient;

    private Function<PartnerUserSignRecordQueryParam, List<PartnerUserSignRecordQueryResult>> queryUserSignRecordFunc = this::doQueryUserSignRecord;
    private Function<PartnerUserSignRecordQueryParam, List<PartnerUserSignRecordQueryResult>> queryUserSignRecordFallbackFunc = t -> Lists.newArrayList();

    private Function<PartnerUserSignCancelParam, BaseResponse<Boolean>> cancelUserSignFunc = param -> doPostByFormData(
        partnerRenewServerCancelClient, partnerRenewServerDomain + CANCEL_SIGN_URL,
        param.toStringMap(),
        "请求对外合作解约接口",
        new ParameterizedTypeReference<BaseResponse<Boolean>>(){});
    private Function<PartnerUserSignCancelParam, BaseResponse<Boolean>> cancelUserSignFallbackFunc = t -> BaseResponse.create(CodeEnum.OUTER_SERVICE_ERROR);

    private List<PartnerUserSignRecordQueryResult> doQueryUserSignRecord(PartnerUserSignRecordQueryParam param) {
        BaseResponse<List<PartnerUserSignRecordQueryResult>> response = doGet(partnerRenewServerClient,
                partnerRenewServerDomain + QUERY_USER_SIGN_RECORD_URL,
                param.toMap(),
                "查询对外合作用户签约关系接口",
                new ParameterizedTypeReference<BaseResponse<List<PartnerUserSignRecordQueryResult>>>() {});
        if (response.isFailure()) {
            return Collections.emptyList();
        }
        return response.getData();
    }

    /**
     * 查询对外合作用户签约关系
     *
     * @param userId
     * @param vipType
     * @return
     */
    public List<PartnerUserSignRecordQueryResult> queryUserSignRecord(Long userId, Long vipType) {
        String vipTypeStr = Objects.toString(vipType, null);
        PartnerUserSignRecordQueryParam param = new PartnerUserSignRecordQueryParam(SYS, userId, vipTypeStr);
        param.setSign(param.generateSign(partnerRenewServerSignKey));
        return new CommonHystrixCommand<>(
                HystrixCommandPropsEnum.QUERY_USER_SIGN_RECORD_URL,
                queryUserSignRecordFunc,
                queryUserSignRecordFallbackFunc,
                param).execute();
    }

    public List<PartnerUserSignRecordQueryResult> queryUserSignRecordWithMultiVipType(Long userId, List<Long> vipTypes) {
        String vipTypesStr = null;
        if (CollectionUtils.isNotEmpty(vipTypes)) {
            vipTypesStr = Joiner.on(',').join(vipTypes);
        }
        PartnerUserSignRecordQueryParam param = new PartnerUserSignRecordQueryParam(SYS, userId, vipTypesStr);
        param.setSign(param.generateSign(partnerRenewServerSignKey));
        return new CommonHystrixCommand<>(
                HystrixCommandPropsEnum.QUERY_USER_SIGN_RECORD_URL,
                queryUserSignRecordFunc,
                queryUserSignRecordFallbackFunc,
                param).execute();
    }

    /**
     * 查询对外合作用户签约关系 异步接口
     *
     * @param userId
     * @param vipType
     * @return
     */
    public Future<List<PartnerUserSignRecordQueryResult>> queryUserSignRecordAsync(Long userId, Long vipType) {
        List<Long> vipTypes = vipType != null ? Collections.singletonList(vipType) : null;
        return queryUserSignRecordWithMultiVipTypeAsync(userId, vipTypes);
    }

    /**
     * 查询对外合作用户签约关系 异步接口
     * @param userId
     * @param vipTypes 不传默认查询所有类型
     * @return
     */
    public Future<List<PartnerUserSignRecordQueryResult>> queryUserSignRecordWithMultiVipTypeAsync(Long userId, List<Long> vipTypes) {
        String vipTypesStr = null;
        if (CollectionUtils.isNotEmpty(vipTypes)) {
            vipTypesStr = Joiner.on(',').join(vipTypes);
        }
        PartnerUserSignRecordQueryParam param = new PartnerUserSignRecordQueryParam(SYS, userId, vipTypesStr);
        param.setSign(param.generateSign(partnerRenewServerSignKey));
        return new CommonHystrixCommand<>(
                HystrixCommandPropsEnum.QUERY_USER_SIGN_RECORD_URL,
                queryUserSignRecordFunc,
                queryUserSignRecordFallbackFunc,
                param).queue();
    }

    /**
     * 根据会员类型解约
     */
    public BaseResponse<Boolean> cancelUserSign(Long userId, Long vipType) {
        return cancelUserSign(userId, vipType, null);
    }

    /**
     * 根据支付渠道解约
     */
    public BaseResponse<Boolean> cancelUserSignByPayChannel(Long userId, Integer payChannel) {
        return cancelUserSign(userId, null, payChannel);
    }

    /**
     * 解约对外合作用户签约关系 异步接口
     */
    public BaseResponse<Boolean> cancelUserSign(Long userId, Long vipType, Integer payChannel) {
        String vipTypeStr = vipType != null ? vipType.toString() : null;
        PartnerUserSignCancelParam param = new PartnerUserSignCancelParam(SYS, userId, vipTypeStr, payChannel);
        param.setSign(param.generateSign(partnerRenewServerSignKey));
        return new CommonHystrixCommand<>(
            HystrixCommandPropsEnum.CANCEL_USER_SIGN_URL,
            cancelUserSignFunc,
            cancelUserSignFallbackFunc,
            param).execute();
    }

    public static boolean notAutoRenew(BaseResponse<Boolean> cancelResponse) {
        return NO_SIGN_RECORD_RESULT_CODE.equals(cancelResponse.getCode());
    }

    public static boolean allCancelSuccess(BaseResponse<Boolean> cancelResponse) {
        return CodeEnum.SUCCESS.getCode().equals(cancelResponse.getCode());
    }

    public static boolean partCancelSuccess(BaseResponse<Boolean> cancelResponse) {
        return PART_CANCEL_SUCCESS_RESULT_CODE.equals(cancelResponse.getCode());
    }

}
