package com.qiyi.boss.outerinvoke.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * @auther: guojing
 * @date: 2023/6/2 4:49 PM
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class CommodityInfo {

    public static final Integer DEDUCTION_MODEL_DIRECT = 1;

    public static final Integer SKU_IDENTIFIER_PURE_SIGN = 5;

    public static final Integer SKU_IDENTIFIER_EXCHANGE_UPGRADE = 6;

    public static final Integer RIGHTS_TIME_LENGTH_ZERO = 0;

    public static final Integer SETTLE_TYPE_RATIO = 1;

    public static final Integer SETTLE_TYPE_ABSOLUTE = 2;

    private String skuId;

    private String spuId;
    /**
     * 后台运营展示名称
     */
    private String skuName;
    /**
     * 用户侧展示名称
     */
    private String displayName;
    private String skuDesc;
    private String catalogId;
    private Long productId;
    private String productCode;
    private Integer amount;
    /**
     * 价格
     */
    private Integer price;
    /**
     * 原价
     */
    private Integer originalPrice;
    /**
     * 有效期
     */
    private Integer period;
    /**
     * 有效期单位
     */
    private Integer periodUnit;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 子类型,套餐(教育、影视等),会员(经济套餐、超值套餐等)
     */
    private Long subType;
    /**
     * 源类型，针对升级套餐
     */
    private Long sourceSubType;
    /**
     * 会员类型code
     */
    private String vipTypeCode;
    /**
     * 升级产品的源会员类型code
     */
    private String sourceVipTypeCode;
    /**
     * 服务类别，会员类：1，教育类：2
     */
    private Integer serviceType;
    /**
     * 地区
     */
    private String area;
    /**
     * 业务类型code
     */
    private String businessCode;
    /**
     * 付费类型
     */
    private Integer chargeType;
    /**
     * 货币单位.
     */
    private String currencyUnit;
    private Timestamp deadline;
    /**
     * 状态
     */
    private Integer status;

    /**
     * 代扣模式字段：0：按照差价升级权益代扣（24年停止售卖）；1：按照高等级权益直接代扣
     */
    private Integer deductionModel;


    private Integer skuIdentifier;

    private Integer rightsTimeLength;

    /**
     * 分摊类型 1-按照比例分摊 2-按照绝对价格分摊
     */
    private Integer settleType;

    /**
     * 分摊值 1-百分数 50%记录为50 2-单位分
     */
    private Integer settleValue;

    private List<CommodityInfo> subSkuList;

}
