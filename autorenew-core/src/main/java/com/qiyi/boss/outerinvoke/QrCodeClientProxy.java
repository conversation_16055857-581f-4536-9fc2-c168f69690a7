package com.qiyi.boss.outerinvoke;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.boss.utils.CommonHystrixCommand;
import com.qiyi.boss.utils.EncodeUtils;
import com.iqiyi.kit.http.client.util.HttpClients;

/**
 * <AUTHOR>
 * @date 2021/8/20 17:46
 * wiki: http://wiki.qiyi.domain/pages/viewpage.action?pageId=563216733
 */
@Slf4j
@Component
public class QrCodeClientProxy extends BaseApi {

    private static final String QRCODER_API_PATH = "/qrcoder";

    @Value("${qr.code.app.id:02e0d03e}")
    private String qrCodeAppId;
    @Value("${qr.code.salt.key:1e35ab3acc5b41a68b60d7943b00312c}")
    private String qrCodeSaltKey;
    @Value("${qr.code.domain:http://qrcode.qiyi.smart}")
    private String qrcodeDomain;

    @Resource
    RestTemplate qrCodeClient;

    private Function<String, byte[]> genQrCoderRunFunc = url -> doGet(qrCodeClient, URI.create(url), "二维码生成接口", byte[].class);
    private Function<String, byte[]> genQrCoderFallbackFunc = url -> null;

    /**
     * url转换为二维码
     * @param url
     * @param width
     * @return 图片字节流的base64
     */
    public String genQrCode(String url, Integer width) {
        String data = EncodeUtils.urlEncode(url);
        Map<String, Object> params = new HashMap<>();
        params.put("data", data);
        params.put("salt", EncodeUtils.MD5(qrCodeSaltKey + data, StandardCharsets.UTF_8.name()));
        params.put("app_id", qrCodeAppId);
        if (width != null) {
            params.put("width", width);
        }
        String urlWithParam = HttpClients.buildQueryUrl(qrcodeDomain + QRCODER_API_PATH, params);
        byte[] response = new CommonHystrixCommand<>(
            HystrixCommandPropsEnum.QR_CODER,
            genQrCoderRunFunc,
            genQrCoderFallbackFunc,
            urlWithParam, true).execute();
        if (response == null) {
            log.error("生成二维码失败，url:{}", url);
            return null;
        }
        return Base64.getEncoder().encodeToString(response);
    }

}
