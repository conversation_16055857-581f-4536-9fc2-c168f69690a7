package com.qiyi.boss.outerinvoke.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * Created at: 2021-11-15
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class VcqContentBaseInfo {

    /**
     * 专辑内容ID，boss库id
     */
    private Long id;
    /**
     * 奇谱id
     */
    private Long qpid;
    /**
     * 专辑名称/单片名称
     */
    private String name;
    /**
     * 是否付费 0免费 1付费
     */
    private Integer isCharge;
    private Integer categoryId;
    private Integer liveStatus;
    private Timestamp liveStartTime;
    private Timestamp liveEndTime;
    private String contentType;
    private String contentUrl;
    private String pictureUrl;
    private String videoUrl;
    private String area;
    private Long previewTime;
    private Long maxPreviewTime;

}
