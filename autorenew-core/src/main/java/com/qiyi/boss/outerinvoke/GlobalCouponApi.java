package com.qiyi.boss.outerinvoke;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Map;
import java.util.function.Function;

import com.qiyi.boss.dto.ConsumeGlobalCouponResponse;
import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.outerinvoke.param.CounsumeCouponReqParm;
import com.qiyi.boss.outerinvoke.param.GlobalCouponQueryParam;
import com.qiyi.boss.outerinvoke.result.GlobalCoupon;
import com.qiyi.boss.utils.CommonHystrixCommand;
import com.qiyi.boss.utils.JacksonUtils;

import static com.qiyi.boss.utils.JsonResult.SUCCESS_CODE;

/**
 * <AUTHOR>
 * @date 2021/9/24 下午 05:56
 */
@Slf4j
@Component
public class GlobalCouponApi extends BaseApi {

    @Resource(name = "globalCouponClient")
    private RestTemplate globalCouponRestTemplate;

    @Resource(name = "globalCouponClient")
    private RestTemplate consumeCouponClient;

    @Value("${global.coupon.search.url}")
    private String globalCouponSearchUrl;

    @Value("${global.coupon.consume.url}")
    private String globalCouponConsumeUrl;


    private Function<GlobalCouponQueryParam, GlobalCoupon> doqueryGlobalCouponRunFunc = this::doQueryGlobalCoupon;
    private Function<GlobalCouponQueryParam, GlobalCoupon> doqueryGlobalCouponFallbackFunc = t -> null;

    private Function<CounsumeCouponReqParm, Boolean> consumeGlobalCouponRunFunc = this::doConsumeGlobalCoupon;
    private Function<CounsumeCouponReqParm, Boolean> consumeGlobalCouponFallbackFunc = this:: doConsumeGlobalCoupon;

    /**
     * 查询国际站代扣券
     */
    public GlobalCoupon queryGlobalCoupon(GlobalCouponQueryParam queryParam) {
        try {
            return new CommonHystrixCommand<>(
                HystrixCommandPropsEnum.GLOBAL_COUPON_API,
                doqueryGlobalCouponRunFunc,
                doqueryGlobalCouponFallbackFunc,
                queryParam,
                true
            ).execute();
        } catch (Exception e) {
            log.error("[GlobalCouponApi][queryGlobalCoupon failed !]", e);
            return null;
        }

    }

    public boolean consumeGlobalCoupon(String couponCode, String orderCode) {
        CounsumeCouponReqParm couponReqParm = CounsumeCouponReqParm.builder()
            .couponCode(couponCode)
            .orderCode(orderCode)
            .build();
        try {
            return new CommonHystrixCommand<>(
                HystrixCommandPropsEnum.GLOBAL_COUPON_API,
                consumeGlobalCouponRunFunc,
                consumeGlobalCouponFallbackFunc,
                couponReqParm,
                true
            ).execute();
        } catch (Exception e) {
            log.error("[queryGlobalCoupon failed !]", e);
            return false;
        }

    }

    private GlobalCoupon doQueryGlobalCoupon(GlobalCouponQueryParam queryParam) {
        ParameterizedTypeReference<BaseResponse<GlobalCoupon>> typeReference = new ParameterizedTypeReference<BaseResponse<GlobalCoupon>>() {};
        Map<String, Object> paramMap = JacksonUtils.beanToMap(queryParam);
        return doGet(globalCouponRestTemplate, globalCouponSearchUrl, paramMap, "查询国际站代扣优惠券", typeReference).getData();
    }

    private boolean doConsumeGlobalCoupon(CounsumeCouponReqParm couponReqParm) {
        ParameterizedTypeReference<BaseResponse<ConsumeGlobalCouponResponse>> typeReference = new ParameterizedTypeReference<BaseResponse<ConsumeGlobalCouponResponse>>() {};
        Map<String, Object> paramMap = JacksonUtils.beanToMap(couponReqParm);
        BaseResponse<ConsumeGlobalCouponResponse> response = doGet(consumeCouponClient, globalCouponConsumeUrl, paramMap, "核销国际站代扣优惠券", typeReference);
        return response != null && SUCCESS_CODE.equals(response.getCode());
    }
}
