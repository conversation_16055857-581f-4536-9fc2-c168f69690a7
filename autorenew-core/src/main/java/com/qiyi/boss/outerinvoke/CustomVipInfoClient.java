package com.qiyi.boss.outerinvoke;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.UUID;
import java.util.function.Function;

import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.model.BaseResponse.CodeEnum;
import com.qiyi.boss.model.VipUser;
import com.qiyi.boss.outerinvoke.param.MultiVipInfoReqParam;
import com.qiyi.boss.outerinvoke.result.MultiVipInfoResult;
import com.qiyi.boss.outerinvoke.result.UsersVipVipInfoResult;
import com.qiyi.boss.utils.CommonHystrixCommand;
import com.qiyi.boss.utils.SimpleHystrixCommand;
import com.qiyi.usercloud.UidEnigma;
import com.qiyi.vip.commons.component.logging.HystrixFallbackLog;
import com.qiyi.vip.commons.component.vo.EncryptUserId;
import com.qiyi.vip.commons.constant.Constants;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;

/**
 * <AUTHOR>
 * @date 2021/7/31 15:29
 */
@Slf4j
@Component
public class CustomVipInfoClient extends BaseApi {

    /**
     * 接口文档：https://iq.feishu.cn/wiki/YuFqw4dbviPV09kDlmNclSRvneb
     */
    private static final String VIP_INFOS = "/internal/users/vip/vip_infos";

    private static final String RIGHT_UPGRADE_INTERVAL_URL = "/internal/upgradeRightInfo";
    /**
     * 接口文档：https://iq.feishu.cn/wiki/YiFcwK776iwH4Nk6C8eccNH1nqg
     */
    private static final String BATCH_QUERY_VIP_INFO = "/internal/batch/vip_users";

    /**
     * 非会员
     */
    private static final String NON_VIP_USER = "Q00302";

    @Resource(name = "vipInfoRestTemplate300")
    private RestTemplate cloudQueryHttpRestTemplate300;

    @Value("${vip.infos.domain:http://vip-info-server-online}")
    private String vipInfoDomain;

    private Function<Map<String, Object>, BaseResponse<List<UsersVipVipInfoResult>>> usersVipVipInfosRunFunc = param -> doGet(cloudQueryHttpRestTemplate300,
        vipInfoDomain + VIP_INFOS, param, "查询用户指定会员类型信息",
        new ParameterizedTypeReference<BaseResponse<List<UsersVipVipInfoResult>>>() {});
    private Function<Map<String, Object>, BaseResponse<List<UsersVipVipInfoResult>>> usersVipVipInfosFallbackFunc = param -> BaseResponse.create(CodeEnum.OUTER_SERVICE_ERROR);

    private Function<MultiVipInfoReqParam, BaseResponse<List<MultiVipInfoResult>>> multiUserVipInfoRunFunc = param -> doPostByJson(cloudQueryHttpRestTemplate300,
        buildUrl(param.getUrlParam()),  param.getBodyParam(), "查询多个用户会员类型信息",
        new ParameterizedTypeReference<BaseResponse<List<MultiVipInfoResult>>>() {});
    private Function<MultiVipInfoReqParam, BaseResponse<List<MultiVipInfoResult>>> multiUserVipInfoFallbackFunc = param -> BaseResponse.createSuccess(Collections.emptyList());


    private String buildUrl(Map<String, Object> params) {
        String baseUrl = vipInfoDomain + BATCH_QUERY_VIP_INFO;
        StringJoiner paramJoiner = new StringJoiner("&");
        try {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (entry.getValue() == null || StringUtils.isBlank(entry.getValue().toString())) {
                    continue;
                }
                String encodedKey = URLEncoder.encode(entry.getKey(), "UTF-8");
                String encodedValue = URLEncoder.encode(entry.getValue().toString(), "UTF-8");
                paramJoiner.add(encodedKey + "=" + encodedValue);
            }
        } catch (Exception e) {
            log.error("buildUrl error, params:{}", params);
        }

        return baseUrl + "?" + paramJoiner.toString();
    }

    public VipUser getVipInfoNew(Long userId, Long vipType) {
        List<MultiVipInfoResult> multiVipInfoResults = batchQueryUserVipInfo(Collections.singletonList(userId), Collections.singletonList(vipType), null);
        if (CollectionUtils.isEmpty(multiVipInfoResults)) {
            return null;
        }
        List<UsersVipVipInfoResult> vipInfoDetails = multiVipInfoResults.get(0).getDetail();
        if (CollectionUtils.isEmpty(vipInfoDetails)) {
            return new VipUser(userId, vipType);
        }
        UsersVipVipInfoResult usersVipVipInfoResult = vipInfoDetails.stream()
            .filter(item -> Objects.equals(item.getVipType(), vipType))
            .findFirst().orElse(null);
        return VipUser.buildFromUsersVipVipInfosResult(usersVipVipInfoResult);
    }

    /**
     * 查询多个uid的会员到期时间
     */
    public List<MultiVipInfoResult> batchQueryUserVipInfo(List<Long> userIds, List<Long> vipTypes, String platform) {
        Map<String, Object> urlParam = new HashMap<>();
        urlParam.put("platform", StringUtils.isNotBlank(platform) ? platform : com.qiyi.boss.Constants.PLATFORM_DEFAULT_CODE);
        urlParam.put("bizSource", Constants.BIZ_SOURCE);
        urlParam.put("messageId", UUID.randomUUID().toString().replaceAll("-", ""));
        urlParam.put("version", "5.0");
        Map<String, Object> bodyParam = new HashMap<>();
        ArrayList<Map<String, Object>> item = new ArrayList<>();
        for (Long userId : userIds) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("uid", userId.toString());
            map.put("vipTypes", vipTypes);
            item.add(map);
        }
        bodyParam.put("list", item);
        MultiVipInfoReqParam reqParam = MultiVipInfoReqParam.builder()
            .urlParam(urlParam)
            .bodyParam(bodyParam)
            .build();
        BaseResponse<List<MultiVipInfoResult>> response = new CommonHystrixCommand<>(HystrixCommandPropsEnum.MULTI_USERS_VIP_INFO,
            multiUserVipInfoRunFunc,
            multiUserVipInfoFallbackFunc,
            reqParam,
            true)
            .execute();
        if (response == null || response.isFailure()) {
            return Collections.emptyList();
        }
        return response.getData();
    }

    @Deprecated
    public VipUser getVipInfo(Long userId, Long vipType, String platform) {
        return getVipInfo(userId, null, vipType, platform);
    }

    @Deprecated
    /**
     * 查询会员信息
     */
    public VipUser getVipInfo(Long userId, String authCookie, Long vipType, String platform) {
        Map<String, Object> params = new HashMap<>();
        params.put("platform", StringUtils.isNotBlank(platform) ? platform : com.qiyi.boss.Constants.PLATFORM_DEFAULT_CODE);
        params.put("bizSource", Constants.BIZ_SOURCE);
        params.put("messageId", UUID.randomUUID().toString().replaceAll("-", ""));
        params.put("version", "5.0");
        params.put("vipType", vipType);
        if (StringUtils.isNotBlank(authCookie)) {
            params.put("P00001", authCookie);
        }
        if (userId != null) {
            EncryptUserId encryptUserId = new EncryptUserId(UidEnigma.encrypt(userId));
            params.put("suid", encryptUserId.getEncryptUserId());
        }
        BaseResponse<List<UsersVipVipInfoResult>> response = new CommonHystrixCommand<>(
            HystrixCommandPropsEnum.USERS_VIP_VIP_INFOS,
            usersVipVipInfosRunFunc,
            usersVipVipInfosFallbackFunc,
            params,
            true).execute();
        if (Objects.equals(NON_VIP_USER, response.getCode())) {
            return new VipUser(userId, vipType);
        }
        if (response.isFailure()) {
            return null;
        }
        List<UsersVipVipInfoResult> responseData = response.getData();
        if (CollectionUtils.isEmpty(responseData)) {
            return new VipUser(userId, vipType);
        }
        UsersVipVipInfoResult usersVipVipInfoResult = responseData.stream()
            .filter(item -> Objects.equals(item.getVipType(), vipType))
            .findFirst().orElse(new UsersVipVipInfoResult(userId, vipType));
        return VipUser.buildFromUsersVipVipInfosResult(usersVipVipInfoResult);
    }

    /**
     * 请求权益查询可升级时长
     * @param userId
     * @param sourceVipType
     * @param targetVipType
     */
    public Integer getUpgradeDays(Long userId, Long sourceVipType, Long targetVipType) {
        Map<String, Object> params = new HashMap<>();
        params.put("bizSource", Constants.BIZ_SOURCE);
        params.put("version", "1.0");
        params.put("sourceVipTypeId", sourceVipType);
        params.put("targetVipTypeId", targetVipType);
        EncryptUserId encryptUserId = new EncryptUserId(UidEnigma.encrypt(userId));
        params.put("suid", encryptUserId.getEncryptUserId());
        return new QueryRightUpgradeDaysCommand(params).execute();
    }

    private class QueryRightUpgradeDaysCommand extends SimpleHystrixCommand<Integer> {
        private static final String GROUP_KEY = "QueryRightUpgradeDaysGroupKey";
        private static final String COMMAND_KEY = "QueryRightUpgradeDaysCmdKey";
        private Map<String, Object> params;

        QueryRightUpgradeDaysCommand(Map<String, Object> params) {
            super(GROUP_KEY, COMMAND_KEY, CloudConfigUtil.getHystrixTimeout(COMMAND_KEY));
            this.params = params;
        }

        @Override
        protected Integer run() {
            BaseResponse<Map<String, Object>> baseResponse = doPostByJson(cloudQueryHttpRestTemplate300,
                vipInfoDomain + RIGHT_UPGRADE_INTERVAL_URL,
                params,
                "查询用户升级资格接口",
                new ParameterizedTypeReference<BaseResponse<Map<String, Object>>>() {});
            if (baseResponse.isFailure()) {
                return null;
            }
            Long intervalTime = MapUtils.getLong(baseResponse.getData(), "intervalTime");
            return intervalTime != null ? (int) (intervalTime / com.qiyi.boss.Constants.ONE_DAY_IN_MILLISECONDS) : null;
        }

        @Override
        protected Integer getFallback() {
            HystrixFallbackLog.logFallback(this, params, getExecutionException());
            return null;
        }
    }

}
