package com.qiyi.boss.outerinvoke.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * 订单处理请求参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderFulfillRequest {

    private String orderCode;
    
    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 沙盒订单标识(-1)
     */
    private Integer type;

    /**
     * 支付中心订单号
     */
    private String centerCode;

    /**
     * 订单创建时间
     */
    private Timestamp tradeCreate;

    /**
     * 订单支付时间
     */
    private Timestamp tradePayment;

    /**
     * 支付中心支付类型
     */
    private String centerPayType;

    /**
     * 支付服务信息
     */
    private Integer centerPayService;

    /**
     * 交易码
     */
    private String tradeCode;

    /**
     * 第三方uid
     */
    private String accountId;

    /**
     * 交易号
     */
    private String tradeNo;

    /**
     * 支付时间
     */
    private Timestamp payTime;

    /**
     * 支付类型
     */
    private Integer payType;

    /**
     * 支付引用信息
     */
    private Map<String, Object> refer;


    private Integer realFee;

    private Integer settlementFee;

    private Integer chargeType;

    private String skuId;

    private List<OrderFulfillRequest> payAndFulfillRequests;
} 