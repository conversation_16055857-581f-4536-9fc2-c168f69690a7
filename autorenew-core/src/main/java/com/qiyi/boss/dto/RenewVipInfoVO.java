package com.qiyi.boss.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @className RenewVipInfoVO
 * @description
 * @date 2022/9/18
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class RenewVipInfoVO {
    @ApiModelProperty(value = "会员类型")
    private Integer vipType;

    @ApiModelProperty(value = "升级前会员类型")
    private Integer sourceVipType;

    @ApiModelProperty(value = "自动续费时长")
    private Integer amount;

    @ApiModelProperty(value = "自动续费产品code")
    private String pid;

    @ApiModelProperty(value = "会员类型名称")
    private String vipTypeName;

    @ApiModelProperty(value = "是否是手机包月，1-是，0-否")
    private boolean monthly;

}
