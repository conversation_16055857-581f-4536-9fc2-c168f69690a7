package com.qiyi.boss.dto;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: <PERSON><PERSON>wanqiang
 * Date: 2020-6-24
 * Time: 21:09
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WechatPreDutRemindReqDto {

	public static final String PARAM_USER_ID = "userId";
	public static final String PARAM_DUT_TYPE = "dutType";
	public static final String PARAM_DEDUCT_DURATION = "deductDuration";
	public static final String PARAM_DEDUCT_DURATION_UNIT = "deductDurationUnit";
	public static final String PARAM_AMOUNT = "amount";
	public static final String PARAM_PARTNER_ORDER_NO = "partner_order_no";
	public static final String PARAM_PARTNER = "partner";
	public static final String PARAM_NOTIFY_URL = "notify_url";
	public static final String PARAM_EXTRA_COMMON_PARAM = "extra_common_param";
	public static final String PARAM_SIGN = "sign";
	public static final String DEDUCT_DURATION_UNIT_DAY = "DAY";

	private Long userId;
	private Integer dutType;
	private Integer amount;
	private String partnerOrderNo;
	private String partner;
	private String notifyUrl;
	private String extraCommonParam;
	private String sign;

	public Map<String, String> buildMap() {
		Map<String, String> map = Maps.newHashMap();
		map.put(PARAM_USER_ID, String.valueOf(userId));
		map.put(PARAM_DUT_TYPE, String.valueOf(dutType));
		map.put(PARAM_AMOUNT, String.valueOf(amount));
		if (StringUtils.isNotBlank(partnerOrderNo)) {
			map.put(PARAM_PARTNER_ORDER_NO, partnerOrderNo);
		}
		map.put(PARAM_PARTNER, partner);
		map.put(PARAM_NOTIFY_URL, notifyUrl);
		if (StringUtils.isNotBlank(extraCommonParam)) {
			map.put(PARAM_EXTRA_COMMON_PARAM, extraCommonParam);
		}
		map.put(PARAM_SIGN, sign);

		return map;
	}

}
