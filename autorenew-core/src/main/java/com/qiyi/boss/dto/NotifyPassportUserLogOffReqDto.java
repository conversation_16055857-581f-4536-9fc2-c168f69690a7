package com.qiyi.boss.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @className NotifyPassportUserLogOffReqDto
 * @description
 * @date 2022/6/20
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class NotifyPassportUserLogOffReqDto {

    private Long uid;
    /**
     * 整型，0或1，0表示不可注销，1表示可以注销，2表示需要用户二次确认注销
     */
    private Integer result;
    /**
     * 如果result=0，details不能为空，列出不可注销的原因；
     * 如果result=1，details参数不传；
     * 如果result=2，details不能为空，列出需要用户确认的内容；
     */
    private String details;

    /**
     * 事件类型，接受到的MQ消息里的type值
     */
    private Integer type;
    /**
     * 子事件类型，接受到的MQ消息里的subType值
     */
    private Integer subType;
    /**
     * 	调用业务名称
     */
    private String source;
    /**
     * 时间戳
     */
    private Long timestamp;
}
