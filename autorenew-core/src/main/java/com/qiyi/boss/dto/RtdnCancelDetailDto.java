package com.qiyi.boss.dto;

import java.sql.Timestamp;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Builder
@ToString
@Getter
public class RtdnCancelDetailDto {

    /**
     * 事件发生的时间戳
     */
    private Long eventTimeMillis;

    /**
     * 用户取消自动续费的时间
     */
    private Long userCancellationTimeMillis;

    /**
     * 取消自动续费的原因 0：用户取消 1：系统取消 2：开通了一个新的自动续费 3：开发者取消
     */
    private Integer cancelReason;

    /**
     * 用户取消自动续费的原因（cancelReason=0） 0. 其他原因 1. 我对该服务的使用不够 2. 技术问题 3. 价格原因 4. 我发现了一个更好的APP
     */
    private Integer cancelSurveyReason;

    /**
     * 用户自己输入的取消原因（cancelSurveyReason=0）
     */
    private String userInputCancelReason;

    /**
     * 订阅过期时间
     */
    private Long expiryTimeMillis;

    public Timestamp getOperationTime() {
        return userCancellationTimeMillis != null ? new Timestamp(userCancellationTimeMillis)
                : (eventTimeMillis != null ? new Timestamp(eventTimeMillis)
                        : new Timestamp(System.currentTimeMillis()));
    }

}
