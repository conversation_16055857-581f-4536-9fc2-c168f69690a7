package com.qiyi.boss.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单扩展信息 实体
 *
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrderExtraDTO {

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", required = true)
    private String orderCode;

    /**
     * 端上每次进入收银台的唯一标识
     */
    @ApiModelProperty(value = "端上每次进入收银台的唯一标识", required = true)
    private String clientTraceId;
    /**
     * 扩展信息。json格式
     */
    @ApiModelProperty(value = "扩展信息。json格式", required = true)
    private String info;
}
