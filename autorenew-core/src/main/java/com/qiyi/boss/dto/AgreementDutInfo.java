package com.qiyi.boss.dto;

import lombok.Data;

import java.sql.Timestamp;

/**
 * 协议代扣信息
 *
 * <AUTHOR>
 * @date 2021/7/28 14:21
 */
@Data
public class AgreementDutInfo {

    /**
     * 权益开始时间
     */
    private Timestamp rightStartTime;

    /**
     * 权益结束时间
     */
    private Timestamp rightEndTime;

    /**
     * 代扣价格
     */
    private Integer dutPrice;

    /**
     * 代扣状态
     */
    private Integer dutStatus;

    /**
     * 代扣时间
     */
    private Timestamp dutTime;

    private PayTypeInfo payTypeInfo;
    /**
     * 开通剧集名称
     */
    private String createOrderSource;

    @Data
    public static class PayTypeInfo {

        /**
         * 支付渠道
         */
        private Integer payChannel;

        /**
         * 代扣方式
         */
        private Integer dutType;
    }
}
