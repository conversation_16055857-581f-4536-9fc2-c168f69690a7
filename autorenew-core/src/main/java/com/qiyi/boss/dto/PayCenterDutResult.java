package com.qiyi.boss.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qiyi.boss.enums.IsSuccessEnum;
import com.qiyi.boss.enums.PayCenterOrderStatusEnum;
import com.qiyi.boss.enums.ResultCodeEnum;
import com.qiyi.boss.utils.ConvertUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2020-10-21
 * Time: 16:23
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class PayCenterDutResult {

	private String msg;

	private String message;

	private String code;

	@JsonProperty("is_success")
	private String isSuccess;

	private String sign;

	private String error;

	private String partner;

	private String uid;

	private String subject;

	@JsonProperty("create_time")
	private Timestamp createTime;

	@JsonProperty("pay_time")
	private Timestamp payTime;

	@JsonProperty("pay_type")
	private String payType;

	@JsonProperty("partner_order_no")
	private String partnerOrderNo;

	@JsonProperty("order_code")
	private String orderCode;

	@JsonProperty("trade_code")
	private String tradeCode;

	@JsonProperty("order_status")
	private String orderStatus;

	@JsonProperty("confirm_type")
	private String confirmType;

	private String fee;

	@JsonProperty("real_fee")
	private String realFee;

	private String charset;

	private String mobile;

	@JsonProperty("area_code")
	private String areaCode;

	private String pid;

	@JsonProperty("extra_common_param")
	private String extraCommonParam;

	@JsonProperty("update_time")
	private Timestamp updateTime;

	@JsonProperty("fee_unit")
	private String feeUnit;

	private String currency;

	@JsonProperty("req_error_type")
	private String reqErrorType;

	@JsonProperty("third_error_code")
	private String thirdErrorCode;

	@JsonProperty("third_error_msg")
	private String thirdErrorMsg;

	@JsonProperty("channel_account_no")
	private String channelAccountNo;

	private String sessionId;

	@JsonProperty("dut_conf_id")
	private String dutConfId;

	@JsonProperty("service_id")
	private String serviceId;

	@JsonProperty("fee_code")
	private String feeCode;

	@JsonProperty("third_uid")
	private String thirdUid;

	private String payMode;

	private DataDto data;

	public boolean isSuccess() {
		return isReqSuccess() && isDealSuccess();
	}

	public boolean isDealSuccess() {
		return PayCenterOrderStatusEnum.ORDER_STATUS_SUC.getPayCenterOrderStatus().equals(this.orderStatus);
	}

	public boolean cardPayDealSuccess() {
		return ResultCodeEnum.isSuccess(code)
				&& data != null
				&& PayCenterOrderStatusEnum.ORDER_STATUS_SUC.getPayCenterOrderStatus().equals(data.getOrderStatus());
	}

	public boolean isReqSuccess() {
		return StringUtils.isNotEmpty(this.isSuccess) && IsSuccessEnum.IS_SUCCESS_T.getSuc().equals(this.isSuccess)
				|| ResultCodeEnum.SUCCESS.value().equals(code)
				|| ResultCodeEnum.NEED_ASYNC_CONFIRM.value().equals(code)
                || "PAY00305".equals(code);
	}

	public boolean isOrderStatusDelivered() {
		return String.valueOf(com.iqiyi.vip.order.dal.model.OrderStatusEnum.DELIVERED.getStatus()).equals(this.orderStatus);
	}

	public boolean isOrderStatusCancel() {
		return String.valueOf(com.iqiyi.vip.order.dal.model.OrderStatusEnum.CANCEL.getStatus()).equals(this.orderStatus);
	}

	public Map<String, String> parseExtraCommonParam() {
		if (StringUtils.isBlank(extraCommonParam)) {
			return null;
		}

		return ConvertUtils.parseExtraCommonParamsToMap(extraCommonParam);
	}

}
