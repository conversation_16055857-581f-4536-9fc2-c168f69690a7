package com.qiyi.boss.autorenew.dto;

import com.qiyi.boss.model.VipUser;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * User: kongwenqiang
 * DateTime: 2017/10/10 下午4:04
 * Mail:<EMAIL>   
 * Description: desc
 */
@Data
@Builder
public class SaveDutUserDto implements Serializable {

    private static final long serialVersionUID = 0L;

    private Long userId;
    private Integer type;
    private Integer status;
    private Integer autoRenew;
    private Integer source;
    private String returnUrl;
    private String fc;
    private String fv;
    private Long platform;
    private String platformCode;
    private Integer renewPrice;
    private VipUser vipUser;
    private Integer amount;
    private String operateScene;
    private Integer payChannel;
}
