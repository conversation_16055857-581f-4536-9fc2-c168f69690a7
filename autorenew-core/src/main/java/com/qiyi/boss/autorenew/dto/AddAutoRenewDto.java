package com.qiyi.boss.autorenew.dto;

import com.qiyi.boss.Constants;
import com.qiyi.boss.model.VipUser;

import lombok.Builder;
import lombok.Data;

/**
 * User: kongwenqiang
 * DateTime: 2017/10/16 下午4:38
 * Mail:<EMAIL>   
 * Description: desc
 */
@Data
@Builder
public class AddAutoRenewDto {

    private Long userId;
    private Integer dutType;
    private Integer agreementNo;
    private Integer renewPrice;
    private Long platformId;
    private String platformCode;
    private VipUser vipUser;
    private Integer amount;
    private String fc;
    private String fv;
    private Long sourceVipType;
    private String partnerNo;
    private String orderCode;
    /**
     * 签约场景：1：双签约
     */
    private Integer signScene;
    /**
     * 第三方支付账号uid
     */
    private String thirdUid;

    private String operateScene;

    private Integer payChannel;

    private Integer agreementType;

    public boolean isTwVipUser() {
        return null != getVipUser() && Constants.VIP_USER_TW == getVipUser().getTypeId();
    }
}
