package com.qiyi.boss.autorenew.enumerate;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2018-8-17
 * Time: 15:10
 * 活动类型枚举类
 */
public enum ActTypeEnum {

	/**
	 * 首X期优惠.
	 * 续费X期后恢复正价
	 */
	FIRST_X_FAVOR(1),

	/**
	 * 续费次数限制.
	 * 续费X期后关闭自动续费
	 */
	STOP_AFTER_X(2),
	;

	private Integer value;

	public Integer getValue() {
		return value;
	}

	ActTypeEnum(Integer value) {
		this.value = value;
	}
}
