package com.qiyi.boss.autorenew.dto;
import com.qiyi.boss.async.task.Task;
import lombok.Builder;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR> peng
 * 保存异步任务到数据库时，需要的关于**任务部分**的信息。
 * 由 JobService 生产。
 */
@Data
@Builder
public class DutTaskDto {
    /**
     * 用户id
     */
    @Deprecated
    Long userId;
    /**
     * 会员类型 用于标注产品类型
     */
    @Deprecated
    Long vipType;
    /**
     * 代扣类型
     */
    Integer dutType;
    /**
     * 重试时间
     */
    Timestamp retryExecTime;
    /**
     *  任务类型
     */
    @Deprecated
    String taskType;

    /**
     * 异步任务
     */
    Task task;
}

