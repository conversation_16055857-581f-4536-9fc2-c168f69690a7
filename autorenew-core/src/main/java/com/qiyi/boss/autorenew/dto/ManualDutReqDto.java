package com.qiyi.boss.autorenew.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @className ManualDutReqDto
 * @description
 * @date 2022/8/23
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ManualDutReqDto {

    @NotBlank(message = "P00001不能为空")
    @ApiModelProperty(name = "P00001", value = "用户authCookie", required = true)
    private String P00001;

    @NotNull(message = "uid不能为空")
    @ApiModelProperty(value = "用户uid", required = true)
    private Long uid;

    @ApiModelProperty(value = "用户会员类型", required = true)
    @NotNull(message = "vipType不能为空")
    private Long vipType;

    @NotBlank(message = "fc不能为空")
    @ApiModelProperty(value = "本次手动发起代扣订单fc", required = true)
    private String fc;
}
