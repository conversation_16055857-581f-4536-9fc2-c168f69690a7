package com.qiyi.boss.autorenew.dto;

import com.alibaba.fastjson.JSON;
import lombok.Builder;
import lombok.Data;

/**
 * @author: sip peng
 * @date: 2018/3/27
 * @VIPDEV:
 */

@Data
@Builder
public class DutRenewLogDesp {
    private String orderFc;
    private Integer actPeriods;
    private String actCode;
    private Integer actType;
    private String taskType;
    private String timeZone;
    private String signTime;
    private String signFlag;
    private Integer serialRenewCount;
    private Integer originalPrice;
    private Integer realDutFee;

    public String toJSONString(){
        return JSON.toJSONString(this);
    }

}
