package com.qiyi.boss.autorenew.dto;

import lombok.Builder;
import lombok.Data;

/**
 * @author: sip peng
 * @date: 2018/3/21
 * @VIPDEV:
 */

@Builder
@Data
public class UpdateUserRenewCountDto {
    public static final String whereSqlTemplate = "user_id = %s and vip_type = %s and type = %s and amount = %s and agreement_type = 1";
    Integer renewCount;
    Integer serialRenewCount;
    Integer interruptFlag;
    Long userId;
    Integer dutType;
    Long vipType;
    Integer amount;

    public String whereSql() {
        return String.format(whereSqlTemplate, userId, vipType, dutType, amount);
    }

    public String updateSql() {
        String setSql = "";
        if (renewCount != null) {
            setSql = setSql + ", renew_count = " + renewCount;
        }
        if (serialRenewCount != null) {
            setSql = setSql + ", serial_renew_count = " + serialRenewCount;
        }
        if (interruptFlag != null) {
            setSql = setSql + ", interrupt_flag =" + interruptFlag;
        }
        return setSql.substring(1);
    }
}
