package com.qiyi.boss.autorenew.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2020/9/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PasswordFreeReqDto {

    @NotNull(message = "用户id为空")
    private Long uid;
    @NotNull(message = "代扣类型为空")
    private List<Integer> dutTypeList;
    @JsonProperty("pay_type")
    private String payType;
    private String returnUrl;
}
