package com.qiyi.boss.autorenew.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "免密签约接口参数类")
public class PasswordFreePureSignParam {

    @ApiModelProperty(value = "P00001")
    private String P00001;
    @NotBlank(message = "platform参数不能为空")
    @ApiModelProperty(value = "平台code")
    private String platform;
    @ApiModelProperty(value = "returnUrl")
    private String returnUrl;
    @ApiModelProperty(value = "fc")
    private String fc;
    @ApiModelProperty(value = "fv")
    private String fv;

}
