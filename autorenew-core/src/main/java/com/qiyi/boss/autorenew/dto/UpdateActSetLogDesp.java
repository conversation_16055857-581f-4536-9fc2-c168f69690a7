package com.qiyi.boss.autorenew.dto;

import com.alibaba.fastjson.JSON;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 活动信息更新日志。
 * <p>
 * 活动更新前记录
 * <p>
 * 用于保存现场后的恢复 以及 统计分析
 *
 * @ author: sip peng
 * @ date: 2018/3/21
 * @ VIPDEV:
 */
@Data
@Builder
public class UpdateActSetLogDesp {

    /**
     * 活动代扣成功后的正常更新。
     */
    public static final int TYPE_NORMAL_ACT_UPDATE = 0;
    /**
     * 最后一次活动代扣，后恢复原价
     */
    public static final int TYPE_FINAL_ACT_UPDATE = 1;
    /**
     * 由于开通活动，导致其他代扣方式活动信息的更新。
     */
    public static final int TYPE_OPEN_DUT_ACT_UPDATE = 2;
    /**
     * 由于关闭自动续费，导致其他代扣方式活动信息更新。
     */
    public static final int TYPE_CLOSE_DUT_ACT_UPDATE = 3;
    private String orderCode;
    private String actCode;
    private Integer renewPrice;
    private Integer actPeriods;
    private Integer contractPrice;
    private Integer type;
    private Integer actType;

    public static String buildUpdateActSetLog(DutUserNew dutUserNew, Integer type) {
        UpdateActSetLogDesp setLogDesp = UpdateActSetLogDesp.builder()
                .actPeriods(dutUserNew.getRemainPeriods() == null ? 0 : dutUserNew.getRemainPeriods())
                .renewPrice(dutUserNew.getRenewPrice())
                .actCode(StringUtils.defaultIfEmpty(dutUserNew.getActCode(), ""))
                .contractPrice(dutUserNew.getContractPrice() == null ? 0 : dutUserNew.getContractPrice())
                .type(type)
                .actType(dutUserNew.getActType())
                .build();
        return JSON.toJSONString(setLogDesp);
    }
}
