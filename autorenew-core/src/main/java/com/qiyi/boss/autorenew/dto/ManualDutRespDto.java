package com.qiyi.boss.autorenew.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @className ManualDutResultRespDto
 * @description
 * @date 2022/8/23
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ManualDutRespDto {

    @ApiModelProperty(value = "用户uid")
    private Long uid;

    @ApiModelProperty(value = "用户会员类型")
    private Integer vipType;

    @ApiModelProperty(value = "用户实付金额")
    private Integer realFee;

    @ApiModelProperty(value = "用户会员有效期")
    private Long deadLine;

    @ApiModelProperty(value = "代扣结果，0：代扣失败，1：代扣成功，2：需要异步确认")
    private Integer dutResult;
}
