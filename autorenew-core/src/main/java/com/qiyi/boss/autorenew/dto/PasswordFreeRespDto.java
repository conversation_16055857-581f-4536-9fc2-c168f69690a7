package com.qiyi.boss.autorenew.dto;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2020/9/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PasswordFreeRespDto {

    private Long uid;
    private List<PasswordFree> passwordFreeList = Lists.newArrayList();

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PasswordFree {
        private int dutType;
        private int payChannel;
        private boolean isPasswordFree;

        public int getDutType() {
            return dutType;
        }

        public PasswordFree setDutType(int dutType) {
            this.dutType = dutType;
            return this;
        }

        public boolean getIsPasswordFree() {
            return isPasswordFree;
        }

        public void setIsPasswordFree(boolean isPasswordFree) {
            this.isPasswordFree = isPasswordFree;
        }
    }
}
