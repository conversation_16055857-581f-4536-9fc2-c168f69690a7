package com.qiyi.boss.autorenew.dto;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import com.qiyi.boss.Constants;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.dto.AgreementOptDto;
import com.qiyi.boss.dto.AppleTicketInfoDto;
import com.qiyi.boss.service.AutorenewRequest;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.JacksonUtils;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * 开通自动续费， set_log 的 description 字段 描述
 * <p>
 * 根据开通后状态记录。
 * <p>
 * 开通包括了普通开通、活动开通。
 *
 * @author: sip peng
 * @date: 2018/3/21
 * @VIPDEV:
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OpenDutSetLogDesp {
	public static final int TYPE_CHANGE = 3;
	public static final int TYPE_BUY_OPEN = 1;
	public static final int TYPE_DIRECT_BIND = 2;
	private String orderCode;
	private String actCode;
	private Integer renewPrice;
	private Integer actPeriods;
	private Integer contractPrice;
	private AppleTicketInfoDto appleTicketInfo;
	private Integer actType;
	private String timeZone;
	private String signTime;
	private String partnerNo;
	/**
	 * 开通类型
	 * - 购买开通
	 * - 纯签约
	 * - IAP切换
	 */
	private Integer type;

	/**
	 * 记录开通或取消来源.
	 */
	private String fc;

	private String fv;
	/**
	 * 签约场景
	 */
	private Integer signScene;
	/**
	 * 第三方支付账号uid
	 */
	private String thirdUid;

	/**
	 * 开通场景
	 */
	private String scene;

	/**
	 * 支付渠道
	 */
	private Integer payChannel;

    /**
     * 渠道来源
     */
    private String source;

	public static String buildOpenSetLogDesp(DutUserNew dutUserNew, String fc, String fv, String thirdUid, OperateSceneEnum operateScene, Integer payChannel, String source) {
		return buildSetLogDesp(dutUserNew, TYPE_BUY_OPEN, fc, fv, thirdUid, operateScene, payChannel, source);
	}

    public static String buildSetLogDesp(DutUserNew dutUserNew, Integer type, String fc, String fv, OperateSceneEnum operateScene, Integer payChannel) {
        return buildSetLogDesp(dutUserNew, type, fc, fv, null, operateScene, payChannel, null);
    }

	private static String buildSetLogDesp(DutUserNew dutUserNew, Integer type, String fc, String fv, String thirdUid, OperateSceneEnum operateScene, Integer payChannel, String source) {
		if (StringUtils.isBlank(fc)) {
			fc = "";
		}
		if (StringUtils.isBlank(fv)) {
			fv = CloudConfigUtil.getDefaultFvByVipType(dutUserNew.getVipType(), Constants.DEFAULT_FV);
		}
		if (StringUtils.isBlank(thirdUid)) {
			thirdUid = "";
		}
		OpenDutSetLogDesp desp = OpenDutSetLogDesp.builder()
				.orderCode(dutUserNew.getOrderCode())
				.contractPrice(dutUserNew.getContractPrice())
				.actPeriods(dutUserNew.getActTotalPeriods())
				.actCode(dutUserNew.getActCode())
				.renewPrice(dutUserNew.getRenewPrice())
				.type(type)
				.fc(fc)
				.fv(fv)
				.timeZone(dutUserNew.getTimeZone())
				.signTime(dutUserNew.getSignTime() == null ? "" : DateHelper.getDateStringByPattern(dutUserNew.getSignTime(), DateHelper.MOST_COMMON_PATTERN))
				.actType(dutUserNew.getActType())
				.thirdUid(thirdUid)
				.scene(operateScene.getValue())
				.payChannel(payChannel)
                .source(source)
				.build();
		return JSON.toJSONString(desp);
	}

	public static String buildOpenSetLogDespWhenDirectOpen(DutUserNew dutUserNew, String fc, String fv, String partnerNo, String operateScene, Integer payChannel) {
		return buildOpenSetLogDespWhenDirectOpen(dutUserNew, fc, fv, partnerNo, null, null, operateScene, payChannel);
	}

	public static String buildOpenSetLogDespWhenDirectOpen(DutUserNew dutUserNew, AddAutoRenewDto addAutoRenewDto) {
		return buildOpenSetLogDespWhenDirectOpen(dutUserNew, addAutoRenewDto.getFc(), addAutoRenewDto.getFv(), addAutoRenewDto.getPartnerNo(),
				addAutoRenewDto.getSignScene(), addAutoRenewDto.getThirdUid(), addAutoRenewDto.getOperateScene(), addAutoRenewDto.getPayChannel());
	}

	public static String buildOpenSetLogDespWhenDirectOpen(DutUserNew dutUserNew, String fc, String fv, String partnerNo, Integer signScene,
														   String thirdUid, String operateScene, Integer payChannel) {
        if (StringUtils.isBlank(fv)) {
            fv = CloudConfigUtil.getDefaultFvByVipType(dutUserNew.getVipType(), Constants.DEFAULT_FV);
        }
	    OpenDutSetLogDesp desp = OpenDutSetLogDesp.builder()
				.orderCode("")
				.contractPrice(dutUserNew.getContractPrice())
				.actPeriods(dutUserNew.getActTotalPeriods())
				.actCode(dutUserNew.getActCode())
				.type(TYPE_DIRECT_BIND)
				.renewPrice(dutUserNew.getRenewPrice())
				.fc(StringUtils.isBlank(fc) ? "" : fc)
				.fv(fv)
				.partnerNo(partnerNo)
				.timeZone(dutUserNew.getTimeZone())
				.signTime(dutUserNew.getSignTime() == null ? "" : DateHelper.getDateStringByPattern(dutUserNew.getSignTime(), DateHelper.MOST_COMMON_PATTERN))
				.actType(dutUserNew.getActType())
				.signScene(signScene)
				.thirdUid(thirdUid)
				.scene(operateScene)
				.payChannel(payChannel)
				.build();
		return JSON.toJSONString(desp);
	}

	public static String buildChangeIAPSetLogDespByTicket(DutUserNew dutUserNew, AppleTicketInfoDto appleTicketInfo,
														  Integer type, String operateScene, Integer payChannel) {
		if (dutUserNew == null) {
			return "";
		}
		OpenDutSetLogDesp desp = OpenDutSetLogDesp.builder()
				.orderCode("")
				.contractPrice(dutUserNew.getContractPrice())
				.actPeriods(dutUserNew.getActTotalPeriods())
				.actCode(dutUserNew.getActCode())
				.type(type)
				.renewPrice(dutUserNew.getRenewPrice())
				.appleTicketInfo(appleTicketInfo)
				.timeZone(dutUserNew.getTimeZone())
				.signTime(dutUserNew.getSignTime() == null ? "" : DateHelper.getMostCommonPatternStr(dutUserNew.getSignTime()))
				.scene(operateScene)
				.payChannel(payChannel)
				.build();
		return JSON.toJSONString(desp);
	}

	public static String buildAgreementOpenDesp(DutUserNew dutUserNew, AgreementOptDto agreementOptDto) {
		OpenDutSetLogDesp desp = OpenDutSetLogDesp.builder()
				.orderCode(dutUserNew.getOrderCode())
				.renewPrice(dutUserNew.getRenewPrice())
				.contractPrice(dutUserNew.getContractPrice())
				.fc(agreementOptDto.getFc())
				.fv(StringUtils.isNotBlank(agreementOptDto.getFv()) ? agreementOptDto.getFv() : Constants.DEFAULT_FV)
				.timeZone(dutUserNew.getTimeZone())
				.signTime(dutUserNew.getSignTime() != null ? DateHelper.getMostCommonPatternStr(dutUserNew.getSignTime()) : null)
				.thirdUid(agreementOptDto.getThirdUid())
				.scene(agreementOptDto.getOperateScene().getValue())
				.payChannel(agreementOptDto.getPayChannel())
				.build();
		return JacksonUtils.toJsonString(desp);
	}

    public static String buildOpenDesp(DutUserNew dutUserNew, AutorenewRequest autoRenewRequest, OperateSceneEnum operateScene) {
        OpenDutSetLogDesp desp = OpenDutSetLogDesp.builder()
            .orderCode(dutUserNew.getOrderCode())
            .renewPrice(dutUserNew.getRenewPrice())
            .contractPrice(dutUserNew.getContractPrice())
            .fc(autoRenewRequest.getFc())
            .fv(StringUtils.isNotBlank(autoRenewRequest.getFv()) ? autoRenewRequest.getFv() : Constants.DEFAULT_FV)
            .timeZone(dutUserNew.getTimeZone())
            .signTime(dutUserNew.getSignTime() != null ? DateHelper.getMostCommonPatternStr(dutUserNew.getSignTime()) : null)
            .thirdUid(autoRenewRequest.getThirdUid())
            .scene(operateScene.getValue())
            .payChannel(autoRenewRequest.getPayChannel())
            .build();
        return JacksonUtils.toJsonString(desp);
    }

}
