package com.qiyi.boss.autorenew.dto;

import com.alibaba.fastjson.JSON;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.dto.AgreementOptDto;
import com.qiyi.boss.dto.AppleTicketInfoDto;
import com.qiyi.boss.dto.CancelAutoRenewDto;
import com.qiyi.boss.dto.CancelAutoRenewOptDto;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.JacksonUtils;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * 取消自动续费
 *
 * @author: sip peng
 * @date: 2018/3/25
 */

@Data
@Builder
public class CancelDutSetLogDesp {

	/**
	 * 用户行为
	 * - 通过短地址取消自动续费
	 * - 通过解绑取消自动续费
	 * - cancelAutoRenew
	 */

	private String action;
	private String orderCode;
	private String actCode;
	private Integer renewPrice;
	private Integer actPeriods;
	private Integer contractPrice;
	private Integer actType;
	/**
	 * 记录取消来源.
	 */
	private String fc;

    private String fv;

    private String thirdUid;

    private String signTime;

	/**
	 * 记录取消苹果订单的描述
	 */
	private AppleTicketInfoDto appleTicketInfoDto;

	private Object extra;

	private String scene;

	private Integer payChannel;

	private String executor;

	private Integer reasonId;

	private String otherReason;

	public static String buildCancelSetLogDesp(DutUserNew dutUserNew, String behaviorDesp, Integer payChannel, CancelAutoRenewDto cancelAutoRenewDto) {
		CancelDutSetLogDesp logDesp = CancelDutSetLogDesp.builder()
				.action(behaviorDesp)
				.orderCode("")
				.contractPrice(dutUserNew.getContractPrice())
				.actPeriods(dutUserNew.getActTotalPeriods())
				.actCode(StringUtils.defaultIfEmpty(dutUserNew.getActCode(), ""))
				.renewPrice(dutUserNew.getRenewPrice())
				.fc(cancelAutoRenewDto.getFc())
				.actType(dutUserNew.getActType())
				.scene(cancelAutoRenewDto.getOperateScene())
				.payChannel(payChannel)
				.executor(cancelAutoRenewDto.getExecutor())
                .reasonId(cancelAutoRenewDto.getReasonId())
                .otherReason(cancelAutoRenewDto.getOtherReason())
				.build();
		return JSON.toJSONString(logDesp);
	}

    public static String buildCancelSetLogDesp(DutUserNew dutUserNew, CancelAutoRenewOptDto cancelAutoRenewOptDto) {
        OperateSceneEnum operateScene = cancelAutoRenewOptDto.getOperateScene();
        CancelDutSetLogDesp logDesp = CancelDutSetLogDesp.builder()
            .renewPrice(dutUserNew.getRenewPrice())
            .contractPrice(dutUserNew.getContractPrice())
            .actPeriods(dutUserNew.getActTotalPeriods())
            .actType(dutUserNew.getActType())
            .actCode(StringUtils.defaultIfEmpty(dutUserNew.getActCode(), ""))
            .fc(cancelAutoRenewOptDto.getFc())
            .fv(cancelAutoRenewOptDto.getFv())
            .thirdUid(cancelAutoRenewOptDto.getThirdUid())
            .signTime(dutUserNew.getSignTime() == null ? null : DateHelper.getMostCommonPatternStr(dutUserNew.getSignTime()))
            .scene(operateScene != null ? operateScene.getValue() : null)
            .payChannel(cancelAutoRenewOptDto.getPayChannel())
            .appleTicketInfoDto(cancelAutoRenewOptDto.getAppleTicketInfo())
            .executor(cancelAutoRenewOptDto.getExecutor())
            .reasonId(cancelAutoRenewOptDto.getReasonId())
            .otherReason(cancelAutoRenewOptDto.getOtherReason())
            .build();
        return JSON.toJSONString(logDesp);
    }

	public static String buildCancelSetLogDespForAppleAutoRenewUser(DutUserNew dutUserNew, String behaviorDesp,
																	AppleTicketInfoDto appleTicketInfo, String fc,
																	String operateScene, Integer payChannel) {
		CancelDutSetLogDesp logDesp = CancelDutSetLogDesp.builder()
				.action(behaviorDesp)
				.orderCode("")
				.renewPrice(dutUserNew.getRenewPrice())
				.contractPrice(dutUserNew.getContractPrice())
				.actPeriods(dutUserNew.getActTotalPeriods())
				.actCode(StringUtils.defaultIfEmpty(dutUserNew.getActCode(), ""))
				.appleTicketInfoDto(appleTicketInfo)
				.fc(fc)
				.scene(operateScene)
				.payChannel(payChannel)
				.build();
		return JSON.toJSONString(logDesp);
	}

	/**
	 * 构建自动续费操作日志里描述信息
	 *
	 * @param dutUserNew DutUserNew
	 * @param behavior   操作类型
	 * @param fc         FC
	 * @param extraInfo  额外信息
	 * @return 组装后的描述信息
	 */
	public static String buildCancelSetLogDescIncludingExtraInfo(DutUserNew dutUserNew, String behavior, String fc,
																 Object extraInfo, String operateScene, Integer payChannel) {
		CancelDutSetLogDesp logDesp = CancelDutSetLogDesp.builder()
				.action(behavior)
				.contractPrice(dutUserNew.getContractPrice())
				.actPeriods(dutUserNew.getActTotalPeriods())
				.actCode(StringUtils.defaultIfEmpty(dutUserNew.getActCode(), ""))
				.renewPrice(dutUserNew.getRenewPrice())
				.fc(fc)
				.actType(dutUserNew.getActType())
				.extra(extraInfo)
				.scene(operateScene)
				.payChannel(payChannel)
				.build();
		return JSON.toJSONString(logDesp);
	}

	public static String buildSetLogDesp(AgreementOptDto optParam) {
		CancelDutSetLogDesp cancelLogDesp = CancelDutSetLogDesp.builder()
				.fc(optParam.getFc())
				.scene(optParam.getOperateScene() != null ? optParam.getOperateScene().getValue() : null)
				.payChannel(optParam.getPayChannel())
				.build();
		return JacksonUtils.toJsonString(cancelLogDesp);
	}

}
