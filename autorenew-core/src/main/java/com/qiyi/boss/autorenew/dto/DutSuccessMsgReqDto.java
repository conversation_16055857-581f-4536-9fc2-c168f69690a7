package com.qiyi.boss.autorenew.dto;

import lombok.Builder;
import lombok.Data;

import com.qiyi.boss.service.AutorenewRequest;

/**
 * 代扣成功消息请求Req
 * @author: sip peng
 * @date: 2018/3/21
 * @VIPDEV:
 */

@Data
@Builder
public class DutSuccessMsgReqDto {
    Integer amount;
    Long platform;
    String platformCode;
    Long userId;
    Integer dutType;
    Long vipType;
    String orderCode;

    public static DutSuccessMsgReqDto buildFromAutoRenewRequest(AutorenewRequest autorenewRequest) {
        return DutSuccessMsgReqDto.builder()
            .amount(autorenewRequest.getAmount())
            .platform(autorenewRequest.getPlatformId())
            .platformCode(autorenewRequest.getPlatformCode())
            .userId(autorenewRequest.getUserId())
            .vipType(autorenewRequest.getVipType())
            .orderCode(autorenewRequest.getOrderCode())
            .dutType(autorenewRequest.getDutType())
            .build();
    }

}
