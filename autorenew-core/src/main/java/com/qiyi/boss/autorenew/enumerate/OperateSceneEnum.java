package com.qiyi.boss.autorenew.enumerate;

import org.apache.commons.lang.StringUtils;

import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2020-12-7
 * Time: 13:56
 * @see {http://wiki.qiyi.domain/pages/viewpage.action?pageId=963024480}
 */
public enum OperateSceneEnum {

	OPEN_BUY("open_buy", "签约支付开通", DutUserNew.RENEW_AUTO),
	OPEN_PURESIGN("open_pureSign", "纯签约开通", DutUserNew.RENEW_AUTO),
	OPEN_BUY_EXTENDSIGN("open_buy_extendSign", "扩展签约支付开通", DutUserNew.RENEW_AUTO),
	OPEN_PURESIGN_EXTENDSIGN("open_pureSign_extendSign", "扩展纯签约开通", DutUserNew.RENEW_AUTO),
	OPEN_DIRECT("open_direct", "一键开通", DutUserNew.RENEW_AUTO),
	OPEN_GOOGLEREOPEN("open_googleReOpen", "Google恢复开通自动续费", DutUserNew.RENEW_AUTO),
    OPEN_APPLE_REOPEN("open_apple_reopen", "苹果恢复开通自动续费", DutUserNew.RENEW_AUTO),
	OPEN_APPLECHANGE("open_appleChange", "苹果切换自动续费订阅", DutUserNew.RENEW_AUTO),

	CANCEL_MANAGEMENT("cancel_management", "自动续费管理页取消", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_MANAGEMENT_UNBIND("cancel_management_unbind", "自动续费管理页解约", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_MANAGEMENT_SECONDARY_PAGE("cancel_management_secondary", "自动续费管理页二级页取消", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_KEFU("cancel_kefu", "客服操作取消(非解约)", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_DUT_TYPE_EXPIRED("cancel_dutTypeExpired", "代扣方式过期", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_CHANGEAMOUNT("cancel_changeAmount", "时长切换", DutUserNew.RENEW_EXCHANGE),
	CANCEL_CHANGEPRIORITY("cancel_changePriority", "提价-优先级切换", DutUserNew.RENEW_EXCHANGE),
	CANCEL_CHANGEVIPTYPE("cancel_changeVipType", "同体系下会员类型切换", DutUserNew.RENEW_EXCHANGE),
	CANCEL_CHANGEUPGRADE("cancel_changeUpGrade", "升级自动续费切换", DutUserNew.RENEW_EXCHANGE),
	CANCEL_STUDENTAUTORENEW24TIMES("cancel_studentAutoRenew24Times", "学生会员续费满24期", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_REFUND("cancel_refund", "退款取消自动续费", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_OTHER("cancel_other", "其他入口取消", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_EXCHANGE("cancel_exchange", "切换自动续费", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_PAYCENTERNOTIFY("cancel_payCenterNotify", "支付中心通知,如苹果票据通知", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_UNREGISTER("cancel_unregister", "账户注销", DutUserNew.RENEW_NOT_AUTO),
    CANCEL_UNREGISTER_KEFU("cancel_unregister_kefu", "客服后台发起账户注销，取消自动续费", DutUserNew.RENEW_NOT_AUTO),
    CANCEL_UNREGISTER_UNKNOWN("cancel_unregister_unknown", "未知来源申请账户注销，取消自动续费", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_UNBIND_OUTSIDE("cancel_unbind_outside", "站外解约取消", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_UNBIND_KEFU("cancel_unbind_kefu", "客服解约取消", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_UNBIND_COMMON("cancel_unbind_common", "系统解约取消,如特定批量处理场景", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_UNBIND("cancel_unbind", "支付中心解约取消", DutUserNew.RENEW_NOT_AUTO),
    CANCEL_UNBIND_AFTER_CANCELED("cancel_unbind_after_canceled", "已取消续费后解约", DutUserNew.RENEW_NOT_AUTO),

	CANCEL_INSIDE_BY_USER("cancel_inside_by_user", "用户在爱奇艺侧解约", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_INSIDE("cancel_inside", "爱奇艺触发解约", DutUserNew.RENEW_NOT_AUTO),
    CANCEL_PAY_TIMEOUT("cancel_pay_timeout", "扣款超时解约", DutUserNew.RENEW_NOT_AUTO),
    CANCEL_AGREEMENT_NO_EXPIRED("cancel_agreement_no_expired", "协议编号过期", DutUserNew.RENEW_NOT_AUTO),
    CANCEL_AGREEMENT_PERIODS_REACHED("cancel_agreement_periods_reached", "协议期满，正常履约完成", DutUserNew.RENEW_NOT_AUTO),
	CANCEL_UNBIND_KEFU_WITH_CLOSE_ORDER("cancel_unbind_kefu_with_close_order", "客服解约取消，同时关单", DutUserNew.RENEW_NOT_AUTO),
    CANCEL_BY_ORDER_PAID_MSG("cancel_by_order_paid_msg", "监听订单支付消息，进行解约", DutUserNew.RENEW_NOT_AUTO),
    CANCEL_BIND_PAGE("cancel_bind_info_page", "绑定管理页取消", DutUserNew.RENEW_NOT_AUTO),
	;

	private String value;

	private String desc;
	/**
	 * 操作类型
	 * 1：开通；0：取消
	 */
	private int sceneType;

    public int getSceneType() {
        return sceneType;
    }

    public String getValue() {
		return value;
	}

    public String getDesc() {
        return desc;
    }

    OperateSceneEnum(String value, String desc, int sceneType) {
		this.value = value;
		this.desc = desc;
		this.sceneType = sceneType;
	}

	public static OperateSceneEnum parseValue(String value) {
		if (StringUtils.isBlank(value)) {
			return null;
		}
		for (OperateSceneEnum sceneEnum : values()) {
			if (sceneEnum.getValue().equals(value)) {
				return sceneEnum;
			}
		}

		return null;
	}

	/**
	 * 开通场景
	 */
	public boolean isOpenScene() {
		return this.sceneType == DutUserNew.RENEW_AUTO;
	}

	/**
	 * 取消场景
	 */
	public boolean isCancelScene() {
		return this.sceneType == DutUserNew.RENEW_NOT_AUTO;
	}

	/**
	 * 切换场景
	 */
	public boolean isExchangeScene() {
		return this.sceneType == DutUserNew.RENEW_EXCHANGE;
	}

    /**
     * 签约支付开通场景
     */
    public static boolean isSignPayScene(String operateScene) {
        return OperateSceneEnum.OPEN_BUY.getValue().equals(operateScene)
            || OperateSceneEnum.OPEN_BUY_EXTENDSIGN.getValue().equals(operateScene);
    }

}
