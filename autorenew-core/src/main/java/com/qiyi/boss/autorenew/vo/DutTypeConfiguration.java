package com.qiyi.boss.autorenew.vo;

import com.qiyi.boss.utils.AppConfig;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class DutTypeConfiguration implements Comparable<DutTypeConfiguration> {
    public static final DutTypeConfiguration GASH_PHONE = new DutTypeConfiguration(
            12,
            AppConfig.getProperty("account.unbindForGash.url"),
            "redirect",
            2
    );

    public static final DutTypeConfiguration GASH_CREDIT_CARD = new DutTypeConfiguration(
            13,
            AppConfig.getProperty("account.unbindForGash.url"),
            "redirect",
            2
    );

    public static final DutTypeConfiguration TW_IOS = new DutTypeConfiguration(
            15, 1);

    public static final DutTypeConfiguration SP_GATEWAY = new DutTypeConfiguration(
            21, 3);

    public static final DutTypeConfiguration GOOGLE_BILLING_MONTH = new DutTypeConfiguration(
            30, 1);
    public static final DutTypeConfiguration GOOGLE_BILLING_QUARTER = new DutTypeConfiguration(
            31, 1);
    public static final DutTypeConfiguration GOOGLE_BILLING_YEAR = new DutTypeConfiguration(
            32, 1);


    private Integer dutTypeId;
    private String cancelLink = AppConfig.getProperty("autorenew.url");
    private String cancelResultType = "json";
    private Integer priority;

    DutTypeConfiguration(Integer dutTypeId, Integer priority) {
        this.dutTypeId = dutTypeId;
        this.priority = priority;
    }

    DutTypeConfiguration(Integer dutTypeId, String cancelLink,
                         String cancelResultType, Integer priority) {
        this.dutTypeId = dutTypeId;
        this.cancelLink = cancelLink;
        this.cancelResultType = cancelResultType;
        this.priority = priority;
    }

    @Override
    public int compareTo(DutTypeConfiguration c) {
        return this.priority.compareTo(c.priority);
    }
}