package com.qiyi.boss.enums;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @className CouponTypeEnum
 * @description
 * @date 2023/12/5
 **/
public enum CouponTypeEnum {

    CASH_COUPON(1, "代金券"),

    DISCOUNT_COUPON(2, "代扣券");

    private final Integer type;

    private final String desc;

    CouponTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    private static final HashMap<Integer, CouponTypeEnum> map = new HashMap<>();

    static {
        for (CouponTypeEnum enumType : CouponTypeEnum.values()) {
            map.put(enumType.getType(), enumType);
        }
    }

    public static CouponTypeEnum parse(Integer type) {
        return map.getOrDefault(type, null);
    }
}
