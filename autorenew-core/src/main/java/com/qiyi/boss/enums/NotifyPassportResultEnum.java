package com.qiyi.boss.enums;

/**
 * <AUTHOR>
 * @className NotifyPassportResultEnum
 * @description
 * @date 2022/6/20
 **/
public enum NotifyPassportResultEnum {
    NOT_ALLOW(0, "不可注销"),
    ALLOW(1, "可以注销"),
    NEED_RECONFIRM(2,"需要二次确认"),;

    private Integer value;
    private String desc;

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    NotifyPassportResultEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
