package com.qiyi.boss.enums;

import java.util.Objects;

/**
 * 免密签约状态
 *
 * @Author: <PERSON>
 * @Date: 2020/9/28
 */
public enum PasswordFreeStatusEnum {
    CLOSED(0, "关闭"),
    OPEN(1, "开通");

    private Integer value;
    private String desc;

    PasswordFreeStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static boolean isPasswordFree(Integer status) {
        return Objects.equals(OPEN.value, status);
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
