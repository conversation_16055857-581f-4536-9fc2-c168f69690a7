package com.qiyi.boss.exception;

import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

import com.qiyi.boss.enums.CodeEnum;
import com.qiyi.boss.model.BaseResponse;

/**
 * Created at: 2021-06-21
 *
 * <AUTHOR>
 */
@ToString(callSuper = true)
@Getter
public class BizException extends RuntimeException {

    private String code;

    public BizException(String code, String msg) {
        super(msg);
        this.code = code;
    }

    public BizException(BaseResponse.CodeEnum codeEnum) {
        super(codeEnum.getMsg());
        this.code = codeEnum.getCode();
    }

    public static BizException newParamException(String msg) {
        return new BizException(BaseResponse.CodeEnum.ERROR_PARAM.getCode(), msg);
    }

    public static BizException newSystemException(String msg) {
        return new BizException(BaseResponse.CodeEnum.ERROR_SYSTEM.getCode(), msg);
    }

    public static BizException newException(BaseResponse.CodeEnum codeEnum) {
        return new BizException(codeEnum);
    }

    public static BizException newException(BaseResponse.CodeEnum codeEnum, String msg) {
        return new BizException(codeEnum.getCode(), msg);
    }

    public static BizException newException(String code, String errorMsg) {
        return new BizException(code, errorMsg);
    }

    public BizException(CodeEnum codeEnum) {
        super(codeEnum.getMsg());
        this.code = codeEnum.getCode();
    }

    public boolean isSystemException() {
        return Objects.equals(BaseResponse.CodeEnum.ERROR_SYSTEM.getCode(), code);
    }

    public boolean isParamException() {
        return Objects.equals(BaseResponse.CodeEnum.ERROR_PARAM.getCode(), code);
    }

}
