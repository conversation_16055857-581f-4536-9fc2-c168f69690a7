package com.qiyi.boss.exception;

import com.qiyi.boss.model.BaseResponse;

/**
 * 订单核心服务异常封装
 * @auther: guojing
 * @date: 2023/9/6 16:26
 */
public class OrderSystemException extends RuntimeException {

    private String code;

    public OrderSystemException(String code, String msg) {
        super(msg);
        this.code = code;
    }

    public static OrderSystemException newSaveOrderException() {
        return new OrderSystemException(BaseResponse.CodeEnum.ERROR_SYSTEM.getCode(), "save order failed");
    }

    public static OrderSystemException newQueryOrderException(BaseResponse baseResponse) {
        String errorMsg = null;
        if (baseResponse != null) {
            errorMsg = baseResponse.getCode() + ":" + baseResponse.getMsg();
        }
        return new OrderSystemException(BaseResponse.CodeEnum.ERROR_SYSTEM.getCode(), "query order failed, respMessage:" + errorMsg);
    }

}
