package com.qiyi.vip.trade.autorenew.dao;

import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;

public interface UserAgreementSetLogMapper {

    int deleteByPrimaryKey(Long id);

    int insert(DutRenewSetLog record);

    int updateByPrimaryKeySelective(DutRenewSetLog record);

    DutRenewSetLog selectBySignKeyAndOptType(@Param("userId") Long userId, @Param("signKey") String signKey, @Param("operateType") Integer operateType);

    List<DutRenewSetLog> getByAgreementNo(@Param("userId") Long userId, @Param("agreementNo") Integer agreementNo, @Param("operator") Integer operator);

    List<DutRenewSetLog> selectByDutType(@Param("userId") Long userId, @Param("dutType") Integer dutType, @Param("operator") Integer operator);

    List<DutRenewSetLog> getByAgreementTypeAndVipTypes(@Param("userId") Long userId, @Param("agreementType") Integer agreementType, @Param("vipTypes") List<Long> vipTypes);

}