package com.qiyi.vip.trade.autorenew.dao;

import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

public interface UserAgreementMapper {

    int deleteByPrimaryKey(Long id);

    int insert(DutUserNew record);

    int updateByPrimaryKeySelective(DutUserNew record);

    int save(DutUserNew dutUserNew);

    int cancelAutoRenew(@Param("id") Long id, @Param("userId") Long userId, @Param("actType") Integer actType, @Param("unBind") Boolean unbind);

    int updateStatus(@Param("id") Long id, @Param("userId") Long userId, @Param("autoRenew") Integer autoRenew);

    int invalidOrFinishRecord(@Param("id") Long id, @Param("userId") Long userId, @Param("autoRenew") Integer autoRenew);

    int updateNextDutTime(@Param("id") Long id, @Param("userId") Long userId, @Param("nextDutTime") Timestamp nextDutTime);

    int updateNextDutTimeAndAgreementNo(@Param("id") Long id, @Param("userId") Long userId, @Param("nextDutTime") Timestamp nextDutTime, @Param("agreementNo") Integer agreementNo);

    int updateNextDutTimeAndExt(@Param("id") Long id, @Param("userId") Long userId, @Param("nextDutTime") Timestamp nextDutTime, @Param("ext") String ext);

    int updateNextDutTimeAndDeadline(@Param("id") Long id, @Param("userId") Long userId, @Param("nextDutTime") Timestamp nextDutTime, @Param("deadline") Timestamp deadline, @Param("ext") String ext);

    int updateRenewCount(@Param("id") Long id, @Param("userId") Long userId, @Param("renewCount") Integer renewCount, @Param("serialRenewCount") Integer serialRenewCount);

    int incrementRenewCount(@Param("id") Long id, @Param("userId") Long userId);

    int incrementRenewCountAndResetNextDutTime(@Param("id") Long id, @Param("userId") Long userId);

    int batchUpdateDeadline(@Param("ids") List<Long> ids, @Param("userId") Long userId, @Param("deadline") Timestamp deadline);

    int batchUpdateNextDutTime(@Param("ids") List<Long> ids, @Param("userId") Long userId, @Param("nextDutTime") Timestamp nextDutTime);

    int batchUpdateDeadlineAndNextDutTime(@Param("ids") List<Long> ids, @Param("userId") Long userId, @Param("deadline") Timestamp deadline, @Param("nextDutTime") Timestamp nextDutTime);

    DutUserNew selectByPrimaryKey(@Param("id") Long id, @Param("userId") Long userId);

    List<DutUserNew> selectByAgreementNoAndVipType(@Param("userId") Long userId, @Param("agreementNo") Integer agreementNo, @Param("vipType") Long vipType, @Param("autoRenew") Integer autoRenew);

    List<DutUserNew> selectByAgreementNoAndDutType(@Param("userId") Long userId, @Param("agreementNo") Integer agreementNo, @Param("dutType") Integer dutType, @Param("sourceVipType")Long sourceVipType, @Param("vipType")Long vipType);

    /**
     * 目前表中同一个userId和dutType有两条记录的情况，需要返回list
     */
    List<DutUserNew> selectByDutTypeAndVipType(@Param("userId") Long userId, @Param("dutType") Integer dutType, @Param("vipType") Long vipType);

    List<DutUserNew> selectByVipType(@Param("userId") Long userId, @Param("sourceVipType") Long sourceVipType, @Param("vipType") Long vipType, @Param("autoRenew") Integer autoRenew);

    List<DutUserNew> selectEffectByUid(@Param("userId") Long userId);

    List<DutUserNew> selectByAgreementTypeAndVipType(@Param("userId") Long userId, @Param("agreementType") Integer agreementType, @Param("vipType") Long vipType, @Param("autoRenew") Integer autoRenew);
    List<DutUserNew> selectByExcludeAgreementTypesAndVipTypes(@Param("userId") Long userId, @Param("excludeAgreementTypeList") List<Integer> excludeAgreementTypeList, @Param("vipTypes") List<Long> vipTypes, @Param("autoRenew") Integer autoRenew);

    List<DutUserNew> selectByAgreementInfo(@Param("userId") Long userId, @Param("agreementNo") Integer agreementNo, @Param("agreementTypeList") List<Integer> agreementTypeList, @Param("agreementStatusList") List<Integer> agreementStatusList);

    DutUserNew selectBySignKey(@Param("userId") Long userId, @Param("signKey") String signKey);

    List<DutUserNew> getUserByAllAgreements(@Param("userId") Long userId, @Param("autoRenew") Integer autoRenew);

    int updateUserAccountBindStatus(@Param("userId") Long userId, @Param("dutType") Integer dutType, @Param("status") Integer status);

}