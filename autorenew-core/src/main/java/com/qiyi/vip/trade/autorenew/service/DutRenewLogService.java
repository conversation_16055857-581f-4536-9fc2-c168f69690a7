package com.qiyi.vip.trade.autorenew.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;

import com.qiyi.boss.dao.Page;
import com.qiyi.boss.dao.PropertyFilter;
import com.qiyi.boss.enums.DutFailedEnum;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.service.impl.QiYuePlatformManager;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.Platform;
import com.qiyi.vip.trade.autorenew.domain.vo.DutRenewLogVO;
import com.qiyi.vip.trade.autorenew.repository.DutRenewLogRepository;
import com.qiyi.vip.trade.qiyue.domain.QiYuePlatform;

/**
 * <AUTHOR>
 * created 2019/3/5 - 15:09
 */
@Service
@Slf4j
public class DutRenewLogService {

    @Resource
    private QiYuePlatformManager platformManager;

    @Resource
    private DutRenewLogRepository dutRenewLogRepository;

    @Resource
    private AutoRenewDutTypeManager autoRenewDutTypeManager;

    public Page<DutRenewLog> searchDutRenewLogKF(Page<DutRenewLog> page, List<PropertyFilter> filters) {
        boolean existUserId = false;
        for (PropertyFilter propertyFilter : filters) {
            if (propertyFilter.getPropertyName().contains("userId")) {
                existUserId = true;
            }
        }
        if (!existUserId) {
            log.warn("查询代扣日志, userId为空!");
            return page;
        }
        Page<DutRenewLog> dutRenewLogPage= dutRenewLogRepository.findPage(page, filters);
        for(DutRenewLog dutRenewLog : dutRenewLogPage.getResult()){
            String platformName = null;
            if (StringUtils.isNotBlank(dutRenewLog.getPlatformCode())) {
                QiYuePlatform qiYuePlatform = platformManager.getPlatformByCode(dutRenewLog.getPlatformCode());
                platformName = qiYuePlatform != null ? qiYuePlatform.getName() : null;
            } else {
                Platform platform = platformManager.getBossPlatformById(dutRenewLog.getPlatform());
                platformName = platform != null ? platform.getName() : null;
            }
            dutRenewLog.setPlatformName(platformName);
        }
        return dutRenewLogPage;
    }

    public DutRenewLog getDutRenewLogByOrderCode(Long userId, String orderCode) {
        return dutRenewLogRepository.getDutRenewLogByOrderCode(userId, orderCode);
    }

    public void processDutRequestFailure(DutRenewLog dutRenewLog) {
        //调用奇悦接口出现异常，保存错误日志
        if (StringUtils.isBlank(dutRenewLog.getErrorCode())) {
            dutRenewLog.setFailedInfo(DutFailedEnum.QUERY_DUT_PAY_TIMEOUT);
        }
        dutRenewLog.setStatus(DutRenewLog.PAY_FAILURE);
        dutRenewLogRepository.save(dutRenewLog);
        log.warn("http processDutRequest fail userId:{}", dutRenewLog.getUserId());
    }

    public void processDutNeedAsyncConfirm(DutRenewLog dutRenewLog) {
        dutRenewLog.setStatus(DutRenewLog.PAY_CONFIRM);
        dutRenewLogRepository.save(dutRenewLog);
        log.info("Dut task confirm dutRenewLog:{}", dutRenewLog);
    }

    public boolean userHadSuccessDutLogToday(Long userId, Long vipType, Integer agreementType) {
        List<Integer> includeDutTypes = autoRenewDutTypeManager.getDutTypeListByVipType(vipType, agreementType);
        return userHadSuccessDutLogToday(userId, includeDutTypes);
    }

    public boolean userHadSuccessDutLogToday(Long userId, List<Integer> dutTypeList) {
        String currentDate = DateHelper.getFormatDate(DateHelper.getDateTime(), DateHelper.SIMPLE_PATTERN);
        String beginTimeStr = currentDate + " 00:00:00";
        String endTimeStr = currentDate + " 23:59:59";
        Timestamp beginTime = DateHelper.getDateTime(DateHelper.getDateFromStr(beginTimeStr, DateHelper.MOST_COMMON_PATTERN));
        Timestamp endTime = DateHelper.getDateTime(DateHelper.getDateFromStr(endTimeStr, DateHelper.MOST_COMMON_PATTERN));
        List<DutRenewLog> dutRenewLogList = dutRenewLogRepository.getDutRenewLog(userId, beginTime, endTime, dutTypeList, DutRenewLog.PAY_SUCCESS);
        return CollectionUtils.isNotEmpty(dutRenewLogList);
    }

    public List<DutRenewLog> getTodayAutoRenewLog(Long userId, Integer type) {
        String currentDate = DateHelper.getFormatDate(DateHelper.getDateTime(), "yyyy-MM-dd");
        String beginTimeStr = currentDate + " 00:00:00";
        String endTimeStr = currentDate + " 23:59:59";
        Timestamp beginTime = DateHelper.getDateTime(DateHelper.getDateFromStr(beginTimeStr, DateHelper.MOST_COMMON_PATTERN));
        Timestamp endTime = DateHelper.getDateTime(DateHelper.getDateFromStr(endTimeStr, DateHelper.MOST_COMMON_PATTERN));
        return dutRenewLogRepository.getDutRenewLogByUpdateTime(userId, beginTime, endTime, null, Collections.singletonList(type));
    }

    public void updateRenewLog(DutRenewLogVO dutRenewLogVO) {
        if (dutRenewLogVO == null
            || dutRenewLogVO.getId() == null
            || dutRenewLogVO.getUserId() == null
            || dutRenewLogVO.getVipType() == null
            || dutRenewLogVO.getType() == null) {
            throw new IllegalArgumentException("id and userId can not be null.");
        }
        log.info("update renew_log param is {}", dutRenewLogVO);

        DutRenewLog updater = new DutRenewLog();
        BeanUtils.copyProperties(dutRenewLogVO, updater);
        dutRenewLogRepository.save(updater);
    }
}
