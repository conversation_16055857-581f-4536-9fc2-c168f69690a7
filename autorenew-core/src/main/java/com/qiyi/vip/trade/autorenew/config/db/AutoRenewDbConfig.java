package com.qiyi.vip.trade.autorenew.config.db;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import javax.sql.DataSource;
import com.qiyi.database.internal.datasource.CloudDataSource;

@Configuration
@Slf4j
@MapperScan(basePackages = "com.qiyi.vip.trade.autorenew.mapper", sqlSessionFactoryRef = "autorenewSqlSessionFactory")
public class AutoRenewDbConfig extends AbstractDBConfig {


    @Bean(name = "masterDataSource")
    protected DataSource masterDataSource() {
        log.info("数据源启动中: masterDataSource");
        return new CloudDataSource("autorenew_master_config");
    }

    @Bean(name = "autorenewSqlSessionFactory")
    protected SqlSessionFactory sqlSessionFactory(@Qualifier("masterDataSource") DataSource dataSource)
            throws Exception {
        log.info("数据源启动完成: autorenew");
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        sqlSessionFactoryBean.setMapperLocations(resolver.getResources("classpath:sqlmap/*Mapper.xml"));
        sqlSessionFactoryBean.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
        return sqlSessionFactoryBean.getObject();
    }

    @Bean(name = "autorenewSqlSessionTemplate")
    protected SqlSessionTemplate sqlSessionTemplate(
            @Qualifier("autorenewSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
