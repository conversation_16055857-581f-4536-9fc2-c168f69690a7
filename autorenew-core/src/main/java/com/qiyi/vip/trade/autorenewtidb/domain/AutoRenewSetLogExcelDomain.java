package com.qiyi.vip.trade.autorenewtidb.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/12/30 下午 10:31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutoRenewSetLogExcelDomain {

    @ExcelProperty("用户id")
    private Long userId;

    @ExcelProperty("操作时间")
    private String operateTime;

    @ExcelProperty("会员类型")
    private Long vipType;

    @ExcelProperty("会员类型名称")
    private String vipTypeName;

    @ExcelProperty("签约时长(单位：月)")
    private Integer amount;

    @ExcelProperty("代扣方式id")
    private Integer type;

    @ExcelProperty("代扣方式名称")
    private String typeName;

    @ExcelProperty("支付渠道")
    private String payChannel;

    @ExcelProperty("取消原因id")
    private Integer reasonId;

    @ExcelProperty("取消原因")
    private String reason;

    @ExcelProperty("其他原因")
    private String otherReason;

    @ExcelProperty("取消场景")
    private String scene;
}
