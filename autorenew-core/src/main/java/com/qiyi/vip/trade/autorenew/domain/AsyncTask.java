package com.qiyi.vip.trade.autorenew.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;


/**
 * <AUTHOR>
 * 异步任务
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AsyncTask implements Serializable {

    public final static int Task_PRIORITY_0 = 0;
    public final static int Task_PRIORITY_1 = 1;
    public final static int Task_PRIORITY_2 = 2;
    public final static int Task_PRIORITY_3 = 3;
    public final static int Task_PRIORITY_4 = 4;

    private static final long serialVersionUID = 2983867246370826078L;

    /**
     * 线程池3，notice
     */
	public final static int POOL_TYPE_NOTICE = 3;
    /**
     * 线程池3，notice
     */
    public final static int POOL_TYPE_CMCC = 4;
    /**
     * 线程池7，设置自动续费状态
     */
    public final static int POOL_TYPE_AUTO_RENEW = 7;
    /**
     * 线程池8，用户VIP信息新增修改通知passport
     */
	public final static int POOL_TYPE_VIP_PASSPORT = 8;
    /**
     * 线程池9，自动续费完成后更新状态
     */
	public final static int POOL_TYPE_AUTO_RENEW_CONFIRM = 9;
    /**
     * 线程池12，微信模板消息续费到期提醒
     */
    public final static int POOL_TYPE_RENEWAL_EXPIRE_REMINDER = 12;
    /**
     * 拆分系统后的自动续费池
     */
    public static final int POOL_TYPE_DUT_AUTO_RENEW_NEW = 50;
    /**
     * 拆分系统后的自动续费池
     */
    public static final int POOL_TYPE_DUT_AUTO_RENEW_MOBILE = 51;
    /**
     * 自动续费扣费前一天短信提醒
     */
	public static final int POOL_TYPE_DUT_AUTO_RENEW_SMS_REMIND_NEW = 52;
    /**
     * 通用自动续费线程池
     */
	public static final int POOL_TYPE_DUT_AUTO_RENEW_COMMON = 53;
    /**
     * 通用自动续费短信提醒线程池
     */
	public static final int POOL_TYPE_DUT_AUTO_RENEW_SMS_REMIND_COMMON = 54;

    /**
     * 线程池102，台湾开通自动续费线程池
     */
    public final static int POOL_TYPE_OPEN_AUTO_RENEW_TAIWAN = 102;
    /**
     * 线程池103，台湾发起代扣线程池
     */
    public final static int POOL_TYPE_DO_AUTO_RENEW_TAIWAN = 103;
    /**
     * 线程池104, 通知自动续费状态线程池
     */
	public static final int POOL_TYPE_NOTIFY_RENEW_SET_INFO = 104;

	public final static int POOL_TYPE_UPDATE_PASSPORT_AUTO_RENEW = 105;
    /**
     * 延迟保存task线程池
     */
    public final static int POOL_TYPE_DELAY_SAVE_TASK = 106;

    /**
     * 不在队列
     */
    public final static int TASK_IN_QUEUE_NO = 0;
    /**
     * 在队列中
     */
    public final static int TASK_IN_QUEUE_YES = 1;

    private Long id;
    /**
     * 序列化后的数据
     */
    private String data;
    /**
     * 数据类型，用于反射
     */
    private String className;
    /**
     * 创建时间
     */
    private Timestamp createdAt;
    /**
     * 类型，每种类型对应一个线程池
     */
    private Integer poolType;
    /**
     * 数据优先级，数值越低优先级越高，每个线程池有独立的优先级体系
     */
    private Integer priority;
    /**
     * 默认不在队列
     */
    private Integer inQueue;
    /**
     * 定时执行时间
     */
    private Timestamp timerRunAt;

    /**
     * 定时表达式,支持任务的重复执行
     */
    private String cronExpression;

    /**
     * 最后更新时间
     */
    private Timestamp updateTime;

    private String taskIp;
    /**
     * task 唯一标识， job可以重复执行
     */
    private String taskId;
    /**
     * 是否支持延迟: 0:不支持 1:支持
     */
    private Integer supportDelay;

    public boolean canDelay() {
        return supportDelay != null && supportDelay == 1;
    }
}
