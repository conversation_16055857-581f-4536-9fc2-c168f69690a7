package com.qiyi.vip.trade.autorenew.domain;

import com.google.common.collect.Sets;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Set;

import com.qiyi.boss.utils.DateHelper;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> liuwanqiang Date: 2017-03-23 Time: 09:30
 */
@Data
public class PaymentDutType {

    /**
     * id.
     */
    protected Long id;

    /**
     * 支付方式.
     */
    private Integer payType;

    /**
     * 代扣方式.
     */
    private Integer dutType;

    /**
     * 支付渠道.
     */
    private Integer payChannel;

    /**
     * 支付渠道类型. 1: 会员发起扣款 2: 由第三方扣款
     */
    private Integer payChannelType;

    /**
     * serviceCode
     */
    private String serviceCode;

    /**
     * 创建时间
     */
    private Timestamp createTime;
    /**
     * 最后更新时间
     */
    private Timestamp updateTime = new Timestamp(System.currentTimeMillis());

    /**
     * 续费时长，1：包月；3：包季；12：包年
     */
    private Integer amount;

    /**
     * 升级源会员类型
     */
    private Integer sourceVipType;

    /**
     * 会员类型：1：黄金；3：白银；4：钻石；5：奇异果；6：台湾黄金
     */
    private Integer vipType;

    /**
     * 活动编码（fs值）
     */
    private String actCode;

    /**
     * 签约价，单位为分
     */
    private Integer renewPrice;

    /**
     * 有效开始时间
     */
    private Timestamp validStartTime;

    /**
     * 有效结束时间
     */
    private Timestamp validEndTime;

    /* 支付渠道 **/
    /**
     * 支付宝
     */
    public static final int PAY_CHANNEL_ALIPAY = 1;
    /**
     * 微信
     */
    public static final int PAY_CHANNEL_WECHAT = 2;
    /**
     * 百度钱包
     */
    public static final int PAY_CHANNEL_BAIDUPAY = 3;
    /**
     * GASH
     */
    public static final int PAY_CHANNEL_GASH = 4;
    /**
     * 手机话费
     */
    public static final int PAY_CHANNEL_MOBILE = 5;
    /**
     * 奇豆
     */
    public static final int PAY_CHANNEL_QIDOU = 6;
    /**
     * 爱奇艺零钱
     */
    public static final int PAY_CHANNEL_QIYI_WALLET = 7;
    /**
     * 银行卡
     */
    public static final int PAY_CHANNEL_BANKCARD = 8;
    /**
     * 苹果应用内支付
     */
    public static final int PAY_CHANNEL_IAP = 9;
    /**
     * 台湾智付通
     */
    public static final int PAY_CHANNEL_SPGATEWAY = 10;
    /**
     * Google Billing
     */
    public static final int PAY_CHANNEL_GOOGLE_BILLING = 11;
    /**
     * PayPal
     */
    public static final int PAY_CHANNEL_PAYPAL = 12;
    /**
     * MasterCard
     */
    public static final int PAY_CHANNEL_MASTERCARD = 15;
    /**
     * 信用卡
     */
    public static final int PAY_CHANNEL_CREDITCARD = 14;
    /**
     * 海外支付宝
     */
    public static final int PAY_CHANNEL_OVERSEA_ALIPAY = 41;
    /**
     * 海外微信
     */
    public static final int PAY_CHANNEL_OVERSEA_WECHAT = 42;
    /**
     * 中国联通
     */
    public static final int PAY_CHANNEL_CUCC = 44;
    /**
     * 中国移动
     */
    public static final int PAY_CHANNEL_CMCC = 45;
    /**
     * 中国电信
     */
    public static final int PAY_CHANNEL_CTCC = 46;
    /**
     * 抖音
     */
    public static final int PAY_CHANNEL_DOU_YIN = 47;

    /**
     * GCASH  此处channel 与qiyue2.0 pay_channel基础数据保持一致
     */
    public static final int PAY_CHANNEL_GCASH = 32;
    /**
     * Fortumo  此处channel 与qiyue2.0 pay_channel基础数据保持一致
     */
    public static final int PAY_CHANNEL_FORTUMO = 33;
    /**
     * DANA  此处channel 与qiyue2.0 pay_channel基础数据保持一致
     */
    public static final int PAY_CHANNEL_DANA = 36;
    /**
     * TRUEMONEY  此处channel 与qiyue2.0 pay_channel基础数据保持一致
     */
    public static final int PAY_CHANNEL_TRUEMONEY = 35;
    /**
     * CODAPAY  此处channel 与qiyue2.0 pay_channel基础数据保持一致
     */
    public static final int PAY_CHANNEL_CODAPAY = 37;
    /**
     * 快手担保支付宝
     */
    public static final int PAY_CHANNEL_KS_GUARANTEE_ALIPAY = 50;
    /**
     * 快手担保微信
     */
    public static final int PAY_CHANNEL_KS_GUARANTEE_WECHAT = 51;
    /**
     * 抖音担保支付支付宝
     */
    public static final int PAY_CHANNEL_DOU_YIN_GUARANTEE_ALIPAY = 52;
    /**
     * 支付渠道类型-会员发起扣款
     */
    public static final int PAY_CHANNEL_TYPE_VIP_DUT_PAY = 1;
    /**
     * 支付渠道类型-由第三方扣款
     */
    public static final int PAY_CHANNEL_TYPE_THIRD_DUT_PAY = 2;

    private static final Set<Integer> THIRD_PAY_CHANNEL_SET = Sets.newHashSet(PAY_CHANNEL_IAP, PAY_CHANNEL_GOOGLE_BILLING, PAY_CHANNEL_PAYPAL);

    public static boolean isThirdPayChannel(Integer payChannel) {
        return THIRD_PAY_CHANNEL_SET.contains(payChannel);
    }

    public boolean isInEffect() {
        Timestamp currentTime = DateHelper.getCurrentTime();
        return this.validStartTime != null && this.validEndTime != null && currentTime.before(this.validEndTime)
            && currentTime.after(this.validStartTime);
    }

}
