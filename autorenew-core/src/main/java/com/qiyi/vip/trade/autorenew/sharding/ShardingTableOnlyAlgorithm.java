package com.qiyi.vip.trade.autorenew.sharding;

import io.shardingjdbc.core.api.algorithm.sharding.PreciseShardingValue;
import io.shardingjdbc.core.api.algorithm.sharding.standard.PreciseShardingAlgorithm;

import java.util.Collection;

/**
 * <AUTHOR>
 * created 2019/2/27 - 14:50
 * 单库下的分表算法
 */
public class ShardingTableOnlyAlgorithm implements PreciseShardingAlgorithm<String> {

    private static final int SHARDING_NUMBER = 256;

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<String> shardingValue) {
        for (String tblName : availableTargetNames) {
            long userId = Long.parseLong(String.valueOf(shardingValue.getValue()));
            if (tblName.endsWith(String.valueOf(userId % SHARDING_NUMBER))) {
                return tblName;
            }
        }
        throw new UnsupportedOperationException();
    }
}
