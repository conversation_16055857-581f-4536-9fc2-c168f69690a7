package com.qiyi.vip.trade.autorenew.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * created 2019/3/5 - 12:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DutRenewLogVO {

    private Long id;
    private Long userId;
    /**
     * 一次签约唯一标识
     */
    private String signKey;
    /**
     * 协议编号
     */
    private Integer agreementNo;
    /**
     * 协议模板类型
     * @see com.qiyi.boss.enums.AgreementTypeEnum
     */
    private Integer agreementType;
    private Timestamp createTime;
    private Integer type;
    private String orderCode;
    private String tradeCode;
    private Integer status;
    private String errorCode;
    private Integer fee;
    private Long platform;
    private String platformCode;
    private String reqErrorType;
    private String thirdErrorCode;
    private String thirdErrorMsg;
    private String description;
    /**
     * 会员类型，1：大陆会员，2：台湾会员
     */
    private Integer vipCategory;
    /**
     * 续费时长
     */
    private Integer amount;

    /**
     * 会员类型
     */
    private Long vipType;

    private Timestamp updateTime;
}
