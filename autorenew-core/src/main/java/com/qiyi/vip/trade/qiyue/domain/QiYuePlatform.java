package com.qiyi.vip.trade.qiyue.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Calendar;

@Table("qiyue_platform")
@Data
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QiYuePlatform implements Serializable {

    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String description;
    /**
     * id
     */
    @Id
    protected Long id;
    /**
     * code
     */
    private String code;
    /**
     * 平台类型,1:PC, 2:移动，3:tv
     */
    private Integer type;
    /**
     * 区域 cn:大陆 tw:台湾
     */
    private String area;
    /**
     * 取值为:iqiyi,pps
     */
    private String corporation;
    /**
     * 取值为：{"1":"大陆会员平台","2":"OTT会员平台","3":"台湾会员平台"}
     */
    private String businessType;
    /**
     * 创建时间
     */
    private Timestamp createTime = new Timestamp(Calendar.getInstance().getTimeInMillis());

    private String tags;

}