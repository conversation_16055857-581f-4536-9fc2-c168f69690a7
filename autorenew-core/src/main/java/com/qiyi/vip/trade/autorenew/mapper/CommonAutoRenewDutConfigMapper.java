package com.qiyi.vip.trade.autorenew.mapper;

import com.qiyi.vip.trade.autorenew.domain.CommonAutoRenewDutConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CommonAutoRenewDutConfigMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(CommonAutoRenewDutConfig record);

    CommonAutoRenewDutConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CommonAutoRenewDutConfig record);

    List<CommonAutoRenewDutConfig> findAll(@Param("category") Integer category,
                                           @Param("vipType") Long vipType,
                                           @Param("payChannel")Integer payChannel,
                                           @Param("status")Integer status);
}