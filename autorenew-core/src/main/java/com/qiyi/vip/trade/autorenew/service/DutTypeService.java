package com.qiyi.vip.trade.autorenew.service;

import com.qiyi.boss.db.DataSource;
import com.qiyi.boss.db.DataSourceEnum;
import com.qiyi.vip.trade.autorenew.domain.DutType;
import com.qiyi.vip.trade.autorenew.mapper.DutTypeMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * created 2019/1/10 - 16:12
 */
@Service
public class DutTypeService {

    @Resource
    private DutTypeMapper dutTypeMapper;

    //@DataSource(DataSourceEnum.SLAVE)
    public DutType getDutType(Integer id) {
        return dutTypeMapper.selectByPrimaryKey(id);
    }

    //@DataSource(DataSourceEnum.SLAVE)
    public List<DutType> findDutTypeByStatus(int status) {
        return dutTypeMapper.findDutTypeByStatus(status);
    }
}
