package com.qiyi.vip.trade.autorenew.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 结算模板的支付方式信息
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AgreementTempPayType implements Serializable {
    /**
     * 主键id
     */
    private Integer id;
    /**
     * 协议编号
     */
    private Integer agreementNo;
    /**
     * 协议模板code
     */
    @Deprecated
    private String agreementCode;
    /**
     * 支付渠道
     */
    private Integer payChannel;
    /**
     * 支付方式
     */
    private Integer payType;
    /**
     * 状态,0:无效;1:有效
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Timestamp createTime;
    /**
     * 更新时间
     */
    private Timestamp updateTime;

}