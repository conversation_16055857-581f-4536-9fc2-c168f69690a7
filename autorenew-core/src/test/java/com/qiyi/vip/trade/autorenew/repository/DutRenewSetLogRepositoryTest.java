package com.qiyi.vip.trade.autorenew.repository;

import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * created 2019/3/26 - 19:42
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class DutRenewSetLogRepositoryTest {

    @Resource
    private DutRenewSetLogRepository dutRenewSetLogRepository;

    public static final int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();

    @Test
    public void testCreateDutRenewSetLog() {
        DutRenewSetLog dutRenewSetLog = new DutRenewSetLog();
        dutRenewSetLog.setUserId(123L);
        dutRenewSetLog.setVipType(1L);
        dutRenewSetLog.setAmount(1);
        dutRenewSetLog.setOperateTime(DateHelper.getCurrentTime());
        dutRenewSetLog.setOperator(1);
        dutRenewSetLogRepository.createDutRenewSetLog(dutRenewSetLog);
        Assert.assertNotNull(dutRenewSetLog.getId());
    }

    @Test
    public void testGetRecentlySetLogByVipType() {
        long userId = 123;
        long vipType = 1;
        int operator = 1;
        DutRenewSetLog dutRenewSetLog = dutRenewSetLogRepository.getRecentlySetLogByVipType(userId, agreementType, vipType, operator);
        System.out.println(dutRenewSetLog);
    }
}
