package com.qiyi.vip.trade.autorenew.repository;

import com.google.common.collect.Lists;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.dao.DutRenewLogDao;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * created 2019/3/4 - 11:53
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class DutRenewLogRepositoryTest {

    @Resource
    private DutRenewLogRepository dutRenewLogRepository;

    @Resource
    private DutRenewLogDao dutRenewLogDao;

    private Long userId = 9527L;
    private String orderCode = "12335543431234312";
    private Integer dutType = 6;
    @Test
    public void testSave() {
        DutRenewLog dutRenewLog = DutRenewLog.builder()
                .userId(9527L)
                .orderCode(orderCode)
                .type(dutType)
                .createTime(DateHelper.getDateTime())
                .updateTime(DateHelper.getDateTime())
                .build();
        dutRenewLogRepository.save(dutRenewLog);
        Assert.assertNotNull(dutRenewLog.getId());
    }

    /**
     * 需要测试正反方向的更新
     */
    @Test
    public void testUpdateRenewLogByPrimaryKeySelective() {
        List<DutRenewLog> dutRenewLogs = dutRenewLogDao.getDutRenewLogByOrderCode(userId, orderCode);
        if (CollectionUtils.isNotEmpty(dutRenewLogs)) {
            DutRenewLog dutRenewLog = dutRenewLogs.get(0);
            dutRenewLog.setAmount(1);
            dutRenewLogRepository.save(dutRenewLog);
        }
        //方向更新
        dutRenewLogs = dutRenewLogDao.getDutRenewLogByOrderCode(userId, orderCode);
        if (CollectionUtils.isNotEmpty(dutRenewLogs)) {
            DutRenewLog dutRenewLog = dutRenewLogs.get(0);
            dutRenewLog.setAmount(121);
            dutRenewLogRepository.save(dutRenewLog);
        }

        DutRenewLog nonShardRenewLog = dutRenewLogDao.getDutRenewLogByOrderCode(userId, orderCode).get(0);
        DutRenewLog shardRenewLog = dutRenewLogDao.getDutRenewLogByOrderCode(userId, orderCode).get(0);

        Assert.assertEquals(nonShardRenewLog.getUpdateTime(), shardRenewLog.getUpdateTime());
        Assert.assertEquals(nonShardRenewLog.getAmount(), shardRenewLog.getAmount());
    }

    @Test
    public void testGetDutRenewLogByTime() {
        Timestamp startTime = DateHelper.getTimestamp("2014-04-24 12:02:04");
        Timestamp endTime = DateHelper.getTimestamp("2019-03-04 12:06:20");
        Integer vipCategory = 1;
        List<DutRenewLog> dutRenewLogs = dutRenewLogRepository.getDutRenewLogByTime(userId, startTime, endTime, vipCategory);

        Assert.assertNotNull(dutRenewLogs);
    }

    @Test
    public void testFindDutRenewLogByTime() {
        Timestamp startTime = DateHelper.getTimestamp("2014-04-24 12:02:04");
        Timestamp endTime = DateHelper.getTimestamp("2019-03-04 12:06:20");
        long vipType = 1;
        List<DutRenewLog> dutRenewLogs = dutRenewLogRepository.findDutRenewLogByTime(userId, startTime, endTime, null, vipType);

        Assert.assertNotNull(dutRenewLogs);
    }

    @Test
    public void testGetLastDutRenewLogAtTimeRange() {
        long vipType = 1;
        int dutType = 9;
        Timestamp startTime = DateHelper.getTimestamp("2014-04-24 12:02:04");
        Timestamp endTime = DateHelper.getTimestamp("2019-03-04 12:06:20");

        DutRenewLog dutRenewLog = dutRenewLogRepository.getLastDutRenewLogAtTimeRange(userId, dutType, vipType, startTime, endTime);
        Assert.assertNotNull(dutRenewLog);
    }

    @Test
    public void testGetRecentlyDutRenewLog() {
        long vipType = 1;
        int dutType = 9;
        DutRenewLog dutRenewLog = dutRenewLogRepository.getRecentlyDutRenewLog(userId, dutType, vipType);
        Assert.assertNotNull(dutRenewLog);
    }

    @Test
    public void testGetDutRenewLog() {
        Integer status = 1;
        List<Integer> dutTypeList = Lists.newArrayList(6, 9);

        Timestamp startTime = DateHelper.getTimestamp("2014-04-24 12:02:04");
        Timestamp endTime = DateHelper.getTimestamp("2019-03-04 12:06:20");

        List<DutRenewLog> dutRenewLogs = dutRenewLogRepository.getDutRenewLog(userId, startTime, endTime, dutTypeList, status);

        Assert.assertNotNull(dutRenewLogs);
    }

    @Test
    public void testGetDutRenewLogByOrderCode() {
        DutRenewLog dutRenewLog = dutRenewLogRepository.getDutRenewLogByOrderCode(userId, orderCode);
        System.out.println(dutRenewLog);

        orderCode = null;
        dutRenewLog = dutRenewLogRepository.getDutRenewLogByOrderCode(userId, orderCode);
        System.out.println(dutRenewLog);
    }

    @Test
    public void testSearch() {
        String startTime = "2014-04-24 12:02:04";
        String endTime = "2019-03-04 12:06:20";
        Integer status = 1;
        List<DutRenewLog> dutRenewLogs = dutRenewLogRepository.search(userId, startTime, endTime, status);

        Assert.assertNotNull(dutRenewLogs);

        dutRenewLogs = dutRenewLogRepository.search(null, startTime, endTime, status);

        Assert.assertNotNull(dutRenewLogs);
    }

    @Test
    public void testGetDutRenewLogByUpdateTime() {
        Timestamp startTime = DateHelper.getTimestamp("2014-04-24 12:02:04");
        Timestamp endTime = DateHelper.getTimestamp("2019-03-04 12:06:20");
        Integer status = 1;
        List<Integer> dutTypeList = Lists.newArrayList(6, 9);

        List<DutRenewLog> dutRenewLogs = dutRenewLogRepository.getDutRenewLogByUpdateTime(userId, startTime, endTime, status, dutTypeList);

        Assert.assertNotNull(dutRenewLogs);
    }
}
