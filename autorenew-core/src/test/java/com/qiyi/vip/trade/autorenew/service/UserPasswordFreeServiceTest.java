package com.qiyi.vip.trade.autorenew.service;

import com.google.common.collect.Lists;
import com.qiyi.boss.autorenew.dto.PasswordFreeRespDto;
import com.qiyi.boss.service.UserPasswordFreeService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * @Author: <PERSON>
 * @Date: 2020/9/28
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class UserPasswordFreeServiceTest {
    @Resource
    private UserPasswordFreeService userPasswordFreeService;

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Test
    public void testGetPwdFreeList() {
        PasswordFreeRespDto passwordFreeRespDto = userPasswordFreeService.getList(1366416400L, Lists.newArrayList(290));
        Assert.assertNotNull(passwordFreeRespDto);
    }
}
