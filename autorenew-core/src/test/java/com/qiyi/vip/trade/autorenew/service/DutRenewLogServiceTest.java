package com.qiyi.vip.trade.autorenew.service;

import com.qiyi.boss.dao.Page;
import com.qiyi.boss.dao.PropertyFilter;
import com.qiyi.vip.trade.autorenew.domain.DutRenewLog;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * created 2019/5/15 - 17:06
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class DutRenewLogServiceTest {

    @Resource
    private DutRenewLogService dutRenewLogService;

    @Test
    public void testProcessDutRequestFailure() {
        DutRenewLog dutRenewLog = dutRenewLogService.getDutRenewLogByOrderCode(1399922400L, "201711101526547311101");
        dutRenewLogService.processDutRequestFailure(dutRenewLog);
        dutRenewLog = dutRenewLogService.getDutRenewLogByOrderCode(1399922400L, "201711101526547311101");

        Assert.assertEquals(dutRenewLog.getStatus().intValue(), DutRenewLog.PAY_FAILURE);
        Assert.assertEquals("Q00333", dutRenewLog.getErrorCode());
    }

    @Test
    public void testProcessDutNeedAsyncConfirm() {
        DutRenewLog dutRenewLog = dutRenewLogService.getDutRenewLogByOrderCode(1399922400L, "201711101526547311101");
        dutRenewLogService.processDutNeedAsyncConfirm(dutRenewLog);
        dutRenewLog = dutRenewLogService.getDutRenewLogByOrderCode(1399922400L, "201711101526547311101");

        Assert.assertEquals(dutRenewLog.getStatus().intValue(), DutRenewLog.PAY_CONFIRM);
    }

    @Test
    public void testSearchDutRenewLogKF() {
        Page<DutRenewLog> page = new Page<>();
        page.setPageNo(1);
        page.setPageSize(5);
        page.setOrder("asc");
        page.setOrderBy("id");

        List<PropertyFilter> filters = new LinkedList<>();
        filters.add(new PropertyFilter("EQL_userId", "2208403200"));

        Page<DutRenewLog> result = dutRenewLogService.searchDutRenewLogKF(page, filters);
        System.out.println(result);
    }

    @Test
    public void testSearchDutRenewLogKFWithoutUserId() {
        Page<DutRenewLog> page = new Page<>();
        page.setPageNo(1);
        page.setPageSize(5);
        page.setOrder("asc");
        page.setOrderBy("id");

        List<PropertyFilter> filters = new LinkedList<>();
        filters.add(new PropertyFilter("EQS_orderCode", "201712261104332306526"));

        Page<DutRenewLog> result = dutRenewLogService.searchDutRenewLogKF(page, filters);
        System.out.println(result);
    }
}
