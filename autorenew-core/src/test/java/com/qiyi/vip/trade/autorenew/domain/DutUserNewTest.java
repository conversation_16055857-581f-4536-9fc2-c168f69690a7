package com.qiyi.vip.trade.autorenew.domain;

import com.qiyi.boss.Constants;
import com.qiyi.boss.utils.DateHelper;
import org.junit.Test;

import java.sql.Timestamp;

import static com.qiyi.boss.utils.DateHelper.MOST_COMMON_PATTERN;

/**
 * <AUTHOR>
 * created 2019/3/4 - 10:40
 */
public class DutUserNewTest {

    private static final int OFFSET_TWO = 2;

    @Test
    public void testDefaultValue() {
        DutUserNew dutUserNew = DutUserNew.builder().build();
        System.out.println(dutUserNew.getRenewCount());
        System.out.println(dutUserNew.getSerialRenewCount());
        System.out.println(dutUserNew.getVipCategory());
    }

    @Test
    public void testBuilderDefaultValue() {
        DutUserNew query = DutUserNew.builder()
                .userId(123L)
                .type(9)
                .vipType(1L)
                .build();

        System.out.println(query);
    }

    @Test
    public void testDut() {
        String maxDutDate = DateHelper.getFormatDate(DateHelper.caculateTime(DateHelper.getDateTime(),
                OFFSET_TWO, Constants.PRODUCT_PERIODUNIT_DAY), "yyyy-MM-dd");
        String maxDutTimeStr = maxDutDate + " 00:00:00";
        System.out.println(DateHelper.getDateTime(DateHelper.getDateFromStr(maxDutTimeStr, "yyyy-MM-dd HH:mm:ss")));

        String beginTimeStr = DateHelper.getFormatDate(DateHelper.caculateTime(DateHelper.getDateTime(),
                1, Constants.PRODUCT_PERIODUNIT_HOUR), "yyyy-MM-dd HH:00:00");
        Timestamp beginTime = DateHelper.getDateTime(DateHelper.getDateFromStr(beginTimeStr, MOST_COMMON_PATTERN));
        Timestamp deadline = DateHelper.caculateTime(beginTime, 1, Constants.PRODUCT_PERIODUNIT_HOUR);

        System.out.println(beginTime);
        System.out.println(deadline);
    }
}
