package com.qiyi.vip.trade.autorenew.api;

import com.qiyi.boss.utils.GoogleReceiptQueryApi;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * @author: zhangtengfei01
 * @date: 2020/10/28 15:16
 * @desc: 测试查询谷歌票据信息
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class GoogleReceiptQueryApiTest {


    static {
        System.setProperty("spring.profiles.active", "dev");
    }


    @Resource
    private GoogleReceiptQueryApi googleReceiptQueryApi;

    /**
     * 测试是否可正常查找到票据信息
     */
    @Test
    public void testQueryGoogleReceipt() {
        Long expireTime = googleReceiptQueryApi.getExpireTime("2020102700002801524", 1420712844L);
        Assert.assertNotNull(expireTime);
    }
}
