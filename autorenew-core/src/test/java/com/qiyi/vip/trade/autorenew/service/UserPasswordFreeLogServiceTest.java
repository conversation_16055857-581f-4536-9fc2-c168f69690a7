package com.qiyi.vip.trade.autorenew.service;

import com.qiyi.boss.service.UserPasswordFreeLogService;
import com.qiyi.vip.trade.autorenew.domain.UserPasswordFreeLog;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * @Author: <PERSON> Pei<PERSON>
 * @Date: 2020/9/28
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class UserPasswordFreeLogServiceTest {
    @Resource
    private UserPasswordFreeLogService userPasswordFreeLogService;

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Test
    public void testGetPwdFreeList() {
        UserPasswordFreeLog userPasswordFreeLog = UserPasswordFreeLog.builder()
                .userId(1366416400L).dutType(2).payChannel(1).operation(1).source("TEST").build();
        userPasswordFreeLogService.save(userPasswordFreeLog);
    }
}
