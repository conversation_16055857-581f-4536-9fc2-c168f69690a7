package com.qiyi.vip.trade.autorenew.service;

import com.google.common.collect.Lists;
import com.qiyi.boss.dao.Page;
import com.qiyi.boss.dao.PropertyFilter;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class DutRenewSetLogServiceTest {

    @Resource
    private DutRenewSetLogService dutRenewSetLogService;

    final static int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();

    @Test
    public void testAddSetLog() {
        DutRenewSetLog toSave = DutRenewSetLog
                .builder()
                .userId(123L)
                .type(6)
                .vipType(1L)
                .operator(1)
                .build();
        dutRenewSetLogService.addDutRenewSetLog(toSave);
        Assert.assertNotNull(toSave.getId());
    }

    @Test
    public void testGetRecentlySetLogByVipTypeAndDutType() {
        long uid = 123L;
        long vipType = 1;
        int operator = 1;
        int dutType = 9;
        DutRenewSetLog dutRenewSetLog = dutRenewSetLogService.getRecentlySetLogByVipTypeAndDutType(uid, vipType, dutType, operator);
        Assert.assertNotNull(dutRenewSetLog);
    }

    @Test
    public void testGetRecentlyDutRenewSetLog() {
        long uid = 123L;
        int operator = 1;
        int dutType = 9;
        DutRenewSetLog dutRenewSetLog = dutRenewSetLogService.getRecentlyDutRenewSetLog(uid, agreementType, dutType, operator);
        Assert.assertNotNull(dutRenewSetLog);
    }

    @Test
    public void testGetDutRenewSetLogList() {
        long uid = 123L;
        int operator = 1;
        List<Integer> dutTypes = Lists.newArrayList(6, 9);
        List<DutRenewSetLog> dutRenewSetLogs = dutRenewSetLogService.getDutRenewSetLogList(uid, agreementType, dutTypes, operator);
        Assert.assertNotNull(dutRenewSetLogs);
    }

    @Test
    public void testQueryDutSetLogsByUserId() {
        long uid = 123L;
        String startTime = "2019-02-26";
        String endTime = null;
        int operator = 1;
        List<DutRenewSetLog> dutRenewSetLogs = dutRenewSetLogService.queryDutSetLogsByUserId(uid, agreementType, startTime, endTime, operator);
        Assert.assertNotNull(dutRenewSetLogs);
    }

    @Test
    public void testSearchPackageCategory() {
        Page<DutRenewSetLog> page = new Page<>();
        page.setPageNo(1);
        page.setPageSize(5);
        page.setOrder("asc");
        page.setOrderBy("id");

        List<PropertyFilter> filters = new LinkedList<>();
        filters.add(new PropertyFilter("EQL_userId", "2208403200"));

        page = dutRenewSetLogService.searchPackageCategory(page, filters);

        System.out.println(page);
    }

    @Test
    public void testSearchPackageCategoryWithoutUserId() {
        Page<DutRenewSetLog> page = new Page<>();
        page.setPageNo(1);
        page.setPageSize(5);
        page.setOrder("asc");
        page.setOrderBy("id");

        List<PropertyFilter> filters = new LinkedList<>();
        filters.add(new PropertyFilter("EQL_operator", "1"));

        page = dutRenewSetLogService.searchPackageCategory(page, filters);

        System.out.println(page);
    }
}
