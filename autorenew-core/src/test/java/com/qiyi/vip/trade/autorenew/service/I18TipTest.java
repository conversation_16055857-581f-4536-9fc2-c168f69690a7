package com.qiyi.vip.trade.autorenew.service;

import com.iqiyi.vip.language.distribute.CorpusTemplate;
import com.qiyi.boss.Constants;
import com.qiyi.boss.utils.I18nConstants;
import com.qiyi.boss.utils.I18nUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * @author: zhangtengfei01
 * @date: 2019/9/6 16:04
 * @desc: 测试收银台购买自动续费商品时提示文案
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class I18TipTest {

    @Resource
    private CorpusTemplate corpusTemplate;

    @Test
    public void testAutoRenewTips_tw() {
        String lang = "zh_TW".toLowerCase();
        String autoRenewTip = corpusTemplate.getTranslation(I18nConstants.I18N_KEY_AUTO_RENEWING_TIP, lang);
        String twAutoRenewTip = corpusTemplate.getTranslation(I18nConstants.I18N_KEY_AUTO_RENEWING_TW_TIP, lang);
        String onceAutoRenewTip = corpusTemplate.getTranslation(I18nConstants.I18N_KEY_ONCE_AUTO_RENEW_TIP, lang);

        String oldAutoRenewTip = I18nUtils.getMultiLangContentByMap(Constants.ZH_TW_LANG, "C00003");
        String oldTwAutoRenewTip = I18nUtils.getMultiLangContentByMap(Constants.ZH_TW_LANG, "C00001");
        String oldOnceAutoRenewTip = I18nUtils.getMultiLangContentByMap(Constants.ZH_TW_LANG, "C00002");

        Assert.assertEquals(autoRenewTip, oldAutoRenewTip);
        Assert.assertEquals(twAutoRenewTip, oldTwAutoRenewTip);
        Assert.assertEquals(onceAutoRenewTip, oldOnceAutoRenewTip);
    }


    @Test
    public void testAutoRenewTips_cn() {
        String lang = "zh_CN".toLowerCase();
        String autoRenewTip = corpusTemplate.getTranslation(I18nConstants.I18N_KEY_AUTO_RENEWING_TIP, lang);
        String twAutoRenewTip = corpusTemplate.getTranslation(I18nConstants.I18N_KEY_AUTO_RENEWING_TW_TIP, lang);
        String onceAutoRenewTip = corpusTemplate.getTranslation(I18nConstants.I18N_KEY_ONCE_AUTO_RENEW_TIP, lang);

        String oldAutoRenewTip = I18nUtils.getMultiLangContentByMap(Constants.ZH_CN_LANG, "C00003");
        String oldTwAutoRenewTip = I18nUtils.getMultiLangContentByMap(Constants.ZH_CN_LANG, "C00001");
        String oldOnceAutoRenewTip = I18nUtils.getMultiLangContentByMap(Constants.ZH_CN_LANG, "C00002");

        Assert.assertEquals(autoRenewTip, oldAutoRenewTip);
        Assert.assertEquals(twAutoRenewTip, oldTwAutoRenewTip);
        Assert.assertEquals(onceAutoRenewTip, oldOnceAutoRenewTip);
    }
}
