package com.qiyi.vip.trade.autorenew.service;

import com.google.common.collect.Lists;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.service.impl.DutManager;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import java.util.List;

/**
 * <AUTHOR>
 * created 2019/3/15 - 17:02
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class DutServiceTest {

    @Autowired
    private DutManager dutManager;

    @Test
    public void testGetDutUsersByNextDutTime() {
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<DutUserNew> dutUserNews = dutManager.getDutUsersByNextDutTime(agreementType, 1, 1, 6L, Lists.newArrayList(21, 36, 37, 38), 1, "boss_dut_user_new_002");
        System.out.println(dutUserNews);
    }
}
