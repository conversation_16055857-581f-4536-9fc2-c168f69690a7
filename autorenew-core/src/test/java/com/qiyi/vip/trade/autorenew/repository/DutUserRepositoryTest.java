package com.qiyi.vip.trade.autorenew.repository;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import com.qiyi.boss.Constants;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.IntroductoryPriceAct;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import com.qiyi.vip.trade.autorenew.domain.vo.DutUserNewVO;
import com.qiyi.vip.trade.autorenew.mapper.IntroductoryPriceActMapper;

/**
 * <AUTHOR>
 * created 2019/3/4 - 17:36
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class DutUserRepositoryTest {

    @Resource
    private DutUserRepository dutUserRepository;
    @Resource
    private IntroductoryPriceActMapper introductoryPriceActMapper;
    @Resource
    private AutoRenewDutTypeManager autoRenewDutTypeManager;
    @Resource
    private UserAgreementService userAgreementService;

    private Long userId = 9527L;
    private Long vipType = 1L;
    private int type = 9;
    private int amount = 1;
    private int vipCategory = 1;

    public static final int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();

    @Test
    public void testSave() {
        DutUserNew toSave = DutUserNew.builder()
                .userId(userId)
                .vipType(vipType)
                .type(type)
                .amount(amount)
                .vipCategory(vipCategory)
                .updateTime(DateHelper.getDateTime())
                .build();
        dutUserRepository.save(toSave);
        Assert.assertNotNull(toSave.getId());
    }

    @Test
    public void testSaveSelective() {
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, vipType);
        DutUserNewVO dutUserNewUpdater = DutUserNewVO.builder()
                .id(dutUserNew.getId())
                .userId(dutUserNew.getUserId())
                .renewCount(13)
                .serialRenewCount(13)
                .interruptFlag(DutUserNew.INTERRUPT_FLAG_NO)
                .updateTime(DateHelper.getCurrentTime())
                .build();
        dutUserRepository.updateSelective(dutUserNewUpdater);
    }

    @Test
    public void testSaveForUpdate() {
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, vipType);

        dutUserNew.setDeadline(DateHelper.getCurrentTime());
        dutUserRepository.save(dutUserNew);
    }

    @Test
    public void testUpdateShardDutUserNew() {
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, vipType);

        DutUserNew toUpdate = DutUserNew.builder()
                .id(dutUserNew.getId())
                .userId(userId)
                .vipType(vipType)
                .type(type)
                .amount(amount)
                .nextDutTime(DateHelper.getCurrentTime())
                .interruptFlag(3)
                .serialRenewCount(123)
                .renewCount(123)
                .updateTime(DateHelper.getDateTime())
                .build();
        dutUserRepository.updateShardDutUserNew(toUpdate);
    }

    @Test
    public void testUpdateByUserIdAndVipType() {
        DutUserNew toUpdate = DutUserNew.builder()
                .userId(userId)
                .vipType(vipType)
                .deadline(DateHelper.getCurrentTime())
                .vipCategory(2)
                .build();
        int rows = dutUserRepository.updateByUserIdAndVipType(toUpdate);
        Assert.assertTrue(rows > 0);
    }

    @Test
    public void testUpdateDutUserDeadline() {
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, vipType);
        Timestamp deadline = DateHelper.getCurrentTime();
        Timestamp originalDeadline = dutUserNew.getDeadline();
        boolean flag = dutUserRepository.updateDutUserDeadline(userId, vipType, type, deadline, originalDeadline);
        Assert.assertTrue(flag);
    }

    @Test
    public void testUpdateMainlandUserDeadline() {
        DutUserNew dutUserNew = DutUserNew.builder()
                .userId(userId)
                .vipType(vipType)
                .vipCategory(1)
                .deadline(DateHelper.getCurrentTime())
                .build();

        int count = dutUserRepository.updateMainlandUserDeadline(dutUserNew);
        Assert.assertTrue(count > 0);
    }

    @Test
    public void testParticipateByUser() {
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, vipType);
        Integer nowPeriods = 1;
        Integer contractPrice = 1980;
        String orderCode = "123";
        IntroductoryPriceAct act = introductoryPriceActMapper.selectByPrimaryKey(1);
        dutUserRepository.participateByUser(dutUserNew, nowPeriods, contractPrice, orderCode, act);
    }

    @Test
    public void testChangeActPeriodsByDutUser() {
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, vipType);
        Integer newPeriods = 12;
        dutUserRepository.changeActPeriodsByDutUser(dutUserNew, newPeriods);
    }

    @Test
    public void testResetActPeriodsByDutUser() {
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, vipType);
        Integer originalPrice = 123;
        dutUserRepository.resetActPeriodsByDutUser(dutUserNew, originalPrice);
    }

    @Test
    public void testExecuteUpdate() {
        Long userId = 123L;
        String updateSql = " renew_count = 1, serial_renew_count = 11, interrupt_flag =0";
        String whereSql = "user_id = 1436825097 and vip_type = 1 and type = 6 and amount = 1";

        dutUserRepository.executeUpdate(userId, updateSql, whereSql);
    }

    @Test
    public void testGetShardDutUserNew() throws Exception {
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, vipType);
        if (dutUserNew != null) {
            Assert.assertEquals(dutUserNew.getUserId(), userId);
            Assert.assertEquals(dutUserNew.getType().intValue(), type);
            Assert.assertEquals(dutUserNew.getVipType(), vipType);
        }
    }

    @Test
    public void testGetShardUpgradeDutUserNew() {
        Long userId = 123L;
        Integer type = 9;
        Long vipType = 1L;
        Long sourceVipType = 4L;
        DutUserNew dutUserNew = dutUserRepository.getShardUpgradeDutUserNew(userId, type, sourceVipType, vipType);
        if (dutUserNew != null) {
            Assert.assertEquals(dutUserNew.getUserId(), userId);
            Assert.assertEquals(dutUserNew.getType(), type);
            Assert.assertEquals(dutUserNew.getVipType(), vipType);
            Assert.assertEquals(dutUserNew.getSourceVipType(), sourceVipType);
        }
    }

    @Test
    public void testGetDutUserNew() {
        Long userId = 123L;
        Integer type = 9;
        List<DutUserNew> dutUserNews = dutUserRepository.getDutUserNew(userId, AgreementTypeEnum.AUTO_RENEW.getValue(), type);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getUserId(), userId);
                Assert.assertEquals(dutUserNew.getType(), type);
            }
        }
    }

    @Test
    public void testGetDutUserNewList() {
        Long userId = 123L;
        Integer vipCategory = 1;
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<DutUserNew> dutUserNews = dutUserRepository.getDutUserNewList(userId, agreementType, vipCategory);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getUserId(), userId);
                Assert.assertEquals(dutUserNew.getVipCategory(), vipCategory);
            }
        }
    }

    @Test
    public void testFindDutUsers() {
        Long userId = 123L;
        Integer autorenew = 1;
        Integer dutType = 9;
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<DutUserNew> dutUserNews = dutUserRepository.findDutUsers(userId, agreementType, autorenew, dutType);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getUserId(), userId);
                Assert.assertEquals(dutUserNew.getAutoRenew(), autorenew);
                Assert.assertEquals(dutUserNew.getType(), dutType);
            }
        }
    }

    @Test
    public void testGetAutoRenewUsersByVipDeadline() {
        Timestamp start = DateHelper.getTimestamp("2016-11-24 18:15:20");
        Timestamp end = DateHelper.getTimestamp("2019-11-24 18:15:20");
        Long vipType = 1L;
        List<Integer> dutTypes = Lists.newArrayList(9);
        Integer autoRenew = 1;
        String shardTblName = "boss_dut_user_new_000";
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<DutUserNew> dutUserNews = dutUserRepository.getAutoRenewUsersByVipDeadline(agreementType, start, end, vipType, dutTypes, autoRenew, shardTblName);

        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getVipType(), vipType);
                Assert.assertEquals(dutUserNew.getAutoRenew(), autoRenew);
                Assert.assertTrue(dutTypes.contains(dutUserNew.getType()));
            }
        }
    }

    @Test
    public void testGetDutUsersByNextDutTime() {
        Timestamp start = DateHelper.getTimestamp("2016-11-24 18:15:20");
        Timestamp end = DateHelper.getTimestamp("2019-11-24 18:15:20");
        Long vipType = 1L;
        List<Integer> dutTypes = Lists.newArrayList(9);
        Integer autoRenew = 1;
        String shardTblName = "boss_dut_user_new_000";
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<DutUserNew> dutUserNews = dutUserRepository.getDutUsersByNextDutTime(agreementType, start, end, vipType, dutTypes, autoRenew, shardTblName);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getVipType(), vipType);
                Assert.assertEquals(dutUserNew.getAutoRenew(), autoRenew);
                Assert.assertTrue(dutTypes.contains(dutUserNew.getType()));
            }
        }
    }

    @Test
    public void testShardGetDutUserNews() {
        Long userId = 123L;
        Integer autoRenew = 1;
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<DutUserNew> dutUserNews = dutUserRepository.shardGetDutUserNews(userId, agreementType, autoRenew);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getUserId(), userId);
                Assert.assertEquals(dutUserNew.getAutoRenew(), autoRenew);
            }
        }
    }

    @Test
    public void testFindDutUserNews() {
        Long userId = 123L;
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<DutUserNew> dutUserNews = dutUserRepository.findDutUserNews(userId, agreementType);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getUserId(), userId);
            }
        }
    }

    @Test
    public void testFindAllByUserIdAndVipType() {
        Long userId = 123L;
        Long vipType = 1L;
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<DutUserNew> dutUserNews = dutUserRepository.findAllByUserIdAndVipType(userId, agreementType, vipType);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getUserId(), userId);
                Assert.assertEquals(dutUserNew.getVipType(), vipType);
            }
        }
    }

    @Test
    public void testFindIgnoreAutoRenewStatus() {
        Long userId = 123L;
        Long sourceVipType = 4L;
        Long targetVipType = 1L;
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<DutUserNew> dutUserNews = dutUserRepository.findIgnoreAutoRenewStatus(userId, agreementType, sourceVipType, targetVipType);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getUserId(), userId);
                Assert.assertEquals(dutUserNew.getSourceVipType(), sourceVipType);
                Assert.assertEquals(dutUserNew.getVipType(), targetVipType);
            }
        }
    }

    @Test
    public void testFindByUidAndVipTypeAndAutoRenew() {
        Long userId = 123L;
        Long vipType = 1L;
        Integer autoRenew = 1;
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<DutUserNew> dutUserNews = dutUserRepository.findByUidAndVipTypeAndAutoRenew(userId, agreementType, vipType, autoRenew);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getUserId(), userId);
                Assert.assertEquals(dutUserNew.getVipType(), vipType);
                Assert.assertEquals(dutUserNew.getAutoRenew(), autoRenew);
            }
        }
    }

    @Test
    public void testGetDutUsers() {
        Long userId = 123L;
        Integer autoRenew = 1;
        Integer dutType = 9;
        Long vipType = 1L;
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<DutUserNew> dutUserNews = dutUserRepository.getDutUsers(userId, agreementType, autoRenew, dutType, vipType);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getUserId(), userId);
                Assert.assertEquals(dutUserNew.getAutoRenew(), autoRenew);
                Assert.assertEquals(dutUserNew.getType(), dutType);
                Assert.assertEquals(dutUserNew.getVipType(), vipType);
            }
        }
    }

    @Test
    public void testGetShardAutoRenewUsers() {
        Long userId = 123L;
        List<Integer> dutTypes = Lists.newArrayList(9, 6);
        Long vipType = 1L;
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<DutUserNew> dutUserNews = dutUserRepository.getShardAutoRenewUsers(agreementType, userId, dutTypes, vipType);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getUserId(), userId);
                Assert.assertTrue(dutTypes.contains(dutUserNew.getType()));
                Assert.assertEquals(dutUserNew.getVipType(), vipType);
            }
        }
    }

    @Test
    public void testGetShardExcludeTypesAutoRenewUsers() {
        Long userId = 123L;
        Long sourceVipType = 4L;
        Long targetVipType = 1L;
        List<Integer> excludeDutTypeList = Lists.newArrayList(9);
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<DutUserNew> dutUserNews = dutUserRepository.getShardExcludeTypesAutoRenewUsers(userId, agreementType, excludeDutTypeList, sourceVipType, targetVipType);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getUserId(), userId);
                Assert.assertTrue(!excludeDutTypeList.contains(dutUserNew.getType()));
                Assert.assertEquals(dutUserNew.getSourceVipType(), sourceVipType);
                Assert.assertEquals(dutUserNew.getVipType(), targetVipType);
            }
        }
    }

    @Test
    public void testGetAutoRenewUsersWithExcludeTypes() {
        Timestamp start = DateHelper.getTimestamp("2016-11-24 18:15:20");
        Timestamp end = DateHelper.getTimestamp("2018-11-24 18:15:20");
        Long sourceVipType = 4L;
        Long vipType = 1L;
        Integer autoRenewStatus = 1;
        List<Integer> excludeTypeList = Lists.newArrayList(6, 9);
//        String shardTblName = "boss_dut_user_new_00";
        String shardTblName = "boss_dut_user_new_128";
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<DutUserNew> dutUserNews = dutUserRepository.getAutoRenewUsersWithExcludeTypes(agreementType, start, end, sourceVipType, vipType, autoRenewStatus, excludeTypeList, shardTblName);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getSourceVipType(), sourceVipType);
                Assert.assertEquals(dutUserNew.getVipType(), vipType);
                Assert.assertEquals(dutUserNew.getAutoRenew(), autoRenewStatus);
                Assert.assertTrue(!excludeTypeList.contains(dutUserNew.getType()));
            }
        }
    }

    @Test
    public void testGetShardAsyncAutoRenewUsers() {
        Timestamp start = DateHelper.getTimestamp("2016-11-24 18:15:20");
        Timestamp end = DateHelper.getTimestamp("2018-11-24 18:15:20");
        Long vipType = 1L;
        Integer autoRenewStatus = 1;
        List<Integer> includeTypes = Lists.newArrayList( 9);
        List<Integer> excludeTypeList = Lists.newArrayList( 6);
        String shardTblName = "boss_dut_user_new_228";
        List<DutUserNew> dutUserNews = dutUserRepository.getShardAsyncAutoRenewUsers(start, end, vipType, autoRenewStatus, includeTypes, excludeTypeList, shardTblName, agreementType);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getVipType(), vipType);
                Assert.assertEquals(dutUserNew.getAutoRenew(), autoRenewStatus);
                Assert.assertTrue(includeTypes.contains(dutUserNew.getType()));
                Assert.assertTrue(!excludeTypeList.contains(dutUserNew.getType()));
            }
        }
    }

    @Test
    public void testMobileDutQueryUser() {
        String nextDate = DateHelper.getFormatDate(DateHelper.caculateTime(DateHelper.getDateTime(),1,Constants.PRODUCT_PERIODUNIT_DAY),"yyyy-MM-dd");
        String begintimeStr = nextDate+" 00:00:00";
        Timestamp begintime = DateHelper.getDateTime(DateHelper.getDateFromStr(begintimeStr, "yyyy-MM-dd HH:mm:ss"));
        //明天00:00+4天（数据库配的是提前四天）
        Timestamp deadline = DateHelper.caculateTime(begintime, 4, Constants.PRODUCT_PERIODUNIT_DAY);
        //结束日期减去一天
        Timestamp startDate = DateHelper.caculateTime(deadline, -1, Constants.PRODUCT_PERIODUNIT_DAY);

        Long vipType = 1L;
        Integer autoRenewStatus = 1;
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<Integer> mobileDutTypeList = autoRenewDutTypeManager.getDutTypeListByVipTypeAndPayChannel(Constants.VIP_USER_SUPER, agreementType, PaymentDutType.PAY_CHANNEL_MOBILE);
        List<Integer> excludeTypeList = Lists.newArrayList(7);
        String shardTblName = "boss_dut_user_new_128";

        List<DutUserNew> dutUserNews = dutUserRepository.getShardAsyncAutoRenewUsers(startDate, deadline, vipType, autoRenewStatus, mobileDutTypeList, excludeTypeList, shardTblName, agreementType);
        if (CollectionUtils.isNotEmpty(dutUserNews)) {
            for (DutUserNew dutUserNew : dutUserNews) {
                Assert.assertEquals(dutUserNew.getVipType(), vipType);
                Assert.assertEquals(dutUserNew.getAutoRenew(), autoRenewStatus);
                Assert.assertTrue(mobileDutTypeList.contains(dutUserNew.getType()));
                Assert.assertTrue(!excludeTypeList.contains(dutUserNew.getType()));
            }
        }
    }
}
