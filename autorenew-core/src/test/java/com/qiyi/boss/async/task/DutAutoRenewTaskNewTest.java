package com.qiyi.boss.async.task;

import com.qiyi.boss.service.impl.FacadeManager;
import com.qiyi.boss.service.impl.UserManager;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml", "classpath:applicationContext-mybatis.xml", "classpath:applicationContext-orderDal.xml"})
@WebAppConfiguration
public class DutAutoRenewTaskNewTest {

    static {
        System.setProperty("spring.profiles.active", "test");
    }

    @Resource
    FacadeManager facadeManager;
    @Resource
    UserManager userManager;


    @Test
    public void testDeserialize() {
        DutAutoRenewTaskNew dutAutoRenewTaskNew = new DutAutoRenewTaskNew();
        dutAutoRenewTaskNew.deserialize("product=4&user=1480001191&dutBindType=9&taskType=normal&notifyCount=0&executedTypes=9&autoRenewLinkedDutConfig=2&autorenewDutConfig=1&fc=");
    }

    @Test
    public void execute() {

        DutAutoRenewTaskNew dutAutoRenewTaskNew = new DutAutoRenewTaskNew();
        dutAutoRenewTaskNew.setFacadeManager(facadeManager);
        dutAutoRenewTaskNew.setUserManager(userManager);
//        dutAutoRenewTaskNew.deserialize("product=4&user=1480402900&dutBindType=342&taskType=retry_8&notifyCount=0&executedTypes=342&autoRenewLinkedDutConfig=794&autorenewDutConfig=7&fc=");
        dutAutoRenewTaskNew.deserialize("user=1480437965&dutBindType=1008&taskType=normal&notifyCount=0&executedTypes=1008&autoRenewLinkedDutConfig=1&autorenewDutConfig=7&fc=&agreementNo=2100801&productCode=a0226bd958843452");
        dutAutoRenewTaskNew.execute();
    }

    @Test
    public void execute2() {

        DutAutoRenewTaskNew dutAutoRenewTaskNew = new DutAutoRenewTaskNew();
        dutAutoRenewTaskNew.setFacadeManager(facadeManager);
        dutAutoRenewTaskNew.setUserManager(userManager);
//        dutAutoRenewTaskNew.deserialize("product=4&user=1480402900&dutBindType=342&taskType=retry_8&notifyCount=0&executedTypes=342&autoRenewLinkedDutConfig=794&autorenewDutConfig=7&fc=");
        String data = "user=1468911788&dutBindType=1368&taskType=retry_29&notifyCount=0&executedTypes=1368&autoRenewLinkedDutConfig=3368&autorenewDutConfig=61&agreementType=8&fc=&agreementNo=2136801";
        dutAutoRenewTaskNew.deserialize(data);
        dutAutoRenewTaskNew.execute();
    }

    @Test
    public void testDutWhiteList() {

        AbstractTask abstractTask = new AbstractTask() {
            @Override
            protected void execute() {

            }

            @Override
            public void deserialize(String data) throws IllegalArgumentException {

            }

            @Override
            public String serialize() {
                return null;
            }
        };

//        Assert.assertTrue(abstractTask.isNotInWhiteList(123456789L));
        Assert.assertTrue(abstractTask.isNotInWhiteList(1111111111L));

    }
}
