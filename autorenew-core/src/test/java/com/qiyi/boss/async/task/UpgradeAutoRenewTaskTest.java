package com.qiyi.boss.async.task;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * created 2019/4/12 - 15:47
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class UpgradeAutoRenewTaskTest {

    @Test
    public void testUpgradeAutoRenewTask() {
        UpgradeAutoRenewTask upgradeAutoRenewTask = new UpgradeAutoRenewTask();
        upgradeAutoRenewTask.deserialize("dutBindType=92&taskType=normal&notifyCount=0&executedTypes=92&autoRenewLinkedDutConfig=96&autorenewDutConfig=9&fc=&user=1174259614");
        upgradeAutoRenewTask.execute();
    }
}
