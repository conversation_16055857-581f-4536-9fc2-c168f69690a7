package com.qiyi.boss.async.task;

import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.vip.trade.autorenew.repository.DutUserRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * created 2019/4/10 - 14:00
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class DutAutoRenewTaskTWTest {

    @Resource
    private DutUserRepository dutUserRepository;

    @Test
    public void testDutAutoRenewTaskTw() {
        dutUserRepository.findDutUserNews(1L, AgreementTypeEnum.AUTO_RENEW.getValue());

        DutAutoRenewTaskTW dutAutoRenewTaskTW = new DutAutoRenewTaskTW();

        dutAutoRenewTaskTW.deserialize("product=af7de4c61c0a1805&user=1480386603&dutBindType=21&configId=13&notifyCount=0");
        dutAutoRenewTaskTW.execute();
        dutAutoRenewTaskTW.deserialize("product=af7de4c61c0a1805&user=1480386603&dutBindType=21&configId=14&notifyCount=0");
        dutAutoRenewTaskTW.execute();
        dutAutoRenewTaskTW.deserialize("product=af7de4c61c0a1805&user=1480386603&dutBindType=21&configId=15&notifyCount=0");
        dutAutoRenewTaskTW.execute();
    }
}
