package com.qiyi.boss.async.task;

import com.qiyi.vip.trade.autorenew.repository.DutUserRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * created 2019/7/29 - 11:00
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class MobileDutAutoRenewTaskTest {

    @Resource
    private DutUserRepository dutUserRepository;

    @Test
    public void testMobileDutAutoRenewTask() {
//        dutUserRepository.findDutUserNews(1L);

        MobileDutAutoRenewTask mobileDutAutoRenewTask = new MobileDutAutoRenewTask();


        mobileDutAutoRenewTask.deserialize("product=4&user=1927267&dutBindType=8&taskType=normal&notifyCount=0&fc=");
        mobileDutAutoRenewTask.execute();
    }
}
