package com.qiyi.boss.async.task;

import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * created 2019/3/26 - 18:57
 */
public class AutoRenewConfirmTaskTest {

    @Test
    public void testCodec() {

        long userId = 1201189236L;
        String orderCode = "201609081712080100001";
        String productTypeStr = "1";

        AutoRenewConfirmTask autoRenewConfirmTask = new AutoRenewConfirmTask(userId, orderCode, false, null);
        String data = autoRenewConfirmTask.serialize();
        System.out.println(data);

        autoRenewConfirmTask = new AutoRenewConfirmTask();
        autoRenewConfirmTask.deserialize(data);

        Assert.assertEquals(userId, (long) autoRenewConfirmTask.getUserId());
        Assert.assertEquals(orderCode, autoRenewConfirmTask.getOrderCode());
    }

    @Test
    public void testOldCodec() {
        String orderCode = "201609081712080100001";
        String productTypeStr = "1";

        String data = "orderCode=201609081712080100001&productTypeStr=1";
        AutoRenewConfirmTask autoRenewConfirmTask = new AutoRenewConfirmTask();
        autoRenewConfirmTask.deserialize(data);

        Assert.assertEquals(orderCode, autoRenewConfirmTask.getOrderCode());
    }
}
