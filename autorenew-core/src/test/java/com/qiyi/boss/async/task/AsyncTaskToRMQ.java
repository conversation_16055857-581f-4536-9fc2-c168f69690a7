package com.qiyi.boss.async.task;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import com.qiyi.boss.Constants;
import com.qiyi.vip.trade.autorenew.domain.AsyncTask;
import com.qiyi.vip.trade.autorenew.mapper.AsyncTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.qiyi.vip.trade.autorenew.domain.AsyncTask.TASK_IN_QUEUE_NO;

/**
 * <AUTHOR>
 * created 2019/9/5 - 09:18
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class AsyncTaskToRMQ {

    private DefaultMQProducer dutDelayTaskMsgProducer;

    private static String asyncTaskRmqServerAddress = "hb-az-2.viptrade-autorenew-asynctask.online001.rocketmq.qiyi.middle:9876;hb-az-4.viptrade-autorenew-asynctask.online002.rocketmq.qiyi.middle:9876";
    private static final String async_task_topic = "viptrade_autorenew_asynctask";
    private static final String asyncTaskProducerGroup = "PG-viptrade_autorenew_asynctask";
    private static String asyncTaskToken = "PT-2564a902-eea5-41c0-8149-15c403d670b9";

    private static final String asyncTaskConsumerGroup = "CG-viptrade_autorenew_asynctask";
    private static String asyncTaskConsumerToken = "CT-ebf44beb-7498-45d1-8db0-b6751972a5bf";

    // test info
//    private static String asyncTaskRmqServerAddress = "vip-autorenew-rocketmq-dev009-bdwg.qiyi.virtual:9876;vip-autorenew-rocketmq-dev011-bdwg.qiyi.virtual:9876";
//    private static final String async_task_topic = "viptrade_autorenew_asynctask";
//    private static final String asyncTaskProducerGroup = "PG-viptrade_autorenew_asynctask";
//    private static String asyncTaskToken = "PT-6761cc42-731f-4a45-b14e-afc9b02e1060";
//
//    private static final String asyncTaskConsumerGroup = "CG-viptrade_autorenew_asynctask";
//    private static String asyncTaskConsumerToken = "CT-5a5d1a7a-34b5-46ac-ac44-0842f2d90234";

//     press info
//    private static String asyncTaskRmqServerAddress = "vip-autorenew-rocketmq-dev009-bdwg.qiyi.virtual:9876;vip-autorenew-rocketmq-dev011-bdwg.qiyi.virtual:9876";
//    private static final String async_task_topic = "viptrade_autorenew_delaymsg_press";
//    private static final String asyncTaskProducerGroup = "PG-viptrade_autorenew_delaymsg_press";
//    private static String asyncTaskToken = "PT-8dca309d-aab6-464f-9285-5ef70489f9cd";
//    private static final String asyncTaskConsumerGroup = "CG-viptrade_autorenew_delaymsg_press";
//    private static String asyncTaskConsumerToken = "CT-493e03ab-11c9-4cd7-93f3-c23317864d17";

    private static final int BATCH_SIZE = 30000;

    @Resource
    private AsyncTaskMapper asyncTaskMapper;

    @Before
    public void setup() throws Exception {
            dutDelayTaskMsgProducer = new DefaultMQProducer(asyncTaskProducerGroup);
            dutDelayTaskMsgProducer.setToken(asyncTaskToken);
            dutDelayTaskMsgProducer.setNamesrvAddr(asyncTaskRmqServerAddress);
            dutDelayTaskMsgProducer.start();
    }

    @Test
    public void testSendDelayMsg() throws Exception {
        List<String> sendFailTaskIds = Files.readAllLines(Paths.get("D:\\data\\sendFailTaskId.txt"));
        for (String taskId : sendFailTaskIds) {
            AsyncTask asyncTask = asyncTaskMapper.selectByPrimaryKey(Long.parseLong(taskId));
            sendAsyncTaskToRMQDirect(asyncTask);
        }
    }

    private void sendAsyncTaskToRMQDirect(AsyncTask asyncTask) {
        if (asyncTask == null) {
            return;
        }
        asyncTask.setTimerRunAt(new Timestamp(System.currentTimeMillis()));
        int delayTimeInMillis = (int)(asyncTask.getTimerRunAt().getTime() - System.currentTimeMillis());
        if (delayTimeInMillis < 0) {
            delayTimeInMillis = 0;
        }
        int delaySeconds = Math.max(delayTimeInMillis / 1000, 5);
        Message msg = new Message(async_task_topic, JSON.toJSONString(asyncTask).getBytes());
        msg.setDelayTimeInSeconds(delaySeconds);
        msg.setKeys(String.valueOf(asyncTask.getId()));
        msg.setTags(Constants.RMQ_NOMAL_TAG);
        try {
            System.out.println(this.dutDelayTaskMsgProducer.send(msg, 3000).getMsgId());
        } catch (Exception e) {
            System.out.println(Thread.currentThread().getName() + " taskId = " + asyncTask.getId());
        }
    }

    /**
     * 1. 关闭异步待执行任务查询功能(异步任务不再执行)
     * 2. 通过下面的方法来将异步任务写入rmq延迟队列中
     * 3. 低峰期进行，例如18点以后。 表里大概有30多万数据
     */
    @Test
    public void queryAsyncTaskConvertToRMQ() throws Exception {
        AsyncTask query = AsyncTask.builder().inQueue(TASK_IN_QUEUE_NO).build();
        List<AsyncTask> asyncTasks = asyncTaskMapper.listPageSelective(query, 0, Integer.MAX_VALUE);
        if (CollectionUtils.isEmpty(asyncTasks)) {
            return;
        }

//        List<AsyncTask> asyncTasks = Lists.newArrayList();
//        for (int i = 1; i <= 345322; i++) {
//            asyncTasks.add(AsyncTask.builder().id((long)i).inQueue(0).timerRunAt(new Timestamp(System.currentTimeMillis())).build());
//        }

        System.out.println("total msg:" + asyncTasks.size());
        // 获取满足条件的最后一条记录，此后的记录都由rmq延迟消息表示
        // 打开 binlog -> rmq 延迟消息开关后，第一条延迟消息的id 和 本次查询输出的id直接的差值表示 没有被执行的任务，需要再次写入rmq。
        System.out.println(asyncTasks.get(asyncTasks.size() - 1).getId());

        int threadCnt = asyncTasks.size() / BATCH_SIZE;
        if (threadCnt * BATCH_SIZE < asyncTasks.size()) {
            threadCnt += 1;
        }
        System.out.println("Total Thread Count is " + threadCnt);

        CountDownLatch countDownLatch = new CountDownLatch(threadCnt);

        ExecutorService threadPool = Executors.newFixedThreadPool(threadCnt);

        for (int i = 0; i < threadCnt; i++) {
            int start = i * BATCH_SIZE;
            int end = Math.min(i * BATCH_SIZE + BATCH_SIZE, asyncTasks.size());
            System.out.println("[" + start + " , " + end + ")");
            List<AsyncTask> subAsyncTasks = asyncTasks.subList(start, end);
            DelayMsgSendWorker sendWorker = new DelayMsgSendWorker(dutDelayTaskMsgProducer, subAsyncTasks, countDownLatch);
            threadPool.submit(sendWorker);
        }
        countDownLatch.await();
        System.out.println("All Worker Done!");
    }

    class DelayMsgSendWorker implements Runnable {
        private DefaultMQProducer dutDelayTaskMsgProducer;
        private List<AsyncTask> asyncTaskList;
        private CountDownLatch countDownLatch;

        public DelayMsgSendWorker(DefaultMQProducer dutDelayTaskMsgProducer, List<AsyncTask> asyncTaskList, CountDownLatch countDownLatch) {
            this.dutDelayTaskMsgProducer = dutDelayTaskMsgProducer;
            this.asyncTaskList = asyncTaskList;
            this.countDownLatch = countDownLatch;

        }

        @Override
        public void run() {
            BufferedWriter writer = null;
            try {
                File file = new File("D:\\data\\async_task\\" + Thread.currentThread().getName() + ".txt");
                if (!file.exists()) {
                    file.createNewFile();
                }
                writer = new BufferedWriter(new FileWriter(file));
                for (AsyncTask asyncTask : asyncTaskList) {
//                    System.out.println(asyncTask.getId());
//                    break;
                    int delayTimeInMillis = (int)(asyncTask.getTimerRunAt().getTime() - System.currentTimeMillis());
                    if (delayTimeInMillis < 0) {
                        delayTimeInMillis = 0;
                    }
                    int delaySeconds = Math.max(delayTimeInMillis / 1000, 5);
                    Message msg = new Message(async_task_topic, JSON.toJSONString(asyncTask).getBytes());
                    msg.setDelayTimeInSeconds(delaySeconds);
                    msg.setTags(Constants.RMQ_NOMAL_TAG);
                    msg.setKeys(String.valueOf(asyncTask.getId()));
                    try {
                        SendResult sendResult = this.dutDelayTaskMsgProducer.send(msg, 5000);
                        writer.write(sendResult.getMsgId() + ":" + asyncTask.getId() + "\n");
                    } catch (Exception e) {
                        System.out.println(Thread.currentThread().getName() + " taskId = " + asyncTask.getId());
                        break;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                countDownLatch.countDown();
                IOUtils.closeQuietly(writer);
            }
        }
    }

    /**
     * 测试rmq延迟消息处理能力
     * 注意： 延迟消息不支持批量发送
     * 1. 延迟消息总数 300w
     * 2. 延迟时间秒级别
     * 3. 消息分布在6个小时内，每个小时内大概50w
     */
    @Test
    public void pressRmqDelayMsg() throws Exception {
        DefaultMQProducer delayMsgProducer = new DefaultMQProducer("PG-viptrade_autorenew_delaymsg_press");
        delayMsgProducer.setToken("PT-8dca309d-aab6-464f-9285-5ef70489f9cd");
        delayMsgProducer.setNamesrvAddr(asyncTaskRmqServerAddress);
        delayMsgProducer.start();

        Date baseTime = DateUtils.parseDate("2019-09-06 13:45:00", "yyyy-MM-dd HH:mm:ss");

        int hours = 1;
        int totalMsg = 300;
        int perHourCnt = totalMsg / hours;

        long start = System.currentTimeMillis();
        int totalSend = 0;

        Map<String, Integer> cntPerSeconds = new TreeMap<>();
        Map<Integer, Integer> delaySecondsCnt = new TreeMap<>();

        List<Message> delayMsgs = Lists.newLinkedList();
        for (int i = 0; i < totalMsg; i++) {
            int hourIdx = i / perHourCnt;

            Date executeDate = DateUtils.addHours(baseTime, hourIdx);

            Random random = new Random();
            int seconds = random.nextInt(3600);
            Date exactExecuteDate = DateUtils.addSeconds(executeDate, seconds);

//            String executeDateStr = DateFormatUtils.format(executeDate, "yyyy-MM-dd HH:mm:ss");
//            if (cntPerSeconds.get(executeDateStr) == null) {
//                cntPerSeconds.put(executeDateStr, 1);
//            } else {
//                cntPerSeconds.put(executeDateStr, cntPerSeconds.get(executeDateStr) + 1);
//            }


            int delayTimeInMillis = (int)(exactExecuteDate.getTime() - System.currentTimeMillis());
            if (delayTimeInMillis < 0) {
                delayTimeInMillis = 0;
            }
            int delaySeconds = delayTimeInMillis / 1000;

            Message msg = new Message("viptrade_autorenew_delaymsg_press", ("delay-msg-" + i).getBytes());
            msg.setKeys("delay-msg-" + i);
            msg.setDelayTimeInSeconds(delaySeconds);

            delayMsgs.add(msg);
            totalSend++;

            if (delaySecondsCnt.get(delaySeconds) == null) {
                delaySecondsCnt.put(delaySeconds, 1);
            } else {
                delaySecondsCnt.put(delaySeconds, delaySecondsCnt.get(delaySeconds) + 1);
            }

            if (delayMsgs.size() == 1000) {
                SendResult sendResult = delayMsgProducer.send(delayMsgs, 1000 * 60);
                System.out.println(sendResult);
                delayMsgs.clear();
            }
        }

        if (delayMsgs.size() > 0) {
            SendResult sendResult = delayMsgProducer.send(delayMsgs, 1000 * 60);
            System.out.println(sendResult);
        }
        System.out.println(String.format("send %d delay msg cost %dms", totalMsg, (System.currentTimeMillis() - start)));

        for (Map.Entry<Integer, Integer> entry : delaySecondsCnt.entrySet()) {
            System.out.println(entry.getKey() + " : " + entry.getValue());
        }

//        for (Map.Entry<String, Integer> entry : cntPerSeconds.entrySet()) {
//            System.out.println(entry.getKey() + " : " + entry.getValue());
//        }
    }

    @Test
    public void pressRmqDelayMsgConsume() throws Exception {
        File file = new File("D:\\data\\async_task\\delay_msg_backup.txt");
        if (!file.exists()) {
            file.createNewFile();
        }
        BufferedWriter writer = new BufferedWriter(new FileWriter(file, true));

        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(asyncTaskConsumerGroup);
        consumer.setToken(asyncTaskConsumerToken);
        consumer.setNamesrvAddr(asyncTaskRmqServerAddress);
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET);
        // Subscribe one more more topics to consume.
        consumer.subscribe(async_task_topic, "*");
        consumer.setPullBatchSize(300);
        consumer.setConsumeMessageBatchMaxSize(30);
        consumer.setConsumeThreadMin(20);
        consumer.setConsumeThreadMax(100);
        consumer.registerMessageListener(new MessageListenerConcurrently() {
            @Override
            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
                for (MessageExt messageExt : msgs) {
                    try {
                        writer.write(new String(messageExt.getBody(), Charsets.UTF_8) + "\n");
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                try {
                    writer.flush();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        });
        consumer.start();
        Thread.sleep(1000 * 6000);
    }
}
