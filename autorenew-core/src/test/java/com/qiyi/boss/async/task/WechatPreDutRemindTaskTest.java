package com.qiyi.boss.async.task;

import com.qiyi.boss.dto.WechatPreDutRemindReqDto;
import org.junit.Test;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2020-6-25
 * Time: 15:21
 */
public class WechatPreDutRemindTaskTest {

	@Test
	public void testC() {

		WechatPreDutRemindReqDto wechatPreDutRemindReqDto = WechatPreDutRemindReqDto.builder()
				.userId(111111111L)
				.dutType(1)
				.amount(1500)
				.partnerOrderNo("20200625134500001")
				.partner("qiyue")
				.notifyUrl("http://serv.vip.qiyi.domain/autorenew/notify/prepay")
//				.extraCommonParam("a=b&c=d")
				.sign("jdfgfjesglkksdfldngsjf")
				.build();

		WechatPreDutRemindTask wechatPreDutRemindTask = new WechatPreDutRemindTask(wechatPreDutRemindReqDto);
		String data = wechatPreDutRemindTask.serialize();
		System.out.println(data);

		wechatPreDutRemindTask = new WechatPreDutRemindTask();
		wechatPreDutRemindTask.deserialize(data);
		System.out.println(wechatPreDutRemindTask);

	}
}
