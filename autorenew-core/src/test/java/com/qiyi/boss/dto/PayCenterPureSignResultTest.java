package com.qiyi.boss.dto;

import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.Test;

import com.qiyi.boss.model.BaseResponse;
import com.qiyi.boss.utils.JacksonUtils;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2021/7/8 15:57
 */
public class PayCenterPureSignResultTest {

    @Test
    public void parseFromJson() {
        String json = "{\"code\":\"A00000\",\"data\":{\"contract_no\":\"1021070855233001045\",\"uid\":\"1480386605\",\"platform_code\":\"97ae2982356f69d8\",\"partner\":\"qiyue\",\"dut_type\":\"938\",\"create_time\":\"20210708032033\",\"pay_type\":\"ZHIMAGOBIND\",\"status\":\"2\",\"business_data\":{\"redirect_url\":\"alipays://platformapi/startapp?appId=2019062465587961&page=pages/hz-enjoy/pass/index?extraData=%257B%2522signParams%2522%253A%2522alipay_sdk%25253Dalipay-sdk-java-dynamicVersionNo%252526app_id%25253D2021002150626207%252526biz_content%25253D%2525257B%25252522partner_id%25252522%2525253A%252525222088701709811972%25252522%2525252C%25252522template_id%25252522%2525253A%252525222021061600020903970009299070%25252522%2525252C%25252522out_request_no%25252522%2525253A%252525221021070858433001045%25252522%2525257D%252526charset%25253DUTF-8%252526format%25253Djson%252526method%25253Dzhima.credit.pe.zmgo.sign.apply%252526return_url%25253Dhttp%2525253A%2525252F%2525252Fpay-test.iqiyi.com%2525252Fpay-channel-alipay%2525252Fzhimago%2525252Fpay%2525252Freturn%2525253Fout_trade_no%2525253D1021070858433001045%252526sign%25253DccIMonVcLgV%2525252Fq91GXlQvBP%2525252FslwjEmZbzWxP1kJDuysJF1NJugEdCKYKi3L9edeB3A1R7FXVa7nyltPtXXTnZSZ4bPToINTqAn3Yu14lxxhVbzcZLBPuiYnJND%2525252BzlIhXOh9NO2vgxRLZfPXO6AhOIQpVKsuZqwcTN%2525252B1YbyHiAUPy2sFKGt8CXRNvT1NNkpUYUE4octI%2525252Frc60Sh0%2525252FtnEg8NruRIxmPEHWOZLnfH6RYgPKGQNHjxy37IoZ5Gtf9GxegsffoJDse%2525252F4j2C7e75X0WWeqNfuQfWQonYTajrV0iquygxTBJ290dNdfWBKLoP87dENd23ybvpi2tNmM6Tvku1A%2525253D%2525253D%252526sign_type%25253DRSA2%252526timestamp%25253D2021-07-08%25252B15%2525253A20%2525253A33%252526version%25253D1.0%2522%252C%2522failedUrl%2522%253A%2522http%25253A%25252F%25252Fpay-test.iqiyi.com%25252Fpay-channel-alipay%25252Fzhimago%25252Fpay%25252Ffail%25253Fout_trade_no%25253D1021070858433001045%2522%257D\"}},\"message\":\"成功\"}";
        BaseResponse<PayCenterPureSignResult> response = JacksonUtils.parseObject(json, new TypeReference<BaseResponse<PayCenterPureSignResult>>() {});
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        System.out.println(response.getData());
    }
}