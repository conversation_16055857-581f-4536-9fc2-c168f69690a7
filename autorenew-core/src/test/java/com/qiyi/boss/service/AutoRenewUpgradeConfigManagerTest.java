package com.qiyi.boss.service;

import com.qiyi.boss.processor.DutProcessor;
import com.qiyi.boss.service.impl.AutoRenewUpgradeConfigManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by IntelliJ IDEA.
 * Author: liuwanqiang
 * Date: 2017-06-08
 * Time: 17:29
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class AutoRenewUpgradeConfigManagerTest {

	/** 黄金升级奇异果代扣（天）*/
	private static final String PRODUCT_GOLD_UPGRADE_TO_TV_DUT_DAY = "b2d12e5af4067e97";
	/** 黄金升级奇异果代扣（月）*/
	private static final String PRODUCT_GOLD_UPGRADE_TO_TV_DUT_MONTH = "9bbe4230d718232e";
	/** 黄金升级奇异果代扣-升级完成后*/
	private static final String PRODUCT_GOLD_UPGRADE_TO_TV_DUT = "8b4a36959eecb953";

	@Autowired
	private AutoRenewUpgradeConfigManager autoRenewUpgradeConfigManager;
	@Autowired
	private DutProcessor dutProcessor;

	@Before
	public void before() {
		assert autoRenewUpgradeConfigManager != null;
	}

	@Test
	public void testGetByTargetVipTypeAndPid() {
		System.out.println(autoRenewUpgradeConfigManager.getByTargetVipTypeAndPid(5L, PRODUCT_GOLD_UPGRADE_TO_TV_DUT));
		System.out.println(autoRenewUpgradeConfigManager.getByTargetVipTypeAndPid(5L, PRODUCT_GOLD_UPGRADE_TO_TV_DUT_DAY));
		System.out.println(autoRenewUpgradeConfigManager.getByTargetVipTypeAndPid(5L, PRODUCT_GOLD_UPGRADE_TO_TV_DUT_MONTH));

		System.out.println(autoRenewUpgradeConfigManager.getByTargetVipTypeAndPid(4L, PRODUCT_GOLD_UPGRADE_TO_TV_DUT));
		System.out.println(autoRenewUpgradeConfigManager.getByTargetVipTypeAndPid(4L, PRODUCT_GOLD_UPGRADE_TO_TV_DUT_DAY));
		System.out.println(autoRenewUpgradeConfigManager.getByTargetVipTypeAndPid(4L, PRODUCT_GOLD_UPGRADE_TO_TV_DUT_MONTH));

		assert autoRenewUpgradeConfigManager.getByTargetVipTypeAndPid(5L, PRODUCT_GOLD_UPGRADE_TO_TV_DUT) != null;
		assert autoRenewUpgradeConfigManager.getByTargetVipTypeAndPid(5L, PRODUCT_GOLD_UPGRADE_TO_TV_DUT_DAY) != null;
		assert autoRenewUpgradeConfigManager.getByTargetVipTypeAndPid(5L, PRODUCT_GOLD_UPGRADE_TO_TV_DUT_MONTH) != null;

	}
}
