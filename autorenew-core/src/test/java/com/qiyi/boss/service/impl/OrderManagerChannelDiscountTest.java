package com.qiyi.boss.service.impl;

import com.google.common.collect.Maps;
import com.qiyi.boss.dto.DutParamsDto;
import com.qiyi.boss.dto.OrderPrepareDto;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.iqiyi.vip.order.dal.model.Order;
import com.iqiyi.vip.order.dal.model.OrderReferDto;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.*;

/**
 * OrderManager 渠道优惠核销功能测试类
 * 
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class OrderManagerChannelDiscountTest {

    @InjectMocks
    private OrderManager orderManager;

    @Before
    public void setUp() {
        // 现在配置逻辑已经移到CloudConfigUtil中，不需要在这里设置
    }

    @Test
    public void testAddChannelDiscountTokenIfNeeded_Success() {
        // 准备测试数据
        OrderReferDto orderReferDto = new OrderReferDto();
        DutParamsDto dutParamsDto = createTestDutParamsDto(1); // 支付宝
        AgreementTypeEnum agreementType = AgreementTypeEnum.AUTO_RENEW;

        // 执行测试
        ReflectionTestUtils.invokeMethod(orderManager, "addChannelDiscountTokenIfNeeded", 
            orderReferDto, dutParamsDto, agreementType);

        // 验证结果
        Map<String, Object> businessProperty = orderReferDto.getBusinessProperty();
        assertNotNull("业务属性不应为null", businessProperty);
        assertTrue("应包含渠道优惠核销token", businessProperty.containsKey("channel_discount_token"));
        assertEquals("token值应正确", "alipay_discount_token_123", businessProperty.get("channel_discount_token"));
    }

    @Test
    public void testAddChannelDiscountTokenIfNeeded_WechatChannel() {
        // 准备测试数据
        OrderReferDto orderReferDto = new OrderReferDto();
        DutParamsDto dutParamsDto = createTestDutParamsDto(2); // 微信
        AgreementTypeEnum agreementType = AgreementTypeEnum.AUTO_RENEW;

        // 执行测试
        ReflectionTestUtils.invokeMethod(orderManager, "addChannelDiscountTokenIfNeeded", 
            orderReferDto, dutParamsDto, agreementType);

        // 验证结果
        Map<String, Object> businessProperty = orderReferDto.getBusinessProperty();
        assertNotNull("业务属性不应为null", businessProperty);
        assertEquals("微信token值应正确", "wechat_discount_token_456", businessProperty.get("channel_discount_token"));
    }

    @Test
    public void testAddChannelDiscountTokenIfNeeded_NoConfigFound() {
        // 准备测试数据 - 使用未配置的渠道
        OrderReferDto orderReferDto = new OrderReferDto();
        DutParamsDto dutParamsDto = createTestDutParamsDto(3); // 未配置的渠道
        AgreementTypeEnum agreementType = AgreementTypeEnum.AUTO_RENEW;

        // 执行测试
        ReflectionTestUtils.invokeMethod(orderManager, "addChannelDiscountTokenIfNeeded", 
            orderReferDto, dutParamsDto, agreementType);

        // 验证结果
        Map<String, Object> businessProperty = orderReferDto.getBusinessProperty();
        // 应该没有添加token，因为未找到配置
        assertTrue("未找到配置时不应添加token", businessProperty == null || !businessProperty.containsKey("channel_discount_token"));
    }

    @Test
    public void testAddChannelDiscountTokenIfNeeded_FeatureDisabled() {
        // 禁用功能开关
        ReflectionTestUtils.setField(orderManager, "enableChannelDiscountToken", false);

        // 准备测试数据
        OrderReferDto orderReferDto = new OrderReferDto();
        DutParamsDto dutParamsDto = createTestDutParamsDto(1); // 支付宝
        AgreementTypeEnum agreementType = AgreementTypeEnum.AUTO_RENEW;

        // 执行测试
        ReflectionTestUtils.invokeMethod(orderManager, "addChannelDiscountTokenIfNeeded", 
            orderReferDto, dutParamsDto, agreementType);

        // 验证结果
        Map<String, Object> businessProperty = orderReferDto.getBusinessProperty();
        // 功能禁用时不应添加token
        assertTrue("功能禁用时不应添加token", businessProperty == null || !businessProperty.containsKey("channel_discount_token"));
    }

    @Test
    public void testSetSubParamActivityCode_Success() {
        // 准备测试数据
        Map<String, String> configs = Maps.newHashMap();
        configs.put("extend_params", "existing_param=value");
        
        DutParamsDto dutParamsDto = createTestDutParamsDto(1);
        Order order = createTestOrderWithToken("test_token_123");

        // 执行测试
        ReflectionTestUtils.invokeMethod(orderManager, "setSubParamActivityCode", configs, dutParamsDto, order);

        // 验证结果
        String extendParams = configs.get("extend_params");
        assertNotNull("extend_params不应为null", extendParams);
        assertTrue("应包含原有参数", extendParams.contains("existing_param=value"));
        assertTrue("应包含subParam_activity_code", extendParams.contains("subParam_activity_code=test_token_123"));
    }

    @Test
    public void testSetSubParamActivityCode_EmptyExtendParams() {
        // 准备测试数据 - 空的extend_params
        Map<String, String> configs = Maps.newHashMap();
        
        DutParamsDto dutParamsDto = createTestDutParamsDto(1);
        Order order = createTestOrderWithToken("test_token_456");

        // 执行测试
        ReflectionTestUtils.invokeMethod(orderManager, "setSubParamActivityCode", configs, dutParamsDto, order);

        // 验证结果
        String extendParams = configs.get("extend_params");
        assertEquals("应只包含subParam_activity_code", "subParam_activity_code=test_token_456", extendParams);
    }

    @Test
    public void testExtractChannelDiscountTokenFromOrder_Success() {
        // 准备测试数据
        Order order = createTestOrderWithToken("extracted_token_789");

        // 执行测试
        String result = ReflectionTestUtils.invokeMethod(orderManager, "extractChannelDiscountTokenFromOrder", order);

        // 验证结果
        assertEquals("应正确提取token", "extracted_token_789", result);
    }

    @Test
    public void testExtractChannelDiscountTokenFromOrder_NoToken() {
        // 准备测试数据 - 没有token的订单
        Order order = new Order();
        order.setOrderCode("test_order_123");

        // 执行测试
        String result = ReflectionTestUtils.invokeMethod(orderManager, "extractChannelDiscountTokenFromOrder", order);

        // 验证结果
        assertNull("没有token时应返回null", result);
    }

    @Test
    public void testGetChannelDiscountToken_Success() {
        // 执行测试
        String result = ReflectionTestUtils.invokeMethod(orderManager, "getChannelDiscountToken", "1_1");

        // 验证结果
        assertEquals("应返回正确的token", "alipay_discount_token_123", result);
    }

    @Test
    public void testGetChannelDiscountToken_NotFound() {
        // 执行测试
        String result = ReflectionTestUtils.invokeMethod(orderManager, "getChannelDiscountToken", "999_999");

        // 验证结果
        assertNull("未找到配置时应返回null", result);
    }

    /**
     * 创建测试用的DutParamsDto
     */
    private DutParamsDto createTestDutParamsDto(Integer payChannel) {
        return DutParamsDto.builder()
            .payChannel(payChannel)
            .agreementType(1)
            .build();
    }

    /**
     * 创建包含token的测试订单
     */
    private Order createTestOrderWithToken(String token) {
        Order order = new Order();
        order.setOrderCode("test_order_" + System.currentTimeMillis());
        
        OrderReferDto referDto = new OrderReferDto();
        Map<String, Object> businessProperty = new HashMap<>();
        businessProperty.put("channel_discount_token", token);
        referDto.setBusinessProperty(businessProperty);
        
        order.setReferByReferDto(referDto);
        return order;
    }
}
