package com.qiyi.boss.service;

import com.qiyi.boss.service.impl.CommonAutorenewDutConfigManager;
import com.qiyi.vip.trade.autorenew.domain.CommonAutoRenewDutConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

import static com.qiyi.vip.commons.enums.PayChannelEnum.PAY_CHANNEL_SPGATEWAY;

/**
 * <AUTHOR>
 * created 2019/1/8 - 16:08
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml", "classpath:applicationContext-mybatis.xml"})
public class CommonAutorenewDutConfigManagerTest {

    @Resource
    private CommonAutorenewDutConfigManager commonAutorenewDutConfigManager;

    @Test
    public void testFindAll() {
        int category = 1;
        long vipType = 1L;
        int status = 1;
        List<CommonAutoRenewDutConfig> dutConfigs = commonAutorenewDutConfigManager.findAll(category, null, null, status);
        System.out.println(dutConfigs);
        Assert.assertNotNull(dutConfigs);
    }

    @Test
    public void testFindById() {
        CommonAutoRenewDutConfig commonAutoRenewDutConfig = commonAutorenewDutConfigManager.findById(1L);
        Assert.assertNotNull(commonAutoRenewDutConfig);
    }

    @Test
    public void testFindNextConfigNotSpGatewayById() {
        Long nextId = 13L;
        CommonAutoRenewDutConfig commonAutoRenewDutConfig = commonAutorenewDutConfigManager.findFirstExcludeFilteredConfigById(nextId, PAY_CHANNEL_SPGATEWAY);
        System.out.println(commonAutoRenewDutConfig);
    }
}
