package com.qiyi.boss.service;

import com.qiyi.boss.service.impl.AppProductManager;
import com.qiyi.vip.trade.qiyue.domain.AppProduct;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * created 2019/1/7 - 11:54
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml", "classpath:applicationContext-mybatis.xml"})
public class AppProductManagerTest {

    @Resource
    private AppProductManager appProductManager;

    @Test
    public void testGetAppProductByAppId() {
        AppProduct appProduct = appProductManager.getAppProductByAppId("iqiyi_vip_ipad_video_1m");
        Assert.assertNotNull(appProduct);
    }
}
