package com.qiyi.boss.service;

import com.qiyi.boss.service.impl.DutUserRenewStatusManager;
import com.qiyi.vip.trade.autorenew.domain.DutUserRenewStatus;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * created 2019/1/8 - 11:45
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml", "classpath:applicationContext-mybatis.xml"})
public class DutUserRenewStatusManagerTest {

    @Resource
    private DutUserRenewStatusManager dutUserRenewStatusManager;

    @Test
    public void testAddUserRenewStatusIfNotExisted() {
        DutUserRenewStatus dutUserRenewStatus = dutUserRenewStatusManager.addUserRenewStatusIfNotExisted(11112222L);
        Assert.assertNotNull(dutUserRenewStatus);
    }

    @Test
    public void testGetDutUserRenewStatus() {
        DutUserRenewStatus dutUserRenewStatus = dutUserRenewStatusManager.getDutUserRenewStatus(11112222L);
        Assert.assertNotNull(dutUserRenewStatus);
    }

    @Test
    public void testUpdate() {
        DutUserRenewStatus dutUserRenewStatus = dutUserRenewStatusManager.getDutUserRenewStatus(11112222L);
        Assert.assertNotNull(dutUserRenewStatus);
        dutUserRenewStatus.setSerialRenewCount(12);
        dutUserRenewStatusManager.saveNew(dutUserRenewStatus);
    }
}
