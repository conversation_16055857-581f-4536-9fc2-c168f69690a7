package com.qiyi.boss.service;


import com.google.common.collect.Maps;
import com.qiyi.boss.Constants;
import com.qiyi.boss.utils.ApplicationContextUtil;
import com.qiyi.boss.utils.EncodeUtils;
import com.qiyi.boss.utils.IPUtil;
import com.qiyi.boss.utils.SignatureUtil;

import com.qiyi.vip.trade.autorenew.domain.FreePayConfig;
import com.qiyi.vip.trade.autorenew.service.BossFreePayConfigService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.PropertySource;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Map;
import java.util.UUID;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml", "classpath:applicationContext-mybatis.xml"})
@PropertySource("classpath:test.properties")
public class AutoRenewGracePeriodFreePayTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(AutoRenewGracePeriodFreePayTest.class);


    private static final String PARAM_AMOUNT = "amount";
    private static final String PARAM_ACT_CODE = "actCode";
    private static final String PARAM_SIGN = "sign";

    /**
     * 批次号、接入方、idempotenParam、appIp
     */
    private static final String PARAM_BATCH_NO = "batchNo";
    private static final String PARAM_PARTNER = "partner";
    private static final String PARAM_IDEMPOTENT_PARAM = "idempotentParam";
    private static final String PARAM_APP_IP = "appIp";
    private static final String BATCH_NO = "201905161542367345438";
    private static final String PARTNER = "vip-trade";
    private static String APP_IP = "";

    static {
        try {
            APP_IP = IPUtil.getLocalIp();
        } catch (Exception ex) {
            LOGGER.error("get LocalIPv4 error", ex);
        }
    }

    @Resource
    private RestTemplate restTemplate;

    @Before
    public void before() {
        assert restTemplate != null;
    }

    @Test
    public void freePay() {
        Map<String, String> params = Maps.newHashMap();
        BossFreePayConfigService freePayConfigService = ApplicationContextUtil.getBean(BossFreePayConfigService.class);
        FreePayConfig freePayConfig = freePayConfigService.getConfigSelective(Constants.FREE_PAY_FOR_GRACE_PERIOD, 1, "iqiyi_vip_iphone_video_autorenew");

        params.put(PARAM_BATCH_NO, freePayConfig.getBatchNo());
        params.put(PARAM_ACT_CODE, freePayConfig.getActCode());
        params.put(PARAM_PARTNER, PARTNER);
        params.put(PARAM_IDEMPOTENT_PARAM, UUID.randomUUID().toString().replaceAll("-", ""));
        params.put(PARAM_AMOUNT, String.valueOf(freePayConfig.getCompensateDay()));
        params.put("uid", String.valueOf("123456"));
        params.put(PARAM_APP_IP, APP_IP);
        String signKey = "2dc89f374cab44709163b69839241748";
        String sign = EncodeUtils.MD5(SignatureUtil.createLinkString(params) + signKey, "UTF-8");
        params.put(PARAM_SIGN, sign);

        MultiValueMap<String, Object> formEntity = new LinkedMultiValueMap<>();
        for (String iKey : params.keySet()) {
            formEntity.add(iKey, params.get(iKey));
        }

        Map<String, String> response = restTemplate.postForObject("http://************:8888/api/send", formEntity, Map.class);
        assert response != null;
    }

}
