package com.qiyi.boss.service;

import com.google.common.collect.Lists;
import com.qiyi.boss.service.impl.AsyncTaskManager;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.domain.AsyncTask;
import com.qiyi.vip.trade.autorenew.mapper.AsyncTaskMapper;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * created 2019/2/26 - 14:26
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml", "classpath:applicationContext-mybatis.xml"})
public class AsyncTaskManagerTest {

    @Resource
    private AsyncTaskManager asyncTaskManager;

    @Resource
    private AsyncTaskMapper asyncTaskMapper;

    @Test
    public void testCRUD() {
        AsyncTask toSave = AsyncTask.builder()
                .data("product=4&user=1480382103&dutBindType=6&taskType=normal&notifyCount=0&executedTypes=6&autoRenewLinkedDutConfig=1&autorenewDutConfig=1&fc=")
                .className("com.qiyi.boss.async.task.DutAutoRenewTaskNew")
                .createdAt(DateHelper.getDateTime())
                .poolType(1)
                .priority(1)
                .inQueue(0)
                .timerRunAt(DateHelper.getDateTime())
                .cronExpression("0 0 0 * *")
                .supportDelay(1)
                .build();
        asyncTaskManager.addAsyncTask(toSave);

        AsyncTask toUpdate = asyncTaskManager.getAsyncTask(toSave.getId());
        Assert.assertNotNull(toUpdate);

        toUpdate.setTaskIp("127.0.0.1");
        asyncTaskManager.editAsyncTask(toUpdate);

        asyncTaskManager.removeAsyncTask(toUpdate);

        Assert.assertNull(asyncTaskManager.getAsyncTask(toSave.getId()));
    }

    @Test
    public void testUpdateAllAsyncTaskStatus() {
        asyncTaskManager.updateAllAsyncTaskStatus(56);
    }

    @Test
    public void testSaveDuplicateTask() {
        AsyncTask toSave = AsyncTask.builder()
            .data("product=4&user=1480382103&dutBindType=6&taskType=normal&notifyCount=0&executedTypes=6&autoRenewLinkedDutConfig=1&autorenewDutConfig=1&fc=")
            .className("com.qiyi.boss.async.task.DutAutoRenewTaskNew")
            .createdAt(DateHelper.getDateTime())
            .poolType(1)
            .priority(1)
            .inQueue(0)
            .timerRunAt(DateHelper.getDateTime())
            .cronExpression("0 0 0 * *")
            .taskId("sky")
            .supportDelay(1)
            .build();
        asyncTaskManager.addAsyncTask(toSave);

        asyncTaskManager.addAsyncTask(toSave);
    }

    @Test
    public void testDelay() {
        AsyncTask toSave1 = AsyncTask.builder()
            .data("product=4&user=1480382103&dutBindType=6&taskType=normal&notifyCount=0&executedTypes=6&autoRenewLinkedDutConfig=1&autorenewDutConfig=1&fc=")
            .className("com.qiyi.boss.async.task.DutAutoRenewTaskNew")
            .createdAt(DateHelper.getDateTime())
            .taskId("sky")
            .supportDelay(0)
            .build();

        AsyncTask toSave2 = AsyncTask.builder()
            .data("product=4&user=1480382103&dutBindType=6&taskType=normal&notifyCount=0&executedTypes=6&autoRenewLinkedDutConfig=1&autorenewDutConfig=1&fc=")
            .className("com.qiyi.boss.async.task.DutAutoRenewTaskNew")
            .createdAt(DateHelper.getDateTime())
            .taskId("sky")
            .supportDelay(1)
            .build();

        List<AsyncTask>  asyncTasks = Lists.newArrayList(toSave1, toSave2);
        List<AsyncTask> shouldDelayedTask = Lists.newLinkedList();

        Iterator<AsyncTask> asyncTaskIterator = asyncTasks.iterator();
        while (asyncTaskIterator.hasNext()) {
            AsyncTask tmpTask = asyncTaskIterator.next();
            if (tmpTask.canDelay()) {
                shouldDelayedTask.add(tmpTask);
                asyncTaskIterator.remove();
            }
        }

        Assert.assertEquals(1, asyncTasks.size());
        Assert.assertEquals(1, shouldDelayedTask.size());
    }

    @Test
    public void testMakeTaskProcessing() {
        AsyncTask query = AsyncTask.builder().inQueue(AsyncTask.TASK_IN_QUEUE_NO).build();
        List<AsyncTask> asyncTasks = asyncTaskMapper.listPageSelective(query, 0, 1);
        if (CollectionUtils.isEmpty(asyncTasks)) {
            return;
        }
        boolean result = asyncTaskManager.makeTaskProcessing(asyncTasks.get(0));
        Assert.assertTrue(result);
    }
}
