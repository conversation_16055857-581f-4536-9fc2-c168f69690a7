package com.qiyi.boss.service;

import com.qiyi.boss.service.impl.VipTypeManager;
import com.qiyi.vip.trade.qiyue.domain.VipType;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * created 2019/1/7 - 14:46
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml", "classpath:applicationContext-mybatis.xml"})
public class VipTypeManagerTest {

    @Resource
    private VipTypeManager vipTypeManager;

    @Test
    public void testGetByCode() {
        VipType vipType = vipTypeManager.getVipTypeByCode("9c4e4c1c18827a41");
        Assert.assertNotNull(vipType);
    }
}
