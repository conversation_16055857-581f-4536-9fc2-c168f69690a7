package com.qiyi.boss.service;

import com.google.common.collect.Maps;
import com.iqiyi.vip.order.dal.model.Order;
import com.qiyi.boss.dto.*;
import com.qiyi.boss.model.Product;
import com.qiyi.boss.service.impl.OrderManager;
import com.qiyi.boss.utils.AppConfig;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.EncodeUtils;
import com.qiyi.boss.utils.PayUtils;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2020-10-26
 * Time: 15:00
 */
@RunWith(MockitoJUnitRunner.class)
@ActiveProfiles("test")
@SpringBootTest
public class OrderManagerTest {

	@Resource
	private OrderManager orderManager;

	private static Long userId;
	private static DutUserNew dutUserNew;
	private static Integer amount;
	private static Product qiyueProduct;
	private static DutParamsDto dutParamsDto;
	private static Map<String, String> params;

	static {

		System.setProperty("spring.profiles.active", "dev");

		userId = **********L;

		dutUserNew = DutUserNew.builder()
				.userId(**********L)
				.vipType(1L)
				.autoRenew(1)
				.type(6)
				.vipCategory(1)
				.amount(1)
				.deadline(DateHelper.getCurrentTime())
				.updateTime(DateHelper.getCurrentTime())
				.renewCount(0)
				.serialRenewCount(0)
				.orderCode("202001010000000000001")
				.platform(10L)
				.platformCode("bb136ff4276771f3")
				.renewPrice(1500)
				.status(1)
				.build();

		amount = 1;
		qiyueProduct = new Product(
				"黄金会员",
				3000,
				1,
				2,
				1,
				1,
				DateHelper.getCurrentTime()
		);

		dutParamsDto = DutParamsDto.builder()
				.dutType(6)
				.accountSignCode("201901010000000000001")
				.amount(1)
				.businessProperty(null)
				.fc("fc_test")
				.fv("fv_test")
				.fee(1500)
				.orderType("autoRenewRetry")
				.partnerNo("201901010000000000001")
				.payChannel(2)
				.payType(65)
				.platform("bb136ff4276771f3")
				.productName(qiyueProduct.getName())
				.serviceCode("lyksc7aq36aedndk")
				.signFlag("1")
				.taskType("retry_1")
				.uid(**********L)
				.vipType(1L)
				.signOrderCode("201901010000000000001")
				.actCode("actCode_test")
				.build();

		params = Maps.newHashMap();
		params.put("pid", "a0226bd958843452");
		params.put("dutType", "6");
		params.put("amount", "1");
		params.put("serviceCode", "lyksc7aq36aedndk");
		params.put("fee", "1500");
		params.put("BACKEND-AUTO-RENEW", "1");
		params.put("uid", "**********");
		params.put("payType", "65");
		params.put("platform", "bb136ff4276771f3");
		params.put("fc", "fc_test");
		params.put("fv", "fv_test");
		params.put("partnerNo", "201901010000000000001");
		params.put("signOrderCode", "201901010000000000001");
		params.put("orderType", "autoRenewRetry");
		params.put("taskType", "retry_1");
		params.put("signFlag", "1");
		params.put("businessProperty", "{\"a\": \"b\", \"c\": \"d\"}");
		params.put("sign", getQiyueSign(params));
	}

	private static String getQiyueSign(Map<String, String> params) {
		//把数组所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串
		String prestr = PayUtils.createLinkString(params);
		//把拼接后的字符串再与安全校验码直接连接起来
		String key = AppConfig.getProperty("boss.pay.key");
		prestr = prestr + key;
		return EncodeUtils.MD5(prestr, "UTF-8");
	}

	@Test
	public void testCreateOrderAndPrepareAndProcessPayments() {

		CreateOrderDto createOrderDto = CreateOrderDto.builder()
				.amount(amount)
				.dutParamsDto(dutParamsDto)
				.dutUserNew(dutUserNew)
				.params(params)
				.commodityInfo(null)
				.userId(userId)
				.build();
		Order order = orderManager.createOrder(createOrderDto);
		Assert.assertNotNull(order);

		OrderPrepareDto orderPrepareDto = OrderPrepareDto.builder()
				.dutParamsDto(dutParamsDto)
				.order(order)
				.userId(userId)
				.build();
		PayCenterDutReq payCenterDutReq = orderManager.prepare(orderPrepareDto);
		Assert.assertTrue(MapUtils.isNotEmpty(payCenterDutReq.getConfigs()));

		Optional<PayCenterDutResult> payCenterDutResultOptional = orderManager.processPayments(payCenterDutReq);
		Assert.assertTrue(payCenterDutResultOptional.isPresent());
	}
}
