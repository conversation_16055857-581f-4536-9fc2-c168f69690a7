package com.qiyi.boss.service;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

import com.qiyi.boss.service.impl.PaymentDutTypeManager;

/**
 * <AUTHOR>
 * created 2019/1/7 - 16:39
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml", "classpath:applicationContext-mybatis.xml"})
public class PaymentDutTypeManagerTest {

    @Resource
    private PaymentDutTypeManager paymentDutTypeManager;

    @Test
    public void testGetDutTypeWithSourceVipTypeV1() {
        Integer payType = 22;
        Integer payChannel = 1;
        Integer vipType = 1;
        Integer amount = 1;

        Integer sourceVipType = null;
        String actCode = null;

        Integer dutType = paymentDutTypeManager.getDutTypeWithSourceVipType(payType, payChannel, sourceVipType, vipType, actCode, amount, 1480402292L, null);
        Assert.assertEquals(1, dutType.intValue());
    }

    @Test
    public void testGetDutTypeWithSourceVipTypeV2() {
        Integer payType = 65;
        Integer payChannel = 2;
        Integer vipType = 5;
        Integer amount = 1;

        Integer sourceVipType = 1;
        String actCode = null;

        Integer dutType = paymentDutTypeManager.getDutTypeWithSourceVipType(payType, payChannel, sourceVipType, vipType, actCode, amount, 1480402292L, null);
        Assert.assertEquals(42, dutType.intValue());
    }

    @Test
    public void testGetDutTypeWithSourceVipTypeV3() {
        Integer payType = 389;
        Integer payChannel = 2;
        Integer vipType = 1;
        Integer amount = 1;

        Integer sourceVipType = null;
        String actCode = "firstrenew_tv";

        System.out.println(paymentDutTypeManager.getDutTypeWithSourceVipType(payType, payChannel, sourceVipType, vipType, actCode, amount,1480402292L, null));
        Integer dutType = paymentDutTypeManager.getDutTypeWithSourceVipType(payType, payChannel, sourceVipType, vipType, actCode, amount, 1480402292L, null);
        Assert.assertEquals(19, dutType.intValue());
    }


    @Test
    public void testGetDutTypesByVipTypeAndAmount() {
        Integer vipType = 1;
        Integer amount = 3;
        System.out.println(paymentDutTypeManager.getDutTypesByVipTypeAndAmount(vipType, amount));
    }

}
