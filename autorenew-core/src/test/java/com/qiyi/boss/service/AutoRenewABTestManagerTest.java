package com.qiyi.boss.service;

import com.qiyi.boss.service.impl.AutoRenewABTestManager;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewABTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2020-6-24
 * Time: 11:15
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
@WebAppConfiguration
public class AutoRenewABTestManagerTest {

	@Resource
	private AutoRenewABTestManager autoRenewABTestManager;

	@Before
	public void before() {
		assert autoRenewABTestManager != null;
	}

	@Test
	public void testList() {

		String category = AutoRenewABTest.CategoryEnum.WECHAT_PRE_DUT_REMIND.getCategory();
		Long vipType = 1L;
		Integer dutType = 5;
		Integer amount = 1;
		Integer index = 45;

		System.out.println(autoRenewABTestManager.query(category, vipType, dutType, amount, index));

	}
}
