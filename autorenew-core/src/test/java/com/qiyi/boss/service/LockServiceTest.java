package com.qiyi.boss.service;

import com.qiyi.vip.trade.autorenew.domain.Lock;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Random;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * created 2019/1/7 - 18:23
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml", "classpath:applicationContext-mybatis.xml"})
public class LockServiceTest {

    @Resource
    private LockService lockService;

    private Random random = new Random();

    @Test
    public void testLock() throws InterruptedException {
        int nThreads = 10;

        ExecutorService threadPool = Executors.newFixedThreadPool(nThreads);
        for (int i = 0; i < nThreads; i++) {
            threadPool.submit(() -> {
                String lockName = Lock.LOCK_ACCOUNT_FOR_DEAL_PREFIX + "sky";
                boolean result = lockService.lock(lockName);
                System.out.println(Thread.currentThread().getName() + " get lock at " + System.currentTimeMillis());
                try {
                    Thread.sleep(random.nextInt(20));
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                lockService.unlock(lockName);
            });
        }

        Thread.sleep(1000 * 100000);
    }
}
