package com.qiyi.boss.service;

import com.qiyi.boss.Constants;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.service.impl.ManagementConfigService;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> liuwanqiang
 * Date: 2019-1-24
 * Time: 22:27
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml", "classpath:applicationContext-mybatis.xml"})
public class ManagementConfigServiceTest {

    public static final int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();

    @Resource
    private ManagementConfigService managementConfigService;

    @Before
    public void before() {
        assert managementConfigService != null;
    }

    @Test
    public void testSearchBy() {
        System.out.println(managementConfigService.searchBy(Constants.VIP_USER_SUPER, agreementType, PaymentDutType.PAY_CHANNEL_MOBILE, Constants.AMOUNT_OF_COMMON_AUTORENEW));
        System.out.println(managementConfigService.searchBy(Constants.VIP_USER_SUPER, agreementType, PaymentDutType.PAY_CHANNEL_MOBILE, Constants.AMOUNT_OF_AUTORENEW_BY_QUARTER_YEAR));

        System.out.println(managementConfigService.searchBy(Constants.VIP_USER_SUPER, agreementType, PaymentDutType.PAY_CHANNEL_IAP, Constants.AMOUNT_OF_AUTORENEW_BY_QUARTER_YEAR));
        System.out.println(managementConfigService.searchBy(Constants.VIP_USER_SUPER, agreementType, PaymentDutType.PAY_CHANNEL_IAP, Constants.AMOUNT_OF_AUTORENEW_BY_YEAR));

        System.out.println(managementConfigService.searchBy(Constants.VIP_USER_SUPER, agreementType,  null, Constants.AMOUNT_OF_AUTORENEW_BY_QUARTER_YEAR));
        System.out.println(managementConfigService.searchBy(Constants.VIP_USER_TENNIS, agreementType, PaymentDutType.PAY_CHANNEL_IAP, Constants.AMOUNT_OF_AUTORENEW_BY_YEAR));
        System.out.println(managementConfigService.searchBy(Constants.VIP_USER_DIAMOND, agreementType,null, Constants.AMOUNT_OF_AUTORENEW_BY_QUARTER_YEAR));

    }
}
