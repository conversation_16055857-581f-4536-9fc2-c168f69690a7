package com.qiyi.boss.service;

import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.service.impl.AutoRenewLinkedDutConfigManager;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewLinkedDutConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * created 2019/1/10 - 16:47
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml", "classpath:applicationContext-mybatis.xml"})
public class AutoRenewLinkedDutConfigManagerTest {

    @Resource
    private AutoRenewLinkedDutConfigManager autoRenewLinkedDutConfigManager;

    public static final int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();

    @Test
    public void testFind() {
        Integer jobType = null;
        Long vipType = null;
        Integer payChannel = null;
        String category = null;
        //查询status = 1， 处于有效期的配置
        AutoRenewLinkedDutConfig autoRenewLinkedDutConfig = autoRenewLinkedDutConfigManager.find(jobType, vipType, null, null, payChannel, category);
        if (autoRenewLinkedDutConfig != null) {
            Assert.assertEquals(autoRenewLinkedDutConfig.getStatus().intValue(), AutoRenewLinkedDutConfig.STATUS_VALID);
            Assert.assertTrue(autoRenewLinkedDutConfig.getValidEndTime().getTime() > System.currentTimeMillis());
        }
    }

    @Test
    public void testFindById() {
        AutoRenewLinkedDutConfig autoRenewLinkedDutConfig = autoRenewLinkedDutConfigManager.findById(1L);
        Assert.assertNotNull(autoRenewLinkedDutConfig);
    }
}
