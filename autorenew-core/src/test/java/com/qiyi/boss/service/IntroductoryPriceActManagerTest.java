package com.qiyi.boss.service;

import com.qiyi.boss.autorenew.dto.IntroductoryActDto;
import com.qiyi.boss.service.impl.IntroductoryPriceActManager;
import com.qiyi.vip.trade.autorenew.domain.IntroductoryPriceAct;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Optional;

/**
 * <AUTHOR>
 * created 2019/1/7 - 19:57
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml", "classpath:applicationContext-mybatis.xml"})
public class IntroductoryPriceActManagerTest {

    @Resource
    private IntroductoryPriceActManager introductoryPriceActManager;

    @Test
    public void testFindParticipatedAct() {
        IntroductoryActDto introductoryActDto = IntroductoryActDto
                .builder()
                .pid("a0226bd958843452")
                .amount(1)
                .fee(1)
                .payTime(new Timestamp(System.currentTimeMillis()))
                .platform("b6c13e26323c537d")
                .payType(65)
                .build();

        Optional<IntroductoryPriceAct> introductoryPriceAct = introductoryPriceActManager.findParticipatedAct(introductoryActDto);
        Assert.assertTrue(introductoryPriceAct.isPresent());
    }
}
