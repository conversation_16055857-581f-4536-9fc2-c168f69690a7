package com.qiyi.boss.service;

import com.qiyi.boss.service.impl.AutoRenewGracePeriodRecordManager;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewGracePeriodRecord;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * created 2019/1/10 - 17:10
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml", "classpath:applicationContext-mybatis.xml"})
public class AutoRenewGracePeriodRecordManagerTest {

    @Resource
    private AutoRenewGracePeriodRecordManager autoRenewGracePeriodRecordManager;

    @Test
    public void testFind() {
        Long userId = null;
        String appId = null;
        Integer amount = null;
        Integer interval = 1000;
        List<AutoRenewGracePeriodRecord> autoRenewGracePeriodRecords = autoRenewGracePeriodRecordManager.find(userId, appId, amount, interval);
        Assert.assertNotNull(autoRenewGracePeriodRecords);
    }

    @Test
    public void testSave() {
        AutoRenewGracePeriodRecord toSave = new AutoRenewGracePeriodRecord();
        toSave.setAmount(1);
        toSave.setCreateTime(DateHelper.getDateTime());
        toSave.setUpdateTime(DateHelper.getDateTime());
        autoRenewGracePeriodRecordManager.save(toSave);

        Assert.assertNotNull(toSave.getId());
    }
}
