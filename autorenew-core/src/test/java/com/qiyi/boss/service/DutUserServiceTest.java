package com.qiyi.boss.service;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

import com.qiyi.boss.dto.AutoRenewStatusDto;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;

/**
 * <AUTHOR>
 * created 2018/12/17 - 16:01
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class DutUserServiceTest {

    private Long userId = 9527L;
    private Long vipType = 1L;
    private int type = 9;
    private int amount = 1;
    private int vipCategory = 1;

    @Resource
    private DutUserService dutUserService;
    @Resource
    private UserAgreementService userAgreementService;

    @Test
    public void testQueryAutoRenewStatus() {
        Long userId = 1329256815L;
        List<Long> vipTypes = Lists.newArrayList(1L , 3L, 4L, 5L, 6L, 7L, 16L);
        List<Integer> payChannels = Lists.newArrayList(1, 2, 3,8, 10);
        List<AutoRenewStatusDto> autoRenewStatusDtoList = dutUserService.queryAutoRenewStatus(userId, vipTypes, payChannels);

        if (CollectionUtils.isNotEmpty(autoRenewStatusDtoList)) {
            autoRenewStatusDtoList.stream().forEach(autoRenewStatusDto -> {
                Assert.assertEquals(autoRenewStatusDto.getUserId(), userId);
                Assert.assertTrue(vipTypes.contains(autoRenewStatusDto.getVipType()));
                Assert.assertTrue(payChannels.contains(autoRenewStatusDto.getPayChannel()));
            });
        }
    }

    @Test
    public void testProcessDutFail() {
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, vipType);
        dutUserService.processDutFail(dutUserNew);

        dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, vipType);

        Assert.assertEquals(dutUserNew.getInterruptFlag().intValue(), DutUserNew.INTERRUPT_FLAG_YES);
    }

    @Test
    public void testProcessDutSuccess() {
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, vipType);
        dutUserService.processDutSuccess(dutUserNew);
        DutUserNew dutUserNewDB = userAgreementService.getByDutTypeAndVipType(userId, type, vipType);

        Assert.assertEquals(1, dutUserNewDB.getRenewCount() - dutUserNew.getRenewCount());
        Assert.assertEquals(1, dutUserNewDB.getSerialRenewCount() - dutUserNew.getSerialRenewCount());
        Assert.assertEquals((int) dutUserNewDB.getInterruptFlag(), DutUserNew.INTERRUPT_FLAG_NO);
    }

    @Test
    public void testProcessDutSuccessForGash() {
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(1201570008L, 12, 6L);
        dutUserService.processDutSuccess(dutUserNew);
        DutUserNew dutUserNewDB = userAgreementService.getByDutTypeAndVipType(1201570008L, 12, 6L);

        Assert.assertEquals(1, dutUserNewDB.getRenewCount() - dutUserNew.getRenewCount());
        Assert.assertEquals(1, dutUserNewDB.getSerialRenewCount() - dutUserNew.getSerialRenewCount());
        Assert.assertEquals((int) dutUserNewDB.getInterruptFlag(), DutUserNew.INTERRUPT_FLAG_NO);
    }


}
