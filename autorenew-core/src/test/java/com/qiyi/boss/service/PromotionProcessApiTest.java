package com.qiyi.boss.service;

import com.qiyi.boss.utils.PromotionProcessApi;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml", "classpath:applicationContext-mybatis.xml"})
public class PromotionProcessApiTest {

    @Autowired
    private PromotionProcessApi promotionProcessApi;

    @Before
    public void before() {
        assert promotionProcessApi != null;
    }

    @Test
    public void testCanBuy() {
        System.out.println(promotionProcessApi.canBuy(1235692734L));
//        System.out.println(promotionProcessApi.canBuy(2146582755L));
    }
}