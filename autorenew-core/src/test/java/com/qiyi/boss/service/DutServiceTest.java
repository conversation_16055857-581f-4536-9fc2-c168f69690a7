package com.qiyi.boss.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.qiyi.boss.Constants;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.service.impl.AutorenewDutConfigManager;
import com.qiyi.boss.service.impl.DutManager;
import com.qiyi.boss.utils.AppConfig;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.domain.AutorenewDutConfig;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import com.iqiyi.solar.config.client.CloudConfig;

import static com.qiyi.vip.trade.autorenew.sharding.ShardingConstants.READ_SHARDING_DB_CLUSTER;

/**
 * Created by qiangxu on 2017/5/15.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
@ActiveProfiles("dev")
public class DutServiceTest {

    public static final int NEXT_DAY_OFFSET = 1;
    public static final int FORWARD_DAYS_OFFSET = 2;
    public static final int START_TIME_OFFSET = -1;
    private static final int ONE_DAY_OFFSET = 1;

    private int tableNum = AppConfig.getInt("dutuser_shard_num_123", 100);

    @Resource
    private DutManager dutManager;
    @Resource
    private UserAgreementService userAgreementService;
    @Resource
    private CloudConfig cloudConfig;
    @Resource
    private AutorenewDutConfigManager autorenewDutConfigService;
    @Resource
    private AutoRenewDutTypeManager autoRenewDutTypeManager;

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Test
    public void shardUpdateDutUserNewTest(){
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(1328215801l,12,6L);
        System.out.println(dutUserNew);
        dutUserNew.setNextDutTime(DutUserNew.caculateNextDutTime(dutUserNew.getNextDutTime()));
        dutUserNew.setRenewCount(dutUserNew.getRenewCount() + 1);
        dutUserNew.setSerialRenewCount(dutUserNew.getSerialRenewCount() + 1);
        dutUserNew.setInterruptFlag(DutUserNew.INTERRUPT_FLAG_YES);
        dutManager.updateShardDutUserNew(dutUserNew);
    }

    /**
     * 测试双写保存和更新
     */
    @Test
    public void testSaveOrUpdateOfDoubleWrite(){
        Long userId = 1095617220L;
        int type = 16;
        long vipType = 1;
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, Constants.VIP_USER_SUPER);
        dutManager.save(dutUserNew);
        Assert.assertNotNull(userAgreementService.getByDutTypeAndVipType(userId, type, vipType));

        System.out.println(dutUserNew.getReturnUrl());
        dutUserNew.setReturnUrl("www.google.com");
        dutManager.save(dutUserNew);

        Assert.assertEquals("www.google.com", userAgreementService.getByDutTypeAndVipType(userId, type, vipType).getReturnUrl());
    }


    /**
     * 其实就是根据userId和vipType更新deadline, update_time, vip_category字段的值
     */
    @Test
    public void testUpdateAll(){
        Long userId = 1095617220L;
        int type = 16;
        long vipType = 1;
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, Constants.VIP_USER_SUPER);
        dutUserNew.setDeadline(DateHelper.getDateTime());
        dutUserNew.setVipCategory(1);
        int rows = dutManager.updateAll(dutUserNew);
        Assert.assertTrue(rows > 0);

        DutUserNew dutUserNewOld = userAgreementService.getByDutTypeAndVipType(userId, type, Constants.VIP_USER_SUPER);
        DutUserNew dutUserNewShard = userAgreementService.getByDutTypeAndVipType(userId, type, vipType);

        Assert.assertEquals(dutUserNewOld.getVipCategory().longValue() , dutUserNewShard.getVipCategory().longValue());
    }

    /**
     * 根据userId 和 代扣类型 及 会员类型（大陆 or 台湾&奇异果）  更新到期时间
     */
    @Test
    public  void testUpdateDeadlineByUserIdAndDutType(){
        //大陆白银
        Long userId = 1095617220L;
        int type = 16;
        long vipType = 1;
        DutUserNew dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, type, Constants.VIP_USER_SUPER);
        boolean result = dutManager.updateUserDeadlineByUserIdAndDutType(userId, dutUserNew.getVipType(), dutUserNew.getType(), new Date(), dutUserNew.getDeadline());
        Assert.assertTrue(result);

        //台湾 or 奇异果
        Long twUserId = 1201570008L;
        int twType = 12;
        DutUserNew dutUserNewShard = userAgreementService.getByDutTypeAndVipType(twUserId, twType, vipType);
        result = dutManager.updateUserDeadlineByUserIdAndDutType(twUserId, dutUserNewShard.getVipType(), dutUserNewShard.getType(), new Date(), dutUserNewShard.getDeadline());
        Assert.assertTrue(result);
    }

    @Test
    public void testUpdateRenewPriceAndNextDutTime() throws Exception {
        String msg = "{\"serviceCode\":\"lyksc7aq36aedndk\",\"payTime\":\"2018-09-19 17:09:08\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"orderFee\":\"1\",\"pid\":\"a0226bd958843452\",\"type\":\"1\",\"couponSettlementFee\":\"\",\"platform\":\"1\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"1480386303\",\"payType\":\"65\",\"centerCode\":\"T2018091917090404001\",\"autoRenew\":\"2\",\"startTime\":\"2018-09-20 00:00:00\",\"renewFlag\":\"1\",\"msgtype\":\"7\",\"productType\":\"1\",\"amount\":\"1\",\"fr_version\":\"\",\"tradeCode\":\"4200000165201809191604792961\",\"settlementFee\":\"19800\",\"orderRealFee\":\"1\",\"centerPayType\":\"WECHATAPPV3DUT\",\"sourceType\":\"\",\"createTime\":\"2018-09-19 17:09:04\",\"payTypeCategory\":\"1\",\"orderCode\":\"201809191709034804521\",\"endTime\":\"2019-09-20 00:00:00\",\"fc\":\"a9e4fec171bb4c74\",\"aid\":\"null\",\"centerPayService\":\"675\",\"gateway\":\"1\",\"platformCode\":\"b6c13e26323c537d\",\"productSubtype\":\"1\",\"status\":\"1\"}";

        AutorenewRequest autorenewRequest = AutorenewRequest.transferFromMessage(JSON.parseObject(msg, Map.class));
        dutManager.updateDutUserByAutoRenewRequest(autorenewRequest);
    }

    @Test
    public void testChangeAutorenewRequest_autorenew() throws Exception {
        String msg = "{\"serviceCode\":\"lyksc7aq36aedndk\",\"payTime\":\"2018-09-19 17:09:08\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"orderFee\":\"1\",\"pid\":\"a0226bd958843452\",\"type\":\"1\",\"couponSettlementFee\":\"\",\"platform\":\"1\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"1480386303\",\"payType\":\"65\",\"centerCode\":\"T2018091917090404001\",\"autoRenew\":\"2\",\"startTime\":\"2018-09-20 00:00:00\",\"renewFlag\":\"1\",\"msgtype\":\"7\",\"productType\":\"1\",\"amount\":\"1\",\"fr_version\":\"\",\"tradeCode\":\"4200000165201809191604792961\",\"settlementFee\":\"19800\",\"orderRealFee\":\"1\",\"centerPayType\":\"WECHATAPPV3DUT\",\"sourceType\":\"\",\"createTime\":\"2018-09-19 17:09:04\",\"payTypeCategory\":\"1\",\"orderCode\":\"201809191709034804521\",\"endTime\":\"2019-09-20 00:00:00\",\"fc\":\"a9e4fec171bb4c74\",\"aid\":\"null\",\"centerPayService\":\"675\",\"gateway\":\"1\",\"platformCode\":\"b6c13e26323c537d\",\"productSubtype\":\"1\",\"status\":\"1\"}";

        AutorenewRequest autorenewRequest = AutorenewRequest.transferFromMessage(JSON.parseObject(msg, Map.class));
        dutManager.updateDutUserByAutoRenewRequest(autorenewRequest);
    }

    /**
     * 验证苹果签约用户切换订阅更新
     * * @throws Exception
     */
    @Test
    public void testUpdateRenewPriceAndNextDutTime_succ() throws Exception {
        String msg = "{\n" +
                "\t\"payTime\": \"2020-09-14 10:32:50\",\n" +
                "\t\"serviceCode\": \"a01c4467724811a9\",\n" +
                "\t\"channel\": \"9b79f47f069d1ee0\",\n" +
                "\t\"chargeType\": \"2\",\n" +
                "\t\"orderFee\": \"6999\",\n" +
                "\t\"pid\": \"83c3b0c9053fc365\",\n" +
                "\t\"type\": \"-1\",\n" +
                "\t\"dutType\": \"422\",\n" +
                "\t\"couponSettlementFee\": \"\",\n" +
                "\t\"deviceId\": \"70352224-7C0C-4BF9-BEDC-3795109F352B\",\n" +
                "\t\"platform\": \"10151\",\n" +
                "\t\"fv\": \"bbb8e166473aee83\",\n" +
                "\t\"uid\": \"***********\",\n" +
                "\t\"businessCode\": \"a01c4467724811a9\",\n" +
                "\t\"businessValues\": \"null\",\n" +
                "\t\"payType\": \"304\",\n" +
                "\t\"centerCode\": \"2020091437967801123\",\n" +
                "\t\"autoRenew\": \"2\",\n" +
                "\t\"startTime\": \"2021-03-11 17:24:33\",\n" +
                "\t\"renewFlag\": \"1\",\n" +
                "\t\"msgtype\": \"7\",\n" +
                "\t\"productType\": \"1\",\n" +
                "\t\"currencyUnit\": \"USD\",\n" +
                "\t\"amount\": \"12\",\n" +
                "\t\"productId\": \"1000096\",\n" +
                "\t\"tradeNo\": \"202009141032470643774\",\n" +
                "\t\"fr_version\": \"d=70352224-7C0C-4BF9-BEDC-3795109F352B&k:201850022e9c23cc5bc0de1fbaa825f9&v=3.9.0&aid=&test=&login=&dfp=90e963a9979d3243589ecb8593bdcf1b33f4b1987a56ff95424b5e19bbb2b59938\",\n" +
                "\t\"beforePaidSign\": \"0\",\n" +
                "\t\"tradeCode\": \"1000000717997748\",\n" +
                "\t\"settlementFee\": \"6999\",\n" +
                "\t\"beforeDeadline\": \"2021-03-11 17:24:33\",\n" +
                "\t\"orderRealFee\": \"0\",\n" +
                "\t\"centerPayType\": \"APPLEIAPDUT\",\n" +
                "\t\"actCode\": \"iqiyi_vip_itv_video_autorenew_12m_69.99meiyuan_bh\",\n" +
                "\t\"expireTime\": \"2020-09-14 11:32:43.0\",\n" +
                "\t\"sourceType\": \"\",\n" +
                "\t\"createTime\": \"2020-09-14 10:32:47\",\n" +
                "\t\"payTypeCategory\": \"1\",\n" +
                "\t\"orderCode\": \"202009141032470643774\",\n" +
                "\t\"payChannel\": \"9\",\n" +
                "\t\"endTime\": \"2022-03-11 17:24:33\",\n" +
                "\t\"fc\": \"8ef01575f452c2aa\",\n" +
                "\t\"aid\": \"null\",\n" +
                "\t\"centerPayService\": \"446\",\n" +
                "\t\"platformCode\": \"a501dc325957b0b1\",\n" +
                "\t\"productSubtype\": \"100040\",\n" +
                "\t\"gateway\": \"5513\",\n" +
                "\t\"status\": \"1\"\n" +
                "}";

        AutorenewRequest autorenewRequest = AutorenewRequest.transferFromMessage(JSON.parseObject(msg, Map.class));
        autorenewRequest.setDutType(422);
        dutManager.updateDutUserByAutoRenewRequest(autorenewRequest);
    }

    @Test
    public void testDutAutoRenewJobQuery() {
//        Timestamp startTime = DateHelper.getTimestamp("2018-08-15 00:00:00");
//        Timestamp deadline = DateHelper.getTimestamp("2019-08-16 00:00:00");

        int tableCnt = tableNum;
        String shardTableTemplate = "boss_dut_user_new_%02d";
        if (cloudConfig.getBooleanProperty(READ_SHARDING_DB_CLUSTER, true)) {
            tableCnt = 256;
            shardTableTemplate = "boss_dut_user_new_%03d";
        }
        long total = 0;
        List<AutorenewDutConfig> autorenewDutConfigList = autorenewDutConfigService
            .findAutorenewDutConfig(AutorenewDutConfig.JOB_TYPE_COMMON, AutorenewDutConfig.STATUS_VALID, AgreementTypeEnum.AUTO_RENEW.getValue());
        for (AutorenewDutConfig autorenewDutConfig : autorenewDutConfigList) {
            String beginTimeStr = DateHelper.getFormatDate(DateHelper.caculateTime(DateHelper.getDateTime(),
                NEXT_DAY_OFFSET + 1, Constants.PRODUCT_PERIODUNIT_DAY), DateHelper.SIMPLE_PATTERN) + " 00:00:00";
            Timestamp beginTime = DateHelper.getDateTime(DateHelper.getDateFromStr(beginTimeStr, DateHelper.MOST_COMMON_PATTERN));

            Timestamp deadline = DateHelper.caculateTime(beginTime, autorenewDutConfig.getAdvanceDays(), Constants.PRODUCT_PERIODUNIT_DAY);
            Timestamp startTime = DateHelper.caculateTime(deadline, START_TIME_OFFSET, Constants.PRODUCT_PERIODUNIT_DAY);

            List<Integer> excludeDutTypes = getExcludeDutTypes(autorenewDutConfig);
            System.out.println("#######################################################################");
            for (int idx = 0; idx < tableCnt; idx++) {
                //找出要续费的会员，到期时间在startTime和deadline之间
                List<DutUserNew> dutUserList =
                    dutManager.getSyncAutoRenewListByTblIndex(AgreementTypeEnum.AUTO_RENEW.getValue(), autorenewDutConfig.getVipType(), startTime, deadline, excludeDutTypes, String.format(shardTableTemplate, idx));
                System.out.println(String.format(shardTableTemplate, idx) + " 记录:" + dutUserList.size());
                total += dutUserList.size();
                dutUserList.clear();
            }
        }
        System.out.println("大陆自动代扣用户总数:" + total);
    }

    @Test
    public void testUpgradeJobQuery() {
        // 读取代扣配置表,查找有效配置信息
        List<AutorenewDutConfig> autorenewDutConfigList = autorenewDutConfigService
            .findAutorenewDutConfig(AutorenewDutConfig.JOB_TYPE_UPGRADE, AutorenewDutConfig.STATUS_VALID, AgreementTypeEnum.AUTO_RENEW.getValue());

        if (CollectionUtils.isEmpty(autorenewDutConfigList)) {
            return;
        }
        long total = 0;
        for (AutorenewDutConfig autorenewDutConfig : autorenewDutConfigList) {
            String beginTimeStr = DateHelper.getFormatDate(DateHelper.caculateTime(DateHelper.getDateTime(),
                ONE_DAY_OFFSET, Constants.PRODUCT_PERIODUNIT_DAY), DateHelper.SIMPLE_PATTERN) + " 00:00:00";
            Timestamp beginTime = DateHelper.getDateTime(DateHelper.getDateFromStr(beginTimeStr, DateHelper.MOST_COMMON_PATTERN));
            Timestamp deadline = DateHelper.caculateTime(beginTime, autorenewDutConfig.getAdvanceDays(), Constants.PRODUCT_PERIODUNIT_DAY);
            Timestamp startTime = DateHelper.caculateTime(deadline, START_TIME_OFFSET, Constants.PRODUCT_PERIODUNIT_DAY);

            List<Integer> excludeDutTypes = getExcludeDutTypes(autorenewDutConfig.getVipType(), autorenewDutConfig.getAgreementType());

            int tableCnt = tableNum;
            String shardTableTemplate = "boss_dut_user_new_%02d";
            if (cloudConfig.getBooleanProperty(READ_SHARDING_DB_CLUSTER, true)) {
                tableCnt = 256;
                shardTableTemplate = "boss_dut_user_new_%03d";
            }

            for (int idx = 0; idx < tableCnt; idx++) {
                //找出要续费的会员，到期时间在startTime和deadline之间
                int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
                List<DutUserNew> dutUserList = dutManager.getSyncUpgradeAutoRenewListByTblIndex(agreementType, autorenewDutConfig.getSourceVipType(),
                    autorenewDutConfig.getVipType(), startTime, deadline, excludeDutTypes, String.format(shardTableTemplate, idx));
                total += dutUserList.size();
            }
        }
        System.out.println("升级自动代扣用户总数:" + total);
    }

    private List<Integer> getExcludeDutTypes(AutorenewDutConfig autorenewDutConfig) {
        Integer agreementType = autorenewDutConfig.getAgreementType();
        List<Integer> excludeDutTypeList = Lists.newArrayList();
        List<Integer> mobileDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(autorenewDutConfig.getVipType(), agreementType, PaymentDutType.PAY_CHANNEL_MOBILE);
        List<Integer> thirdDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannelType(autorenewDutConfig.getVipType(), agreementType, PaymentDutType.PAY_CHANNEL_TYPE_THIRD_DUT_PAY);
        excludeDutTypeList.addAll(mobileDutTypeList);
        excludeDutTypeList.addAll(thirdDutTypeList);
        return excludeDutTypeList;
    }

    private List<Integer> getExcludeDutTypes(Long vipType, Integer agreementType) {
        List<Integer> excludeDutTypeList = Lists.newArrayList();
        List<Integer> mobileDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannel(vipType, agreementType, PaymentDutType.PAY_CHANNEL_MOBILE);
        List<Integer> thirdDutTypeList = autoRenewDutTypeManager
            .getDutTypeListByVipTypeAndPayChannelType(vipType, agreementType, PaymentDutType.PAY_CHANNEL_TYPE_THIRD_DUT_PAY);
        excludeDutTypeList.addAll(mobileDutTypeList);
        excludeDutTypeList.addAll(thirdDutTypeList);
        return excludeDutTypeList;
    }

    @Test
    public void testStream() {

        Long uid = 1239954608L;
        Long vipType = 1L;
        List<Integer> bindList = Arrays.asList(1, 23);
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        List<DutUserNew> dutUserNewList = dutManager.findAllByUserIdAndVipType(1239954608L, agreementType,1L);
        System.out.println(dutUserNewList.stream().filter(DutUserNew::isAutoRenewUser).map(DutUserNew::getId).collect(Collectors.toList()));
        System.out.println(dutUserNewList.stream().anyMatch(DutUserNew::isAutoRenewUser));

        System.out.println(bindList.stream().map(dutType -> userAgreementService.getByDutTypeAndVipType(uid, dutType, vipType)).filter(dutUserNew -> dutUserNew != null).collect(Collectors.toList()));
    }

    @Test
    public void testGetSupportDirectOpenDutTypes() {
        Long uid = 1239954608L;
        Long vipType = 1L;
        int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();
        Assert.assertNotNull(dutManager.getSupportDirectOpenDutTypes(uid, vipType, agreementType));
    }

    @Test
    public void testDecimalFormat() {
        DecimalFormat df = new DecimalFormat("#.#");
        df.setRoundingMode(RoundingMode.HALF_UP);
        System.out.println(df.format(1234.15));
        System.out.println(df.format(123.15));
        System.out.println(df.format(12.15));
        System.out.println(df.format(1.15));
        System.out.println(df.format(0.15));
        System.out.println(df.format(0.5));
        System.out.println(df.format(9.9));
        System.out.println(df.format(15.0));
        System.out.println(df.format(0.01));
        System.out.println(df.format(0.1));
        System.out.println(df.format(1.0));
        System.out.println(df.format(1));
    }

}
