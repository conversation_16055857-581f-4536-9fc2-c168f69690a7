package com.qiyi.boss.service;

import com.qiyi.boss.utils.DateHelper;
import com.qiyi.vip.trade.autorenew.domain.AutorenewPreDutRecord;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2020-6-28
 * Time: 23:15
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
@WebAppConfiguration
@ActiveProfiles("dev")
public class AutorenewPreDutRecordServiceTest {

	@Resource
	private AutorenewPreDutRecordService autorenewPreDutRecordService;

	@Test
	public void test() {
		AutorenewPreDutRecord autorenewPreDutRecordQuery = AutorenewPreDutRecord.builder()
				.id(2L)
				.userId(1235692734L)
				.build();
		System.out.println(autorenewPreDutRecordService.list(autorenewPreDutRecordQuery));

		AutorenewPreDutRecord autorenewPreDutRecord = AutorenewPreDutRecord.builder()
				.userId(1595581429L)
				.vipType(1L)
				.dutType(879)
				.amount(3)
				.fee(5800)
				.status(7)
				.eventType(1)
				.groupKey("1_6_1_B")
				.errorCode("error_code")
				.centerOrderCode("center_order_code")
				.contractCode("contractCode")
				.createTime(DateHelper.getCurrentTime())
				.build();
		autorenewPreDutRecordService.save(autorenewPreDutRecord);
		System.out.println(autorenewPreDutRecordService.list(autorenewPreDutRecordQuery));
	}

}
