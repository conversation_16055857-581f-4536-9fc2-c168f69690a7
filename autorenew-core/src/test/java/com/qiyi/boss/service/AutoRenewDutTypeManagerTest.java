package com.qiyi.boss.service;

import com.google.common.collect.Lists;
import com.qiyi.boss.Constants;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.service.impl.DutManager;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 * Author: liuwanqiang
 * Date: 2017-06-08
 * Time: 17:29
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:applicationContext.xml"})
public class AutoRenewDutTypeManagerTest {

	static {
		System.setProperty("spring.profiles.active", "dev");
	}

	@Resource
	private AutoRenewDutTypeManager autoRenewDutTypeManager;
	@Resource
	private DutManager dutManager;

    public static final int agreementType = AgreementTypeEnum.AUTO_RENEW.getValue();

    @Before
	public void before() {
		assert autoRenewDutTypeManager != null;
	}

	@Test
	public void testGetTvDutTypeList() {

        System.out.println(autoRenewDutTypeManager.getDutTypeListByVipType(Constants.VIP_USER_TV, agreementType).toString());
		System.out.println(autoRenewDutTypeManager.getDutTypeListByVipType(Constants.VIP_USER_TV, agreementType).contains(DutUserNew.TV_BIND_TYPE_ALIPAY));
		System.out.println(autoRenewDutTypeManager.getDutTypeListByVipType(Constants.VIP_USER_TV, agreementType).contains(DutUserNew.TV_BIND_TYPE_WECHAT));
	}

	@Test
	public void testGetDutTypeListByVipTypeAndPayChannel() {
		List<Integer> autoRenewDutTypes = autoRenewDutTypeManager.getDutTypeListByVipTypeAndPayChannel(Constants.VIP_USER_SUPER, agreementType, PaymentDutType.PAY_CHANNEL_WECHAT);
		Assert.assertNotNull(autoRenewDutTypes);
	}

	@Test
	public void testGetVipTypeByDutType() {
		System.out.println(autoRenewDutTypeManager.getVipTypeByDutType(DutUserNew.TV_BIND_TYPE_WECHAT));

		List<Integer> commonDutTypeList = Lists.newArrayList();
		commonDutTypeList.addAll(autoRenewDutTypeManager.getDutTypeListByVipTypeAndPayChannelType(1L, agreementType, PaymentDutType.PAY_CHANNEL_TYPE_VIP_DUT_PAY));
		commonDutTypeList.removeAll(autoRenewDutTypeManager.getDutTypeListByVipTypeAndPayChannel(1L, agreementType, PaymentDutType.PAY_CHANNEL_MOBILE));
		System.out.println(commonDutTypeList);
	}

	@Test
	public void testGetByDutType() {
	    AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(1);
        Assert.assertNotNull(autoRenewDutType);

		List<Integer> dutTypes = Arrays.asList(1, 3, 6);
		List<AutoRenewDutType> autoRenewDutTypes = autoRenewDutTypeManager.getByDutTypes(dutTypes);
		Assert.assertNotNull(autoRenewDutTypes);
		Assert.assertEquals(3, autoRenewDutTypes.size());
	}

    @Test
    public void testFindPureSignDutType() {
		Long userId = 1234567890L;
		Long sourceVipType = null;
		Long vipType = 1L;
		Integer payChannel = 2;
		Integer amount = 1;

		Assert.assertNotNull(dutManager.findPureSignDutType(userId, sourceVipType, vipType, agreementType, payChannel, amount));
		Assert.assertEquals(331, dutManager.findPureSignDutType(userId, sourceVipType, vipType, agreementType, payChannel, amount).intValue());
	}

	@Test
	public void testGetPriority() {
		Long userId = 1468911713L;
		Long sourceVipType = null;
		Long vipType = 1L;
		Integer payChannel = 1;
		Integer amount = 1;

		Assert.assertNotNull(dutManager.getPriority(userId, sourceVipType, vipType, agreementType, payChannel, amount));
		Assert.assertEquals(1, dutManager.getPriority(userId, sourceVipType, vipType, agreementType, payChannel, amount).intValue());
	}

}
