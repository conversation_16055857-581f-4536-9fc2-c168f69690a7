package com.qiyi.boss.utils;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.Test;

import java.util.UUID;

import com.qiyi.boss.dto.DutDiscountTemplate;
import com.qiyi.boss.dto.UserAgreementPerformanceRecord;

import static org.junit.Assert.assertNotNull;

/**
 * Created at: 2021-04-23
 *
 * <AUTHOR>
 */
public class JacksonUtilsTest {

    @Test
    public void name() {
        String temp = "{\"id\":3,\"batchNo\":\"984cb8a79829f10c\",\"vipType\":1,\"minAmount\":1,\"maxAmount\":1,\"value\":400,\"minimumLimit\":1900,\"status\":1,\"createTime\":1610527261000,\"updateTime\":1610527261000,\"available\":true}";
        DutDiscountTemplate dutDiscountTemplate1 = JacksonUtils.parseObject(temp, DutDiscountTemplate.class);
        assertNotNull(dutDiscountTemplate1);
        DutDiscountTemplate dutDiscountTemplate2 = JacksonUtils.parseObject(temp, new TypeReference<DutDiscountTemplate>() {});
        assertNotNull(dutDiscountTemplate2);
    }

    @Test
    public void toJsonString() {
        UserAgreementPerformanceRecord record = new UserAgreementPerformanceRecord();
        record.setDutStatus(1);
        record.setPrice(100);
        record.setDutTime(DateHelper.getCurrentTime());
        System.out.println(JacksonUtils.toJsonString(record));
        System.out.println(JSON.toJSONString(record));
    }

    @Test
    public void validJson() {
        String jsonStr1 = "";
        String jsonStr2 = "2345tgw456";
        String jsonStr3 = "{}";
        String jsonStr4 = "[]";
        String jsonStr5 = "{\"key\": 1}";
        String jsonStr6 = "[{\"key\": 1}]";
        String jsonStr7 = "12456";
        System.out.println(JacksonUtils.parseMap(jsonStr1));
        System.out.println(JacksonUtils.validJson(jsonStr1));
        System.out.println(JacksonUtils.validJson(jsonStr2));
        System.out.println(JacksonUtils.validJson(jsonStr3));
        System.out.println(JacksonUtils.validJson(jsonStr4));
        System.out.println(JacksonUtils.validJson(jsonStr5));
        System.out.println(JacksonUtils.validJson(jsonStr6));
        System.out.println(JacksonUtils.validJson(jsonStr7));
    }

    @Test
    public void decode() {
        System.out.println(Encode.decode("6a6462633a6d7973716c3a2f2f626a2e6875697975616e6f726465722e722e716979692e64623a383535342f6875697975616e5f6f726465723f757365556e69636f64653d7472756526636861726163746572456e636f64696e673d5554462d3826757365723d6875697975616e5f6f726465722670617373776f72643d7465777074717157"));
        System.out.println(Encode.decode("6875697975616e5f6f72646572"));
        System.out.println(Encode.decode("7465777074717157"));

        System.out.println(Encode.encode("*************************************************************************************************"));
        System.out.println(Encode.encode("vip_trade_order"));
        System.out.println(Encode.encode("qptg798%^$#*/QTP=!390."));

        System.out.println(UUID.randomUUID().toString().replace("-", "").substring(16, 32));
    }
}