package com.qiyi.boss.utils;

import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import com.qiyi.boss.enums.AgreementTypeEnum;

import static org.junit.Assert.*;

/**
 * Created at: 2021-06-24
 *
 * <AUTHOR>
 */
public class SignKeyGeneratorTest {

    @Test
    public void test() throws Exception {
        Long uid = 1480407671L;
        String signKey = SignKeyGenerator.getSignKey(uid, AgreementTypeEnum.AUTO_RENEW.getValue());
        assertNotNull(signKey);
        assertEquals(22, signKey.length());

        List<Thread> threads = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            Thread thread = new Thread(() -> {
                String oneKey = SignKeyGenerator.getSignKey(uid, AgreementTypeEnum.AUTO_RENEW.getValue());
                System.out.println("threadName: " + Thread.currentThread().getName() + ", " + oneKey);
                assertEquals(22, signKey.length());
            });
            threads.add(thread);
        }
        for (Thread thread : threads) {
            thread.start();
            thread.join();
        }
        System.out.println("finished");
    }
}