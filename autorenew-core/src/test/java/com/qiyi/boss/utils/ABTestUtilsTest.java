package com.qiyi.boss.utils;

import org.junit.Test;

import java.util.Arrays;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: l<PERSON>wanqiang
 * Date: 2020-6-25
 * Time: 17:39
 */
public class ABTestUtilsTest {

	@Test
	public void testGetIndex() {

		String sample = "1235692734";
		System.out.println(ABTestUtils.getIndex(sample));

		System.out.println("=================");

		int[] temp = new int[100];
		Arrays.stream(temp).limit(100).forEach(p -> {
			p = 0;
		});
		for (int i = 0; i < 1000; i++) {
			int res = ABTestUtils.getIndex(String.valueOf(i));
			if (temp[res] == 0) {
				temp[res] = 1;
			} else {
				temp[res] += 1;
			}
		}

		System.out.println("=================");

		int[] temp2 = new int[600000];
		for (int p = 0; p < 600000; p++) {
			long random = Math.round(Math.random() * 1000000000);
			temp2[p] = ABTestUtils.getIndex(String.valueOf(random));
		}
		System.out.println("0~44: " + Arrays.stream(temp2).filter(value -> value >= 0).filter(value -> value <= 44).count());
		System.out.println("45~89: " + Arrays.stream(temp2).filter(value -> value >= 45).filter(value -> value <= 89).count());
		System.out.println("90~99: " + Arrays.stream(temp2).filter(value -> value >= 90).filter(value -> value <= 99).count());
	}
}
