package com.qiyi.boss.utils;

import com.iqiyi.solar.config.client.CloudConfig;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * created 2019/4/1 - 11:49
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext*.xml"})
@WebAppConfiguration
public class AutoRenewConfigTest {

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Resource
    private AutoRenewConfig autoRenewConfig;

    @Resource(name = "cloudHystrixConfig")
    private CloudConfig cloudHystrixConfig;


    @Test
    public void testCloudConfig() {
        Assert.assertEquals(autoRenewConfig.getAutoRenewTaskServerNum().intValue(), 1);
    }

    @Test
    public void testCloudHystrixConfig() {
        String key = "hystrix.command.AutoRenewMarketingProxy.execution.isolation.thread.timeoutInMilliseconds";
        Assert.assertTrue(cloudHystrixConfig.getIntProperty(key,0) == 1000);
    }

    @Test
    public void testGetAutorenewTaskRetryExecTime() {
        String key = "autorenew.task.retry.exec.time";
        System.out.println(CloudConfigUtil.getAutorenewTaskRetryExecTime());
        Assert.assertTrue("07:00:00".equals(CloudConfigUtil.getAutorenewTaskRetryExecTime()));
    }

}


