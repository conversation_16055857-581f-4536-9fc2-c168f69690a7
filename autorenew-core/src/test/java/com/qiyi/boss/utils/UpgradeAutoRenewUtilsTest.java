package com.qiyi.boss.utils;

import org.junit.Test;

/**
 * @author: guojing
 * @date: 2024/4/1 15:18
 */
public class UpgradeAutoRenewUtilsTest {

    @Test
    public void calcUpgradeInfo() {
//        Timestamp fromDeadline = DateHelper.getTimestamp("2024-10-01 23:59:59");
//        Timestamp toDeadline = DateHelper.getTimestamp("2024-04-01 23:59:59");
//        boolean dutByMonth = UpgradeAutoRenewUtils.dutByMonth(fromDeadline, toDeadline);
//        Assert.assertTrue(dutByMonth);
//
//        toDeadline = DateHelper.getTimestamp("2024-03-30 23:59:59");
//        dutByMonth = UpgradeAutoRenewUtils.dutByMonth(fromDeadline, toDeadline);
//        Assert.assertTrue(dutByMonth);
//
//        fromDeadline = DateHelper.getTimestamp("2024-04-30 23:59:59");
//        toDeadline = DateHelper.getTimestamp("2024-04-01 23:59:59");
//        dutByMonth = UpgradeAutoRenewUtils.dutByMonth(fromDeadline, toDeadline);
//        boolean dutByDay = UpgradeAutoRenewUtils.dutByDay(fromDeadline, toDeadline);
//        int upgradeDays = UpgradeAutoRenewUtils.calcDays(fromDeadline, toDeadline);
//        Assert.assertFalse(dutByMonth);
//        Assert.assertTrue(dutByDay);
//        System.out.println(upgradeDays);
//
//        fromDeadline = DateHelper.getTimestamp("2024-04-30 23:59:59");
//        toDeadline = DateHelper.getTimestamp("2024-03-30 23:59:59");
//        dutByMonth = UpgradeAutoRenewUtils.dutByMonth(fromDeadline, toDeadline);
//        dutByDay = UpgradeAutoRenewUtils.dutByDay(fromDeadline, toDeadline);
//        upgradeDays = UpgradeAutoRenewUtils.calcDays(fromDeadline, toDeadline);
//        Assert.assertFalse(dutByMonth);
//        Assert.assertTrue(dutByDay);
//        System.out.println(upgradeDays);
    }
}