package com.qiyi.boss.utils;

import org.junit.Assert;
import org.junit.Test;

import com.qiyi.boss.dto.DiscountAgreementTip;
import com.qiyi.boss.dto.RenewGiftVO;
import com.qiyi.boss.dto.UserRenewDiscountInfo;


/**
 * @author: guojing
 * @date: 2024/1/8 16:01
 */
public class TipsUtilsTest {

    @Test
    public void buildRenewDiscountTips() {
        UserRenewDiscountInfo userRenewDiscountInfo = UserRenewDiscountInfo.builder()
            .amount(1)
            .renewPrice(1500)
            .contractPrice(2500)
            .totalPeriods(12)
            .usedPeriods(3)
            .remainPeriods(9)
            .build();
        DiscountAgreementTip discountAgreementTip = DiscountAgreementTip.newDefaultTip();
        RenewGiftVO renewGiftVO = TipsUtils.buildRenewDiscountTips(discountAgreementTip, userRenewDiscountInfo, "黄金VIP会员连续包月");
        Assert.assertNotNull(renewGiftVO);
        System.out.println(JacksonUtils.toJsonString(renewGiftVO));
    }
}