package com.qiyi.boss.utils;

import com.qiyi.boss.Constants;
import com.qiyi.boss.model.UserInfo;
import com.qiyi.vip.trade.autorenew.config.CloudConfigUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.servlet.http.HttpServletRequest;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext*.xml"})
public class PassportApiTest {

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    private static final long CLOSED_UID = 1003351662L;
    private static final long NORMAL_UID = **********L;
    private static final String NON_EXISTENT_USERNAME = "121212343434565656787878909090";

    @Autowired
    private PassportApi passportApi;

//    @Test
//    public void get_user_id_by_phone() {
//        Long uid = passportApi.getUserIdByUsername("***********");
//        assertThat(uid, is(NORMAL_UID));
//    }
//
//    @Test
//    public void when_get_user_id_by_phone_then_not_existed() {
//        Long uid = passportApi.getUserIdByUsername(NON_EXISTENT_USERNAME);
//        assertNull(uid);
//    }
//
//    @Test
//    public void user_is_closed() {
//        assertTrue(passportApi.isAccountClosed(CLOSED_UID));
//    }
//
//    @Test
//    public void user_not_closed() {
//        assertFalse(passportApi.isAccountClosed(NORMAL_UID));
//    }

    @Test
    public void testGetUserInfoByAuthCookie() {
        String authCookie = "54m3Jm3eXb26bLskirCm11UbX0po7byQwqBcLm2m1wlkDwdWxr9rA0AtLx5IvdyPkVjhKOfe8";
        HttpServletRequest  mockedRequest = mock(HttpServletRequest.class);
        when(mockedRequest.getParameter(Constants.PASSPORT_USERID_COOKIE_KEY)).thenReturn(authCookie);
        when(mockedRequest.getHeader("X-Forwarded-For")).thenReturn("127.0.0.1");
        when(mockedRequest.getHeader("x-real-ip")).thenReturn("127.0.0.1");
        UserInfo userInfo = passportApi.getUserByPassport(mockedRequest);
        Assert.assertTrue("***********".equals(userInfo.getId().toString()));
    }

    @Test
    public void testGetVipUserWithOpenIdsByUid() {
        Long id = **********L;
        UserInfo user = passportApi.getVipUserWithOpenIdsByUid(id);
        System.out.println(user.toString());
        Assert.assertNotNull(user);
    }

    @Test
    public void testGetUserIdByUsername() {
        String phone = "***********";
        Long id = passportApi.getUserIdByUsername(phone);
        Assert.assertTrue("**********".equals(id.toString()));
    }

    @Test
    public void test() {
        Long id = 1480409402L;
        Assert.assertTrue(passportApi.isAccountClosed(id));

    }

    @Test
    public void testOperate() {
        Assert.assertTrue(CloudConfigUtil.shouldSendAutoRenewSignRelationChangeMQ(0));
    }

//    @Test
//    public void testMockito() {
//        // mock creation
//        List mockedList = mock(List.class);
//
////        // using mock object - it does not throw any "unexpected interaction" exception
//        mockedList.add("one");
//        mockedList.clear();
////
////        // selective, explicit, highly readable verification
//        verify(mockedList).add("one");
//        verify(mockedList).clear();
////
////        mockedList.add("one");
//        when(mockedList.get(0)).thenReturn(0);
//        when(mockedList.get(1)).thenThrow(new IndexOutOfBoundsException());
//
//        System.out.println(mockedList.get(0));
////        mockedList.get(1);
//
//
//        //stubbing using built-in anyInt() argument matcher
//        when(mockedList.get(anyInt())).thenReturn("element");
//
//        System.out.println(mockedList.get(999));
//        //you can also verify using an argument matcher
//        verify(mockedList, atLeastOnce()).get(anyInt());
//    }
//
//    /**
//     * 验证方法的调用顺序.
//     */
//    @Test
//    public void testInvokeOrder() {
//        List<String> firstMock = mock(List.class);
//        List<String> secondMock = mock(List.class);
//
//        firstMock.add("was called first");
//        firstMock.add("was called first");
//        secondMock.add("was called second");
//        secondMock.add("was called third");
//
//        /* 如果mock方法的调用顺序和InOrder中verify的顺序不同，那么测试将执行失败。 */
//
//        InOrder inOrder = inOrder(firstMock, secondMock);
//        inOrder.verify(firstMock, times(2)).add("was called first");
//
//        inOrder.verify(secondMock).add("was called second");
//        inOrder.verify(secondMock).add("was called third");
//        // 因为在secondMock.add("was called third")之后已经没有多余的方法调用了。
//        inOrder.verifyNoMoreInteractions();// 表示此方法调用后再没有多余的交互
//    }
//
//    @Test
//    public void argumentMatchersTest2() {
//        Map<Integer, String> mapMock = mock(Map.class);
//        when(mapMock.put(anyInt(), anyString())).thenReturn("world");
//        mapMock.put(1, "hello");
//        // 注：在最后的验证时如果只输入字符串”hello”是会报错的，必须使用Matchers 类内建的eq方法。
//        // 如果将anyInt()换成1进行验证也需要用eq(1)。
//        verify(mapMock).put(anyInt(), eq("hello"));
//    }
}