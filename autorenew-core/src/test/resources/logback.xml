<?xml version="1.0" encoding="UTF-8"?>

<configuration>
    <appender name="ORDER_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/autorenew/order.log</file>
        <encoder>
            <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss}] [%C{1}:%M:%L] %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/autorenew/order.log.%d{yyyy-MM-dd}</fileNamePattern>
        </rollingPolicy>
    </appender>
    <appender name ="ASYNC_ORDER_LOG" class= "ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold >0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref ="ORDER_LOG"/>
    </appender>
    <appender name="CMCC_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/autorenew/cmcc.log</file>
        <encoder>
            <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss}] [%C{1}:%M:%L] %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/autorenew/cmcc.log.%d{yyyy-MM-dd}</fileNamePattern>
        </rollingPolicy>
    </appender>
    <appender name ="ASYNC_CMCC_LOG" class= "ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold >0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref ="CMCC_LOG"/>
    </appender>
    <appender name="COMMON_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/autorenew/common.log</file>
        <encoder>
            <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss}] [%C{1}:%M:%L] %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/autorenew/common.log.%d{yyyy-MM-dd}</fileNamePattern>
        </rollingPolicy>
    </appender>
    <appender name ="ASYNC_COMMON_LOG" class= "ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold >0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref ="COMMON_LOG"/>
    </appender>
    <appender name="SMS_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/autorenew/sms.log</file>
        <encoder>
            <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss}] [%C{1}:%M:%L] %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/autorenew/sms.log.%d{yyyy-MM-dd}</fileNamePattern>
        </rollingPolicy>
    </appender>
    <appender name ="ASYNC_SMS_LOG" class= "ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold >0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref ="SMS_LOG"/>
    </appender>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <Target>System.out</Target>
        <encoder>
            <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss}] [%C{1}:%M:%L] %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>
    <appender name="DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/autorenew/autorenew.log</file>
        <encoder>
            <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss}] [%C{1}:%M:%L] %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/autorenew/autorenew.log.%d{yyyy-MM-dd}</fileNamePattern>
        </rollingPolicy>
    </appender>
    <appender name ="ASYNC_DAILY_ROLLING_FILE" class= "ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold >0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref ="DAILY_ROLLING_FILE"/>
    </appender>
    <appender name="ERROR_DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/autorenew/error.log</file>
        <encoder>
            <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss}] [%C{1}:%M:%L] %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/autorenew/error.log.%d{yyyy-MM-dd_HH}</fileNamePattern>
        </rollingPolicy>
    </appender>
    <appender name ="ASYNC_ERROR_DAILY_ROLLING_FILE" class= "ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold >0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref ="ERROR_DAILY_ROLLING_FILE"/>
    </appender>
    <appender name="CHARGE_VOD" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>/data/logs/autorenew/charge/charge_vod.log</File>
        <encoder>
            <pattern>%m%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/autorenew/charge/charge_vod_%d{yyyyMMdd}.log</fileNamePattern>
        </rollingPolicy>
    </appender>
    <appender name ="ASYNC_CHARGE_VOD" class= "ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold >0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref ="CHARGE_VOD"/>
    </appender>
    <appender name="MONITOR_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/autorenew/monitor.log</file>
        <encoder>
            <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss}] [%C{1}:%M:%L] %m%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/autorenew/monitor.log.%d{yyyy-MM-dd_HH}</fileNamePattern>
        </rollingPolicy>
    </appender>
    <appender name ="ASYNC_MONITOR_LOG" class= "ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold >0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref ="MONITOR_LOG"/>
    </appender>

    <logger name="org.apache.commons" level="ERROR"/>
    <logger name="order" level="DEBUG" additivity="false">
        <appender-ref ref="ASYNC_ORDER_LOG"/>
    </logger>
    <logger name="cmcc" level="DEBUG" additivity="false">
        <appender-ref ref="ASYNC_CMCC_LOG"/>
    </logger>
    <logger name="common" level="DEBUG" additivity="false">
        <appender-ref ref="ASYNC_COMMON_LOG"/>
        <!--<appender-ref ref="MAIL"/>-->
    </logger>

    <logger name="sms" level="DEBUG" additivity="false">
        <appender-ref ref="ASYNC_SMS_LOG"/>
    </logger>
    <logger name="charge_log" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_CHARGE_VOD"/>
    </logger>
    <logger name="monitor" level="DEBUG" additivity="false">
        <appender-ref ref="ASYNC_MONITOR_LOG"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_DAILY_ROLLING_FILE"/>
        <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
    </root>
</configuration>
