host=TEST-Linux-autorenew-api-1.0.291

mail.user.name=vipmessage
mail.user.address=<EMAIL>
mail.user.token=8u36q9d63g96hqlr
mail.error.contact=l<PERSON><PERSON><PERSON>@qiyi.com,yang<PERSON><PERSON>@qiyi.com,l<PERSON><PERSON><PERSON><PERSON>@qiyi.com,zhougu<PERSON><EMAIL>

internal.pay.key=123456

qiyi.pay.key=**********
qiyi.pay.key.new=d65f3fb538374715a557937cb3e8612f
pay.key.qibubble_audio=0c95208a4664432e887a6b5f7ecb2df0
pay.center.dut.mobile=http://inter.pay.qiyi.domain/dut/gateway.action
pay.centerpay.gateway.domain=http://inter.pay.qiyi.domain/pay/gateway.action
pay.centerpay.frontend.domain=http://inter.pay.qiyi.domain/pay-web-frontend/frontend/pay
pay.centerpay.close.order.url=http://inter.pay.qiyi.domain/pay-web-frontend/frontend/closeOrder

boss.autorenew.key=**********
partner.autorenew.key=**********
partner.sign.page=https://vip.iqiyi.com/html5VIP/activity/partner_sign/index.html

#BOSS\u652F\u4ED8\u5BC6\u94A5
boss.pay.key=123456
boss.pay.key.new=123456

#\u8D26\u6237\u4E2D\u5FC3\u8D60\u5145\u503C\u5947\u8C46\u7B7E\u540Dkey
account.pay.key=**********

account.dutquery.url=http://inter-test.account.qiyi.domain/pay/dut/query.action
account.dutsign.url=http://account.iqiyi.com/pay/alipaydeduct/sign.action
account.dutquery.key=123456
account.unbind.key=f1a34dc464325d35f6dc90c1f3

promotion.process.key=iua7#6UDH5%sa678z
promotion.process.url=http://*************:8080/promotion/internal/common/process.action

passport.query.isclosed.url=http://passport.qiyi.domain/apis/inner/user_info.action

autorenew.sms.remind.task.execute.time=10:00:00

shortUrl_wappay_prefix=http://**************/t/

vip.freeorder.url=http://i.vip.qiyi.domain/api/internal/free-pay/dopay.action
vip.freeorder.key=123456

#\u514D\u8D39\u4F1A\u5458\u63A5\u5165 \u8BF7\u6C42\u5730\u5740
vip.freepay.url=http://act.vip.qiyi.domain/freepay/api/send
#\u514D\u8D39\u4F1A\u5458\u63A5\u5165 \u7B7E\u540Dkey
vip.freepay.key=2dc89f374cab44709163b69839241748
#\u514D\u8D39\u4F1A\u5458\u63A5\u5165 partner
free.pay.partner=vip-trade

#VIP\u4FE1\u606F\u540C\u6B65Passport URL
notify_url_passport=http://vip.passport.qiyi.domain/apis/vip/updateVipInfo.action

encoding=utf-8
#boss-api \uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0123\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u013C\uFFFD\uFFFD\uFFFD\uFFFD
boss.api.config=${boss.api.config}

account.unSign.url=http://inter.account.qiyi.domain/pay/dut/unSignNoLogin.action

isDecoupledWithProductTable=true

auto_api_biz_config_file=test_auto_api_biz_config_file

shortUrlCancelAutoRenew.key=**********

app.name=autorenew-api
module.name=autorenew-api

#\u652F\u4ED8\u4E2D\u5FC3\u7EAF\u7B7E\u7EA6\u63A5\u53E3url(\u5947\u827A\u57DF\u540D)
pay.dut.bind.url=http://pay.iqiyi.com/dut/bind.action
pay.dut.bind.url.https=https://pay.iqiyi.com/dut/bind.action
inter.pay.dut.bind.url=http://inter.pay.qiyi.domain/dut/bind.action
#\u652F\u4ED8\u4E2D\u5FC3\u7EAF\u7B7E\u7EA6\u63A5\u53E3url(\u94F6\u6CB3\u57DF\u540D)
pay.yinhe.dut.bind.url=http://pay-test.iqiyi.com/dut/bind.action
#\u652F\u4ED8\u4E2D\u5FC3\u7EAF\u7B7E\u7EA6\u5F02\u6B65\u901A\u77E5\u5730\u5740(\u5947\u827A\u57DF\u540D)
pay.dut.bind.notify.url=http://*************:8202/services/notifyAutoRenew.action
#\u652F\u4ED8\u4E2D\u5FC3\u7EAF\u7B7E\u7EA6\u5F02\u6B65\u901A\u77E5\u5730\u5740(\u94F6\u6CB3\u57DF\u540D)
pay.yinhe.dut.bind.notify.url=http://*************:8202/services/notifyAutoRenew.action
#\u652F\u4ED8\u4E2D\u5FC3\u514D\u5BC6\u652F\u4ED8\u7EAF\u7B7E\u7EA6\u5F02\u6B65\u901A\u77E5\u5730\u5740(\u5947\u827A\u57DF\u540D)
pay.passwordfree.bind.notify.url=http://serv.vip.qiyi.domain/services/autorenew/passwordFree/pureSign/notify
#\u626B\u7801\u5F00\u901A\u81EA\u52A8\u7EED\u8D39,userAgent\u4E0D\u652F\u6301\u7684\u8DF3\u8F6C\u5730\u5740
ua.not.support.page=http://vip.iqiyi.com/actcodeOther.html
#\u626B\u7801\u5F00\u901A\u81EA\u52A8\u7EED\u8D39,\u6210\u529F\u7684\u5730\u5740
open.autorenew.success.page=http://vip.iqiyi.com/actcodeSuccess.html
#\u626B\u7801\u5F00\u901A\u81EA\u52A8\u7EED\u8D39,\u5931\u8D25\u7684\u5730\u5740
open.autorenew.fail.page=http://vip.iqiyi.com/actcodeFail.html

autorenew.dataservice.serverAddr=http://*************:8089
autorenew.dataservice.connectTimeout=500
autorenew.dataservice.readTimeout=10000
autorenew.dataservice.invoke.channel=qiyue-web
autorenew.dataservice.invoke.signkey=f24d31f28ad963c6f48679c668cc092d

# iqiyi cloud config
cloud.config.app.name=vip-autorenew
cloud.config.app.env=test
cloud.config.app.region=default

dutPayNew.url=http://i.vip.qiyi.domain/pay/dutPay-New.action

query.boss.vip.info=http://serv.vip.qiyi.domain/api/internal/auth/vip_info.action
query.vip.infos.url=http://vinfo.vip.qiyi.domain/internal/users/{uid}/vip_infos
huiyuan.vipinfo.url=http://vinfo.vip.qiyi.domain/internal/vip_users

dut.bind.returnUrl=http://serv.vip.iqiyi.com/services/dutBindReturn.action
wechat.notice.url=http://serv.vip.iqiyi.com/services/weixinNotice.action

account.unbindForGash.url=http://account.iqiyi.com/pay/dut/unbindForGash.action
autorenew.url=http://serv.vip.iqiyi.com/services/autoRenew.action

# passport \u4E3B\u7AD9\u63A5\u53E3
passport.queryUserByUid.url=http://passport.qiyi.domain/apis/profile/byUid.action
passport.queryUserByUsername.url=http://passport.qiyi.domain/apis/profile/byUsername.action
passport.batchQueryUserByUids.url=http://passport.qiyi.domain/apis/profile/batch/byUids.action
passport.profileInfo.url=http://passport.qiyi.domain/apis/profile/info.action
passport.inner.user.uid.info.url=http://am.passport.qiyi.domain/apis/user/inner/uid.action

# passport \u56FD\u9645\u7AD9\u63A5\u53E3
#passport.queryUserByUid.url=http://intl-passport.qiyi.domain/intl/inner/user/byUid.action
#passport.batchQueryUserByUids.url=http://intl-passport.qiyi.domain/intl/inner/user/batch/byUids.action
#passport.queryUserByUsername.url=http://intl-passport.qiyi.domain/intl/inner/user/byUsername.action
#passport.profileInfo.url=http://intl-passport.qiyi.domain/intl/user/info.action
passport.authCookie.key=P00001



# vip-commons-component \u914D\u7F6E
userInfoApi.fallbackTimeOut=500
vinfo.queryVipInfo.url=http://vinfo.vip.qiyi.domain/internal/vip_users
serv.orderVerify.url=http://serv.vip.qiyi.domain/order/verify.action
serv.autoRenewStatus.url=http://serv.vip.qiyi.domain/services/autoRenewStatus.action
serv.notityAutoRenewDut.url=http://serv.vip.qiyi.domain/pay/notify/dut-autorenew.action
vinfo.queryVipsInfos.url=http://vinfo.vip.qiyi.domain/internal/users/{uid}/vip_infos
vinfo.batchQueryVipUsers.url=http://vinfo.vip.qiyi.domain/internal/batch/vip_users

#\u81EA\u52A8\u7EED\u8D39\u53D8\u66F4\u6D88\u606FRocketMQ\u914D\u7F6E
autorenew.msg.rmq.namesrvaddr=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
autorenew.remind.msg.producer.token=PT-bf91335d-3b89-43c3-9190-462466f45fa7

#\u81EA\u52A8\u7EED\u8D39\u8425\u9500\u6D88\u606FRocketMQ\u914D\u7F6E
autorenew.marketing.rmq.namesrvaddr=vip-act-rocketmq-dev001-bjdx11.qiyi.virtual:9876;vip-act-rocketmq-dev003-bjdx11.qiyi.virtual:9876
dut.discount.consumed.producer.token=PT-3c1797fb-988b-4948-bae2-053d11cc68f3

# Order Sharding Configuration
order.sharding.database.urls=******************************************************,******************************************************
order.sharding.database.username=vip_order_test
order.sharding.database.password=agh3!schingood5TR$
order.sharding.tableSize=2
#\u4E92\u52A8\u8425\u9500\u8425\u9500\u4F4D\u4FE1\u606F\u67E5\u8BE2\u63A5\u53E3
interact.marketing.show.api=http://act.vip.qiyi.domain/interact/api/v2/show

#\u81EA\u52A8\u7EED\u8D39\u8425\u9500\u670D\u52A1\u57DF\u540D
autorenew.marketing.domain=http://act.vip.qiyi.domain
autorenew.marketing.invoke.channel=autorenew
autorenew.marketing.invoke.sign.key=123456

#Smart Jedis\u914D\u7F6E, hostName=vip-test.bjdx.qiyi.redis
smart.jedis.appId=boss-cloud-config-redis
smart.jedis.cluster=vip-test
smart.jedis.namespace=application
smart.jedis.env=PRO
smart.jedis.password=1b86da5e24413b2ec3961ceccbe0a3a4
smart.jedis.pool.minIdle=10
smart.jedis.pool.maxIdle=30
smart.jedis.pool.maxActive=30
smart.jedis.pool.maxWaitMillis=1000
smart.jedis.read.timeout=2000
smart.jedis.connection.timeout=1000

#redisson lock
redisson.address=redis://vip-test.bjdx.qiyi.redis:18524
redisson.password=ZSKnkI71BWSTUCoNoB4
redisson.connectTimeout=2000
redisson.timeout=1000
redisson.retryAttempts=2
redisson.retryInterval=300
redisson.connectionPoolSize=30
redisson.connectionMinimumIdleSize=10

#\u4E1A\u52A1\u52A0\u9501\u8BBE\u7F6E\uFF08\u901A\u7528\uFF09
business.redisson.lock.leaseTime=3000

## \u8C37\u6B4C\u7968\u636E\u4FE1\u606F\u67E5\u8BE2
google.receipt.query.url=http://inter.pay.qiyi.domain/pay-product-iap/google/googlebillingsign/getReceipt?
##退款接口
#refund.url=http://i.vip.qiyi.domain/refundService/order/common/refund
refund.url=http://localhost:8002/refundService/order/common/refund
refund.sign.key=1c8c1d2cbfe5e5695476aacf3ad52f9
