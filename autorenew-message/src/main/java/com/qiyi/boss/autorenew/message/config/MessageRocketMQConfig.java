//package com.qiyi.boss.autorenew.message.config;
//
//import org.apache.rocketmq.client.producer.DefaultMQProducer;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import com.qiyi.vip.trade.autorenew.constants.MQConstants;
//
///**
// * Created at: 2020-05-27
// *
// * <AUTHOR>
// */
////@Configuration
//public class MessageRocketMQConfig {
//
//    @Value("${autorenew.msg.rmq.namesrvaddr}")
//    private String autorenewMsgRMQNameServAddr;
//    @Value("${autorenew.changed.msg.producer.token}")
//    private String autorenewChangedMsgProducerToken;
//    @Value("${passwordfree.changed.msg.producer.token}")
//    private String passwordFreeChangedMsgProducerToken;
//    @Value("${autorenew.signrelation.changed.msg.producer.token}")
//    private String autorenewSignRelationChangedMsgProducerToken;
//    @Value("${agreement.changed.msg.producer.token}")
//    private String agreementChangedMsgProducerToken;
//
//    @Value("${autorenew.open.direct.status.msg.producer.token}")
//    private String autoRenewOpenDirectStatusProducerToken;
//
//
//    @Bean(name = "autorenewChangedMsgProducer", initMethod = "start",destroyMethod = "shutdown")
//    public DefaultMQProducer autorenewChangedMsgProducer() {
//        DefaultMQProducer producer = new DefaultMQProducer(MQConstants.AUTORENEW_CHANGED_PRODUCER_GROUP);
//        producer.setNamesrvAddr(autorenewMsgRMQNameServAddr);
//        producer.setToken(autorenewChangedMsgProducerToken);
//        producer.setSendMsgTimeout(1000);
//        return producer;
//    }
//
//    @Bean(name = "autorenewSignRelationChangedMsgProducer", initMethod = "start",destroyMethod = "shutdown")
//    public DefaultMQProducer autorenewSignRelationChangedMsgProducer() {
//        DefaultMQProducer producer = new DefaultMQProducer(MQConstants.AUTORENEW_SIGN_RELATION_CHANGED_PRODUCER_GROUP);
//        producer.setNamesrvAddr(autorenewMsgRMQNameServAddr);
//        producer.setToken(autorenewSignRelationChangedMsgProducerToken);
//        producer.setSendMsgTimeout(1000);
//        return producer;
//    }
//
//    @Bean(name = "passwordFreeChangedMsgProducer", initMethod = "start",destroyMethod = "shutdown")
//    public DefaultMQProducer passwordFreeChangedMsgProducer() {
//        DefaultMQProducer producer = new DefaultMQProducer(MQConstants.PASSWORDFREE_CHANGED_PRODUCER_GROUP);
//        producer.setNamesrvAddr(autorenewMsgRMQNameServAddr);
//        producer.setToken(passwordFreeChangedMsgProducerToken);
//        producer.setSendMsgTimeout(2000);
//        return producer;
//    }
//
//    @Bean(name = "agreementChangedMsgProducer", initMethod = "start",destroyMethod = "shutdown")
//    public DefaultMQProducer agreementChangedMsgProducer() {
//        DefaultMQProducer producer = new DefaultMQProducer(MQConstants.AGREEMENT_CHANGED_PRODUCER_GROUP);
//        producer.setNamesrvAddr(autorenewMsgRMQNameServAddr);
//        producer.setToken(agreementChangedMsgProducerToken);
//        producer.setSendMsgTimeout(3000);
//        return producer;
//    }
//
//    @Bean(name = "openDirectTagStatusProducer", initMethod = "start",destroyMethod = "shutdown")
//    public DefaultMQProducer openDirectTagStatusProducer() {
//        DefaultMQProducer producer = new DefaultMQProducer(MQConstants.OPEN_DIRECT_TAG_STATUS_PRODUCER_GROUP);
//        producer.setNamesrvAddr(autorenewMsgRMQNameServAddr);
//        producer.setToken(autoRenewOpenDirectStatusProducerToken);
//        producer.setSendMsgTimeout(3000);
//        return producer;
//    }
//
//}
