package com.qiyi.boss.autorenew.message.handlers;

import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> jiexiu
 * DateTime: 18-9-3 下午3:35
 * Mail:<EMAIL>
 * Desc: description
 */
@Component
@Slf4j
public class DutRenewLogHandler extends AbstractEventHandler<DutUserNew> {

    private static final String TABLE_NAME = "boss_dut_renew_log";

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    @Override
    protected void doHandleEvent(String event) {
        log.info(event);
    }
}
