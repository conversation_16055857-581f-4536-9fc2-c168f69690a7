package com.qiyi.boss.autorenew.message.handlers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.qiyi.boss.Constants;
import com.qiyi.boss.autorenew.dto.OpenDutSetLogDesp;
import com.qiyi.boss.autorenew.enumerate.ActTypeEnum;
import com.qiyi.boss.autorenew.enumerate.OperateSceneEnum;
import com.qiyi.boss.autorenew.message.mysqlio.CanalEvent;
import com.qiyi.boss.autorenew.message.mysqlio.CanalEventUtil;
import com.qiyi.boss.autorenew.message.mysqlio.DutRenewSetLogEvent;
import com.qiyi.boss.autorenew.message.service.AutorenewChangedMsgSender;
import com.qiyi.boss.enums.AgreementStatusEnum;
import com.qiyi.boss.enums.AgreementTypeEnum;
import com.qiyi.boss.enums.HystrixCommandPropsEnum;
import com.qiyi.boss.enums.SameTimeClosedStatusEnum;
import com.qiyi.boss.enums.VipTypeUpgradeGroupEnum;
import com.qiyi.boss.model.AgreementChangedMsg;
import com.qiyi.boss.service.UserAgreementService;
import com.qiyi.boss.service.impl.AgreementNoInfoManager;
import com.qiyi.boss.service.impl.AgreementTemplateManager;
import com.qiyi.boss.service.impl.AutoRenewDutTypeManager;
import com.qiyi.boss.service.impl.AutoRenewVipTypeUpgradeConfigManager;
import com.qiyi.boss.service.impl.DutManager;
import com.qiyi.boss.service.impl.PaymentDutTypeManager;
import com.qiyi.boss.service.impl.QiYuePlatformManager;
import com.qiyi.boss.utils.ApplicationContextUtil;
import com.qiyi.boss.utils.AutoRenewConfig;
import com.qiyi.boss.utils.CommonHystrixCommand;
import com.qiyi.boss.utils.DateHelper;
import com.qiyi.boss.utils.PromotionProcessApi;
import com.qiyi.vip.commons.constant.QueryConstants;
import com.qiyi.vip.commons.enums.OrderStatusEnum;
import com.qiyi.vip.commons.enums.ProductTypeEnum;
import com.qiyi.vip.commons.enums.VipTypesEnum;
import com.qiyi.vip.trade.autorenew.constants.MQConstants;
import com.qiyi.vip.trade.autorenew.domain.AgreementNoInfo;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewDutType;
import com.qiyi.vip.trade.autorenew.domain.AutoRenewVipTypeUpgradeConfig;
import com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog;
import com.qiyi.vip.trade.autorenew.domain.DutUserNew;
import com.qiyi.vip.trade.autorenew.domain.PaymentDutType;
import com.qiyi.vip.trade.autorenew.domain.Platform;
import com.qiyi.vip.trade.autorenew.mq.MsgSenderService;
import com.qiyi.vip.trade.autorenew.service.DutRenewSetLogService;
import com.qiyi.vip.trade.dataservice.client.DataServiceClient;
import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;
import com.qiyi.vip.trade.dataservice.client.request.QueryOrdersRequest;
import com.qiyi.vip.trade.dataservice.client.response.QueryOrdersResponse;
import com.iqiyi.solar.config.client.CloudConfig;

import static com.qiyi.boss.constants.AgreementConstants.EXCLUDE_AGREEMENT_TYPES;
import static com.qiyi.vip.trade.autorenew.constants.ConfigConstants.ACCMULATE_AUTORENEW_MSG;
import static com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog.RENEW_CANCEL;
import static com.qiyi.vip.trade.autorenew.domain.DutRenewSetLog.RENEW_SET;

/**
 * <AUTHOR> jiexiu
 * DateTime: 18-9-3 下午3:41
 * Mail:<EMAIL>
 * Desc: description
 */
@Component
@Slf4j
public class DutRenewSetLogHandler extends AbstractEventHandler<DutRenewSetLog> {

    private static final String TABLE_NAME = "boss_dut_renew_set_log";
    private static final String THIRD_UID = "thirdUid";

    public static final String CANCEL_EVENT_TYPE_USER = "1";
    
    public static final String CANCEL_EVENT_TYPE_SYSTEM = "2";

    @Resource
    private CloudConfig cloudConfig;

    @Resource
    private AutoRenewConfig autoRenewConfig;

    @Resource
    private DutManager dutManager;

    @Resource
    private PromotionProcessApi promotionProcessApi;

    @Resource
    AutorenewChangedMsgSender autorenewChangedMsgSender;
    @Resource
    MsgSenderService msgSenderService;
    @Resource
    AutoRenewDutTypeManager autoRenewDutTypeManager;
    @Resource
    QiYuePlatformManager qiYuePlatformManager;
    @Resource
    PaymentDutTypeManager paymentDutTypeManager;
    @Resource
    DutRenewSetLogService dutRenewSetLogService;
    @Resource
    UserAgreementService userAgreementService;
    @Resource
    AgreementNoInfoManager agreementNoInfoManager;

    @Resource
    AutoRenewVipTypeUpgradeConfigManager autoRenewVipTypeUpgradeConfigManager;

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    @Override
    public boolean accept(CanalEvent<?> event) {
        return super.accept(event) && CanalEventUtil.isInsert(event.getEventType());
    }

    @Override
    protected void doHandleEvent(String event) {
        log.info("DutRenewSetLogHandler, event:{}", event);
        if (cloudConfig.getBooleanProperty(ACCMULATE_AUTORENEW_MSG, false)) {
            throw new RuntimeException("累积自动续费状态变更消息!");
        }

        DutRenewSetLogEvent dutRenewSetLogEvent = JSON.parseObject(event, DutRenewSetLogEvent.class);
        DutRenewSetLog dutRenewSetLog = dutRenewSetLogEvent.getRowAfter();
        Long userId = dutRenewSetLog.getUserId();
        if (Objects.isNull(userId)) {
            log.error("发送自动续费消息-userId 不能为空");
            return;
        }
        Long vipType = dutRenewSetLog.getVipType();
        Integer dutType = dutRenewSetLog.getType();
        Integer agreementNo = dutRenewSetLog.getAgreementNo();
        Integer operate = dutRenewSetLog.getOperator();
        AgreementTypeEnum agreementType = AgreementTypeEnum.valueOf(dutRenewSetLog.getAgreementType());
        if (agreementType == null || !EXCLUDE_AGREEMENT_TYPES.contains(agreementType.getValue())) {
            DutUserNew dutUserNew = null;
            if (agreementNo != null) {
                dutUserNew = userAgreementService.getByAgreementNoAndVipType(userId, agreementNo, vipType);
            } else {
                dutUserNew = userAgreementService.getByDutTypeAndVipType(userId, dutType, vipType);
            }
            if (null == dutUserNew) {
                log.error("发送自动续费消息-不能找到对应的用户签约信息");
                return;
            }
            Map<String, String> messageBody = constructMessageBody(dutRenewSetLog, dutUserNew);
            if (dutManager.shouldSendAutoRenewSignRelationChangeMQ(operate)) {
                boolean isSuccess = autorenewChangedMsgSender.sendAutorenewSignRelationChangedMsg(messageBody);
                log.info("Send autorenew sign relation changed message success!. topicData:{} topic:{} result:{}",
                        messageBody, MQConstants.AUTORENEW_SIGN_RELATION_CHANGED_PRODUCER_GROUP, isSuccess);
            }

            if (!dutManager.shouldSendAutoRenewStatusChangeMQ(operate, userId, vipType, dutType)) {
                return;
            }

            if (RENEW_CANCEL == operate && studentRemainsZeroPeriod(dutUserNew)) {
                send24TimesRemind(dutRenewSetLog);
            }

            if (RENEW_SET == operate) {
                addSameTimeClosedStatus(messageBody, dutRenewSetLog);
            }
            boolean isSuccess = autorenewChangedMsgSender.sendAutorenewChangedMsg(messageBody);
            log.info("Send autorenew changed message success!. topicData:{} topic:{} result:{}",
                    messageBody, MQConstants.AUTORENEW_CHANGED_TOPIC, isSuccess);
        }
        if (agreementNo != null) {
            AgreementNoInfo agreementNoInfo = agreementNoInfoManager.getById(agreementNo);
            AgreementChangedMsg agreementChangedMsg = AgreementChangedMsg.buildFromSetLog(dutRenewSetLog, agreementNoInfo);
            autorenewChangedMsgSender.sendAgreementChangedMsg(agreementChangedMsg);
        }
    }

    private void addSameTimeClosedStatus(Map<String, String> messageBody, DutRenewSetLog dutRenewSetLog) {
        if (MapUtils.isEmpty(messageBody)) {
            return;
        }
        Long userId = dutRenewSetLog.getUserId();
        String operateTime = DateHelper.getDateString(dutRenewSetLog.getOperateTime());
        if (StringUtils.isBlank(operateTime)) {
            return;
        }
        Integer agreementType = dutRenewSetLog.getAgreementType();
        DutRenewSetLog sameTimeClosedSetLog = dutRenewSetLogService.queryDutSetLogsByUserId(userId, agreementType, operateTime, operateTime, null)
            .stream()
            .filter(Objects::nonNull)
            .filter(s -> s.getOperator().equals(RENEW_CANCEL))
            .filter(s -> !EXCLUDE_AGREEMENT_TYPES.contains(s.getAgreementType()))
            .findFirst().orElse(null);
        boolean sameTimeClosed = sameTimeClosedSetLog != null;
        int sameTimeClosedStatus = sameTimeClosed ? SameTimeClosedStatusEnum.CLOSED.getValue() : SameTimeClosedStatusEnum.NOT_CLOSED.getValue();
        messageBody.put("sameTimeClosedStatus", String.valueOf(sameTimeClosedStatus));
        boolean hasOtherSameVipGroupAppleAutoRenew = hasOtherSameVipGroupAppleAutoRenew(dutRenewSetLog);
        int autoRenewContinuedStatus = BooleanUtils.toInteger(sameTimeClosed || hasOtherSameVipGroupAppleAutoRenew);
        messageBody.put("autoRenewContinuedStatus", String.valueOf(autoRenewContinuedStatus));
    }

    private boolean hasOtherSameVipGroupAppleAutoRenew(DutRenewSetLog dutRenewSetLog) {
        Long userId = dutRenewSetLog.getUserId();
        Long vipType = dutRenewSetLog.getVipType();
        Integer type = dutRenewSetLog.getType();
        Integer agreementType = dutRenewSetLog.getAgreementType();
        List<Long> vipTypeList = autoRenewVipTypeUpgradeConfigManager.getSameGroupVipTypes(vipType, agreementType);
        AutoRenewVipTypeUpgradeConfig vipTypeUpgradeConfig = autoRenewVipTypeUpgradeConfigManager.getByVipType(vipType, agreementType);
        if (vipTypeUpgradeConfig != null && VipTypeUpgradeGroupEnum.isMainSiteUpgradeGroup(vipTypeUpgradeConfig.getUpgradeGroup())) {
            vipTypeList.add(VipTypesEnum.VIPTYPE_BASIC.getValue().longValue());
        }
        Set<Integer> appleDutTypes = autoRenewDutTypeManager.listByPayChannelAndAgreementType(PaymentDutType.PAY_CHANNEL_IAP, agreementType)
            .stream()
            .map(AutoRenewDutType::getDutType)
            .collect(Collectors.toSet());
        DutUserNew sameGroupAppleDutUserNew = userAgreementService.getByAgreementTypeAndVipType(userId, AgreementTypeEnum.AUTO_RENEW, null, AgreementStatusEnum.VALID)
            .stream()
            .filter(d -> !d.getType().equals(type))
            .filter(d -> appleDutTypes.contains(d.getType()))
            .filter(d -> vipTypeList.contains(d.getVipType()))
            .filter(d -> d.getUpdateTime() != null && d.getUpdateTime().before(dutRenewSetLog.getOperateTime()))
            .findAny().orElse(null);
        return sameGroupAppleDutUserNew != null;
    }

    private Map<String, String> constructMessageBody(DutRenewSetLog dutRenewSetLog, DutUserNew dutUserNew) {
        try {
            return constructAutoRenewMQMap(dutRenewSetLog, dutUserNew);
        } catch (JsonProcessingException e) {
            log.error("解析数据异常, dutRenewSetLog:{}", dutRenewSetLog);
            return Collections.emptyMap();
        }
    }

    private Map<String, String> constructAutoRenewMQMap(DutRenewSetLog dutRenewSetLog, DutUserNew currentDutUserNew) throws JsonProcessingException {
        Map<String, String> topicData = Maps.newHashMap();
        Long userId = dutRenewSetLog.getUserId();
        Long vipType = dutRenewSetLog.getVipType();
        Integer type = dutRenewSetLog.getType();
        topicData.put("msgtype", Constants.MESSAGE_TYPE_AUTO_RENEW);
        topicData.put("uid", String.valueOf(userId));
        topicData.put("vipType", String.valueOf(vipType));
        topicData.put("operator", String.valueOf(dutRenewSetLog.getOperator()));
        // 兼容原有的消息
        topicData.put("type", String.valueOf(type));
        topicData.put("agreementNo", dutRenewSetLog.getAgreementNo() != null ? dutRenewSetLog.getAgreementNo().toString() : null);
        topicData.put("agreementType", dutRenewSetLog.getAgreementType() != null ? dutRenewSetLog.getAgreementType().toString() : null);
        topicData.put("platformCode", dutRenewSetLog.getPlatformCode());
        String description = dutRenewSetLog.getDescription();
        topicData.put("description", description);
        topicData.put("fc", dutRenewSetLog.getFc());
        topicData.put("amount", dutRenewSetLog.getAmount() == null ? DutUserNew.DEFAULT_AUTORENEW_AMOUNT : String.valueOf(dutRenewSetLog.getAmount()));
        Long sourceVipType = currentDutUserNew.getSourceVipType();
        topicData.put("isUpGrade", sourceVipType != null ? "1" : "0");
        topicData.put("sourceVipType", sourceVipType == null ? null : sourceVipType.toString());
        topicData.put("renew_price", currentDutUserNew.getRenewPrice() != null ? String.valueOf(currentDutUserNew.getRenewPrice()) : null);
        topicData.put("operate_time", String.valueOf(dutRenewSetLog.getOperateTime().getTime()));
        topicData.put("operate_time_new", String.valueOf(System.currentTimeMillis()));


        String sceneType = getSceneType(dutRenewSetLog);
        if (StringUtils.isNotBlank(sceneType)) {
            topicData.put("sceneType", sceneType);
        }
        if (StringUtils.isBlank(dutRenewSetLog.getPlatformCode()) && dutRenewSetLog.getPlatform() != null) {
            Platform platform = qiYuePlatformManager.getBossPlatformById(dutRenewSetLog.getPlatform());
            if (platform != null) {
                topicData.put("platformCode", platform.getCode());
            }
        }

        AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(type);
        if (autoRenewDutType != null) {
            Integer payChannel = autoRenewDutType.getPayChannel();
            topicData.put("payChannel", String.valueOf(payChannel));
            if (shouldSearchAliPayAccountId(dutRenewSetLog, autoRenewDutType)) {
                Optional<String> aliPayUid = searchAliPayAccountId(dutRenewSetLog, autoRenewDutType);
                addAliPayUid(aliPayUid, topicData, description);
                updateDutUserNewSetLog(aliPayUid, dutRenewSetLog);
            }
        }
        List<DutUserNew> allDutUserNews = userAgreementService.getByExcludeAgreementTypesAndVipTypes(userId, EXCLUDE_AGREEMENT_TYPES, null, AgreementStatusEnum.VALID);
        if (needReQueryAutoRenew(allDutUserNews, sceneType, dutRenewSetLog)) {
            allDutUserNews = userAgreementService.getByExcludeAgreementTypesAndVipTypes(userId, EXCLUDE_AGREEMENT_TYPES, null, AgreementStatusEnum.VALID);
            log.info("reQueryAutoRenew :{}", allDutUserNews);
        }
        List<DutUserNew> sameVipDutUserNew = allDutUserNews.stream().filter(d -> ObjectUtils.equals(d.getVipType(), vipType)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sameVipDutUserNew)) {
            List<Integer> dutTypes = sameVipDutUserNew.stream().map(DutUserNew::getType).collect(Collectors.toList());
            List<AutoRenewDutType> autoRenewDutTypes = autoRenewDutTypeManager.getByDutTypes(dutTypes);
            List<Integer> payChannels = autoRenewDutTypes.stream()
                .map(AutoRenewDutType::getPayChannel)
                .distinct()
                .sorted(Integer::compareTo)
                .collect(Collectors.toList());
            String payChannelArrays = CollectionUtils.isNotEmpty(payChannels) ? JSONArray.toJSONString(payChannels) : null;
            if (StringUtils.isNotBlank(payChannelArrays)) {
                topicData.put("openedPayChannels", payChannelArrays);
            }
            //获取用户其他已开通的自动续费信息
            List<DutUserNew> otherSameVipDutUserNewList = sameVipDutUserNew
                .stream()
                .filter(e -> !ObjectUtils.equals(e.getType(), type) || !ObjectUtils.equals(e.getAgreementNo(), dutRenewSetLog.getAgreementNo()))
                .collect(Collectors.toList());
            putOtherAutoRenewList(getAutoRenewList(otherSameVipDutUserNewList), topicData, "otherAutoRenewList");
        }

        List<DutUserNew> allOtherDutUserNewList = allDutUserNews.stream()
            .filter(a -> !ObjectUtils.equals(a.getType(), type) || !ObjectUtils.equals(a.getAgreementNo(), dutRenewSetLog.getAgreementNo()))
            .collect(Collectors.toList());
        List<AutoRenew> autoRenewList = getAutoRenewList(allOtherDutUserNewList);
        putOtherAutoRenewList(autoRenewList, topicData, "allOtherAutoRenewList");
        putAllOtherPayChannelAndAgreementTypeStr(autoRenewList, topicData);
        return topicData;
    }

    private boolean needReQueryAutoRenew(List<DutUserNew> allDutUserNews, String sceneType, DutRenewSetLog dutRenewSetLog) {
        return CollectionUtils.isEmpty(allDutUserNews)
            && ObjectUtils.equals(String.valueOf(DutUserNew.RENEW_EXCHANGE), sceneType)
            && dutRenewSetLog.getOperator().equals(RENEW_CANCEL);
    }

    private static void putAllOtherPayChannelAndAgreementTypeStr(List<AutoRenew> autoRenewList, Map<String, String> topicData) {
        if (CollectionUtils.isEmpty(autoRenewList)) {
            return;
        }
        String allOtherPayChannelAndAgreementStr = autoRenewList.stream()
            .filter(Objects::nonNull)
            .filter(a -> a.getAgreementType() != null)
            .map(a -> String.format("%s_%s", a.getPayChannel(), a.getAgreementType()))
            .distinct()
            .collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(allOtherPayChannelAndAgreementStr)) {
            topicData.put("allOtherPayChannelAndAgreementTypeStr", allOtherPayChannelAndAgreementStr);
        }
    }

    private void putOtherAutoRenewList(List<AutoRenew> otherAutoRenewList, Map<String, String> topicData, String key) throws JsonProcessingException {
        if (CollectionUtils.isEmpty(otherAutoRenewList)) {
            return;
        }
        ObjectMapper mapper = new ObjectMapper();
        topicData.put(key, mapper.writeValueAsString(otherAutoRenewList));
    }

    private List<AutoRenew> getAutoRenewList(List<DutUserNew> otherDutUserNewList) {
        List<AutoRenew> otherAutoRenewList = Lists.newArrayList();
        for (DutUserNew dutUserNew : otherDutUserNewList) {
            AutoRenew autoRenew = AutoRenew.builder()
                    .dutType(dutUserNew.getType())
                    .agreementNo(dutUserNew.getAgreementNo())
                    .agreementType(dutUserNew.getAgreementType())
//                    .payChannel(autoRenewDutTypeManager.getByDutType(dutUserNew.getType()).getPayChannel())
                    .amount(dutUserNew.getAmount())
                    .orderCode(dutUserNew.getOrderCode())
                    .build();
            AutoRenewDutType autoRenewDutType = autoRenewDutTypeManager.getByDutType(dutUserNew.getType());
            if (autoRenewDutType != null) {
                Integer payChannel = autoRenewDutType.getPayChannel();
                autoRenew.setPayChannel(payChannel);
            }
            otherAutoRenewList.add(autoRenew);
        }
        return otherAutoRenewList;
    }

    private String getSceneType(DutRenewSetLog dutRenewSetLog) {
        String scene = Optional.ofNullable(dutRenewSetLog)
            .map(DutRenewSetLog::parseOpenSetLogDescription)
            .map(OpenDutSetLogDesp::getScene)
            .orElse(null);
        Integer operator = dutRenewSetLog.getOperator();
        if (scene == null) {
            if (Objects.equals(operator, RENEW_CANCEL)) {
                return String.valueOf(DutUserNew.RENEW_NOT_AUTO);
            }
            if (Objects.equals(operator, RENEW_SET)) {
                return String.valueOf(DutUserNew.RENEW_AUTO);
            }
            return null;
        }
        return Optional.ofNullable(OperateSceneEnum.parseValue(scene))
            .map(OperateSceneEnum::getSceneType)
            .map(String::valueOf)
            .orElse(null);
    }

    private boolean missingAliPayThirdUid(DutRenewSetLog dutRenewSetLog, AutoRenewDutType autoRenewDutType) {
        ObjectMapper objectMapper = new ObjectMapper();
        String deacription = dutRenewSetLog.getDescription();
        try {
            return StringUtils.isNotBlank(deacription)
                    && autoRenewDutType.isMainlandAlipayDutType()
                    && RENEW_SET == dutRenewSetLog.getOperator()
                    && !objectMapper.readTree(deacription).has(THIRD_UID);
        } catch (IOException e) {
            log.error("parse dutRenewSetLog description error! error:{}", e);
            return false;
        }

    }

    private Optional<String> searchAliPayAccountId(DutRenewSetLog dutRenewSetLog, AutoRenewDutType autoRenewDutType) {
        log.info("[searchAliPayAccountId][dutRenewSetLog:{}]", dutRenewSetLog);
        Integer payChannel = autoRenewDutType.getPayChannel();
        Integer vipType = autoRenewDutType.getVipType().intValue();

        List<Integer> payTypes = paymentDutTypeManager.getPayTypes(payChannel, vipType);
        if (CollectionUtils.isEmpty(payTypes)) {
            return Optional.empty();
        }
        QueryOrdersRequest queryOrdersRequest = new QueryOrdersRequest();
        queryOrdersRequest.addParam("productType", String.valueOf(ProductTypeEnum.PRODUCT_TYPE_MONTHLY.getType()));
        queryOrdersRequest.addParam("userId", dutRenewSetLog.getUserId().toString());
        queryOrdersRequest.addParam("status", String.valueOf(OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()));
        queryOrdersRequest.addParam("IN_payType", payTypes.stream().map(String::valueOf).collect(Collectors.joining(",")));
        queryOrdersRequest.addParam(QueryConstants.QUERY_PARAM_LIMIT, "3");
        queryOrdersRequest.addParam(QueryConstants.QUERY_PARAM_SORT, "-create_time");
        try {
            Function<QueryOrdersRequest, QueryOrdersResponse> queryOrdersRunFunc = this::doGet;
            Function<QueryOrdersRequest, QueryOrdersResponse> queryOrdersFallbackFunc = t -> null;
            QueryOrdersResponse response = new CommonHystrixCommand<>(
                    HystrixCommandPropsEnum.AUTORENEW_QUERY_DATASERVICE,
                    queryOrdersRunFunc,
                    queryOrdersFallbackFunc,
                    queryOrdersRequest).
                    execute();
            if (!response.isSuccessful() || response.getData() == null) {
                log.info("[searchAliPayAccountUid: not find!,uid:{}, vipType:{}", dutRenewSetLog.getUserId(), vipType);
                return Optional.empty();
            }
            return response.getData().stream().map(OrderDto::getAccountId).filter(StringUtils::isNotBlank).findFirst();
        } catch (Exception e) {
            log.info("订单查询查询订单异常, params:{}", queryOrdersRequest);
            return Optional.empty();
        }
    }



    private QueryOrdersResponse doGet(QueryOrdersRequest queryOrdersRequest) {
        DataServiceClient dataServiceClient = ApplicationContextUtil.getBean(DataServiceClient.class);
        return dataServiceClient.execute(queryOrdersRequest);
    }


    /**
     * @param dutUserNew {@link DutUserNew}
     * @return return true if period is zero.
     */
    private boolean studentRemainsZeroPeriod(DutUserNew dutUserNew) {
        return ActTypeEnum.STOP_AFTER_X.getValue().equals(dutUserNew.getActType())
                && !promotionProcessApi.canBuy(dutUserNew.getUserId());
    }

    private void send24TimesRemind(DutRenewSetLog dutRenewSetLog) {
        Map<String, String> dataMap = Maps.newHashMap();
        String msgType = Constants.MESSAGE_TYPE_STUDENT_AUTORENEW_24_TIMES;
        dataMap.put("msgtype", msgType);
        dataMap.put("uid", String.valueOf(dutRenewSetLog.getUserId()));
        dataMap.put("vipType", String.valueOf(dutRenewSetLog.getVipType()));
        boolean isSuccess = msgSenderService.sendAutorenewRemindMsg(dataMap, msgType);
        log.info("[DutRenewSetLogHandler] [Send MQ when student autorenew reaches 24 times.] [params:{}] [result:{}]", dataMap, isSuccess);
    }

    private Boolean shouldSearchAliPayAccountId(DutRenewSetLog dutRenewSetLog, AutoRenewDutType autoRenewDutType) {
        return autoRenewConfig.autorenewMessageSearchAliPayAccountId() && missingAliPayThirdUid(dutRenewSetLog, autoRenewDutType);
    }

    private void addAliPayUid(Optional<String> aliPayUid, Map<String, String> topicData, String description) {
        if (!aliPayUid.isPresent()) {
            return;
        }
        String uid = MapUtils.getString(topicData,"uid");
        String vipType = MapUtils.getString(topicData, "vipType");
        log.info("[searchAliPayAccountUid: find aliPayUid !,uid:{}, vipType:{}, aliPayUid:{}", uid, vipType, aliPayUid.get());
        try {
            ObjectNode objectNode = (ObjectNode) new ObjectMapper().readTree(description);
            objectNode.put("thirdUid", aliPayUid.get());
            topicData.put("description", objectNode.toString());
        } catch (Exception e) {
            log.error("自动续费开通消息体添加支付宝id失败！error:{}", e);
        }
    }

    private void updateDutUserNewSetLog(Optional<String> aliPayUid, DutRenewSetLog dutRenewSetLog) {
        if (aliPayUid.isPresent()) {
            JSONObject descriptionObject = JSONObject.parseObject(dutRenewSetLog.getDescription());
            descriptionObject.put("thirdUid", aliPayUid.get());
            dutRenewSetLog.setDescription(descriptionObject.toString());
            dutRenewSetLogService.updateSetLog(dutRenewSetLog);
        }
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class AutoRenew {
        private int dutType;
        private Integer agreementNo;
        private int payChannel;
        private int amount;
        private String orderCode;
        private Integer agreementType;
    }

}
