spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true
spring.mvc.pathmatch.matching-strategy=ant_path_matcher

spring.jackson.serialization.write-dates-as-timestamps=true

#HTTP encoding
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true
spring.jackson.serialization.indent_output=true

logging.config=classpath:logback.xml


#对外合作签约关系域名
partner.renew.server.domain=http://partner-renew-api-online
partner.renew.server.sign.key=da2d37db8ef62c0c

#内容查询服务域名
content.query.server.domain=http://vip-content-query-server-online
content.query.server.sign.key=54CC7F940A4E30F12FD5D7BCC341EFFE

#jetCache config
jetcache.penetrationProtect=true
jetcache.areaInCacheName=false
jetcache.statIntervalMinutes=3
jetcache.hidePackages=com.alibaba
jetcache.local.default.type=caffeine
jetcache.local.default.keyConvertor=jackson
jetcache.local.default.limit=1000
jetcache.local.default.expireAfterWriteInMillis=300000

# 停机等待时间
v.spring.cloud.service-registry.graceful-shutdown.wait-timeout-millis=20000
# 这个配置会导致不能前置主动下线
eureka.client.healthcheck.enabled=false
# eureka client刷新本地缓存时间, 默认30s
eureka.client.registryFetchIntervalSeconds=5