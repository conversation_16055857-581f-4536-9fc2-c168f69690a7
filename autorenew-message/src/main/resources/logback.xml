<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <Pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%X{tracing_id}] [%C{1}:%M:%L] %m%n</Pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>/data/logs/autorenew-message/autorenew-message.log</File>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <Pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%X{tracing_id}] [%C{1}:%M:%L] %m%n</Pattern>
            </layout>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/autorenew-message/autorenew-message.log.%d{yyyy-MM-dd_HH}</fileNamePattern>
            <maxHistory>15</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    <appender name ="ASYNC_ROLLING_FILE" class= "ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold >0</discardingThreshold>
        <queueSize>1024</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref ="RollingFile"/>
    </appender>

    <appender name="ERROR_DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>/data/logs/autorenew-message/autorenew-message-error.log</File>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <Pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%X{tracing_id}] [%C{1}:%M:%L] %m%n</Pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/autorenew-message/autorenew-message-error.log.%d{yyyy-MM-dd_HH}</fileNamePattern>
            <maxHistory>15</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    <appender name ="ASYNC_ERROR_DAILY_ROLLING_FILE" class= "ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold >0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>1024</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref ="ERROR_DAILY_ROLLING_FILE"/>
    </appender>

    <logger name="com.qiyi.boss" level="INFO"/>

    <logger name="monitor" level="DEBUG" additivity="false"/>

    <springProfile name="dev,test">
        <root level="INFO">
            <appender-ref ref="Console"/>
            <appender-ref ref="ASYNC_ROLLING_FILE"/>
            <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="ASYNC_ROLLING_FILE"/>
            <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
        </root>
    </springProfile>

</configuration>